[tool:pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts =
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=80
    --asyncio-mode=auto
asyncio_default_fixture_loop_scope = function
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    requires_neo4j: Tests that require Neo4j database
    requires_llm: Tests that require LLM API access
