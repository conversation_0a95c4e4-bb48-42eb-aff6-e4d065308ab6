# Get the model name from the environment or default to gpt-4o
import os
import httpx

from dotenv import load_dotenv
from langchain_openai import OpenAI

# Load environment variables
load_dotenv()

models = [
     os.getenv("OPENAI_MODEL_NAME", "azure/gpt-4"),
    "azure/gpt-4o",
    "bedrock/anthropic.claude-3.5-v2",
    "bedrock/anthropic.claude-3.7",
    "azure/dalle3"
]

current_model = models[1]  # Default to the first model in the list

api_key     = os.getenv("OPENAI_API_KEY" ,    "")
base_url    = os.getenv("OPENAI_ENDPOINT",    "")
api_version = os.getenv("OPENAI_API_VERSION", "")

def llm() -> OpenAI:
    """
    Get the initialized LLM instance.

    Returns:
        OpenAI: The initialized LLM instance.
    """    
    return OpenAI(
        api_key     = api_key,  # Unpack the tuple
        base_url    = base_url,  # Unpack the tuple
        #temperature = 0.7,
        max_tokens  = 16384,
        model_name  = current_model, 
        http_client = httpx.Client(verify=False)  # Disable SSL verification for local testing
    )

WRAPPED_RESPONSE = False

def invoke_llm(messages: list) -> str:
    if WRAPPED_RESPONSE:
        return llm.invoke(messages).content
    else:
        return llm.invoke(messages)

# Initialize the LLM via Azure OpenAI (using LangChain's OpenAI binding)
llm = llm()






