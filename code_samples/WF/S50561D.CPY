000100**** START OF S50561D ***** IDS FPS COMMON CONDITION CODES *******
000200*                                                                *
000300*    COMMON IDS FPS CONDITION CODE CONSTANTS                     *
000400*                                                                *
000500******************************************************************
000600*
000700 01  IDS-FPS-COMMON-COND-CODES-X.
000800*
000900*        *** FUNCTION/COMMAND INVALID       ***
001000     05  COND-CD-56001       PIC S9(8) COMP  VALUE +56001.
001100     05  FILLER REDEFINES COND-CD-56001.
001200         10  FILLER                    PIC XX.
001300         10  COND-CODE-56001           PIC XX.
001400*
001500*        *** ENTER IDS PROD/ACCOUNT NUMBER  ***
001600     05  COND-CD-56002       PIC S9(8) COMP  VALUE +56002.
001700     05  FILLER REDEFINES COND-CD-56002.
001800         10  FILLER                    PIC XX.
001900         10  COND-CODE-56002           PIC XX.
002000*
002100*        *** ENTER DDA PROD/ACCOUNT NUMBER  ***
002200     05  COND-CD-56003       PIC S9(8) COMP  VALUE +56003.
002300     05  FILLER REDEFINES COND-CD-56003.
002400         10  FILLER                    PIC XX.
002500         10  COND-CODE-56003           PIC XX.
002600*
002700*        *** ENTER TDA PROD/ACCOUNT NUMBER  ***
002800     05  COND-CD-56004       PIC S9(8) COMP  VALUE +56004.
002900     05  FILLER REDEFINES COND-CD-56004.
003000         10  FILLER                    PIC XX.
003100         10  COND-CODE-56004           PIC XX.
003200*
003300*        *** ENTER PRODUCT CODE             ***
003400     05  COND-CD-56005       PIC S9(8) COMP  VALUE +56005.
003500     05  FILLER REDEFINES COND-CD-56005.
003600         10  FILLER                    PIC XX.
003700         10  COND-CODE-56005           PIC XX.
003800*
003900*        *** ENTER ACCOUNT NUMBER           ***
004000     05  COND-CD-56006       PIC S9(8) COMP  VALUE +56006.
004100     05  FILLER REDEFINES COND-CD-56006.
004200         10  FILLER                    PIC XX.
004300         10  COND-CODE-56006           PIC XX.
004400*
004500*        *** PRODUCT CODE INVALID           ***
004600     05  COND-CD-56007       PIC S9(8) COMP  VALUE +56007.
004700     05  FILLER REDEFINES COND-CD-56007.
004800         10  FILLER                    PIC XX.
004900         10  COND-CODE-56007           PIC XX.
005000     EJECT
005100*        *** ACCOUNT NOT FOUND              ***
005200     05  COND-CD-56008       PIC S9(8) COMP  VALUE +56008.
005300     05  FILLER REDEFINES COND-CD-56008.
005400         10  FILLER                    PIC XX.
005500         10  COND-CODE-56008           PIC XX.
005600*
005700*        *** NO TRANSACTIONS FOUND          ***
005800     05  COND-CD-56009       PIC S9(8) COMP  VALUE +56009.
005900     05  FILLER REDEFINES COND-CD-56009.
006000         10  FILLER                    PIC XX.
006100         10  COND-CODE-56009           PIC XX.
006200*
006300*        *** FUNC NOT SUPPORTED FOR PRODUCT ***
006400     05  COND-CD-56010       PIC S9(8) COMP  VALUE +56010.
006500     05  FILLER REDEFINES COND-CD-56010.
006600         10  FILLER                    PIC XX.
006700         10  COND-CODE-56010           PIC XX.
006800*
006900*        *** FUNC NOT SUPPORTED FOR APPL    ***
007000     05  COND-CD-56011       PIC S9(8) COMP  VALUE +56011.
007100     05  FILLER REDEFINES COND-CD-56011.
007200         10  FILLER                    PIC XX.
007300         10  COND-CODE-56011           PIC XX.
007400*
007500*        *** FUNC ONLY VALID FOR IDS ACCTS  ***
007600     05  COND-CD-56012       PIC S9(8) COMP  VALUE +56012.
007700     05  FILLER REDEFINES COND-CD-56012.
007800         10  FILLER                    PIC XX.
007900         10  COND-CODE-56012           PIC XX.
008000*
008100*        *** FUNC ONLY VALID FOR CDA ACCTS  ***
008200     05  COND-CD-56013       PIC S9(8) COMP  VALUE +56013.
008300     05  FILLER REDEFINES COND-CD-56013.
008400         10  FILLER                    PIC XX.
008500         10  COND-CODE-56013           PIC XX.
008600*
008700*        *** FUNC ONLY VALID FOR CSV ACCTS  ***
008800     05  COND-CD-56014       PIC S9(8) COMP  VALUE +56014.
008900     05  FILLER REDEFINES COND-CD-56014.
009000         10  FILLER                    PIC XX.
009100         10  COND-CODE-56014           PIC XX.
009200*
009300*        *** FUNC ONLY VALID FOR REA ACCTS  ***
009400     05  COND-CD-56015       PIC S9(8) COMP  VALUE +56015.
009500     05  FILLER REDEFINES COND-CD-56015.
009600         10  FILLER                    PIC XX.
009700         10  COND-CODE-56015           PIC XX.
009800     EJECT
009900*        *** FUNC ONLY VALID FOR RSV ACCTS  ***
010000     05  COND-CD-56016       PIC S9(8) COMP  VALUE +56016.
010100     05  FILLER REDEFINES COND-CD-56016.
010200         10  FILLER                    PIC XX.
010300         10  COND-CODE-56016           PIC XX.
010400*
010500*        *** FUNC ONLY VALID FOR TOA ACCTS  ***
010600     05  COND-CD-56017       PIC S9(8) COMP  VALUE +56017.
010700     05  FILLER REDEFINES COND-CD-56017.
010800         10  FILLER                    PIC XX.
010900         10  COND-CODE-56017           PIC XX.
011000*
011100*        *** FUNC ONLY VALID FOR DDA ACCTS  ***
011200     05  COND-CD-56018       PIC S9(8) COMP  VALUE +56018.
011300     05  FILLER REDEFINES COND-CD-56018.
011400         10  FILLER                    PIC XX.
011500         10  COND-CODE-56018           PIC XX.
011600*
011700*        *** FUNC ONLY VALID FOR LOC ACCTS  ***
011800     05  COND-CD-56019       PIC S9(8) COMP  VALUE +56019.
011900     05  FILLER REDEFINES COND-CD-56019.
012000         10  FILLER                    PIC XX.
012100         10  COND-CODE-56019           PIC XX.
012200*
012300*        *** COMMAND/LINE ACTION CONFLICT   ***
012400     05  COND-CD-56020       PIC S9(8) COMP  VALUE +56020.
012500     05  FILLER REDEFINES COND-CD-56020.
012600         10  FILLER                    PIC XX.
012700         10  COND-CODE-56020           PIC XX.
012800*
012900*        *** LINE ACTION INVALID            ***
013000     05  COND-CD-56021       PIC S9(8) COMP  VALUE +56021.
013100     05  FILLER REDEFINES COND-CD-56021.
013200         10  FILLER                    PIC XX.
013300         10  COND-CODE-56021           PIC XX.
013400*
013500*        *** NO LINE ACTION INDICATED       ***
013600     05  COND-CD-56022       PIC S9(8) COMP  VALUE +56022.
013700     05  FILLER REDEFINES COND-CD-56022.
013800         10  FILLER                    PIC XX.
013900         10  COND-CODE-56022           PIC XX.
014000*
014100*        *** NO CHANGES INDICATED           ***
014200     05  COND-CD-56023       PIC S9(8) COMP  VALUE +56023.
014300     05  FILLER REDEFINES COND-CD-56023.
014400         10  FILLER                    PIC XX.
014500         10  COND-CODE-56023           PIC XX.
014600     EJECT
014700*        *** MUST FIRST "INQ" ON ACCOUNT    ***
014800     05  COND-CD-56024       PIC S9(8) COMP  VALUE +56024.
014900     05  FILLER REDEFINES COND-CD-56024.
015000         10  FILLER                    PIC XX.
015100         10  COND-CODE-56024           PIC XX.
015200*
015300*        *** "SF" (PF-8) TO VIEW MORE ACCTS ***
015400     05  COND-CD-56025       PIC S9(8) COMP  VALUE +56025.
015500     05  FILLER REDEFINES COND-CD-56025.
015600         10  FILLER                    PIC XX.
015700         10  COND-CODE-56025           PIC XX.
015800*
015900*        *** "SF" (PF-8) TO VIEW MORE TXNS  ***
016000     05  COND-CD-56026       PIC S9(8) COMP  VALUE +56026.
016100     05  FILLER REDEFINES COND-CD-56026.
016200         10  FILLER                    PIC XX.
016300         10  COND-CODE-56026           PIC XX.
016400*
016500*        *** LAST PAGE OF ACCOUNTS          ***
016600     05  COND-CD-56027       PIC S9(8) COMP  VALUE +56027.
016700     05  FILLER REDEFINES COND-CD-56027.
016800         10  FILLER                    PIC XX.
016900         10  COND-CODE-56027           PIC XX.
017000*
017100*        *** LAST PAGE OF TRANSACTIONS      ***
017200     05  COND-CD-56028       PIC S9(8) COMP  VALUE +56028.
017300     05  FILLER REDEFINES COND-CD-56028.
017400         10  FILLER                    PIC XX.
017500         10  COND-CODE-56028           PIC XX.
017600*
017700*        *** AMF MASTER FILE I/O ERROR      ***
017800     05  COND-CD-56029       PIC S9(8) COMP  VALUE +56029.
017900     05  FILLER REDEFINES COND-CD-56029.
018000         10  FILLER                    PIC XX.
018100         10  COND-CODE-56029           PIC XX.
018200*
018300*        *** TDA MASTER FILE I/O ERROR      ***
018400     05  COND-CD-56030       PIC S9(8) COMP  VALUE +56030.
018500     05  FILLER REDEFINES COND-CD-56030.
018600         10  FILLER                    PIC XX.
018700         10  COND-CODE-56030           PIC XX.
018800*
018900*        *** SIM CALL RTN'D SICB-RESULT = 1 ***
019000*        *** INVALID SIM FUNCTION CALL      ***
019100     05  COND-CD-56031       PIC S9(8) COMP  VALUE +56031.
019200     05  FILLER REDEFINES COND-CD-56031.
019300         10  FILLER                    PIC XX.
019400         10  COND-CODE-56031           PIC XX.
019500     EJECT
019600*        *** SIM CALL RTN'D SICB-RESULT = 2 ***
019700*        *** TO SYSTEM/APPL NOT AVAILABLE   ***
019800     05  COND-CD-56032       PIC S9(8) COMP  VALUE +56032.
019900     05  FILLER REDEFINES COND-CD-56032.
020000         10  FILLER                    PIC XX.
020100         10  COND-CODE-56032           PIC XX.
020200*
020300*        *** SIM CALL RTN'D SICB-RESULT = 3 ***
020400*        *** SIM FUNCTION NOT SUPPORTED     ***
020500     05  COND-CD-56033       PIC S9(8) COMP  VALUE +56033.
020600     05  FILLER REDEFINES COND-CD-56033.
020700         10  FILLER                    PIC XX.
020800         10  COND-CODE-56033           PIC XX.
020900*
021000*        *** SIM CALL RTN'D SICB-RESULT = ? ***
021100*        *** UNKNOWN SICB-RESULT FROM SIM   ***
021200     05  COND-CD-56034       PIC S9(8) COMP  VALUE +56034.
021300     05  FILLER REDEFINES COND-CD-56034.
021400         10  FILLER                    PIC XX.
021500         10  COND-CODE-56034           PIC XX.
021600*
021700*        *** TRANS HISTORY FILE I/O ERROR   ***
021800     05  COND-CD-56035       PIC S9(8) COMP  VALUE +56035.
021900     05  FILLER REDEFINES COND-CD-56035.
022000         10  FILLER                    PIC XX.
022100         10  COND-CODE-56035           PIC XX.
022200*
022300*        *** ENTER LOC PROD/ACCOUNT NUMBER  ***
022400     05  COND-CD-56036       PIC S9(8) COMP  VALUE +56036.
022500     05  FILLER REDEFINES COND-CD-56036.
022600         10  FILLER                    PIC XX.
022700         10  COND-CODE-56036           PIC XX.
022800*
022900*        *** ADD/CHG/DEL INVALID - MEMO-INQ ***
023000     05  COND-CD-56037       PIC S9(8) COMP  VALUE +56037.
023100     05  FILLER REDEFINES COND-CD-56037.
023200         10  FILLER                    PIC XX.
023300         10  COND-CODE-56037           PIC XX.
023400*
023500*        *** END OF COMPANY                 ***
023600     05  COND-CD-56038       PIC S9(8) COMP  VALUE +56038.
023700     05  FILLER REDEFINES COND-CD-56038.
023800         10  FILLER                    PIC XX.
023900         10  COND-CODE-56038           PIC XX.
024000     EJECT
024100*
024200*        *** END OF FILE                    ***
024300     05  COND-CD-56039       PIC S9(8) COMP  VALUE +56039.
024400     05  FILLER REDEFINES COND-CD-56039.
024500         10  FILLER                    PIC XX.
024600         10  COND-CODE-56039           PIC XX.
024700*
024800*        *** INVALID FPS PCD1690 USER ENVMT ***
024900     05  COND-CD-56040       PIC S9(8) COMP  VALUE +56040.
025000     05  FILLER REDEFINES COND-CD-56040.
025100         10  FILLER                    PIC XX.
025200         10  COND-CODE-56040           PIC XX.
025201*
025202*        *** FUNC ONLY VALID FOR TDA ACCTS  ***
025203     05  COND-CD-56068       PIC S9(8) COMP  VALUE +56068.
025204     05  FILLER REDEFINES COND-CD-56068.
025205         10  FILLER                    PIC XX.
025206         10  COND-CODE-56068           PIC XX.
025300*                                                                *
025400**** END OF S50561D ******* IDS FPS COMMON CONDITION CODES *******
