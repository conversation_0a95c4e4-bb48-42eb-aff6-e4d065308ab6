000100* HOGAN OS390                                                     CC078712
000200 IDENTIFICATION DIVISION.
000300 PROGRAM-ID. T50543                                               CC078712
       COPY COS390RE.
000500 AUTHOR.
000600*CRT*************************************************************
000700*CRV*      IDS  VERSION 4  RELEASE 1  CST 0  MLU 0              *
000800*CRT*************************************************************
000900*CRT*  This software contains trade secrets and confidential    *
001000*CRT*  information which are proprietary to Computer Sciences   *
001100*CRT*  Corporation.  The use, reproduction, distribution, or    *
001200*CRT*  disclosure of the software, in whole or in part, without *
001300*CRT*  The express written permission of Computer Sciences      *
001400*CRT*  Corporation is prohibited.  This software is also an     *
001500*CRT*  unpublished work protected under the copyright laws of   *
001600*CRT*  the United States of America and other countries.  If    *
001700*CRT*  this software becomes published, the following notice    *
001800*CRT*  shall apply:                                             *
001900*CRY*      Copyright (C) 2012 Computer Sciences Corporation     *
002000*CRT*      All Rights Reserved.                                 *
002100*CRT*************************************************************
002200*CRK* IDS4.1.0.0
002300 DATE-COMPILED.
002400*****************************************************************
002500*
002600*            PROGRAM INFORMATION
002700*
002800*                NAME:           AIMU ACCT INQUIRY/DISPLAY DRVR
002900*                PEM PROGRAM ID: 2543
003000*                LINK ACTIVITY:  2543
003100*
003200*            PROGRAM PURPOSE
003300*
003400*                THIS PROGRAM DRIVES ALL DATA SCREENS FOR THE
003500*                ACCOUNT INQUIRY FACILITY (AIMU).  IT SUPPORTS
003600*                DDA, LOC, AND TDA PRODUCTS.
003700*
003800     EJECT
003900****************************************************************
004000*                                                              *
004100*   THE ACCOUNT INQUIRY MENU AND DATA SCREENS ARE ACCESSED     *
004200*   THRU THE INTEGRATED DEPOSIT SYSTEM MASTER MENU (IDMU)      *
004300*   BY SELECTING AIMU OR OPTION 4 FROM THE MENU.               *
004400*                                                              *
004500*   THE FUNCTIONS THIS PROGRAM PERFORMS INCLUDE:               *
004600*                                                              *
004700*    - VALIDATE THAT THE OPERATOR ACTION IS INQ/NXT OR         *
004800*      DDA/LOC FOR REQUESTING DDA/LOC DATA SCREEN SWAPPING.    *
004900*      IF OPERATOR ACTION IS NOT INQ/NXT/DDA/LOC, SET          *  CC068155
005000*      CONDITION CODE '56001' (FUNCTION/COMMAND INVALID).      *  CC068155
005100*      THE FPS SCENARIO WILL USE '56001' TO BRANCH TO THE      *  CC068155
005200*      FPS ENTRY WITH KEY OF "IDS AIMU 997".                   *  CC068155
005300*                                                              *
005400*    - LOOKUP THE PRODUCT IN T50999D AND DETERMINE IF THE      *
005500*      PRODUCT IS VALID.                                       *  CC068155
005600*                                                              *
005700*    - DYNAMICALLY ALLOCATE EITHER AMF (12XX) OR TDA (35XX)    *
005800*      DATAGROUPS IN THE LINKAGE SECTION.                      *
005900*                                                              *
006000*    - READ EITHER THE AMF, AMFIN, TDA, OR TDAIN MASTER FILE   *
006100*      RANDOMLY (INQ) OR SEQUENTIALLY (NXT).                   *
006200*                                                              *
006300*    - SET CONDITION CODE IN THE TCB TO CONTROL THE FPS        *
006400*      SCENARIO TO EITHER DISPLAY A SCREEN OR BRANCH TO        *
006500*      OTHER AIMU FUNCTIONS IF PRODUCT CODE IS CHANGED.        *
006600*                                                              *
006700****************************************************************
006800     EJECT
006900 ENVIRONMENT DIVISION.
007000 CONFIGURATION SECTION.
007100 SOURCE-COMPUTER.     IBM-370.
007200 OBJECT-COMPUTER.     IBM-370.
007300 INPUT-OUTPUT SECTION.
007400 FILE-CONTROL.
007500 DATA DIVISION.
007600*
007700 FILE SECTION.
007800*
007900 WORKING-STORAGE SECTION.
008000 77  CC-T50543                PIC  X(08)  VALUE 'T50543'.
008100 77  CC-DDA-WILDCARD          PIC  X(03)  VALUE 'DD*'.            CC068155
008200 77  CC-TDA-WILDCARD          PIC  X(03)  VALUE 'TD*'.            CC068155
008300 77  CP-ONE                   PIC S9(03)  VALUE +001  COMP-3.
008400*
008500 01  CC-CONSTANTS.
008600     05  CC-NXT               PIC  X(04)  VALUE 'NXT '.
008700     05  CC-INQ               PIC  X(04)  VALUE 'INQ '.
008800     05  CC-DDA               PIC  X(03)  VALUE 'DDA'.
008900     05  CC-LOC               PIC  X(03)  VALUE 'LOC'.
009000     05  CC-TDA               PIC  X(03)  VALUE 'TDA'.
009100     05  CC-YES               PIC  X      VALUE 'Y'.              CC060004
009200     05  CC-NO                PIC  X      VALUE 'N'.              CC060004
009300*
009400*
009500 01  CH-CONSTANTS.
009600     05  CH-FULLWORDS.
009700         10  FILLER           PIC S9(08) COMP VALUE  +00003.
009800         10  FILLER           PIC S9(08) COMP VALUE  +00004.
009900     05  CH-HALFWORDS         REDEFINES CH-FULLWORDS.
010000         10  FILLER.
010100             15  FILLER       PIC XX.
010200             15  CH-03        PIC XX.
010300         10  FILLER.
010400             15  FILLER       PIC XX.
010500             15  CH-04        PIC XX.
010600
010700*
010800 01  ACTIVITIES-X.
010900*    *** VERIFY O/L - COMPANY/ENVIRONMENT/FILE STATUS ***
011000     05  PEM-ACTY-13399.
011100         10 FILLER      PIC S9(08) COMP VALUE +13399.
011200     EJECT
       COPY S50561D.
011400     EJECT
011500****************************************************************
011600*    CONDITION CODES USED BY THIS PROGRAM                      *
011700****************************************************************
011800*
011900 01  CONDITION-CODES-X.
012000*        *** COMMAND INVALID FROM CURR SCRN ***
012100     05  COND-CD-50624       PIC S9(08) COMP  VALUE +50624.
012200     05  FILLER REDEFINES COND-CD-50624.
012300         10  FILLER                    PIC XX.
012400         10  COND-CODE-50624           PIC XX.
012500*
012600*        *** LOC DOES NOT EXIST FOR ACCT    ***
012700     05  COND-CD-50625       PIC S9(08) COMP  VALUE +50625.
012800     05  FILLER REDEFINES COND-CD-50625.
012900         10  FILLER                    PIC XX.
013000         10  COND-CODE-50625           PIC XX.
013100*
013200*        *** END OF COMPANY                 ***
013300     05  COND-CD-50626       PIC S9(08) COMP  VALUE +50626.
013400     05  FILLER REDEFINES COND-CD-50626.
013500         10  FILLER                    PIC XX.
013600         10  COND-CODE-50626           PIC XX.
013700*
013800*        *** END OF FILE                    ***
013900     05  COND-CD-50627       PIC S9(08) COMP  VALUE +50627.
014000     05  FILLER REDEFINES COND-CD-50627.
014100         10  FILLER                    PIC XX.
014200         10  COND-CODE-50627           PIC XX.
014300*
014400*        *** SWAP BETWEEN DDA/LOC/TDA SCRNS ***
014500     05  COND-CD-50629       PIC S9(08) COMP  VALUE +50629.
014600     05  FILLER REDEFINES COND-CD-50629.
014700         10  FILLER                    PIC XX.
014800         10  COND-CODE-50629           PIC XX.
014900*                                                                 CC068155
015000*        *** PRODUCT CHG CAUSED SCREEN SWAP  **                   CC068155
015100     05  COND-CD-56069       PIC S9(08) COMP  VALUE +56069.       CC068155
015200     05  FILLER REDEFINES COND-CD-56069.                          CC068155
015300         10  FILLER                    PIC XX.                    CC068155
015400         10  COND-CODE-56069           PIC XX.                    CC068155
015500     EJECT
       COPY P49003D.
015700     EJECT
       COPY P49022D.
015900     EJECT
       COPY P49023D.
016100     EJECT
       COPY T50999D.
016300     EJECT
016400 01  APPLICATION-ACTIVITY-TABLE.
016500*
016600     05  FILLER         PIC  X(03)       VALUE 'DDA'.
016700     05  FILLER         PIC S9(08) COMP  VALUE +01200.
016800     05  FILLER         PIC S9(08) COMP  VALUE +01201.
016900     05  FILLER         PIC S9(08) COMP  VALUE +02552.
017000     05  FILLER         PIC S9(08) COMP  VALUE +02554.
017100     05  FILLER         PIC S9(08) COMP  VALUE +02524.
017200*
017300     05  FILLER         PIC  X(03)       VALUE 'LOC'.
017400     05  FILLER         PIC S9(08) COMP  VALUE +01200.
017500     05  FILLER         PIC S9(08) COMP  VALUE +01231.
017600     05  FILLER         PIC S9(08) COMP  VALUE +02552.
017700     05  FILLER         PIC S9(08) COMP  VALUE +02554.
017800     05  FILLER         PIC S9(08) COMP  VALUE +02524.
017900*
018000     05  FILLER         PIC  X(03)       VALUE 'TDA'.
018100     05  FILLER         PIC S9(08) COMP  VALUE +03500.
018200     05  FILLER         PIC S9(08) COMP  VALUE +03501.
018300     05  FILLER         PIC S9(08) COMP  VALUE +02553.
018400     05  FILLER         PIC S9(08) COMP  VALUE +02555.
018500     05  FILLER         PIC S9(08) COMP  VALUE +02525.
018600*
018700 01  APPL-ACT-TABLE-ENTRIES  REDEFINES APPLICATION-ACTIVITY-TABLE.
018800     05  APPL-ACT-ENTRY        OCCURS 3 TIMES
018900                               INDEXED BY INDX-APPL.
019000         10  APPL-NAME.
019100             15  APPL-NAME1       PIC X(01).
019200             15  APPL-NAME2-3     PIC X(02).
019300         10  APPL-DYN-DGID-POS3   PIC X(04).
019400         10  APPL-DYN-DGID-POS4   PIC X(04).
019500         10  APPL-READ-ACTY       PIC X(04).
019600         10  APPL-INQ-READ-ACTY   PIC X(04).
019700         10  APPL-DG-INT-ACTY     PIC X(04).
019800     EJECT
019900 LINKAGE SECTION.
       COPY P49000D.
020100     EJECT
       COPY I53589D.
020300     EJECT
       COPY T50543D.
020500     EJECT
       COPY T50000D.
020700     EJECT
       COPY T50001D.
020900     EJECT
       COPY T50031D.
021100     EJECT
       COPY I47018D.
021300     EJECT
       COPY S50560D.
021500     EJECT
       COPY U50470D.
021700     EJECT                                                        CC068155
       COPY T50120D.
021900     EJECT                                                        CC068155
022000 PROCEDURE DIVISION
022100     USING
022200         TRANSACTION-CONTROL-BLOCK
022300         W-FPS-COMM-DATA-GROUP
022400         W543-WORK-DATAGROUP
022500         M-KEY-GROUP
022600         M-ACCOUNT-CODING-STRUCTURE
022700         L-ACCOUNT-CODING-STRUCTURE
022800         PCD-DEP-DATA
022900         W560-GLOBAL-IDSP-DATA-GROUP                              CC068155
023000         W470-IDS-FPS-SCENARIO-CNTL-DG                            CC068155
023100         M-ASSOCIATED-ACCOUNTS.                                   CC068155
023200
023300
023400 AA000-INITIALIZATION SECTION.                                    CC055864
023500
023600     MOVE LOW-VALUES  TO  TCB-USER-CC
023700                          TCB-RESULT
023800                          TCB-USER-CC-APP.
023900
024000     IF  W-FPS-COMM-RESULT IS EQUAL TO COND-CODE-56069            CC068155
024100*        *** PRODUCT CHG CAUSED SCREEN SWAP --- SIMULATE A  ***   CC068155
024200*        *** DISPLAY FIRST AND JUST DISPLAY CURRENT SCREEN  ***   CC068155
024300         GO TO ZZ000-END-OF-PROCESSING.                           CC068155
024400                                                                  CC068155
024500     MOVE W-FPS-COMM-CURR-SCRN-ID TO W543-CURR-SCRN-ID.           CC068155
024600                                                                  CC068155
024700     IF  W-FPS-COMM-ACTION-CD IS GREATER THAN SPACES
024800         MOVE  W-FPS-COMM-ACTION-CD-1-6  TO  W543-OPER-ACTION
024900     ELSE
025000         MOVE  W543-OPER-ACTION  TO  W-FPS-COMM-ACTION-CD.
025100
025200     IF  W543-ACCOUNT-NUMBER NOT NUMERIC
025300         MOVE  ZEROES  TO  W543-ACCOUNT-NUMBER.
025400
025500     MOVE W-FPS-COMM-RESULT TO W560-IDSP-COMM-RESULT-X.
025600     MOVE LOW-VALUES        TO W560-IDSP-COMM-RESULT-HI.          CC055864
025700     IF  W560-IDSP-COMM-RESULT-EQ-11560
025800         MOVE W560-IDSP-INQUIRY-KEY  TO  W543-ACCOUNT-KEY
025900     ELSE
026000         MOVE W543-ACCOUNT-KEY  TO  W560-IDSP-INQUIRY-KEY.
026100
026200     EJECT
026300 BA000-MAINLINE-T50543 SECTION.
026400*
026500     PERFORM BB000-VALIDATE-ACTION.
026600     IF  TCB-USER-CC IS NOT EQUAL TO LOW-VALUES
026700         GO TO ZZ000-END-OF-PROCESSING.
026800*
026900     PERFORM BC000-VALIDATE-PRODUCT.
027000     IF  TCB-USER-CC IS NOT EQUAL TO LOW-VALUES
027100         GO TO ZZ000-END-OF-PROCESSING.
027200*
027300     PERFORM BD000-VALIDATE-APPLICATION.
027400     IF  TCB-USER-CC IS NOT EQUAL TO LOW-VALUES
027500         GO TO ZZ000-END-OF-PROCESSING.
027600*
027700**** VERIFY ONLINE - COMPANY/ENVIRONMENT/FILE/ETC
027800*
027900     MOVE PEM-ACTY-13399 TO TCB-LONG-ACTIVITY.
028000     PERFORM CA000-CALL-PEM.
028100     IF  TCB-USER-CC IS NOT EQUAL TO LOW-VALUES
028200         GO TO ZZ000-END-OF-PROCESSING.
028300*
028400     PERFORM BE000-VALIDATE-ACCOUNT.
028500*
028600     GO TO ZZ000-END-OF-PROCESSING.
028700*
028800 BA999-EXIT.
028900     EXIT.
029000     EJECT
029100 BB000-VALIDATE-ACTION SECTION.
029200
029300     IF  W543-OPER-ACTION NOT GREATER THAN SPACES
029400         MOVE CC-INQ TO W543-OPER-ACTION                          CC068155
029500         GO TO BB999-EXIT.                                        CC068155
029600
029700****************************************************************
029800*                                                              *
029900*    ANY FUNCTION/COMMAND ENTERED OTHER THAN 'INQ', 'NXT',     *
030000*    'DDA' OR 'LOC' WILL BE FURTHER INTERROGATED TO DECIDE     *
030100*    WHAT SHOULD BE DONE WITH IT.                              *
030200*                                                              *
030300****************************************************************
030400
030500     IF  NOT W543-VALID-OPER-ACTION
030600         MOVE SPACES TO W543-OPER-ACTION                          CC068155
030700*        **** FUNCTION/COMMAND INVALID ****                       CC068155
030800         MOVE COND-CODE-56001 TO TCB-USER-CC                      CC068155
030900         GO TO BB999-EXIT.
031000*
031100****************************************************************
031200*                                                              *
031300*    IF USING 'NXT' COMMAND WITH A BLANK PRODUCT CODE, SET     *
031400*    PRODUCT CODE TO 'DDA', 'LOC' OR 'TDA' AS INDICATED BY     *
031500*    THE CURRENT SCREEN ID.                                    *
031600*                                                              *
031700****************************************************************
031800*
031900     IF  W543-OPER-ACTION  IS EQUAL TO CC-NXT
032000     AND W543-PRODUCT-CODE NOT GREATER THAN SPACES
032100         IF  W543-DDA-SCRN-ID
032200             MOVE CC-DDA TO W543-PRODUCT-CODE
032300                            W560-IDSP-INQ-CC-PROD-CODE
032400         ELSE
032500         IF  W543-LOC-SCRN-ID
032600             MOVE CC-LOC TO W543-PRODUCT-CODE
032700                            W560-IDSP-INQ-CC-PROD-CODE
032800         ELSE
032900         IF  W543-TDA-SCRN-ID
033000             MOVE CC-TDA TO W543-PRODUCT-CODE
033100                            W560-IDSP-INQ-CC-PROD-CODE.
033200     EJECT
033300****************************************************************
033400*                                                              *
033500*    HANDLE PF-11 -- SWAPPING BETWEEN DDA/LOC SCREENS          *
033600*                                                              *
033700****************************************************************
033800*
033900     IF  W543-DDA-LOC-SWAP-SCRN-ACTION
034000         NEXT SENTENCE
034100     ELSE
034200         GO TO BB999-EXIT.
034300
034400     IF  W543-DDA-SCRN-ID
034500     AND W543-OPER-ACTION IS EQUAL TO CC-LOC
034600         MOVE CC-LOC TO W543-PRODUCT-CODE
034700         MOVE CC-INQ TO W543-OPER-ACTION
034800                        W-FPS-COMM-ACTION-CD
034900         GO TO BB999-EXIT.
035000
035100     IF  W543-LOC-SCRN-ID
035200     AND W543-OPER-ACTION IS EQUAL TO CC-DDA
035300         MOVE CC-DDA TO W543-PRODUCT-CODE
035400         MOVE CC-INQ TO W543-OPER-ACTION
035500                        W-FPS-COMM-ACTION-CD
035600         GO TO BB999-EXIT.
035700
035800*    **** COMMAND INVALID FROM CURR SCRN ****
035900     MOVE COND-CODE-50624 TO TCB-USER-CC.
036000*
036100 BB999-EXIT.
036200     EXIT.
036300     EJECT
036400
036500 BC000-VALIDATE-PRODUCT SECTION.
036600*
036700     IF  W543-PRODUCT-CODE NOT GREATER THAN SPACES
036800*        **** ENTER PRODUCT CODE ****
036900         MOVE COND-CODE-56005   TO TCB-USER-CC
037000         GO TO BC999-EXIT.
037100
037200     SET INDX-T50999D TO CP-ONE.
037300     SEARCH  PRODUCT-TABLE-T50999D
037400       AT END
037500*              **** PRODUCT CODE INVALID ***
037600               MOVE COND-CODE-56007 TO TCB-USER-CC
037700               GO TO BC999-EXIT
037800       WHEN
037900          ALPHA-PROD-CODE-T50999D (INDX-T50999D)
038000               IS EQUAL TO W543-PRODUCT-CODE
038100               NEXT SENTENCE.
038200
038300 BC999-EXIT.
038400     EXIT.
038500     EJECT
038600 BD000-VALIDATE-APPLICATION SECTION.
038700*
038800     SET INDX-APPL TO CP-ONE.
038900
039000     SEARCH  APPL-ACT-ENTRY
039100       AT END
039200*              **** FUNC NOT SUPPORTED FOR APPL ****
039300               MOVE COND-CODE-56011 TO TCB-USER-CC
039400               GO TO BD999-EXIT
039500       WHEN
039600               APPL-NAME (INDX-APPL) IS EQUAL TO
039700               ALPHA-APPL-CODE-T50999D (INDX-T50999D)
039800               NEXT SENTENCE.
039900*
040000*
040100 BD999-EXIT.
040200     EXIT.
040300     EJECT
040400 BE000-VALIDATE-ACCOUNT SECTION.
040500
040600     IF  W543-ACCOUNT-NUMBER GREATER THAN ZERO
040700         GO TO BE100-DYN-DG-ALLOC.
040800*
040900     IF  W543-OPER-ACTION IS NOT EQUAL TO CC-NXT
041000*        **** ENTER ACCOUNT NUMBER ****
041100         MOVE COND-CODE-56006 TO TCB-USER-CC
041200         GO TO BE400-NO-ACCOUNT-CONDITION.
041300
041400 BE100-DYN-DG-ALLOC.
041500
041600     PERFORM SB000-INIT-ACCOUNT-WORK-FIELDS.
041700*
041800     MOVE APPL-DG-INT-ACTY (INDX-APPL) TO TCB-LONG-ACTIVITY.
041900     PERFORM CA000-CALL-PEM.
042000
042100     MOVE  CA-LONG-PEM-DYN-DG-LENGTH   TO TCB-LONG-ACTIVITY.      C0077034
042200     MOVE  APPL-DYN-DGID-POS3 (INDX-APPL) TO TCB-LONG-DGID.
042300     MOVE  CH-03                       TO TCB-PARM-POS.
042400     PERFORM CA000-CALL-PEM.
042500     SET ADDRESS OF M-KEY-GROUP TO TCB-DYNAMIC-DG-ADDR.           C0077034
042600
042700     MOVE  CA-LONG-PEM-DYN-DG-LENGTH   TO TCB-LONG-ACTIVITY.      C0077034
042800     MOVE  APPL-DYN-DGID-POS4 (INDX-APPL) TO TCB-LONG-DGID.
042900     MOVE  CH-04                       TO TCB-PARM-POS.
043000     PERFORM CA000-CALL-PEM.
043100     SET ADDRESS OF M-ACCOUNT-CODING-STRUCTURE                    C0077034
043200                                  TO TCB-DYNAMIC-DG-ADDR.         C0077034
043300
043400     EJECT
043500 BE200-READ-MASTER.
043600
043700     MOVE DGA-READ-KEY-EQ              TO M-ACTION-T50000D.
043800     MOVE TCB-CO-ID                    TO M-CO-ID.
043900     MOVE W543-ACCOUNT-NUMBER          TO M-KEY.
044000     MOVE BINARY-PROD-CODE-T50999D (INDX-T50999D) TO M-PRDCT-CD.
044100
044200     IF W543-PRODUCT-CODE IS EQUAL TO CC-TDA OR CC-DDA OR CC-LOC
044300        MOVE LOW-VALUES                TO M-PRDCT-CD.
044400
044500     IF W543-OPER-ACTION    IS EQUAL TO CC-NXT OR
044600        W543-PRODUCT-CODE   IS EQUAL TO CC-TDA
044700        MOVE DGA-READ-KEY-GE           TO M-ACTION-T50000D
044800        MOVE M-PRDCT-CD                TO W543-PRDCT-CD
044900        ADD  1                         TO W543-PRDCT-CD-NUM
045000        MOVE W543-PRDCT-CD             TO M-PRDCT-CD.
045100
045200     IF M-KEY IS EQUAL TO ZERO
045300        MOVE HIGH-VALUES               TO M-PRDCT-CD.
045400
045500     IF  P-MEMO-INQ
045600         MOVE APPL-INQ-READ-ACTY (INDX-APPL) TO TCB-LONG-ACTIVITY
045700     ELSE
045800         MOVE APPL-READ-ACTY (INDX-APPL) TO TCB-LONG-ACTIVITY.
045900     PERFORM CA000-CALL-PEM.
046000
046100     EJECT
046200 BE300-CHECK-MASTER-RESULTS.
046300
046400     IF  M-RESULT-T50000D IS EQUAL TO DGR-OK      AND
046500         TCB-CO-ID    IS NOT EQUAL TO M-CO-ID
046600*        **** END OF COMPANY ****
046700         MOVE COND-CODE-50626 TO TCB-USER-CC
046800         GO TO BE400-NO-ACCOUNT-CONDITION.
046900
047000     IF  M-RESULT-T50000D IS EQUAL TO DGR-OK      AND
047100         W543-OPER-ACTION IS EQUAL TO CC-INQ      AND
047200         ALPHA-PROD-CODE-T50999D (INDX-T50999D)
047300                          IS EQUAL TO CC-TDA      AND
047400         M-KEY IS NOT EQUAL TO W543-ACCOUNT-NUMBER
047500*        **** ACCOUNT NOT FOUND ****
047600         MOVE COND-CODE-56008 TO TCB-USER-CC
047700         GO TO BE400-NO-ACCOUNT-CONDITION.
047800
047900     IF  M-RESULT-T50000D  IS EQUAL TO DGR-OK     AND
048000         W543-PRODUCT-CODE IS EQUAL TO CC-LOC     AND
048100         L-RESULT-T50031D  IS NOT EQUAL TO DGR-OK
048200         MOVE M-KEY            TO  W543-ACCOUNT-NUMBER
048300         MOVE W543-ACCOUNT-KEY TO  W560-IDSP-INQUIRY-KEY
048400*        **** LOC DOES NOT EXIST FOR ACCT ****
048500         MOVE COND-CODE-50625 TO TCB-USER-CC
048600         PERFORM SD000-CHECK-PROD-CODE-SWAP                       CC068155
048700         GO TO BE400-NO-ACCOUNT-CONDITION.
048800
048900     IF  M-RESULT-T50000D EQUAL DGR-OK
049000         MOVE M-KEY             TO  W543-ACCOUNT-NUMBER
049100         MOVE M-PRDCT-CODE      TO  W543-PRODUCT-CODE
049200         MOVE W543-ACCOUNT-KEY  TO  W560-IDSP-INQUIRY-KEY
049300         PERFORM SC000-SET-ACCOUNT-WORK-FIELDS
049400         PERFORM SD000-CHECK-PROD-CODE-SWAP                       CC068155
049500         GO TO BE999-EXIT.
049600
049700     IF  M-RESULT-T50000D EQUAL DGR-NO-FIND
049800*        **** ACCOUNT NOT FOUND ****
049900         MOVE COND-CODE-56008 TO TCB-USER-CC
050000         GO TO BE400-NO-ACCOUNT-CONDITION.
050100
050200     IF  M-RESULT-T50000D EQUAL DGR-END-DATA
050300*        **** END OF FILE ****
050400         MOVE COND-CODE-50627 TO TCB-USER-CC
050500         GO TO BE400-NO-ACCOUNT-CONDITION.
050600
050700     EJECT
050800*
050900     IF  W543-DDA-SCRN-ID  OR  W543-LOC-SCRN-ID
051000*        **** AMF MASTER FILE I/O ERROR
051100         MOVE COND-CODE-56029 TO TCB-USER-CC
051200     ELSE
051300*        **** TDA MASTER FILE I/O ERROR
051400         MOVE COND-CODE-56030 TO TCB-USER-CC.
051500*
051600 BE400-NO-ACCOUNT-CONDITION.
051700
051800     MOVE APPL-DG-INT-ACTY (INDX-APPL) TO TCB-LONG-ACTIVITY.
051900     PERFORM CA000-CALL-PEM.
052000
052100 BE999-EXIT.
052200     EXIT.
052300     EJECT
052400*
052500 CA000-CALL-PEM SECTION.
052600*
052700****************************************************************
052800*                                                              *
052900*     THIS SECTION OF THE PROGRAM IS USED FOR ALL CALLS TO     *
053000*     THE PROGRAM INTERFACE.                                   *
053100*                                                              *
053200****************************************************************
053300*
053400     CALL 'PEM' USING TRANSACTION-CONTROL-BLOCK.
053500*
053600 CA999-EXIT.
053700     EXIT.
053800     EJECT
053900 SB000-INIT-ACCOUNT-WORK-FIELDS SECTION.
054000*
054100     MOVE SPACES TO W543-LOC-INDICATOR                            CC068155
054200                    W543-PRIMARY-ACCT-PROD-CODE.                  CC068155
054300*
054400 SB999-EXIT.
054500     EXIT.
054600     EJECT
054700 SC000-SET-ACCOUNT-WORK-FIELDS  SECTION.
054800*
054900     IF  W543-PRODUCT-CODE IS EQUAL TO CC-DDA
055000     AND L-RESULT-T50031D  IS EQUAL TO DGR-OK
055100         MOVE CC-YES TO W543-LOC-INDICATOR
055200     ELSE
055300         MOVE CC-NO  TO W543-LOC-INDICATOR.
055400*
055500***************************************************************** CC068155
055600**   DETERMINE ALPHA PRODUCT CODE FOR THE PRIMARY ACCOUNT.     ** CC068155
055700**   PROCESS TDA ACCOUNTS ONLY. DO NOT PROCESS IF TDA          ** CC068155
055800**   ACCOUNT IS NOT LINKED.                                    ** CC068155
055900***************************************************************** CC068155
056000                                                                  CC068155
056100* **** IF NO RELATED ACCOUNTS, PRODUCT CODE NOT NEEDED ****       CC068155
056200                                                                  CC068155
056300     IF  W543-TDA-SCRN-ID                                         CC068155
056400         IF   M-RESULT-T50120D EQUAL DGR-OK AND                   CC068155
056500              M-RLK-PRM-ACCT-PRDCT-CD NOT EQUAL LOW-VALUES        CC068155
056600              SET INDX-T50999D TO CP-ONE                          CC068155
056700              SEARCH  PRODUCT-TABLE-T50999D                       CC068155
056800                WHEN                                              CC068155
056900                      BINARY-PROD-CODE-T50999D (INDX-T50999D)     CC068155
057000                      IS EQUAL TO M-RLK-PRM-ACCT-PRDCT-CD         CC068155
057100                      MOVE ALPHA-PROD-CODE-T50999D (INDX-T50999D) CC068155
057200                        TO W543-PRIMARY-ACCT-PROD-CODE.           CC068155
057300*                                                                 CC068155
057400 SC999-EXIT.
057500     EXIT.
057600     EJECT
057700 SD000-CHECK-PROD-CODE-SWAP  SECTION.                             CC068155
057800***************************************************************** CC068155
057900**   ENSURE DISPLAYING CORRECT SCREEN FOR PRODUCT CODE READ    ** CC068155
058000***************************************************************** CC068155
058100*                                                                 CC068155
058200     SET W470-PROD-IDX TO +1.                                     CC068155
058300*                                                                 CC068155
058400     SEARCH W470-VALID-PRODUCTS                                   CC068155
058500         AT END                                                   CC068155
058600               GO TO SD999-EXIT                                   CC068155
058700         WHEN                                                     CC068155
058800               W470-PRODUCT (W470-PROD-IDX) EQUAL                 CC068155
058900                                     W543-PRODUCT-CODE            CC068155
059000           OR                                                     CC068155
059100              (W470-PRODUCT (W470-PROD-IDX) EQUAL CC-TDA-WILDCARD CC068155
059200                    AND W543-TDA-SCRN-ID)                         CC068155
059300           OR                                                     CC068155
059400              (W470-PRODUCT (W470-PROD-IDX) EQUAL CC-DDA-WILDCARD CC068155
059500                    AND NOT W543-TDA-SCRN-ID)                     CC068155
059600               NEXT SENTENCE.                                     CC068155
059700*                                                                 CC068155
059800     IF  W470-PRODUCT-SWAP-KEY (W470-PROD-IDX) EQUAL SPACES       CC068155
059900         GO TO SD999-EXIT.                                        CC068155
060000*                                                                 CC068155
060100***************************************************************** CC068155
060200**   SET UP FPS PROCESSING SWAP BETWEEN PRODUCT SCREENS        ** CC068155
060300***************************************************************** CC068155
060400*                                                                 CC068155
060500     MOVE W470-PSK-APP  (W470-PROD-IDX)                           CC068155
060600                         TO W-FPS-COMM-APPL-CD.                   CC068155
060700     MOVE W470-PSK-FUNC (W470-PROD-IDX)                           CC068155
060800                         TO W-FPS-COMM-NEXT-CMND.                 CC068155
060900     MOVE W470-PSK-SEQ  (W470-PROD-IDX)                           CC068155
061000                         TO W-FPS-COMM-FUNC-BEGIN-SEQ.            CC068155
061100*                                                                 CC068155
061200*    **** SWAP BETWEEN PRODUCT SCRNS WITH DISPLAY FIRST ****      CC068155
061300     MOVE COND-CODE-56069 TO TCB-USER-CC.                         CC068155
061400*                                                                 CC068155
061500 SD999-EXIT.                                                      CC068155
061600     EXIT.                                                        CC068155
061700     EJECT                                                        CC068155
       COPY T50001P.
061900 END PROGRAM T50543.                                              CC078712
