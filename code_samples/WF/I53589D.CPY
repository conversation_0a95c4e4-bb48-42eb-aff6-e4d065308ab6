000100**** Start of I53589D ***** FPS Common Communication DG    *******
000200*                                                                *
000300*    Data Group Number  13589                                    *
000400*                                                                *
000500******************************************************************
000600*
000700 01  W-FPS-COMM-DATA-GROUP.
000800     05  W-ACTION-I53589D             PIC XX.
000900     05  W-RESULT-I53589D             PIC XX.
001000*
001100     05  W-FPS-COMM-REQUEST-TYPE      PIC X.                      W589ACTN
001200         88  W-FPS-COMM-REQUEST-PROMPT          VALUE 'P'.
001300         88  W-FPS-COMM-REQUEST-PROCESS         VALUE ' '.
001400*
001500     05  W-FPS-COMM-APPL-FUNC.
001600         10  W-FPS-COMM-APPL-CD       PIC X(3).                   W589APCD
001700         10  W-FPS-COMM-ORIG-FUNC     PIC X(4).                   W589ORFU
001800*
001900     05  W-FPS-COMM-NEXT-CMND         PIC X(4).                   W589NXTC
002000     05  W-FPS-COMM-CURR-SCRN-ID      PIC X(4).                   W589CSCR
002100     05  W-FPS-COMM-SYS-DATE          PIC 9(7)  COMP-3.           W589DATE
002200     05  W-FPS-COMM-TIME              PIC 9(7)  COMP-3.           W589TIME
002300     05  W-FPS-COMM-APPL-OP-ID        PIC X(4).                   W589OPID
002400     05  W-FPS-COMM-PSWD              PIC X(4).                   W589PSWD
002500*
002600     05  W-FPS-COMM-CTF-KEY-INFO.                                 W589CTFK
002700         10  W-FPS-COMM-LAST-OP-ID    PIC X(4).                   W589OPIL
002800         10  W-FPS-COMM-ORIG-APPL-ID  PIC 99.                     W589APPL
002900*
003000     05  W-FPS-COMM-MODE              PIC X(3).                   W589MODE
003100         88  W-FPS-COMM-MODE-PROD               VALUE 'PRD' SPACE.
003200         88  W-FPS-COMM-MODE-TEST               VALUE 'TST'.
003300*
003400     05  W-FPS-COMM-BASE-DG-FND-IND   PIC X.
003500         88  W-FPS-COMM-BASE-DG-FND             VALUE 'Y'.
003600*
003700     05  W-FPS-COMM-MSG-COND-CODE.
003800         10  W-FPS-COMM-MSG-COND-CODE-N PIC 9(4) BINARY.          W589CC
003900*
004000     05  W-FPS-COMM-MSG               PIC X(30).                  W589MSG
005900*
006000     05  W-FPS-COMM-PER-MSG           PIC X(60).
006100         88  W-FPS-COMM-PER-MSG-CLEAR           VALUE '*'.
006200     05  W-FPS-COMM-SYSTEM-ID         PIC X(4).
006300     05  W-FPS-COMM-UMBRELLA-RELEASE  PIC X(8).
006400     05  FILLER                       PIC X(49).
006500     05  W-FPS-COMM-RTN-FROM-DISP-FLAG PIC X.
006600         88  W-FPS-COMM-RTN-FROM-DISP           VALUE 'Y'.
006700     05  W-FPS-COMM-COND-CODE-APP.
006800         10  W-FPS-COMM-COND-CODE-APP-N PIC 9(4) BINARY.
006900*
007000     05  W-FPS-COMM-PGM-FUNC-KEY      PIC X.                      W589PFK
007100*        SUPV--Process command if entered, else current screen
007200         88  W-FPS-COMM-PGM-PROCESS-CMND        VALUE 'P'.
007300*        SUPV--Increase function number
007400         88  W-FPS-COMM-PGM-INCR-FUNC-NO        VALUE 'I'.
007500*        SUPV--Decrease function number
007600         88  W-FPS-COMM-PGM-DECR-FUNC-NO        VALUE 'D'.
007700*        SUPV--Return to previous processing level
007800         88  W-FPS-COMM-PGM-RTN-PREV-LVL        VALUE 'L'.
007900*        BOTH--Return to previous processing point
008000         88  W-FPS-COMM-PGM-RTN-BACK            VALUE 'R'.
008100*        PROG--Hold current screen after processing
008200         88  W-FPS-COMM-PGM-HOLD-SCRN           VALUE 'H'.
008300*        BOTH--Continue on if screen allows
008400         88  W-FPS-COMM-PGM-CONTINUE-ON         VALUE 'C'.
008500*        SUPV--Process current screen, then exit to command
008600         88  W-FPS-COMM-PGM-EXIT-AFTER          VALUE 'X'.
008700*        SUPV--End current function
008800         88  W-FPS-COMM-PGM-END-FUNC            VALUE 'E'.
008900*        PROG--Batch processing
009000         88  W-FPS-COMM-PGM-FUNC-BATCH          VALUE 'B'.
009100*        SUPV--Help request
009200         88  W-FPS-COMM-PGM-FUNC-HELP           VALUE 'A'.
009300*        BOTH--Function exit to menu
009400         88  W-FPS-COMM-PGM-FUNC-EXIT           VALUE 'F'.
009500*        Func keys which do not require password
009600         88  W-FPS-COMM-PGM-BYPASS-PSWD         VALUE 'B'.
009700*        Func keys defined on CDMF format 48323
009800         88  W-FPS-COMM-PGM-FUNC-DEFER          VALUE 'Z'.
009900*
010000     05  W-FPS-COMM-STATUS.
010100         10  W-FPS-COMM-STATUS-1      PIC X.
010200         10  W-FPS-COMM-STATUS-2      PIC X.
010300         10  W-FPS-COMM-STATUS-3      PIC X.
010400         10  W-FPS-COMM-STATUS-4      PIC X.
010500         10  W-FPS-COMM-STATUS-5      PIC X.
010600             88  W-FPS-COMM-STATUS-5-PROD       VALUE 'P'.
010700             88  W-FPS-COMM-STATUS-5-TEST       VALUE 'T'.
010800*
010900     05  W-FPS-COMM-FUNC-EXIT-FLAG    PIC X.
011000         88  W-FPS-COMM-FUNC-EXIT               VALUE 'Y'.
011100*
011200     05  W-FPS-COMP-VALID-IND         PIC X(1).
011300     05  W-FPS-TEST-VALID-MODE        PIC X(1).
011400     05  W-FPS-REQ-APPL               PIC X(3).
011500     05  W-FPS-COMM-FUNC-BEGIN-SEQ    PIC X(3).                   W589BSEQ
011600     05  W-FPS-COMM-FUNC-NO           PIC 9     COMP-3.           W589FUN#
011700*
011800     05  W-FPS-COMM-RESULT.
011900         10  W-FPS-COMM-RESULT-N      PIC 9(4)  BINARY.           W589CRSL
012000     05  W-FPS-COMM-INIT-RESULT.
012100         10  W-FPS-COMM-INIT-RESULT-N PIC 9(4)  BINARY.           W589INRS
012200     05  W-FPS-COMM-RESULT-APP.
012300         10  W-FPS-COMM-RESULT-APP-N  PIC 9(4)  BINARY.           W589RSAP
012400     05  FILLER                       PIC X(4).
012500*
012600     05  W-FPS-COMM-APPL-GLBL-STATS.
012700         10  W-FPS-COMM-APPL-GLBL-STAT1 PIC X.
012800             88  W-FPS-COMM-APPL-GLBL-DG1-PRES  VALUE 'Y'.
012900             88  W-FPS-COMM-APPL-GLBL1-RESTORE  VALUE 'R'.
013000         10  W-FPS-COMM-APPL-GLBL-STAT2 PIC X.
013100             88  W-FPS-COMM-APPL-GLBL-DG2-PRES  VALUE 'Y'.
013200             88  W-FPS-COMM-APPL-GLBL2-RESTORE  VALUE 'R'.
013300         10  W-FPS-COMM-APPL-GLBL-STAT3 PIC X.
013400             88  W-FPS-COMM-APPL-GLBL-DG3-PRES  VALUE 'Y'.
013500             88  W-FPS-COMM-APPL-GLBL3-RESTORE  VALUE 'R'.
013600         10  W-FPS-COMM-APPL-GLBL-STAT4 PIC X.
013700             88  W-FPS-COMM-APPL-GLBL-DG4-PRES  VALUE 'Y'.
013800             88  W-FPS-COMM-APPL-GLBL4-RESTORE  VALUE 'R'.
013900*
014000     05  W-FPS-COMM-BINARY-APP-ID     PIC 9(4)  BINARY.           W589ORAB
014100     05  W-FPS-COMM-MSG-CC-APP-PREV.
014200         10  W-FPS-COMM-MSG-CC-APP-PREV-N PIC 9(4) BINARY.
014300     05  W-FPS-COMM-CURR-EX-LVL       PIC 9(4)  BINARY.           W589CEXL
014400     05  W-FPS-COMM-CO-ID.
014500         10  W-FPS-COMM-CO-ID-N       PIC 9(4)  BINARY.           W589COID
014600     05  FILLER                       PIC X(6).
014700     05  W-FPS-COMM-HALFWORD-WORK-X.
014800         10  W-FPS-COMM-HALFWORD-WORK PIC 9(4)  BINARY.
014900     05  W-FPS-COMM-FULLWORD-WORK     PIC 9(9)  BINARY.
015000     05  W-FPS-COMM-FULLWORD-WORK-X REDEFINES
015100                                 W-FPS-COMM-FULLWORD-WORK.
015200         10  W-FPS-COMM-FULLWORD-BYTE-1-2 PIC XX.
015300         10  W-FPS-COMM-FULLWORD-BYTE-3-4 PIC XX.
015400     05  W-FPS-COMM-MSG-COND-CODE-PREV.
015500         10  W-FPS-COMM-MSG-CC-PREV-N     PIC 9(4) BINARY.
015600     05  W-FPS-COMM-SCRN-ID-PREV          PIC X(4).               W589PRCR
015700*
015800     05  W-FPS-COMM-RCD-SENS-INDS.
015900         10  W-FPS-COMM-RCD-SENS      PIC X     OCCURS 9 TIMES
016000             INDEXED BY W-FPS-COMM-SENS-INDX.
016100             88  W-FPS-COMM-RCD-SENS-OK         VALUE 'Y'.
016200*
016300     05  W-FPS-COMM-VIEW-BAL          PIC X.
016400         88  W-FPS-COMM-VIEW-BAL-OK             VALUE 'Y'.
016500     05  W-FPS-COMM-EMPL-RCDS         PIC X.
016600         88  W-FPS-COMM-EMPL-RCDS-OK            VALUE 'Y'.
016700     05  W-FPS-COMM-CROSS-COMP        PIC X.
016800         88  W-FPS-COMM-CROSS-COMP-OK           VALUE 'Y'.
016900     05  W-FPS-COMM-MAINT-IND         PIC X.
017000         88  W-FPS-COMM-MAINT-OK                VALUE 'Y'.
017100     05  W-FPS-COMM-FUNC-LVL          PIC 9     COMP-3.
017200     05  W-FPS-COMM-OPER-LVL          PIC 9     COMP-3.
017300*
017400     05  W-FPS-COMM-ACTION-CD         PIC X(40).                  W589ACCD
017500     05  FILLER REDEFINES             W-FPS-COMM-ACTION-CD.
017600         10  W-FPS-COMM-ACTION-CD-1-1 PIC X(1).
017700             88  MENU-COMMAND                   VALUE '='.
017800             88  DELIMETER                      VALUE ',' '.' '/'
017900                                                SPACE.
018000         10  W-FPS-COMM-ACTION-CD-2-40 PIC X(39).
018100     05  FILLER REDEFINES             W-FPS-COMM-ACTION-CD.
018200         10  W-FPS-COMM-ACTION-CD-1-4 PIC X(4).
018300         10  FILLER                   PIC X(36).
018400     05  FILLER REDEFINES             W-FPS-COMM-ACTION-CD.
018500         10  W-FPS-COMM-ACTION-CD-1-6 PIC X(6).
018600         10  FILLER                   PIC X(34).
018700     05  FILLER REDEFINES             W-FPS-COMM-ACTION-CD.
018800         10  W-FPS-COMM-ACTION-CD-SPS PIC X(20).                  W589ACW2
018900         10  FILLER                   PIC X(20).
019000*
019100     05  W-FPS-COMM-ACTION-CD-WK40    PIC X(40).                  W589ACW1
019200     05  FILLER REDEFINES             W-FPS-COMM-ACTION-CD-WK40.
019300         10  FILLER                   PIC X(1).
019400             88  MENU-COMMAND-WK      VALUE '='.
019500         10  W-FPS-COMM-ACTION-CD-WK40-2-40 PIC X(39).
019600     05  W-FPS-COMM-ACTION-CD-WK20    PIC X(20).
019700*
019800     05  W-FPS-COMM-FORMAT-ID.
019900         10  W-FPS-COMM-FORMAT-ID-N   PIC 9(9) BINARY.
020000*
020100     05  W-FPS-COMM-PFKEY-DISPLAY.
020200         10  FILLER                    PIC X(4).
020300         10  W-FPS-PFKEY-DISPLAY-TABLE PIC X(75).                 I5358901
020400     05  FILLER                        PIC X(8).
020500*
020600*        Fields that used to be HW are now (aligned) FW here
020700     05  W-FPS-COMM-FUNC-INIT-ACTY.
020800         10  W-FPS-COMM-FUNC-INIT-ACTY-N    PIC 9(9)  BINARY.
020900*
021000     05  W-FPS-COMM-APPL-GLBL-DGS.
021100         10  W-FPS-COMM-APPL-GLBL-DG1.
021200             15  W-FPS-COMM-APPL-GLBL-DG1-N PIC 9(9)  BINARY.
021300         10  W-FPS-COMM-APPL-GLBL-DG2.
021400             15  W-FPS-COMM-APPL-GLBL-DG2-N PIC 9(9)  BINARY.
021500         10  W-FPS-COMM-APPL-GLBL-DG3.
021600             15  W-FPS-COMM-APPL-GLBL-DG3-N PIC 9(9)  BINARY.
021700         10  W-FPS-COMM-APPL-GLBL-DG4.
021800             15  W-FPS-COMM-APPL-GLBL-DG4-N PIC 9(9)  BINARY.
021900*
022000     05  W-FPS-COMM-APPL-STAT-ACTY.
022100         10  W-FPS-COMM-APPL-STAT-ACTY-N    PIC 9(9)  BINARY.
022200     05  W-FPS-COMM-APPL-SECUR-ACTY.
022300         10  W-FPS-COMM-APPL-SECUR-ACTY-N   PIC 9(9)  BINARY.
022400     05  W-FPS-COMM-PRE-FPS-EXIT.
022500         10  W-FPS-COMM-PRE-FPS-EXIT-N      PIC 9(9)  BINARY.
022600     05  W-FPS-COMM-POST-FPS-EXIT.
022700         10  W-FPS-COMM-POST-FPS-EXIT-N     PIC 9(9)  BINARY.
022800*
022900     05  FILLER                       PIC XX.
023000     05  W-FPS-COMM-PREV-APPL-FUNC.
023100         10  W-FPS-COMM-PREV-APPL-CD  PIC X(3).
023200         10  W-FPS-COMM-PREV-FUNC     PIC X(4).
023300*
023400*        Event tracker navigation fields
023500     05  W-FPS-COMM-ET-CAPT-FF        PIC X.
023600     05  W-FPS-COMM-ET-EV-TYP         PIC X(8).
023700     05  W-FPS-MONETARY-KEY           PIC X(3).                   W589MON
023800     05  W-FPS-PRES-CURRENCY-CD       PIC X(3).                   W589PCUR
023900     05  W-FPS-PRES-CURR-RND-IN       PIC X(1).                   W589PCRI
024000* Do not round presentation currency - adds an extra decimal digit
024100         88  W-FPS-DO-NOT-ROUND-PRES-CURR               VALUE 'N'.
024200* Round presentation currency
024300         88  W-FPS-ROUND-PRES-CURR                      VALUE 'Y'.
024400*
024500**** FPS is executing under PAS if MAPMSD is greater than spaces
024600     05  W-FPS-COMM-PAS-MAPMSD        PIC X(7).
024700**
024800     05  W-FPS-VCTF-IND               PIC X(01).
024801*    Event tracker use bits (one or more bits 'on' at same time)
024802     05  W-FPS-COMM-ET-USE-BITS       PIC X.
024803*        B'10000000'             - Appl ET use bits are set
024804*        B'00000001'             - Appl uses ET auth events
024805*        B'00000010'             - Appl uses ET trig events
024806*        B'00000100'             - Appl uses ET audit logs
024807*        B'00001000'             - Appl uses ET api calls
024808     05  FILLER                       PIC X(4).
024900****** End of I53589D ********************************************
