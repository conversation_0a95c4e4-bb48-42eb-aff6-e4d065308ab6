000100**** START OF U50470D ********* IDS FPS SCENARIO CONTROL DG *****
000200**                                                             **
000300**   DATA GROUP: 13470                                         **
000400**                                                             **
000500**   THIS PCD CONTROLS COMMON IDS FPS SCENARIO PROCESSING      **
000600**   LOGIC REQUIRED OF ALL IDS FUNCTIONS RUNNING UNDER FPS.    **
000700**                                                             **
000800*****************************************************************
000900*
001000 01  W470-IDS-FPS-SCENARIO-CNTL-DG.
001100     05  W470-ACTION-U50470D   PIC XX.
001200     05  W470-RESULT-U50470D   PIC XX.
001300*
001400     05  W470-PCD-1690-XREF-KEY.
001500         10  W470-USER-KEY.
001600             15  W470-APP    PIC X(03).
001700             15  W470-FUNC   PIC X(04).
001800             15  W470-SEQ    PIC X(03).
001900         10  FILLER          PIC X.
002000*
002100     05  W470-OVERRIDE-APPL-ID-X.
002200         10  W470-OVERRIDE-APPL-ID     PIC S9(04) COMP.
002300     05  W470-OVERRIDE-FUNC-ID-X.
002400         10  W470-OVERRIDE-FUNC-ID     PIC S9(04) COMP.
002500*
002600*****************************************************************
002700**   PRE-PROCESSING EXITS                                      **
002800*****************************************************************
002900*
003000     05  W470-PRE-PROCESSING-ACTY-TABLE.
003100         10  W470-PRE-PROCESSING-ACTIVITIES OCCURS 04 TIMES
003200                                    INDEXED BY W470-PRE-IDX.
003300             15  W470-PRE-PROCESSING-ACTIVITY-X.
003400                 20  W470-PRE-PROCESSING-ACTIVITY
003500                                       PIC S9(08) COMP.
003600*
003700*****************************************************************
003800**   PROCESSING CONTROL SWITCHES                               **
003900*****************************************************************
004000*
004100     05  W470-XREF-DB-ACCESS-REQUIRED  PIC X.
004200     05  W470-COMPANY-CHG-ALLOWED      PIC X.
004300     05  W470-VERIFY-CO-ENVMT-FILE     PIC X.
004400     05  W470-SITE-ID-SENSITIVE        PIC X.
004500     05  FILLER                        PIC X(35).
004501*
004502*****************************************************************
004503**   VALID NUMERIC ACTION CONTROL INFORMATION                  **
004504*****************************************************************
004505*
004506     05  W470-NUMERIC-ACTION-LOW       PIC 999.
004507     05  W470-NUMERIC-ACTION-HIGH      PIC 999.
004600*
004700*****************************************************************
004800**   APPLICATION SWITCHES                                      **
004900*****************************************************************
005000*
005100     05  W470-PRODUCT-CODE-REQUIRED    PIC X.
005200     05  W470-ACCT-NUMBER-REQUIRED     PIC X.
005300*
005400     05  W470-APPL-DDA-SW              PIC X.
005500         88  W470-APPL-DDA-YES         VALUE 'Y' ' '.
005600         88  W470-APPL-DDA-NO          VALUE 'N'.
005700     05  W470-APPL-TDA-SW              PIC X.
005800         88  W470-APPL-TDA-YES         VALUE 'Y' ' '.
005900         88  W470-APPL-TDA-NO          VALUE 'N'.
006000*
006100*****************************************************************
006200**   VALID PRODUCT CODE TABLE AND "SWAP" CONTROL INFORMATION   **
006300*****************************************************************
006400*
006500     05  W470-VALID-PRODUCT-TABLE.
006600         10  W470-VALID-PRODUCTS OCCURS 08 TIMES
006700                                 INDEXED BY W470-PROD-IDX.
006800             15  W470-PRODUCT          PIC X(03).
006900             15  W470-PRODUCT-SWAP-KEY.
007000                 20  W470-PSK-APP      PIC X(03).
007100                 20  W470-PSK-FUNC     PIC X(04).
007200                 20  W470-PSK-SEQ      PIC X(03).
007300*
007400*****************************************************************
007500**   VALID ACTION CONTROL INFORMATION                          **
007600*****************************************************************
007700*
007800     05  W470-DEFAULT-ACTION           PIC X(04).
007900     05  FILLER                        PIC X(04).
008100     05  W470-VALID-ACTION-TABLE.
008200         10  W470-VALID-ACTIONS OCCURS 18 TIMES
008300                                INDEXED BY W470-ACTN-IDX.
008400             15  W470-VALID-ACTION     PIC X(04).
008500*
008600*****************************************************************
008700**   ACTIVITIES                                                **
008800*****************************************************************
008900*
009000     05  W470-ACTIVITY-TABLE.
009100         10  W470-ACTIVITIES OCCURS 08 TIMES
009200                             INDEXED BY W470-ACTY-IDX.
009300             15  W470-ACTIVITY-X.
009400                 20  W470-ACTIVITY     PIC S9(08) COMP.
009500*
009600     05  W470-CONDITION-CODES.
009700         10  W470-ACTION-CODE-INVALID-CC    PIC XX.
009800         10  W470-PRODUCT-CODE-REQUIRED-CC  PIC XX.
009900         10  W470-ACCT-NUMBER-REQUIRED-CC   PIC XX.
010000         10  W470-APPLICATION-ERROR-CC      PIC XX.
010100         10  W470-PRODUCT-CODE-ERROR-CC     PIC XX.
010200         10  W470-PROD-SCENARIO-TRANSFER-CC PIC XX.
010300         10  W470-INT-SITE-ID-TRANSFER-CC   PIC XX.
010400**** END OF U50470D *********************************************
