000100**** Start of P49000D  *****  TCB ********************************
000200* This copybook now defines four distinct data groups:           *
000300* 1) D.G. 00001, the User Transaction Control Block and the CDMF *
000400*    Application Control Block;                                  *
000500* 2) D.G. 00012, the SYSIN input data group;                     *
000600* 3) D.G. 00013, the SYSPRINT output data group; and             *
000700* 4) D.G. 00010, the Security Control Block, the TCB User Area   *
000800*    and the User TCB Extension Area.                            *
000900******************************************************************
001000     SKIP1
001100******************************************************************
001200* Data Group 1 (User Transaction Control Block)                  *
001300******************************************************************
001400*
001500 01  TRANSACTION-CONTROL-BLOCK.
001600     05  FILLER                          PIC XXXX.
001700     05  TCB-TRANS-NO                    PIC XXXX.
001800     05  TCB-CO-ID.
001900         10  TCB-CO-ID-N                 PIC 9(4) BINARY.
002000     05  TCB-APPL-ID.
002100         10  TCB-APPL-ID-N               PIC 9(4) BINARY.
002200     05  TCB-FUNC-ID.
002300         10  TCB-FUNC-ID-N               PIC 9(4) BINARY.
002400     05  TCB-SOURCE-TYPE.
002500         10  TCB-SOURCE-TYPE-N           PIC 9(4) BINARY.
002600             88  TCB-ONLINE                  VALUE 3 5 6.
002700             88  TCB-BTCH                    VALUE 4.
002800             88  TCB-APPL-SOURCE             VALUE 2.
002900             88  TCB-AUTHORIZATIONS          VALUE 5 6.
003000             88  TCB-MANNED-TELLER           VALUE 5.
003100             88  TCB-UNMANNED-TELLER         VALUE 6.
003200     05  TCB-ACTIVITY.
003300         10  TCB-ACTIVITY-N              PIC 9(4) BINARY.
003400     SKIP1
003500     05  TCB-RESULT.
003600         10  TCB-RESULT-N                PIC 9(4) BINARY.
003700             88  TCB-RSLT-OK                    VALUE 0000.
003800             88  TCB-RSLT-ERROR                 VALUE 0001.
003900             88  TCB-RSLT-NO-ACT-DEF            VALUE 0002.
004000             88  TCB-RSLT-NOT-AUTH              VALUE 0003.
004100             88  TCB-RSLT-FAIL                  VALUE 0004.
004200             88  TCB-RSLT-ABEND-EXIT            VALUE 0006.
004300             88  TCB-RSLT-ROLLBACK              VALUE 0007.
004400             88  TCB-RSLT-DATA-BASE-FULL        VALUE 0008.
004500             88  TCB-RSLT-DB-NOT-AVAILABLE      VALUE 0009.
004600             88  TCB-RSLT-OK-CHECKPOINT         VALUE 0010.
004700             88  TCB-RSLT-FAIL-CHECKPOINT       VALUE 0011.
004800             88  TCB-RSLT-ABEND-TRANS           VALUE 0012.
004900             88  TCB-RSLT-DEFERRED              VALUE 0020.
005000             88  TCB-RSLT-RANDOMIZER-ERROR      VALUE 0021.
005100             88  TCB-RSLT-DATA-NOT-AVAILABLE    VALUE 0022.
005101             88  TCB-RSLT-DB-OR-DATA-NOT-AVAIL  VALUE 22 9.
005200     SKIP1
005300     05  TCB-USER-DATA.
005400         10  FILLER                      PIC X(6).
005500         10  TCB-DATA-GROUP.
005600             15  TCB-DATA-GROUP-N        PIC 9(4) BINARY.
005700         10  TCB-PARM-POS.
005800             15  TCB-PARM-POS-N          PIC 9(4) BINARY.
005900     SKIP1
006000     05  FILLER                  REDEFINES TCB-USER-DATA.
006100         10  TCB-USER-INFO.
006101             15  TCB-USER-INFO-N         PIC 9(9) BINARY.
006200         10  TCB-USER-COND.
006300             15  TCB-USER-COND-N         PIC 9(4) BINARY.
006400         10  FILLER                      PIC X(4).
006500     SKIP1
006600     05  FILLER                  REDEFINES TCB-USER-DATA.
006700         10  TCB-USER-CC.
006800             15  TCB-USER-CC-N           PIC 9(4) BINARY.
006900         10  TCB-USER-RESULT.
007000             15  TCB-USER-RESULT-N       PIC 9(4) BINARY.
007100         10  TCB-USER-ENVMT.
007200             15  TCB-USER-ENVMT-N        PIC 9(4) BINARY.
007300         10  FILLER                      PIC X(4).
007400     SKIP1
007500     05  FILLER                  REDEFINES TCB-USER-DATA.
007600         10  TCB-EOJ-CALL                PIC XXXX.
007700         10  FILLER              REDEFINES TCB-EOJ-CALL.
007800             15  TCB-SOT-CALL            PIC XXXX.
007900         10  FILLER                      PIC X(6).
008000     SKIP1
008100     05  TCB-TIME                        PIC S9(7)   COMP-3.
008200     05  TCB-SYS-DATE                    PIC S9(7)   COMP-3.
008300     05  TCB-SOURCE                      PIC X(8).
008400     SKIP1
008500     05  TCB-OPERATOR.
008600         10  TCB-UMBRELLA-OPERATOR       PIC X(8).
008700     05  TCB-OPERATOR-FILLER             PIC X(12).
008701     05  TCB-DXRF          REDEFINES TCB-OPERATOR-FILLER.
008702         10  TCB-DXRF-ID                 PIC X(4).
008703         10  FILLER                      PIC X(8).
008704     05  TCB-DYN-KRange-ID REDEFINES TCB-OPERATOR-FILLER.
008705         10  TCB-DKR-ID                  PIC X(4).
008706         10  FILLER                      PIC X(8).
008707     05  TCB-ENQ           REDEFINES TCB-OPERATOR-FILLER.
008708         10  TCB-ENQ-ID                  PIC X(8).
008709         10  FILLER                      PIC X(4).
009700     SKIP1
009800     05  TCB-DESTINATION                 PIC X(8).
009900     05  TCB-TERM-DATA.
010000         10  TCB-PFKEY                   PIC X.
010100             88  TCB-ENTER                   VALUE QUOTE.
010200             88  TCB-PF01                    VALUE '1'.
010300             88  TCB-PF02                    VALUE '2'.
010400             88  TCB-PF03                    VALUE '3'.
010500             88  TCB-PF04                    VALUE '4'.
010600             88  TCB-PF05                    VALUE '5'.
010700             88  TCB-PF06                    VALUE '6'.
010800             88  TCB-PF07                    VALUE '7'.
010900             88  TCB-PF08                    VALUE '8'.
011000             88  TCB-PF09                    VALUE '9'.
011100             88  TCB-PF10                    VALUE ':'.
011200             88  TCB-PF11                    VALUE '#'.
011300             88  TCB-PF12                    VALUE '@'.
011400             88  TCB-PF13                    VALUE 'A'.
011500             88  TCB-PF14                    VALUE 'B'.
011600             88  TCB-PF15                    VALUE 'C'.
011700             88  TCB-PF16                    VALUE 'D'.
011800             88  TCB-PF17                    VALUE 'E'.
011900             88  TCB-PF18                    VALUE 'F'.
012000             88  TCB-PF19                    VALUE 'G'.
012100             88  TCB-PF20                    VALUE 'H'.
012200             88  TCB-PF21                    VALUE 'I'.
012300             88  TCB-PF22                    VALUE X'4A'.
012400             88  TCB-PF23                    VALUE '.'.
012500             88  TCB-PF24                    VALUE '<'.
012600             88  TCB-PFKEY-NOT-PRESENT       VALUE LOW-VALUE.
012700         10  TCB-OP                      PIC X.
012701             88  TCB-X6F                     VALUE X'6F'.
012702             88  TCB-XF5                     VALUE X'F5'.
012703             88  TCB-X7E                     VALUE X'7E'.
012704             88  TCB-XF2                     VALUE X'F2'.
012705             88  TCB-XF6                     VALUE X'F6'.
012706             88  TCB-X6E                     VALUE X'63'.
012707             88  TCB-XF1                     VALUE X'F1'.
012708             88  TCB-XF3                     VALUE X'F3'.
012709             88  TCB-X00                     VALUE X'00'.
012710         10  TCB-WCC                    PIC X.
012711             88  TCB-XC7                     VALUE X'C7'.
012712             88  TCB-XC6                     VALUE X'C6'.
012713             88  TCB-XC5                     VALUE X'C5'.
012714             88  TCB-XC4                     VALUE X'C4'.
012715             88  TCB-XC3                     VALUE X'C3'.
012716             88  TCB-XC2                     VALUE X'C2'.
012717             88  TCB-XC1                     VALUE X'C1'.
012718             88  TCB-X40                     VALUE X'40'.
012719             88  TCB-X4F                     VALUE X'4F'.
012720             88  TCB-X4E                     VALUE X'4E'.
012721             88  TCB-X4D                     VALUE X'4D'.
012722             88  TCB-X4C                     VALUE X'4C'.
012723             88  TCB-X4B                     VALUE X'4B'.
012724             88  TCB-X4A                     VALUE X'4A'.
012725             88  TCB-XC9                     VALUE X'C9'.
012726             88  TCB-XCB                     VALUE X'CB'.
012800     SKIP1
012900     05  TCB-GENP-LOG                    PIC X.
013000         88  TCB-GENP-NO-LOGGING             VALUE 'N'.
013100         88  TCB-GENP-LOGGING                VALUE 'Y' ' '
013200                                                   LOW-VALUE.
013300     05  TCB-EFFECTIVE-DATE              PIC S9(7)   COMP-3.
013400     SKIP1
013500     05  TCB-DEVICE-TYPE-2.
013600         10  TCB-DEVICE-TYPE             PIC X.
013700             88  TCB-3270-2                  VALUE 'A'.
013800             88  TCB-BATCH                   VALUE 'B'.
013900             88  TCB-3270-MOD1-PRINTER       VALUE 'J'.
014000             88  TCB-3270-MOD2-PRINTER       VALUE 'K'.
014100*
014200***** Reserved for TCB-DEVICE expansion to PIC X(2)
014300         10  FILLER                      PIC X.
014400     SKIP1
014500     05  TCB-LONG-ACTIVITY-N             PIC 9(9) BINARY.
014600         88  TCB-ACT-SYSPRINT-WRITE          VALUE     8.
014700         88  TCB-ACT-NO-OP                   VALUE    13.
014800         88  TCB-ACT-TRANS-DUMP              VALUE    14.
014900         88  TCB-ACT-DUMP-TCB-TRACE          VALUE    16.
015000         88  TCB-ACT-DUMP-SINGLE-DG          VALUE    17.
015100         88  TCB-ACT-ENABLE-ABEND-EXIT       VALUE    20.
015200         88  TCB-ACT-DISABLE-ABEND-EXIT      VALUE    21.
015300         88  TCB-ACT-SET-STEP-COND-CODE      VALUE    22.
015400         88  TCB-ACT-CHECKPOINT              VALUE    28.
015500         88  TCB-ACT-ROLLBACK                VALUE    30.
015600         88  TCB-ACT-DLI-SYNCPOINT           VALUE    32.
015700         88  TCB-ACT-USERCC-EXCEPT           VALUE    35.
015800         88  TCB-ACT-MSG9-ABEND              VALUE    39.
015900         88  TCB-ACT-DYN-DG-LENGTH           VALUE    74.
016000         88  TCB-ACT-DYN-PTR-REL             VALUE    75.
016100         88  TCB-ACT-DYN-PTR-INIT            VALUE    76.
016200         88  TCB-ACT-DYN-PTR-ANO             VALUE    77.
016300         88  TCB-ACT-PDG-HAS-CHANGED         VALUE    88.
016400         88  TCB-ACT-PCD-READ                VALUE  1013.
016500         88  TCB-ACT-PCD-API                 VALUE  1014.
016600         88  TCB-ACT-DATE-SERVICES           VALUE  1900.
016700         88  TCB-ACT-CDMF-API                VALUE 48000.
016800     05  TCB-LONG-ACTIVITY       REDEFINES TCB-LONG-ACTIVITY-N.
016900         10  TCB-LONG-ACT-HI             PIC XX.
017000         10  TCB-LONG-ACT-LO             PIC XX.
017100     SKIP1
017200     05  TCB-LONG-DGID-N                 PIC 9(9) BINARY.
017300     05  TCB-LONG-DGID           REDEFINES TCB-LONG-DGID-N.
017400         10  TCB-LONG-DG-HI              PIC XX.
017500         10  TCB-LONG-DG-LO              PIC XX.
017600     SKIP1
017700     05  TCB-USER-CC-APP.
017800         10  TCB-USER-CC-APP-N           PIC 9(4) BINARY.
017900     05  TCB-RESULT-2.
018000         10  TCB-RESULT-2-N              PIC 9(4) BINARY.
018100             88  TCB-RSLT2-GET-ONLY-DB-DATA  VALUE 30.
018200     EJECT
018300******************************************************************
018400* CDMF APPLICATION CONTROL BLOCK                                 *
018500******************************************************************
018600     05  CDMF-CONTROL-BLOCK.
018700         10  CDMF-ACTION.
018800             15  CDMF-ACTION-N           PIC 9(4) BINARY.
018900                 88  CDMF-ACTION-NOP            VALUE     0.
019000                 88  CDMF-ACTION-ADD            VALUE     1.
019100                 88  CDMF-ACTION-ADD-DUMMY      VALUE     2.
019200                 88  CDMF-ACTION-CHANGE         VALUE     3.
019300                 88  CDMF-ACTION-DELETE         VALUE     4.
019400                 88  CDMF-ACTION-INQUIRY        VALUE     5.
019500                 88  CDMF-ACTION-NEXT           VALUE     6.
019600                 88  CDMF-ACTION-NEXT-EFF       VALUE     7.
019700                 88  CDMF-ACTION-KEYGE          VALUE    14.
019800                 88  CDMF-ACTION-TABLE-INQ      VALUE     8.
019900                 88  CDMF-ACTION-TABLE-NEXT     VALUE     9.
020000                 88  CDMF-ACTION-TABLE-NEXT-EFF VALUE    10.
020100                 88  CDMF-ACTION-TABLE-KEYGE    VALUE    15.
020200                 88  CDMF-ACTION-DB-INQ         VALUE    11.
020300                 88  CDMF-ACTION-DB-NEXT        VALUE    12.
020400                 88  CDMF-ACTION-DB-NEXT-EFF    VALUE    13.
020500                 88  CDMF-ACTION-DB-KEYGE       VALUE    16.
020600     SKIP1
020700         10  CDMF-RESULT.
020701             15  CDMF-RESULT-N           PIC 9(4) BINARY.
020702                 88  CDMF-RESULT-NO-ERRORS           VALUE 00000.
020703                 88  CDMF-RESULT-DB-NOT-AVAILABLE    VALUE 00009.
020704                 88  CDMF-RESULT-INVALID-ACTION      VALUE 48001.
020705                 88  CDMF-RESULT-ITEM-NOT-FOUND      VALUE 48002.
020706                 88  CDMF-RESULT-FORMAT-NOT-FOUND    VALUE 48003.
020707                 88  CDMF-RESULT-UNABLE-TO-ALLOC-DG  VALUE 48004.
020708                 88  CDMF-RESULT-END-OF-FORMAT       VALUE 48005.
020709                 88  CDMF-RESULT-DUPE-KEY-ON-ADD     VALUE 48006.
020710                 88  CDMF-RESULT-INVALID-EFF-DATE    VALUE 48007.
020711                 88  CDMF-RESULT-INVALID-APPL        VALUE 48008.
020712                 88  CDMF-RESULT-SKEY-NOT-FOUND      VALUE 48009.
020713                 88  CDMF-RESULT-SKEY-READ-ERR       VALUE 48010.
020714                 88  CDMF-RESULT-INVALID-CC-NO       VALUE 48011.
020715                 88  CDMF-RESULT-INVALID-FAMILY      VALUE 48012.
020716                 88  CDMF-RESULT-NO-OWNER-CHANGE     VALUE 48013.
020717                 88  CDMF-RESULT-SECURITY-VIOLATION  VALUE 48030.
020718                 88  CDMF-RESULT-SECURITY-INACTIVE   VALUE 48031.
020719                 88  CDMF-RESULT-DUMMY-REC-FOUND     VALUE 48099.
020720                 88  CDMF-RESULT-CC-NO-MISSING       VALUE 48371.
020721                 88  CDMF-RESULT-CC-NO-CLOSED        VALUE 48372.
022900     SKIP1
023000         10  CDMF-KEY-FIELDS.
023100             15  CDMF-FORMAT.
023200                 20  CDMF-FORMAT-N       PIC 9(9) BINARY.
023300             15  CDMF-COID.
023400                 20  CDMF-COID-N         PIC 9(4) BINARY.
023500             15  CDMF-EFF-DATE           PIC S9(7)   COMP-3.
023600         10  CDMF-EXP-DATE               PIC S9(7)   COMP-3.
023700         10  CDMF-COID-FOUND             PIC XX.
023800             88  CDMF-DEFAULT-COID-FOUND     VALUE HIGH-VALUES.
023900         10  CDMF-EFF-DATE-FOUND         PIC S9(7)   COMP-3.
024000         10  CDMF-HIGH-USE-FLAG          PIC X.
024100             88  CDMF-HIGH-USE-ITEM          VALUE 'Y'.
024200             88  CDMF-NON-PURGEABLE          VALUE 'P'.
024300*
024400***** Item ownership is always returned in CDMF-OWNER-APPLICATION.
024500***** Ownership may be retrieved and updated from data group 48007
024600***** if this flag is set to a 'Y'.  Ownership may be updated from
024700***** CDMF-OWNER-APPLICATION if this flag is set to a 'C'.
024800         10  CDMF-OWNER-APP-FLAG         PIC X.
024900             88  CDMF-OWNER-APP-REQUEST      VALUE 'Y'.
025000             88  CDMF-OWNER-APP-IN-CTL-BLK   VALUE 'C'.
025100         10  CDMF-ITEM-LOCATION          PIC X.
025200             88  CDMF-ITEM-FOUND-IN-TABLE    VALUE 'Y'.
025300         10  FILLER                      PIC X.
025400         10  CDMF-CC-NO.
025500             15  CDMF-CC-NO-N            PIC 9(9) BINARY.
025600         10  CDMF-LAST-CHANGE-DATA.
025700             15  CDMF-LAST-CHANGE-DATE   PIC S9(7)   COMP-3.
025800             15  CDMF-LAST-CHANGE-TIME   PIC S9(7)   COMP-3.
025900             15  CDMF-LAST-CHANGE-CC-NO.
026000                 20  CDMF-LAST-CHANGE-CC-NO-N PIC 9(9) BINARY.
026100             15  CDMF-LAST-CHANGE-SOURCE PIC X(8).
026200             15  CDMF-LAST-CHANGE-OPER   PIC X(8).
026300         10  CDMF-SECONDARY-KEY-ID       PIC X(4).
026400         10  CDMF-SUBSTITUTE-DGID.
026500             15  CDMF-SUBSTITUTE-DGID-N  PIC 9(9) BINARY.
026600*
026700***** This field is reserved for INTERNAL UMBRELLA USE ONLY.
026800         10  CDMF-REL-CONTROL-DG-LEN     PIC 9(4) BINARY.
026801         10  CDMF-REL-CONTROL-FLAGS      PIC X.
026802             88  CDMF-REL-LAST-MAINT     VALUE X'80' .
026803             88  CDMF-REL-DEFER-XREF     VALUE X'40' .
026804             88  CDMF-REL-RCS-DG-LEN     VALUE X'20' .
026900*
027000***** The item application ownership is always returned in this
027100***** field.  This field may only be used in an update when
027200***** CDMF-OWNER-APP-FLAG is set to a 'C'.
027300         10  CDMF-OWNER-APPLICATION      PIC X(3).
027400         10  CDMF-DUMP-RESTORE-FLAGS     PIC X.
027401             88  CDMF-DUMP-RESTORE-NOCC      VALUE X'80'.
027402*        10  FILLER                      PIC X.
027403***** The Celeriti Business Parameters Process Flag byte is used
027404***** to indicate special processing required during CDMF
027405***** requests.
027406         10  CDMF-CBP-PROCESS-FLAG       PIC X.
027407             88  CDMF-CBP-FLAG-RESET         VALUE LOW-VALUES.
027408             88  CDMF-CBP-DIS-PRE-W-D        VALUE '1'.
027500****** END OF DATA GROUP 00001 ***********************************
027600     EJECT
027700******************************************************************
027800* Data Group 12 (SYSIN input data group)                         *
027900*----------------------------------------------------------------*
028000* NOTE: Programs that reference data group 12 need not code the  *
028100* data group on the program definition.  Instead, you may refer  *
028200* to this area directly & issue activities that reference DG 12. *
028300******************************************************************
028400*
028500***** Data Group prefix (DO NOT ALTER)
028600     05  DATA-GROUP-12-PREFIX            PIC X(8).
028700     05  DATA-GROUP-12.
028800         10  DG12-ACTION                 PIC XX.
028900         10  DG12-RESULT                 PIC XX.
029000         10  DG12-CARD-IMAGE             PIC X(80).
029100****** END OF DATA GROUP 00012 ***********************************
029200     SKIP1
029300******************************************************************
029400* Data Group 13 (SYSPRINT output data group)                     *
029500*----------------------------------------------------------------*
029600* NOTE: Programs that reference data group 13 need not code the  *
029700* data group on the program definition.  Instead, you may refer  *
029800* to this area directly & issue activities that reference DG 13. *
029900******************************************************************
030000*
030100***** Data Group prefix (DO NOT ALTER)
030200     05  DATA-GROUP-13-PREFIX            PIC X(8).
030300     05  DATA-GROUP-13.
030400         10  DG13-ACTION                 PIC XX.
030500         10  DG13-RESULT                 PIC XX.
030600         10  DG13-PRINT-LINE.
030601             15  DG13-CONTROL-CHAR       PIC X.
030602             15  DG13-PRINT-DATA         PIC X(132).
030800     05  FILLER                          PIC X(7).
030900****** END OF DATA GROUP 00013 ***********************************
031000     SKIP1
031100******************************************************************
031200* Data Group 10 (TCB Extension Areas)                            *
031300*----------------------------------------------------------------*
031400* This data group contains the Security Control Block, the TCB   *
031500* User Area (for client use) and the User TCB Extension Area.    *
031600* ***** NOTE: Do not code this data group on your program        *
031700* *****       definition.  Instead, you should refer to this     *
031800* *****       area directly since it is part of the User         *
031900* *****       Transaction Control Block.                         *
032000******************************************************************
032100*
032200***** Data Group prefix (DO NOT ALTER)
032300     05  DATA-GROUP-10-PREFIX.
032301         10  DG10-TYPE                   PIC  X(2).
032302         10  DG10-ID.
032303             15  DG10-ID-N               PIC  9(9)  BINARY.
032304         10  DG10-SIZE                   PIC  X(2).
032400     05  DATA-GROUP-10.
032500     EJECT
032600******************************************************************
032700* Security Control Block                                         *
032800******************************************************************
032900         10  SECURITY-CONTROL-BLOCK.
033000             15  SCB-ACTION-X.
033100                 20  SCB-ACTION          PIC 9(4)   BINARY.
033101                      88  SCB-ACT-START-OF-TRANS      VALUE 1.
033102                      88  SCB-ACT-AFTER-MAP-DEBLOCK   VALUE 2.
033103                      88  SCB-ACT-BEFORE-MAP-DISPLAY  VALUE 3.
033104                      88  SCB-ACT-BEFORE-DB-READ      VALUE 4.
033105                      88  SCB-ACT-AFTER-DB-READ       VALUE 5.
033106                      88  SCB-ACT-BEFORE-DB-WRITE     VALUE 6.
033107                      88  SCB-ACT-BEFORE-DB-DELETE    VALUE 7.
033108                      88  SCB-ACT-BEFORE-PROGRAM-LINK VALUE 8.
033109                      88  SCB-ACT-BEFORE-CDMF-READ    VALUE 20.
033110                      88  SCB-ACT-AFTER-CDMF-READ     VALUE 21.
033111                      88  SCB-ACT-BEFORE-CDMF-ADD     VALUE 22.
033112                      88  SCB-ACT-BEFORE-CDMF-CHANGE  VALUE 23.
033113                      88  SCB-ACT-BEFORE-CDMF-DELETE  VALUE 24.
033200             15  SCB-RESULT-X.
033300                 20  SCB-RESULT          PIC 9(4)   BINARY.
033400                     88  SCB-AUTHORIZATION-VALID     VALUE 0.
033500                     88  SCB-AUTHORIZATION-FAILED    VALUE 4.
033600                     88  SCB-AUTHORIZATION-ERROR     VALUE 8.
033700                     88  SCB-EXT-SECURITY-INACTIVE   VALUE 12.
033800             15  SCB-VIOLATION-ACTION    PIC 9(4)   BINARY.
033900                 88  SCB-ABEND-TASK          VALUE 0.
034000                 88  SCB-RETURN              VALUE 1.
034100             15  SCB-LOGGING-FLAG        PIC X.
034200                 88  SCB-LOG-EXCPTNS         VALUE 'Y'.
034300                 88  SCB-BYPASS-LOG          VALUE 'N'.
034400             15  SCB-PROCESSING-TYPE     PIC X.
034500             15  SCB-PEM-FLAG1           PIC X.
034600             15  SCB-FUTURE-FLAGS        PIC X(3).
034700             15  SCB-FORMAT-NUMBER       PIC XXXX.
034800             15  SCB-FORMAT-NAME         PIC X(10).
034900*
035000***** This field is for assembler programs only!
035100             15  SCB-ADDR-FMT-TARGET-DG  PIC XXXX.
035200*
035300             15  SCB-TARGET-DG-ID        PIC XXXX.
035400             15  SCB-ITEM-OWNER          PIC X(3).
035500             15  SCB-PREV-OWNER          PIC X(3).
035600             15  SCB-MESSAGE-NO.
035700                 20  SCB-MESSAGE-NO-N    PIC 9(4)   BINARY.
035800             15  SCB-EXCEPTION-MESSAGE.
035900                 20  SCB-RULE-NAME       PIC X(40).
036000                 20  FILLER              PIC X(4).
036100             15  SCB-USER-DATA           PIC X(25).
036200             15  FILLER                  PIC X.
036300             15  SCB-RESERVED            PIC X(8).
036400     EJECT
036500******************************************************************
036600* TCB User Area                                                  *
036700*----------------------------------------------------------------*
036800* This area is reserved for clients and will never be used by    *
036900* Computer Sciences Corporation.                                 *
037000******************************************************************
037100         10  TCB-USER-AREA.
037200             15  FILLER                  PIC X(104).
037300     SKIP1
037400******************************************************************
037500* User TCB Extension Area                                        *
037600*----------------------------------------------------------------*
037700* This area is reserved for new TCB fields to be added and       *
037800* updated by Computer Sciences Corporation.                      *
037900******************************************************************
038000         10  TCB-EXTENSION-AREA.
038100*                Cursor position after a deblock; row and column
038200             15  TCB-ROW                 PIC 9(4) BINARY.
038300             15  TCB-COLUMN              PIC 9(4) BINARY.
038400             15  TCB-BATCH-DISP-OPTION   PIC X.
038500                 88  TCB-BATCH-DISP-DUMP     VALUE LOW-VALUES.
038600*                    Batch display to SYSPRINT in dump format.
038700                 88  TCB-BATCH-DISP-FORMAT   VALUE 'F'.
038800*                    Batch display to SYSPRINT in screen format.
038900                 88  TCB-BATCH-DISP-RETURN   VALUE 'R'.
039000*                    Batch display data in DG 47.  Not printed.
039100             15  TCB-PEM-LOGGING-OPTION  PIC X.
039101                 88  TCB-PEM-LOGGING-ENABLED     VALUE 'Y'.
039102                 88  TCB-PEM-LOGGING-DISABLED    VALUE 'N'.
039200             15  TCB-DYN-TXN-ID          PIC X(8).
039300             15  TCB-OPTIONS-1           PIC X.                   TCB$OPT0
039400                88 TCB-RETURN-TCPIP-ERR        VALUE X'01'.
039500                88 TCB-RETURN-DATA-UNAVAILABLE VALUE X'01' X'03'.
039600                88 TCB-RETURN-DB-NOT-AVAILABLE VALUE X'02' X'03'.
039700                88 TCB-RETURN-EXCI-ERROR-DATA  VALUE X'02' X'03'.
039800                88 TCB-RETURN-DB-OR-DATA-UNAVA VALUE X'03'.
039900                88 TCB-RETURN-ACT-NOT-DEFINED  VALUE X'08'.
039901                88 TCB-RETURN-DG-NOT-DEFINED   VALUE X'10'.
040000             15  TCB-OPTIONS-2           PIC X.                   TCB$OPT2
040100             15  FILLER                  PIC X(40).
040200             15  TCB-SQL-ACTION.
040300                 20  TCB-SQL-ACTION-N    PIC 9(4) BINARY.
040400             15  TCB-SQL-RESULT.
040500                 20  TCB-SQL-RESULT-N    PIC 9(4) BINARY.
040600             15  TCB-SQL-DYNPLAN         PIC X(8).
040700             15  TCB-SQL-CURPLAN         PIC X(8).
040800             15  TCB-SQL-DYN-SUBSID      PIC X(4).
040900             15  TCB-SQL-SUBSID          PIC X(4).
041000             15  TCB-CKPT-COUNT          PIC 9(9) BINARY.
041100             15  TCB-BATCH-CC-NO         PIC 9(9) BINARY.
041101             15  TCB-BATCH-LOGGING-FLAG  PIC X.
041102                 88  TCB-BATCH-LOGGING   VALUE 'Y'.
041200             15  TCB-APPC-SERVICE-AREAS.
041300                 20 TCB-APPC-DATA-GROUP  PIC X(4).
041400                 20 TCB-APPC-SYSTEM-KEY  PIC X(4).
041500                 20 TCB-APPC-APPL-KEY    PIC X(8).
041600                 20 TCB-APPC-XMIT-IMMED  PIC X(1).
041700                    88 TRANSMIT              VALUE 'Y'.
041800                    88 TRAN-PREPARE-RECEIVE  VALUE 'R'.
041900                 20 TCB-APPC-XMIT-ERROR  PIC X(1).
042000*                 **88 ISSUE-ERROR           VALUE +1.
042100*                 **88 ISSUE-ERROR-W-DATA    VALUE +17.
042200*                 **88 ISSUE-ABEND           VALUE +2.
042300                 20 TCB-APPC-MORE-DATA   PIC X(1).
042400                 20 TCB-APPC-BYPSS-ERR   PIC X(1).
042500                 20 TCB-APPC-RETURN-CDE  PIC X(6).
042600             15  TCB-BREAK-POINT         PIC X.
042700             15  TCB-VSAM-RELATIVE-NUMB  PIC X(4).
042800             15  TCB-MONETARY-KEY        PIC X(3).                TCB$MON
042900             15  TCB-PRES-CURRENCY-CD    PIC X(3).                TCB$PCUR
043000             15  TCB-LANGUAGE-KEY        PIC X(3).
043100             15  TCB-PACK-COLLECT-NAME   PIC X(18).
043200             15  TCB-DYN-COLLECT-NAME.
043300                 20  TCB-DYN-PLAN-PREF   PIC X(2).
043400                 20  TCB-DYN-COMP-GRP    PIC X(8).
043500                 20  TCB-DYN-PROC-GRP    PIC X(4).
043600                 20  TCB-DYN-KEY-RANGE   PIC X(4).
043700             15  FILLER                  PIC X(8).
043800             15  TCB-DB2-KRID            PIC X(4).
043900             15  TCB-LANG-ENABLED        PIC X.
044000             15  TCB-DEFAULT-LANG        PIC X(3).
044100             15  TCB-LANG-ENCODE-FLAG    PIC X.
044200             15  TCB-PROC-GROUP          PIC X(4).
044300             15  TCB-PROC-GROUP-BRANCH   PIC 9(5) COMP-3.
044400             15  TCB-PROCESSING-ID       REDEFINES
044500                 TCB-PROC-GROUP-BRANCH   PIC 9(5) COMP-3.
044600             15  TCB-UDFL-LANG           PIC X(3).
044700             15  TCB-SPS-IMPLODE-LANG    PIC X(3).
044800             15  TCB-OPERATOR-REGION     PIC 9(5)  COMP-3.
044900             15  TCB-OPERATOR-BRANCH     PIC 9(5)  COMP-3.
045000             15  TCB-DYN-LANG            PIC X(3).
045100             15  TCB-DMAP-ID             PIC X(7).
045200             15  TCB-DB2-PROC-GRP-ID     PIC X(4).
045300             15  TCB-DB2-PROC-GRP-BRANCH PIC 9(5)  COMP-3.
045400             15  TCB-DB2-PROCESSING-ID   REDEFINES
045500                 TCB-DB2-PROC-GRP-BRANCH PIC 9(5)  COMP-3.
045600             15  TCB-USER-WORK-AREA.
045700                 20  TCB-DYNAMIC-DG-ADDR POINTER.
045800                 20  TCB-DYNAMIC-DG-ADDRESS  REDEFINES
045900                     TCB-DYNAMIC-DG-ADDR POINTER.
046000                 20  TCB-DYNAMIC-DG-LENGTH PIC 9(9) BINARY.
046100                 20  FILLER              PIC X(4).
046200             15  TCB-HDP-RESERVED        PIC X(10).
046300             15  TCB-DFLT-LANG-ENCODE-BYTE
046400                                         PIC X.
046500             15  TCB-DB2-TEST-POOL-ID    PIC X(02).
046600             15  TCB-PRES-CURR-RND-IN    PIC X(01).               TCB$PCRI
046700* Do not round presentation currency - adds an extra decimal digit
046800                 88  TCB-DO-NOT-ROUND-PRES-CURR         VALUE 'N'.
046900* Round presentation currency
047000                 88  TCB-ROUND-PRES-CURR                VALUE 'Y'.
047100             15  TCB-RAND-REC-ADDR-NR    PIC 9(9) COMP-5.
047200             15  TCB-JOB-ID              PIC X(8).
047300             15  TCB-OPERATOR-PROCESSING-ID PIC 9(5)  COMP-3.
047400             15  TCB-DFLT-CENTURYWINDOW-CUTOFF PIC S999 COMP-3.
047500             15  TCB-CICS-STARTCODE      PIC XX.
047600                 88  TCB-CICS-START-DPL               VALUE 'D '.
047700                 88  TCB-CICS-START-DPL-SYNC          VALUE 'DS'.
047800                 88  TCB-CICS-START-TD-TRIGGER        VALUE 'QD'.
047900                 88  TCB-CICS-START-CMD               VALUE 'S '.
048000                 88  TCB-CICS-START-CMD-DATA          VALUE 'SD'.
048100                 88  TCB-CICS-START-FEPI              VALUE 'SZ'.
048200                 88  TCB-CICS-START-TERMINAL-INPUT    VALUE 'TD'.
048300                 88  TCB-CICS-START-USER-ATTACH       VALUE 'U '.
048400             15  TCB-DYNAMIC-TCPIP-ID.
048500                 20  TCB-DYNAMIC-TCPIP-NAME PIC X(8).
048600                 20  TCB-DYNAMIC-TCPIP-LOC  PIC X.
048700             15  TCB-MSG-LENGTH-PROCESSED   PIC 9(9) BINARY.
048800             15  TCB-MSG-CHANNEL-STATUS     PIC X.
048900                 88  TCB-MCA-INACTIVE                VALUE X'00'.
049000                 88  TCB-TCPIP-CLIENT                VALUE X'80'.
049100                 88  TCB-TCPIP-CHILD-SERVER          VALUE X'40'.
049101                 88  TCB-OPENLEGACYIAPI             VALUE X'10'
049102                                                          X'12'
049103                                                          X'13'.
049104                 88  TCB-USE-CHANNEL                VALUES X'02'
049105                                                           X'03'.
049106                 88  TCB-API-TRANSACTION            VALUES X'01'.
049200                 88  TCB-USE-COMM-AREA              VALUES X'08'
049300                                                           X'0C'.
049400                 88  TCB-BATCH-TO-ONLINE-ACTIVE     VALUES X'04'
049500                                                           X'0C'.
049600             15  TCB-PGM-TECHNOLOGY-AT-ENTRY PIC X.
049700                 88  TCB-PROGRAM-IS-HALFWORD         VALUE 'H'.
049800                 88  TCB-PROGRAM-IS-FULLWORD         VALUE 'F'.
049900             15  TCB-ACTIVITY-SUPPRESSION-FLAG PIC X.
050000                 88  TCB-SUPPRESS-ACTUAL-ACTIVITY    VALUE 'A'.
050100                 88  TCB-SUPPRESS-BOTH-ACTUAL-AFTER  VALUE 'B'.
050200* Whether COBOL module monitors the RETURN-CODE special
050300* register after each 'CALL PEM' request
050400             15  TCB-COBOL-RC-MONITOR    PIC X.
050500                 88  TCB-COBOL-RC-MONITORED  VALUE 'Y'.
050600             15  TCB-DYNAMIC-MQSERIES-QNAME PIC X(48).
050700             15  TCB-Token               PIC X(08).
050800             15  TCB-Session-ID          PIC X(08).
050900             15  TCB-RSVP-Trancode       PIC X(08).
051000* Address of PEM Monitor DataGroup 51
051100             15  TCB-PPM-USER-AREA       POINTER.
051200* Name of module that invoked the current LINK/SQL activity
051300             15  TCB-CALLING-MODULE-NAME PIC X(8).
051400             15  TCB-CHECKPOINT-ITEMS-REMAINING PIC S9(9) BINARY.
051500             15  TCB-CHECKPOINT-STATUS   PIC X.
051600                 88 TCB-CHECKPOINT-INACTIVE VALUES 'N' LOW-VALUES.
051700                 88 TCB-CHECKPOINT-ACTIVE   VALUES 'Y' 'R'.
051800                 88 TCB-IN-RESTART          VALUE  'R'.
051900             15  TCB-PPM-STATUS          PIC X.
052000                 88  TCB-PPM-INACTIVE    VALUES 'N' LOW-VALUES.
052100                 88  TCB-PPM-ACTIVE      VALUES 'Y'.
052200             15  TCB-Umbrella-VERS-RLSE.
052300                 20  TCB-Umbrella-VERS        PIC X(01).
052400                 20  TCB-Umbrella-RLSE        PIC X(01).
052500                 20  TCB-Umbrella-CST         PIC X(01).
052600                 20  TCB-Umbrella-BASELINE    PIC X(02).
052700                 20  TCB-Umbrella-MLU         PIC X(02).
052800             15  TCB-Special-App              PIC X.
052900                 88  TCB-Special-App-PAS          VALUE 'P'.
053000                 88  TCB-Special-App-ODS          VALUE 'O'.
053100             15  TCB-Special-App-Flag1        PIC X.
053200             15  TCB-Special-App-Flag2        PIC X.
053300             15  TCB-Special-App-Parm         PIC X(08).
053301             15  FILLER                       PIC X(08).
053302             15  Filler                       PIC X(08).
053400
053500             15  TCB-FREE-COUNTER             PIC XX.
053501             15  TCB-PAS-IND                  PIC X.
053502                  88 TCB-PAS                      VALUE 'Y'.
053503                  88 TCB-PAS-NO                   VALUE 'N'.
053504             15  TCB-MSG-Source               PIC X(8).
053505             15  TCB-Time-Shift-Identity      PIC X(20).
053506             15  TCB-actual-ACTIVITY-N    PIC 9(9) BINARY SYNC.
053507             15  FILLER                       PIC X(4).
053508             15  TCB-Channel                  PIC X(16).
053509             15  TCB-Container                PIC X(16).
053510             15  FILLER                       PIC X(116).
053600     SKIP1
053700****** END OF DATA GROUP 00010 ***********************************
053800     SKIP1
053900*                                                                *
054000****** END OF P49000D  *******************************************
