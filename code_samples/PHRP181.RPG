  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE    1

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
    100      h/Title PHRP181 - Batch Creation Process - AR/GL Batch                                                     05/16/05
    200      h Copyright('2003-2016 INTERMOUNTAIN HEALTH CARE, INC. (IHC)')                                             02/02/16
    300      h actgrp('QILE') dftactgrp(*no) option(*nodebugio:*srcstmt)                                                10/15/03
    400      h bnddir('QC2LE':'PHBIND':'GPBIND')                                                                        11/06/03
    500      h alwnull(*usrctl) cvtopt(*novarchar)                                                                      06/17/14
    600      h********************************************************************                                      10/15/03
    700      h* COPYRIGHT 2003-2016 BY INTERMOUNTAIN HEALTH CARE, INC. (IHC)                                            02/02/16
    800      h*                      ALL RIGHTS RESERVED                                                                10/15/03
    900      h*                                                                                                         10/15/03
   1000      h* THIS MATERIAL IS FURNISHED UNDER A LICENSE AND MAY BE USED,                                             10/15/03
   1100      h* COPIED, OR DISCLOSED ONLY IN ACCORDANCE WITH THE TERMS OF SUCH                                          10/15/03
   1200      h* LICENSE AND WITH THE INCLUSION OF THE ABOVE COPYRIGHT NOTICE.                                           10/15/03
   1300      h* THIS MATERIAL OR ANY OTHER COPIES THEREOF MAY NOT BE PROVIDED                                           10/15/03
   1400      h* OR OTHERWISE MADE AVAILABLE TO ANY OTHER PERSON.                                                        10/15/03
   1500      h*                                                                                                         10/15/03
   1600      h* THIS MATERIAL IS PROPRIETARY INFORMATION OF IHC.  ITS RECEIPT                                           10/15/03
   1700      h* AND/OR POSSESSION DOES NOT CONVEY ANY RIGHT TO REPRODUCE,                                               10/15/03
   1800      h* DISCLOSE, TRANSMIT, MANUFACTURE, USE, OR SELL ANYTHING IT MAY                                           10/15/03
   1900      h* DESCRIBE.                                                                                               10/15/03
   2000      h*                                                                                                         10/15/03
   2100      h* REPRODUCTION, DISCLOSURE, TRANSMISSION, OR USE WITHOUT THE                                              10/15/03
   2200      h* SPECIFIC WRITTEN AUTHORIZATION OF IHC IS STRICTLY FORBIDDEN.                                            10/15/03
   2300      h********************************************************************                                      10/15/03
   2400      h* Comments:                                                                                               10/15/03
   2500      h*                                                                                                         10/15/03
   2600      h********************************************************************                                      10/15/03
   2700      h*  DATE      SMRF   PGMR  DESCRIPTION                                                                     10/15/03
   2800      h*  --------  -----  ----  ------------------------------------------                                      10/15/03
   2900      h*  10-15-03  25371  3524  CREATION                                                                        10/17/03
   3000      h*  03-15-04  25371  3524  Move @NewFacility to @Hdr to fix timing issue                                   03/22/04
   3100      h*  03-22-04  25371  3524  Accomodate hosp# for pyfac changed from 3 to 7,                                 03/22/04
   3200      h*                         load in charge date schedule date, include CC with CK                           03/22/04
   3300      h*  03-26-04  25863  3508  Change in PHPWLETD key length                                                   03/27/04
   3400      h*  03-31-04  25371  3524  Add City and State to ACH6 for POP                                              03/31/04
   3500      h*  04-02-04  25371  3508  Set PHPFHDR status to 'A'                                                       04/02/04
   3600      h*  04-15-04  25371  3524  Check Creation and Schedule Date for 601/602                                    04/15/04
   3700      h*  04-16-04  25371  3524  Fix problem with MassType                                                       04/16/04
   3800      h*  04-19-04  25371  3524  Different ACH company id's per facility                                         04/20/04
   3900      h*  04-22-04  25899  3508  Web Collect - Check limits to GPPLOCN                                           04/28/04
   4000       *                         Recompile only                                                                  04/28/04
   4100      h*  04-29-04  25900  3508  Web Collect - Process reject checks                                             04/29/04
   4200      h*  05-11-04  25900  3524  Make sure Routing number is 9 length                                            05/11/04
   4300      h*  05-13-04  25951  3524  Process type 'S' checks                                                         05/14/04
   4400      h*  05-19-04  25951  3508  Correct PHPFHDR batch on type 'S'                                               05/19/04
   4500      h*  03-28-05  26664  3524  Add Payment Type Code for WEB transactions (ACH)                                04/08/05
   4600      h*  08/16/04  25546  3508  Collapse Partition - New Receipting                                             08/16/04
   4700       *                         Decide facility by facility if DDM should be                                    08/16/04
   4800       *                          used.                                                                          08/16/04
   4900      h*  11-15-04  26142  3524  Add Write Detail GL Batches                                                     11/15/04
   5000      h*  02-01-05  26402  3508  BetaLib:  add jobd to GPPLOCN                                                   02/01/05
   5100      H*  02/07/05  26171  3508  Web Collect -GL - Changes to PHPPYMT                                            02/08/05
   5200      h*                         Recompile for file change                                                       03/22/05
   5300      h*  05-05-05  26142  3524  Fix problem with GL department (evalr)                                          05/16/05







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE    2

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
   5400      h*  05-16-05  26657  3524  Split payment, remove Bank batch processing                                     05/16/05
   5500      h*  10-10-05  26657  3524  Remove DDMF for PH files                                                        10/10/05
   5600      h*  03-27-06  27289  3524  Assign CO library list for Fac:400                                              03/27/06
   5700      h*  06-07-06  27657  3524  Change QSECOFR to QSYSOPR, change from GPRPM251 to                              06/07/06
   5800      h*                         GPCPM251, skip to next fac if unable to ChangeLibl                              06/07/06
   5900      h*  08-03-06  27760  3524  Remove PHPPYMSPH from process to fix split payments issues                      08/08/06
   6000      h*  09-26-06  27813  3524  Update Processing Date in Payment file                                          10/04/06
   6100      h*  09-04-06  27863  3524  Remove code for GL/400, it will be taking care by GPPLOCNX ovrdbf               09/04/06
   6200      h*  12-07-06  27089  3524  Add cafeteria types                                                             12/21/06
   6300      h*  02-06-07  28203  3524  Remove cafeteria type RS from 6286 exception list                               02/06/07
   6400      h*  02-07-07  28208  3524  Remove AG and AC from this pgm                                                  02/08/07
   6500      h*  03-14-07  28297  3524  Summarize GL transactions by fac, dept, pmtp, cardtyp                           03/13/07
   6600      h*  06-01-07  28487  3524  Do not create Cash offset for PFS pypmtp = 'IA'                                 06/01/07
   6700      h*  05-08-07  28409  3524  Add GLHOSP cash clearing account                                                05/08/07
   6800      h*  08-30-07  28320  3524  Handle dept charge - intercompany                                               09/19/07
   6900      h*  10-02-07  28750  3524  New GL summary, add key desc for GL#, Init by.                                  10/04/07
   7000      h*                         Add batch summary                                                               10/04/07
   7100      h*  04/18/08  28715  3524  Recompile.                                                                      04/18/08
   7200      h*  05/19/08  29272  3524  Change GL description.                                                          02/19/09
   7300      h*  01/28/09  29850  3524  Check DB transaction date/time                                                  01/28/09
   7400      h*  02/19/09  29906  3524  Readd smrf 29272                                                                02/19/09
   7500      h*  02/20/09  29912  3524  GL desc from 27->40, 400, CK                                                    04/29/09
   7600      h*  02/05/10  30656  3524  Check AR bal on schedule payments                                               02/05/10
   7700      h*  12/09/10  31443  3524  Change to SQLRPGLE, add ARE/GLE                                                 01/18/11
   7800      h*  07/14/11  31443  3524  Change criteria for WriteDetlAR with ARE                                        07/14/11
   7900      h*  01/19/12  32177  3524  Add override desc to AR for BATCHPOST 5050/5625                                 01/23/12
   8000      h*  02/24/12  32177  3524  Change BATCHPOST for ENTBATPOST                                                 02/24/12
   8100      h*  08/11/11  31799  3524  Changes for Enterprise Transfers                                                08/11/11
   8200      h*  04/02/12  31799  3524  Update AR Status for Ent transfers                                              04/02/12
   8300      h*  11/20/12  32623  3524  Process refund Checks                                                           11/20/12
   8400      h*  05/07/13  32834  3524  Change @NewFacility, move get GLFac                                             05/13/13
   8500      h*  07/10/13  32920  3524  Change $fac to 107 for ARE and owned T                                          07/10/13
   8600      h*  04/24/13  32808  3524  ERP changes                                                                     04/24/13
   8700      h*  07/31/13  32808  3524  ERP changes - compare with parms of PHRPM181                                    07/31/13
   8800      h*  08/07/13  32808  3524  ERP changes - changes for GLPPSTRN(PSDOCID,PSDESC,PSRCPT)                       08/07/13
   8900      h*  08/12/13  32808  3524  ERP chanegs - do not offset for pyinv# <> ' '                                   08/12/13
   9000      h*  10/22/13  32808  3524  ERP chanegs - MMS and 107 setting                                               10/22/13
   9100      h*  10/30/13  32808  3524  ERP changes - fix phpupljw (uplco#,upldpt,uplact)                               10/30/13
   9200      h*  12/13/13  33106  3524  Fix problem with 0 AR batches                                                   12/13/13
   9300      h*  12/20/13  33106  3524  Fill phppymtar.parrst = 'Y' even if not processed                               12/20/13
   9400      h*  12/30/13  33106  3524  Clear field bupc, actid, fund, prj, class when creating offset                  12/30/13
   9500      h*  05/08/14  33194  3524  Change validation from PYINV# to GL ACCT for Misc AR                            05/08/14
   9600      h*  06/17/14  33225  3524  Cerner changes                                                                  06/17/14
   9700      h*  09/11/14  33290  3524  Cerner changes for CPA/CPM                                                      09/11/14
   9800      h*  02/21/15  33368  3524  Cerner changes for CPA/CPM - ORGSYS CHANGES                                     02/21/15
   9900      h*  06/10/15  33397  3524  Change PHPPYRV to GLPPSTRN processing, remove reference to PHPUPLJW             06/10/15
  10000      h*  02/02/16  33490  3524  Set 3 digits GL Facility                                                        02/02/16
  10100      h*  02/03/16  33488  3524  Change for Facility 10                                                          02/03/16
  10200      h********************************************************************                                      10/15/03
  10300      fphppymt   uf   e           k disk                                                                         12/09/10
  10400      fphpfhdr   uf   e           k disk    usropn                                                               11/06/03
  10500      fphpfdet   uf a e           k disk    usropn                                                               12/03/03
  10600      fphpacct   if   e           k disk    usropn                                                               02/05/10







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE    3

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  10700      fphppymtar uf   e           k disk                                                                         02/23/04
  10800      fphppymgl  uf   e           k disk                                                                         11/11/04
  10900 33397f***phpupljw  o    e             disk    usropn                                                            06/10/15
  11000 32808f***glpmstr   if   e           k disk                                                                      05/13/13
  11100      fphppyrv   o    e           k disk                                                                         04/21/08
  11200      fphlpyrvb  if   e           k disk    rename(phppyrva:phppyrvb)                                            03/13/07
  11300      fphldscpa  if   e           k disk                                                                         10/02/07
  11400      fphpglbtch uf a e           k disk                                                                         10/05/07
  11500      fphppyrs   if   e           k disk                                                                         01/28/09
  11600      fphlpymte  if   e           k disk    rename(phppymta:phlpymtea)                                           01/28/09
  11700      f                                     prefix(e_)                                                           01/28/09
  11800      fphpntwkwebo  a e           k disk                                                                         02/09/10
  11900       *                                                                                                         10/15/03
  12000       /Copy GPSSRC,GPRIP180                                                                                     05/23/05
  12100       *                                                                                                         11/11/03
  12200      d                sds                                                                                       01/23/04
  12300      d $Proc             *Proc                                                                                  01/23/04
  12400      d $Status           *Status                                                                                01/23/04
  12500      d $Parms            *Parms                                                                                 01/23/04
  12600      d $Routine          *Routine                                                                               01/23/04
  12700      d $LineNum               21     28                                                                         01/23/04
  12800 32808d $CreateUser           254    263                                                                         05/22/13
  12900       *                                                                                                         01/23/04
  13000 32808d $SourceSys      s             10a   inz('WebPost')                                                       05/10/13
  13100      d $HdrWritten     s              1a   inz('N')                                                             11/06/03
  13200      d $Facility       s                   like(pyfac) inz(*zero)                                               10/30/03
  13300      d $BatchEC        s                   like(pybec) inz(*blank)                                              10/30/03
  13400      d $PmtType        s                   like(pypmtp) inz(*blank)                                             10/30/03
  13500      d $RcdStatus      s                   like(pyrst) inz(*blanks)                                             05/14/04
  13600      d $TransType      s                   like(pyttp) inz(*blanks)                                             06/07/06
  13700      d $TransType2     s              2a   inz(*blank)                                                          06/07/06
  13800 33106d $OrgSysFlg      s                   like($orgSys) inz(*blanks)                                           12/13/13
  13900 33106d $OwnedFlg       s                   like($owned) inz(*blanks)                                            12/13/13
  14000       *                                                                                                         11/11/03
  14100      d $FailFac        s                   like(pyfac) inz(*zero)                                               06/08/06
  14200      d $FailSts        s                   like(pyrst) inz('"')                                                 06/08/06
  14300      d $FailType       s                   like(pyttp) inz(*blanks)                                             06/08/06
  14400       *                                                                                                         06/08/06
  14500      d $Count          s                   like(hcnt)                                                           11/10/03
  14600      d $Dollar         s                   like(hdlrs)                                                          11/10/03
  14700      d $CheckNum       s             14  0                                                                      11/11/03
  14800      d $GLCount        s              7s 0                                                                      12/11/06
  14900      d $ARCount        s                   like($GLCount)                                                       12/11/06
  15000      d $GLSumCount     s                   like($GLCount)                                                       03/15/07
  15100      d $GLSumCnt       s                   like($GLCount)                                                       03/22/07
  15200      d                 ds                                                                                       08/08/05
  15300      d $nDateIso                      8  0                                                                      08/08/05
  15400      d  $ncc                          2  0 overlay($nDateIso)                                                   08/08/05
  15500      d  $nyy                          2  0 overlay($nDateIso:*next)                                             08/08/05
  15600      d  $nmm                          2  0 overlay($nDateIso:*next)                                             08/08/05
  15700      d  $ndd                          2  0 overlay($nDateIso:*next)                                             08/08/05
  15800      d $nTimeIso       s              6  0                                                                      08/08/05
  15900      d                 ds                                                                                       11/11/03







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE    4

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  16000      d $wschg#                        7  0 inz(9900000)                                                         11/11/03
  16100      d  $ChrgNum                      6  0 overlay($wschg#:1)                                                   11/13/03
  16200      d  $wscar#                       4  0 overlay($wschg#:3)                                                   11/12/03
  16300      d  $ChrgDgt                      1  0 overlay($wschg#:7)                                                   11/13/03
  16400       *                                                                                                         11/10/03
  16500 32808d*                 ds                                                                                      05/10/13
  16600 32808d* $GL#                          11a                                                                       05/10/13
  16700 32808d*  $GLNotUsed                    2a   overlay($GL#)                                                       05/10/13
  16800 32808d  $GLDept        s             10a                                                                        05/10/13
  16900 32808d  $GLAcct        s             10a                                                                        05/10/13
  17000       *                                                                                                         11/11/04
  17100      d                 ds                                                                                       11/11/04
  17200      d yupldte                        8a                                                                        03/26/07
  17300      d  $month                        2a   overlay(yupldte)                                                     03/26/07
  17400      d  $blank                        4a   overlay(yupldte:*next)                                               03/26/07
  17500      d  $year                         2a   overlay(yupldte:*next)                                               03/26/07
  17600       *                                                                                                         11/11/04
  17700      d                 ds                                                                                       11/11/04
  17800      d upldol                        16a                                                                        11/11/04
  17900      d  $amt                         15a   overlay(upldol)                                                      11/11/04
  18000      d  $sign                         1a   overlay(upldol:*next)                                                11/11/04
  18100       *                                                                                                         11/11/04
  18200      d                 ds                                                                                       03/22/07
  18300      d a_upldol                      16a                                                                        03/22/07
  18400      d  $a_lamt                      15a   overlay(a_upldol)                                                    03/22/07
  18500      d  $a_lsign                      1a   overlay(a_upldol:*next)                                              03/22/07
  18600       *                                                                                                         03/22/07
  18700      d $day            s              2a                                                                        11/11/04
  18800       *                                                                                                         11/11/04
  18900      d $Batch_In       S              5P 0 inz(99900)                           Start for range                 11/10/03
  19000      d $Batch_End      S              5P 0 inz(99949)                           Batch End of range              11/10/03
  19100      d $Batch_Rst      S              5P 0 inz(90000)                           Batch Restart                   11/10/03
  19200      d $Batch_Out      S              5P 0                                      Batch # assigned                11/10/03
  19300       *                                                                                                         11/06/03
  19400      d $App            s              2a                                                                        07/26/05
  19500      d $GLCnt          s              7a                                                                        07/27/05
  19600      d q               c                   ''''                                                                 07/27/05
  19700      d $ErrTxt         s             60a                                                                        10/10/05
  19800      d $ErrFlg         s              2a                                                                        10/10/05
  19900      d $fac3           s              3  0                                                                      10/10/05
  20000       *                                                                                                         07/26/05
  20100      d $errorCode      s              7    import('_EXCP_MSGID')                                                07/26/05
  20200      d $cmdR           s           3000a                                                                        03/28/06
  20300      d $cmdResult      s             10i 0                                                                      06/07/06
  20400       *                                                                                                         03/28/06
  20500      d $tcnt           s             11s 0 inz(*zero)                                                           10/04/06
  20600       *                                                                                                         10/04/06
  20700      d $Cmd            s             10a                                                                        10/04/06
  20800      d $Val1           s             50a                                                                        10/04/06
  20900      d $Val2           s             50a                                                                        10/04/06
  21000      d $Val3           s             50a                                                                        10/04/06
  21100      d $Val4           s             50a                                                                        10/04/06
  21200      d $Val5           s             50a                                                                        10/04/06







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE    5

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  21300       *                                                                                                         03/13/07
  21400      d $Dscp_Rec     e ds                  extname(PHPDSCP) prefix(A_)                                          03/13/07
  21500      d $R_Fid          s              3A                                        Facility Id                     03/13/07
  21600 32808d $R_Ktp          s              4A   inz(*blank)                          Key Type                        05/06/13
  21700 32808d $R_Kv1          s             12A   inz(*blank)                          Key Var. 1                      05/06/13
  21800      d $R_Dtpt         s              3A   inz(*blank)                          Pat. type                       03/13/07
  21900      d $R_Car          s              4A   inz(*blank)                          Carrier code                    03/13/07
  22000      d $R_Kv2          s             12A   inz(*blank)                          Key Var. 2                      03/13/07
  22100      d $R_Odr          s              4A   inz('DTPT')                          Sort order                      03/13/07
  22200      d $R_Ptwc         s              1A   inz(*blank)                          Pat type wild card              03/13/07
  22300      d $R_Crwc         s              1A   inz(*blank)                          Car cde wild card               03/13/07
  22400      d $R_Fiwc         s              1A   inz(*blank)                          Fac id wild card                03/26/07
  22500      d $R_Err          s              1n                                        Record error flag               03/13/07
  22600       *                                                                                                         03/13/07
  22700      d $GlHospFac      s                   like(ygsid)                                                          03/13/07
  22800      d $GlHospDeptInd  s              1n                                                                        03/13/07
  22900 32808d $GLHospGLDept   s                   like(ygldept)                                                        05/03/13
  23000 32808d $GLHospGLAcct   s                   like(ygl#)                                                           05/03/13
  23100 32808d $DftGLDept      s                   like(ygldept) inz('100')                                             05/03/13
  23200 32808d $DftGLAcct      s                   like(ygl#) inz(6286)                                                 05/03/13
  23300      d $ZeroDept       s                   like(pycdpt) inz('0')                                                02/19/09
  23400 32808d $PsLive         s              1a   dtaara(PHAPSLIVE)                                                    05/14/13
  23500      d $rec          e ds                  extname(phppyrv) prefix('A_')                                        03/26/07
  23600       *                                                                                                         10/02/07
  23700      d $GLSumGLCnt     s              5s 0                                                                      10/02/07
  23800      d                 ds                                                                                       10/02/07
  23900 32808d $GLSumGL                      23a   dim(500)                                                             05/14/13
  24000 32808d  $GLSumGLFac                   3a   overlay($GLSumGL:1)                                                  05/14/13
  24100 32808d  $GLSumGLDept                 10a   overlay($GLSumGL:*Next)                                              05/14/13
  24200 32808d  $GLSumGLAcct                 10a   overlay($GLSumGL:*Next)                                              05/14/13
  24300       *                                                                                                         10/02/07
  24400      d $GLSumInitCnt   s              5s 0                                                                      10/02/07
  24500      d                 ds                                                                                       10/02/07
  24600      d $GLSumInit                    13a   dim(500)                                                             10/02/07
  24700      d  $GLSumInitFac                 3a   overlay($GLSumInit:1)                                                10/02/07
  24800      d  $GLSumInitBy                 10a   overlay($GLSumInit:*Next)                                            10/02/07
  24900 33194 *                                                                                                         05/08/14
  25000 33194d $ArMiscCnt      s              5s 0                                                                      05/08/14
  25100 33194d                 ds                                                                                       05/08/14
  25200 33194d $ArMisc                       12a   dim(500)                                                             05/08/14
  25300 33194d  $ArMiscAcct                  12a   overlay($ArMisc:1)                                                   05/08/14
  25400       *                                                                                                         10/02/07
  25500      d $glktp          s                   like(dpmktp)                                                         10/02/07
  25600      d $glkv1          s                   like(dpmkv1)                                                         10/02/07
  25700       *                                                                                                         10/04/07
  25800      d $GLSumBatch     s                   like(gbbatch) inz(*zero)                                             10/04/07
  25900       *                                                                                                         01/28/09
  26000      d $aDateTimeBank  s             14a                                                                        01/28/09
  26100      d $aDateTimeTran  s                   like($aDateTimeBank)                                                 01/28/09
  26200      d $aTimeBank      s              6a   inz('003000')                                                        01/28/09
  26300       *                                                                                                         02/08/10
  26400      d $ArBalMod       s                   like(paramt)                                                         02/08/10
  26500      d $ArNote         s                   like(ntln01)                                                         02/09/10







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE    6

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  26600      d $ArNoteCnt      s              5  0 inz(*zero)                                                           02/09/10
  26700      d #FileType       s              3a   Inz('WEB')                                                           02/09/10
  26800       *                                                                                                         12/09/10
  26900      d $pyfac          s                   like(pyfac)                                                          12/09/10
  27000      d $pyrcpt         s                   like(pyrcpt)                                                         12/09/10
  27100       *                                                                                                         12/23/10
  27200      d $mode           s              5  0                                                                      02/21/11
  27300      d $rcpt_tran      s             11p 0                                                                      02/21/11
  27400      d $orgSys         s              3a                                                                        02/21/11
  27500      d $team           s              3a                                                                        02/21/11
  27600      d $owned          s              1a                                                                        02/21/11
  27700      d $debitCo        s              5p 0                                                                      02/21/11
  27800 32808d $debitGLDept    s             10a                                                                        05/10/13
  27900 32808d $debitGLAcct    s             10a                                                                        05/10/13
  28000 32808d*$debitGL        s             11p 0                                                                      05/10/13
  28100      d $creditCo       s              5p 0                                                                      02/21/11
  28200 32808d $creditGLDept   s             10a                                                                        05/10/13
  28300 32808d $creditGLAcct   s             10a                                                                        05/10/13
  28400 32808d*$creditGL       s             11p 0                                                                      05/10/13
  28500      d $curDateTime    s               z                                                                        12/23/10
  28600      d $pgmName        s             10a   inz('PHRP181')                                                       02/28/11
  28700       *                                                                                                         07/31/13
  28800 32808d $savSqlcod      s                   like(sqlcod)                                                         07/31/13
  28900 32808d $savSqlstt      s                   like(sqlstt)                                                         07/31/13
  29000 32808d $glDesc         s            256a                                                                        08/07/13
  29100 32808d $receipt        s             11a                                                                        08/07/13
  29200 33194d $isGlArMisc     s               n                                                                        05/08/14
  29300       *                                                                                                         06/17/14
  29400 33225d $paract         s             11s 0                                                                      06/17/14
  29500       *-------------------------------------------------------------------                                      11/26/03
  29600       * Prototypes                                                                                              11/26/03
  29700       *-------------------------------------------------------------------                                      11/26/03
  29800       * Entry Parms/Self Prototype                                                                              01/23/04
  29900      d BatchCreation   pr                  ExtPgm('PHRP181')                                                    01/23/04
  30000      d  parm1                              like($Code)                                                          10/10/05
  30100      d  parm2                              like($Reject)                                                        10/10/05
  30200      d  parm3                              like($SysDatea)                                                      10/10/05
  30300      d  parm4                              like($Time6)                                                         10/10/05
  30400      d  parm5                              like($QrySlt)                                                        12/09/10
  30500      d  parm6                              like($Return)                                                        12/09/10
  30600      d BatchCreation   pi                                                                                       01/23/04
  30700      d  $Code                         1A                                                                        12/09/03
  30800      d  $Reject                       1A                                                                        03/31/04
  30900      d  $SysDatea                     8A                                                                        08/08/05
  31000      d  $Time6                        6A                                                                        08/08/05
  31100      d  $QrySlt                    5000A                                                                        12/09/10
  31200      d  $Return                       1A                                                                        12/16/03
  31300       *                                                                                                         11/25/03
  31400      d WriteNewHeader  pr                  ExtProc('PHRSM100')                                                  11/26/03
  31500      d  parm1                              like($Batch_In)                                                      11/26/03
  31600      d  parm2                              like($Batch_End)                                                     11/26/03
  31700      d  parm3                              like($Batch_Rst)                                                     11/26/03
  31800      d  parm4                              like($Batch_Out)                                                     11/26/03







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE    7

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  31900       *                                                                                                         11/26/03
  32000      d RunGLUp         pr                  ExtPgm('PHCP181GL')                                                  07/26/05
  32100      d  parm1                              like($GLCnt)                                                         10/10/05
  32200       *                                                                                                         07/26/05
  32300      d ChangeLibl      pr                  ExtProc('GPCPM251')                                                  08/02/06
  32400      d  parm1                              like($fac3)                                                          10/10/05
  32500      d  parm2                              like($App)                                                           10/10/05
  32600      d  parm3                              like($ErrTxt)                                                        10/10/05
  32700      d  parm4                              like($ErrFlg)                                                        10/10/05
  32800       *                                                                                                         10/10/05
  32900      d RunCmd          pr            10i 0 ExtProc('system')                                                    07/26/05
  33000      d  parm1                          *   value options(*string)                                               07/26/05
  33100       *                                                                                                         07/26/05
  33200      d Commands        pr                  ExtProc('PHCPM181')                                                  10/04/06
  33300      d  parm1                              like($Cmd)                                                           10/04/06
  33400      d  parm2                              like($Val1)                                                          10/04/06
  33500      d  parm3                              like($Val2)                                                          10/04/06
  33600      d  parm4                              like($Val3)                                                          10/04/06
  33700      d  parm5                              like($Val4)                                                          10/04/06
  33800      d  parm6                              like($Val5)                                                          10/04/06
  33900       *                                                                                                         03/13/07
  34000      d GetKeys         pr                  ExtProc('PHRIM089')                                                  03/13/07
  34100      d  parm1                              likeDs($Dscp_Rec)                                                    03/13/07
  34200      d  parm2                              like($R_Fid)                         Facility Id                     03/13/07
  34300      d  parm3                              like($R_Ktp)                         Key Type                        03/13/07
  34400      d  parm4                              like($R_Kv1)                         Key Var. 1                      03/13/07
  34500      d  parm5                              like($R_Dtpt)                        Pat. type                       03/13/07
  34600      d  parm6                              like($R_Car)                         Carrier code                    03/13/07
  34700      d  parm7                              like($R_Kv2)                         Key Var. 2                      03/13/07
  34800      d  parm8                              like($R_Odr)                         Sort order                      03/13/07
  34900      d  parm9                              like($R_Ptwc)                        Pat type wild card              03/13/07
  35000      d  parm10                             like($R_Crwc)                        Car cde wild card               03/13/07
  35100      d  parm11                             like($R_Fiwc)                        Fac id wild card                03/13/07
  35200      d  parm12                             like($R_Err)                         Record error flag               03/13/07
  35300       *                                                                                                         03/13/07
  35400      d ProcEntRcd      pr                  ExtProc('PHRPM181')                                                  02/21/11
  35500      d  parm1                              like($mode)                                                          02/21/11
  35600      d  parm2                              like($rcpt_tran)                                                     02/21/11
  35700      d  parm3                              like($orgSys)                                                        02/21/11
  35800      d  parm4                              like($team)                                                          02/21/11
  35900      d  parm5                              like($owned)                                                         02/21/11
  36000      d  parm6                              like($debitCo)                                                       02/21/11
  36100 32808d  parm7                              like($debitGLDept)                                                   05/10/13
  36200 32808d  parm8                              like($debitGLAcct)                                                   05/10/13
  36300 32808d* parm7                              like($debitGL)                                                       05/10/13
  36400      d  parm9                              like($creditCo)                                                      05/13/13
  36500 32808d  parm10                             like($creditGLDept)                                                  05/13/13
  36600 32808d  parm11                             like($creditGLAcct)                                                  05/13/13
  36700 32808d* parm9                              like($creditGL)                                                      05/13/13
  36800      d  parm12                             like($curDateTime)                                                   05/13/13
  36900      d  parm13                             like($pgmName)                                                       05/13/13
  37000       *                                                                                                         01/18/11
  37100      d getGLDesc       pr            40a                                                                        02/20/09







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE    8

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  37200      d  parm1                         3  0                                      ygsid                           10/03/07
  37300 32808d  parm2                        10a                                        ygldept                         05/10/13
  37400      d  parm3                        11  0                                      ygl#                            05/10/13
  37500      d  parm4                        10a                                        pyiusr                          05/10/13
  37600      d  parm5                         1n                                        $GLHospdeptInd                  05/10/13
  37700 33488d  parm6                        10  0                                      pyafac                          02/04/16
  37800 32808d  parm7                        10a                                        pycdpt                          05/10/13
  37900 32808d  parm8                        10a                                        $ZeroDept                       05/10/13
  38000      d  parm9                         2a                                        pypmtp                          05/10/13
  38100      d  parm10                       10a                                        pypcct                          05/10/13
  38200      d  parm11                       11  0                                      pglrcpt                         05/10/13
  38300      d  parm12                       60a                                        pglmemo                         05/10/13
  38400      d  parm13                        5a                                        yuplco#                         05/10/13
  38500      d  parm14                        1a                                        ysumm                           05/10/13
  38600      d  parm15                        3a                                        pybec                           05/10/13
  38700 33194d***  parm16                       30a                                        pyinv#                       05/08/14
  38800       *                                                                                                         10/03/07
  38900      d IsSummaryReq    pr              n                                                                        10/02/07
  39000      d  parm1                         3  0                                      ygsid                           10/03/07
  39100 32808d  parm2                        10a                                        ygldept                         05/06/13
  39200 32808d  parm3                        11  0                                      ygl#                            05/06/13
  39300 32808d  parm4                        10a                                        pyiusr                          05/06/13
  39400 32808d***  parm5                        30a                                        pyinv#                       05/08/14
  39500       *                                                                                                         01/28/09
  39600      d IsValidRcpt     pr              n                                                                        02/08/10
  39700       *                                                                                                         04/29/09
  39800 33397d AddBatchtoDesc  pr           256a                                                                        06/10/15
  39900 33397d  parm1                         5a                                                                        06/10/15
  40000      d  parm2                              like(a_yupldsc)                                                      04/29/09
  40100      d  parm3                              like($GLSumBatch)                                                    04/29/09
  40200 33194 *                                                                                                         05/08/14
  40300 33194d LoadIsArMisc    pr              n                                                                        05/08/14
  40400 33194d  parm1                        11  0                                      ygl#                            05/08/14
  40500       *-------------------------------------------------------------------                                      07/26/05
  40600       * KList                                                                                                   07/26/05
  40700       *-------------------------------------------------------------------                                      07/26/05
  40800      c     kphppymt      klist                                                                                  12/09/10
  40900      c                   kfld                    $pyfac                                                         12/09/10
  41000      c                   kfld                    $pyrcpt                                                        12/09/10
  41100       *                                                                                                         12/09/10
  41200      c     kphppymtar    klist                                                                                  12/09/10
  41300      c                   kfld                    pyfac                                                          12/09/10
  41400      c                   kfld                    pyrcpt                                                         12/09/10
  41500       *                                                                                                         11/10/03
  41600      c     kphpfdet      klist                                                                                  12/03/03
  41700      c                   kfld                    fbtch                                                          01/08/04
  41800      c                   kfld                    fseq#                                                          12/03/03
  41900       *                                                                                                         03/31/04
  42000      c     kphppymgl     klist                                                                                  11/11/04
  42100      c                   kfld                    pyfac                                                          11/11/04
  42200      c                   kfld                    pyrcpt                                                         11/11/04
  42300       *                                                                                                         11/11/04
  42400      c     kphlpyrvb     klist                                                                                  03/22/07







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE    9

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  42500      c                   kfld                    $nDateIso                                                      03/22/07
  42600      c                   kfld                    $nTimeIso                                                      03/22/07
  42700 33397c***                kfld                    $GlHospFac                                                     06/10/15
  42800 33397c***                kfld                    $RcdStatus                                                     06/10/15
  42900 33397c***                kfld                    $TransType                                                     06/10/15
  43000       *                                                                                                         11/11/04
  43100      c     kphldscpa     klist                                                                                  10/02/07
  43200      c                   kfld                    $glktp                                                         10/02/07
  43300      c                   kfld                    $glkv1                                                         10/02/07
  43400       ********************************************************************                                      10/15/03
  43500       * Main Line                                                                                               10/15/03
  43600       ********************************************************************                                      10/15/03
  43700      c                   exsr      @SelectRcds                                                                  12/09/10
  43800       *                                                                                                         10/29/03
  43900      c                   dow       sqlcod = 0                                                                   12/09/10
  44000      c/exec sql                                                                                                 12/09/10
  44100      c+ fetch c1 into :$pyfac, :$pyrcpt                                                                         12/09/10
  44200      c/end-exec                                                                                                 12/09/10
  44300      c                   if        sqlstt > '01999'                                                             12/09/10
  44400      c                   leave                                                                                  12/09/10
  44500      c                   endif                                                                                  12/09/10
  44600      c     kphppymt      chain     phppymt                                                                      12/09/10
  44700      c                   if        %found(phppymt)                                                              12/09/10
  44800       *                                                                                                         01/28/09
  44900      c                   if        IsValidRcpt = *on                                                            02/08/10
  45000       *                                                                                                         11/20/12
  45100      c                   exsr      @GetEntInfo                                                                  11/20/12
  45200       *                                                                                                         01/28/09
  45300      c                   eval      $tcnt = $tcnt + 1                                                            10/04/06
  45400      c                   if        ($FailFac <> pyfac) or                                                       06/08/06
  45500      c                             ($FailSts <> pyrst) or                                                       06/08/06
  45600      c                             ($FailType <> pyttp)                                                         06/08/06
  45700      c                   if        ($Facility <> pyfac) or                                                      10/30/03
  45800      c                             ($BatchEC <> pybec) or                                                       10/30/03
  45900      c                             ($PmtType <> pypmtp) or                                                      05/14/04
  46000      c                             ($RcdStatus <> pyrst) or                                                     11/08/04
  46100 33106c                             ($TransType <> pyttp) or                                                     12/13/13
  46200 33106c                             ($OrgSysFlg <> $orgSys) or                                                   12/13/13
  46300 33106c                             ($OwnedFlg <> $owned)                                                        12/13/13
  46400      c                   exsr      @HDR                                                                         11/06/03
  46500      c                   endif                                                                                  10/30/03
  46600       *                                                                                                         11/07/03
  46700      c                   if        ($FailFac <> pyfac) or                                                       11/06/06
  46800      c                             ($FailSts <> pyrst) or                                                       11/06/06
  46900      c                             ($FailType <> pyttp)                                                         11/06/06
  47000       *                                                                                                         02/22/10
  47100      c                   if        pyrst = ' '                                                                  02/22/10
  47200      c                   select                                                                                 11/08/04
  47300      c                   when      pyttp = 'AR'                                                                 02/17/11
  47400      c                   exsr      @WriteDetlAR                                                                 12/11/06
  47500      c                   when      (pyttp = 'GL') or (pyttp = 'GLHO') or                                        02/17/11
  47600      c                             (pyttp = 'GLE')                                                              02/17/11
  47700      c                   exsr      @WriteDetlGL                                                                 11/08/04







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   10

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  47800      c                   when      pyttp = 'ARE'                                                                02/17/11
  47900      c                   exsr      @ProcARE                                                                     02/21/11
  48000      c                   endsl                                                                                  11/08/04
  48100      c                   if        (pyttp = 'ARE') or (pyttp = 'GLE')                                           01/18/11
  48200      c                   eval      $mode = 3                                                                    02/21/11
  48300      c                   eval      $rcpt_tran = pyrcpt                                                          02/21/11
  48400      c                   callp     ProcEntRcd($mode:$rcpt_tran:$orgSys:$team:                                   02/21/11
  48500 32808c                             $owned:$debitCo:$debitGLDept:$debitGLAcct:                                   05/13/13
  48600 32808c                             $creditCo:$creditGLDept:$creditGLAcct:                                       05/13/13
  48700 32808c                             $curDateTime:$pgmName)                                                       05/13/13
  48800      c                   endif                                                                                  01/18/11
  48900      c                   endif                                                                                  02/22/10
  49000       *                                                                                                         11/03/03
  49100      c                   exsr      @UpdatePymt                                                                  12/02/03
  49200      c                   endif                                                                                  06/08/06
  49300      c                   endif                                                                                  11/06/06
  49400       *                                                                                                         12/02/03
  49500      c                   endif                                                                                  12/09/10
  49600      c                   endif                                                                                  01/28/09
  49700       *                                                                                                         01/28/09
  49800      c                   enddo                                                                                  10/30/03
  49900       *                                                                                                         10/21/03
  50000      c/exec sql                                                                                                 12/09/10
  50100      c+ close c1                                                                                                12/09/10
  50200      c/end-exec                                                                                                 12/09/10
  50300       *                                                                                                         12/09/10
  50400      c                   exsr      @CheckGL                                                                     10/11/05
  50500      c                   exsr      @CreateNotes                                                                 02/09/10
  50600       *                                                                                                         11/11/03
  50700      c                   exsr      @Exit                                                                        10/30/03
  50800       *                                                                                                         10/15/03
  50900       ********************************************************************                                      12/09/10
  51000       * Select Records                                                                                          12/09/10
  51100       ********************************************************************                                      12/09/10
  51200      c     @SelectRcds   begsr                                                                                  12/09/10
  51300       *                                                                                                         12/09/10
  51400      c/exec sql                                                                                                 12/09/10
  51500      c+ prepare s1 from : $QrySlt                                                                               12/09/10
  51600      c/end-exec                                                                                                 12/09/10
  51700      c/exec sql                                                                                                 12/09/10
  51800      c+ declare c1 cursor for s1                                                                                12/09/10
  51900      c/end-exec                                                                                                 12/09/10
  52000      c/exec sql                                                                                                 12/09/10
  52100      c+ open c1                                                                                                 12/09/10
  52200      c/end-exec                                                                                                 12/09/10
  52300       *                                                                                                         12/09/10
  52400      c                   endsr                                                                                  12/09/10
  52500       ********************************************************************                                      10/29/03
  52600       * New Facility                                                                                            10/29/03
  52700       ********************************************************************                                      10/29/03
  52800      c     @NewFacility  begsr                                                                                  10/29/03
  52900       *                                                                                                         07/26/05
  53000      c                   eval      $FailFac = *zero                                                             11/06/06







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   11

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  53100      c                   eval      $FailSts = ''                                                                11/06/06
  53200      c                   eval      $FailType = *blanks                                                          11/06/06
  53300      c                   close     phpfhdr                                                                      07/26/05
  53400      c                   close     phpfdet                                                                      07/26/05
  53500      c                   close     phpacct                                                                      02/05/10
  53600       *                                                                                                         05/13/13
  53700 33397c***                exsr      @CheckGL                                                                     06/10/15
  53800       *                                                                                                         05/13/13
  53900      c                   eval      $Facility = pyfac                                                            05/13/13
  54000      c                   eval      $RcdStatus = pyrst                                                           05/13/13
  54100      c                   eval      $TransType = pyttp                                                           05/13/13
  54200       *                                                                                                         05/13/13
  54300 33490 * Get 3 digits GL Facility                                                                                02/02/16
  54400 33490c                   select                                                                                 02/02/16
  54500 33490c                   when      ((pyttp = 'ARE' or pyttp = 'GLE') and                                        02/02/16
  54600 33490c                              $owned = 'T') or                                                            02/02/16
  54700 33490c                             (pyttp = 'ARC') or                                                           02/02/16
  54800 33490c                             ($orgSys = 'ARC' or $orgSys = 'MMS')                                         02/02/16
  54900 33490c                   eval      $fac3 = 107                                                                  02/02/16
  55000 33490c                   when      (pyttp = 'ARPD' or pyiusr = 'IDX' or                                         02/02/16
  55100 33490c                              $orgSys = 'CPM')                                                            02/02/16
  55200 33490c                   eval      $fac3 = 400                                                                  02/02/16
  55300 33490c                   other                                                                                  02/02/16
  55400 33490c                   eval      $fac3 = pyfac                                                                02/02/16
  55500 33490c                   endsl                                                                                  02/02/16
  55600 33490 *                                                                                                         02/02/16
  55700 33490c***33290           if        ($orgSys <> 'CPA') and ($orgSys <> 'CPM')                                    02/02/16
  55800 33490c***32808           if        (pyttp = 'ARE' and $owned = 'T') or                                          02/02/16
  55900 33490c***32808                     ($orgSys = 'MMS')                                                            02/02/16
  56000 33490c***32834           eval      $fac3 = 107                                                                  02/02/16
  56100 33490c***32920           else                                                                                   02/02/16
  56200 33490c***32920           eval      $fac3 = pyfac                                                                02/02/16
  56300 33490c***32834           endif                                                                                  02/02/16
  56400 32834 *Get GL Description Key                                                                                   05/13/13
  56500 32834c                   eval      $GLHospFac  = $fac3                                                          05/13/13
  56600 32834c                   eval      $GlHospDeptInd = *off                                                        05/13/13
  56700 32808c                   eval      $GLHospGLDept = $DftGLDept                                                   05/13/13
  56800 32808c                   eval      $GlHospGLAcct = $DftGLAcct                                                   05/13/13
  56900 32834c                   eval      $R_Fid = %char($fac3)                                                        05/13/13
  57000 32808c                   eval      $R_Ktp = 'WEBR'                                                              05/13/13
  57100 32808c                   eval      $R_kv1 = 'GLHOSP'                                                            05/13/13
  57200 32834c                   callp     GetKeys($Dscp_rec:$R_Fid:$R_Ktp:$R_Kv1:                                      05/13/13
  57300 32834c                                     $R_Dtpt:$R_Car:$R_Kv2:$R_Odr:                                        05/13/13
  57400 32834c                                     $R_Ptwc:$R_Crwc:$R_Fiwc:$R_Err)                                      05/13/13
  57500 32834c                   if        ($R_Err = *off)                                                              05/13/13
  57600 32834c                   if        (a_dpmdat1 > 0)                                                              05/13/13
  57700 32834c                   eval      $GlHospFac = a_dpmdat1                                                       05/13/13
  57800 32834c                   endif                                                                                  05/13/13
  57900 32834c                                                                                                          05/13/13
  58000 32834c                   if        a_dpmfld1 = 'Y'                                                              05/13/13
  58100 32834c                   eval      $GlHospDeptInd = *on                                                         05/13/13
  58200 32834c                   endif                                                                                  05/13/13
  58300 32834c                                                                                                          05/13/13







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   12

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  58400 32808c                   if        %len(%trim(a_dpmfld3)) > 0                                                   05/13/13
  58500 32834c                   monitor                                                                                05/13/13
  58600 32808c                   eval      $GlHospGLAcct = %uns(%trim(a_dpmfld3))                                       05/13/13
  58700 32808c                   eval      $GlHospGLDept = a_dpmfld2                                                    05/28/13
  58800 32834c                   on-error                                                                               05/13/13
  58900 32834c                   endmon                                                                                 05/13/13
  59000 32834c                   endif                                                                                  05/13/13
  59100 32834c                   endif                                                                                  05/13/13
  59200       *                                                                                                         07/26/05
  59300       *Change Library List                                                                                      10/10/05
  59400 33490c                   if        (pyttp = 'AR') or                                                            02/02/16
  59500 33490c                             (pyttp = 'ARE' and $orgSys = 'FAC' and                                       02/02/16
  59600 33490c                              $owned = 'F')                                                               02/02/16
  59700 32623 *                                                                                                         11/20/12
  59800      c                   eval      $App = 'PH'                                                                  10/10/05
  59900      c                   callp     ChangeLibl($fac3:$app:$ErrTxt:$ErrFlg)                                       10/10/05
  60000      c                   if        ($ErrTxt <> ' ') or ($ErrFlg <> ' ')                                         10/10/05
  60100      c                   eval      $cmdR = 'SNDMSG MSG(' + q +                                                  10/10/05
  60200      c                             'PHRP181 (Bank Batch Creation-@NewFacility)'+                                06/08/06
  60300      c                             ' Unable to change Library List for fac '+                                   06/08/06
  60400      c                             %char($fac3) + ' - type ' + pyttp +                                          06/08/06
  60500      c                             ' (rcpt ' + %char(pyrcpt) + ') error(' +                                     06/08/06
  60600      c                             $ErrTxt + ' ' + $ErrFlg + ')' +                                              06/08/06
  60700      c                             q + ') TOUSR(QSYSOPR) MSGTYPE(*INFO)'                                        06/08/06
  60800      c                   eval      $cmdResult = RunCmd($cmdR)                                                   06/07/06
  60900      c                   eval      $FailFac = pyfac                                                             06/08/06
  61000      c                   eval      $FailSts = pyrst                                                             06/08/06
  61100      c                   eval      $FailType = pyttp                                                            06/08/06
  61200      c                   leavesr                                                                                06/08/06
  61300      c                   endif                                                                                  10/10/05
  61400       *                                                                                                         03/13/07
  61500 33290c                   endif                                                                                  09/11/14
  61600       *                                                                                                         03/13/07
  61700      c                   open      phpfdet                                                                      10/28/03
  61800      c                   open      phpacct                                                                      02/05/10
  61900 33397c***                open      phpupljw                                                                     06/10/15
  62000       *                                                                                                         10/15/03
  62100      c                   endsr                                                                                  10/15/03
  62200       ********************************************************************                                      11/03/03
  62300       * Header                                                                                                  01/23/04
  62400       ********************************************************************                                      11/03/03
  62500      c     @HDR          begsr                                                                                  11/03/03
  62600       *                                                                                                         11/03/03
  62700       *New Facility                                                                                             03/15/04
  62800      c                   if        ($Facility <> pyfac) or                                                      05/14/04
  62900      c                             ($RcdStatus <> pyrst) or                                                     06/07/06
  63000      c                             ($TransType <> pyttp) or                                                     02/21/15
  63100 33368c                             ($OrgSysFlg <> $orgSys)                                                      02/21/15
  63200      c                   exsr      @NewFacility                                                                 03/15/04
  63300      c                   if        ($FailFac = pyfac) and                                                       06/08/06
  63400      c                             ($FailSts = pyrst) and                                                       06/08/06
  63500      c                             ($FailType = pyttp)                                                          06/08/06
  63600      c                   leavesr                                                                                06/08/06







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   13

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  63700      c                   endif                                                                                  06/08/06
  63800      c                   endif                                                                                  03/15/04
  63900       *Write a new Header                                                                                       11/06/03
  64000      c                   if        ($Code = ' ') or ($Code = '1') or                                            12/09/03
  64100      c                             ($Code = '2') or ($Code = '5')                                               12/09/03
  64200      c                   eval      $Count  = *zero                                                              11/10/03
  64300      c                   eval      $Dollar = *zero                                                              11/10/03
  64400      c                   eval      $HdrWritten = 'Y'                                                            11/06/03
  64500      c                   eval      $Batch_Out = *zero                                                           11/06/03
  64600      c                   if        %open(phpfhdr)                                                               03/15/04
  64700      c                   close     phpfhdr                                                                      03/15/04
  64800      c                   endif                                                                                  03/15/04
  64900 31799c                   if        (pypmtp <> 'CC' and pypmtp <> 'ET') and                                      04/02/12
  65000 32623c                             (pyttp = 'AR' or                                                             11/20/12
  65100 32623c                              (pyttp = 'ARE' and $orgSys = 'FAC' and                                      11/20/12
  65200 32623c                               $owned = 'F'))                                                             11/20/12
  65300 25951c                   If        pyrst <> 'S'                                                                 05/19/04
  65400      c                   callp     WriteNewHeader($Batch_In:$Batch_End:                                         11/26/03
  65500      c                                            $Batch_Rst:$Batch_Out)                                        11/26/03
  65600       * Delete Detail rcds for new Batch#                                                                       02/08/10
  65700      c     $Batch_Out    setll     phpfdet                                                                      02/08/10
  65800      c     $Batch_Out    reade     phpfdet                                                                      02/08/10
  65900      c                   dow       not %eof(phpfdet)                                                            02/08/10
  66000      c                   delete    phpfdeta                                                                     02/08/10
  66100      c     $Batch_Out    reade     phpfdet                                                                      02/08/10
  66200      c                   enddo                                                                                  02/08/10
  66300 25951c                   endif                                                                                  05/19/04
  66400      c                   endif                                                                                  05/19/04
  66500      c                   if        not %open(phpfhdr)                                                           03/15/04
  66600      c                   open      phpfhdr                                                                      03/15/04
  66700      c                   endif                                                                                  03/15/04
  66800      c                   endif                                                                                  12/09/03
  66900       *Set Values                                                                                               12/09/03
  67000      c                   eval      $BatchEC = pybec                                                             12/09/03
  67100      c                   eval      $PmtType = pypmtp                                                            12/09/03
  67200      c                   eval      $TransType2 = %subst(pyttp:1:2)                                              06/07/06
  67300 33106c                   eval      $OrgSysFlg = $orgSys                                                         12/13/13
  67400 33106c                   eval      $OwnedFlg = $owned                                                           12/13/13
  67500       *                                                                                                         12/09/03
  67600      c                   endsr                                                                                  11/03/03
  67700       ********************************************************************                                      12/02/03
  67800       * Update Header                                                                                           12/02/03
  67900       ********************************************************************                                      12/02/03
  68000      c     @UpdateHeader begsr                                                                                  12/02/03
  68100       *                                                                                                         12/02/03
  68200      c     $Batch_Out    chain     phpfhdr                                                                      12/02/03
  68300      c                   if        %found(phpfhdr)                                                              12/02/03
  68400      c                   eval      hbtyy = $nyy                                                                 08/08/05
  68500      c                   eval      hbtmm = $nmm                                                                 08/08/05
  68600      c                   eval      hbtdd = $ndd                                                                 08/08/05
  68700      c                   eval      htype = 'R'                                                                  12/02/03
  68800      c                   eval      hcnt = $Count                                                                12/02/03
  68900      c                   eval      hdlrs = $Dollar                                                              12/02/03







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   14

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  69000      c                   eval      hcmt = 'WEB RECEIPTS - ' + pybec + '-' +                                     01/08/04
  69100      c                                    pypmtp                                                                01/08/04
  69200      c                   eval      hdept = 'WEBR'                                                               01/27/04
  69300      c                   eval      hglty = 'U'                                                                  12/02/03
  69400      c                   evalr     hosp# = %editc(pyfac:'X')                                                    03/22/04
  69500      c                   eval      hwkst = *blanks                                                              12/02/03
  69600      c                   eval      huser = *blanks                                                              12/02/03
  69700      c                   eval      hsyyy = hbtyy                                                                12/02/03
  69800      c                   eval      hsymm = hbtmm                                                                12/02/03
  69900      c                   eval      hsydd = hbtdd                                                                12/02/03
  70000      c                   eval      hstme = $nTimeIso                                                            08/08/05
  70100      c                   eval      htot1 = $Count                                                               01/08/04
  70200      c                   eval      htot2 = $Dollar                                                              01/08/04
  70300      c                   eval      hrcnt = $Count                                                               01/08/04
  70400      c                   eval      hrsts1 = 'A'                                                                 04/02/04
  70500      c                   update    phpfhdra                                                                     12/02/03
  70600      c                   endif                                                                                  12/02/03
  70700       *                                                                                                         12/02/03
  70800      c                   endsr                                                                                  12/02/03
  70900       ********************************************************************                                      11/03/03
  71000       * Write Detail AR                                                                                         08/11/11
  71100       ********************************************************************                                      11/03/03
  71200      c     @WriteDetlAR  begsr                                                                                  12/11/06
  71300       *                                                                                                         11/10/03
  71400      c     kphppymtar    setll     phppymtar                                                                    02/23/04
  71500      c     kphppymtar    reade     phppymtar                                                                    02/23/04
  71600       *                                                                                                         02/08/10
  71700      c                   dow       not %eof(phppymtar)                                                          02/23/04
  71800       *                                                                                                         02/08/10
  71900      c                   exsr      @CheckArBal                                                                  02/08/10
  72000      c                   if        paramt <> 0                                                                  02/08/10
  72100       *                                                                                                         02/08/10
  72200      c                   eval      $Count += 1                                                                  11/10/03
  72300      c                   eval      fbtch = $Batch_Out                                                           01/08/04
  72400      c                   eval      fseq# = $Count                                                               12/03/03
  72500      c                   if        ($code = ' ') or ($code = '1') or                                            12/09/03
  72600      c                             ($code = '2') or ($code = '5')                                               12/09/03
  72700      c     kphpfdet      chain     phpfdet                                                                      12/03/03
  72800      c                   if        %found(phpfdet)                                                              12/03/03
  72900      c                   delete    phpfdeta                                                                     12/03/03
  73000      c                   endif                                                                                  12/03/03
  73100      c                   clear                   phpfdeta                                                       11/10/03
  73200      c                   endif                                                                                  12/09/03
  73300      c                   eval      fdept = 'WEBR'                                                               01/27/04
  73400      c                   eval      fbtch = $Batch_Out                                                           01/08/04
  73500      c                   eval      fseq# = $Count                                                               11/10/03
  73600 33225c                   monitor                                                                                06/17/14
  73700 33225c                   eval      fpat# = %dec(%trim(paract):11:0)                                             06/17/14
  73800 33225c                   on-error                                                                               06/17/14
  73900 33225c                   eval      fpat# = 0                                                                    06/17/14
  74000 33225c                   endmon                                                                                 06/17/14
  74100      c                   eval      $wscar# = parpcd                                                             11/11/03
  74200      c                   eval      $CheckNum = $ChrgNum                                                         11/13/03







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   15

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  74300      c                   eval      $ChrgDgt = Chkdigit212($CheckNum)                                            11/13/03
  74400      c                   eval      fpro# = $wschg#                                                              11/11/03
  74500      c                   eval      fquan = 1                                                                    11/10/03
  74600      c                   eval      $Dollar += paramt                                                            11/11/03
  74700      c                   eval      famt = paramt                                                                11/11/03
  74800      c                   eval      ftryy = %uns(%subst(%editc(pysdt:'X'):3:2))                                  03/22/04
  74900      c                   eval      ftrmm = %uns(%subst(%editc(pysdt:'X'):5:2))                                  03/22/04
  75000      c                   eval      ftrdd = %uns(%subst(%editc(pysdt:'X'):7:2))                                  03/22/04
  75100      c                   eval      frsts1 = 'A'                                                                 11/10/03
  75200      c                   if        parbdf = 'B'                                                                 03/31/04
  75300      c                   eval      fsmcs = 1                                                                    03/31/04
  75400      c                   endif                                                                                  03/31/04
  75500       *                                                                                                         08/17/04
  75600       *  Fill override description with payer name if patient type is U**                                       08/17/04
  75700       *      (Institutional Bill)                                                                               08/17/04
  75800       *                                                                                                         08/17/04
  75900 25546                                                                                                           08/17/04
  76000 25546c                   if        %subst(paraptl :1 :1) = 'U'                                                  08/17/04
  76100 25546c                   eval      Fdesc = %trim(pyplnm) +', ' +                                                08/17/04
  76200 25546c                              %trim(pypfnm) + ' ' + pypmnm                                                08/17/04
  76300 25546c                   Endif                                                                                  08/17/04
  76400 25546                                                                                                           08/17/04
  76500 32177c***                   if        pyiusr = 'BATCHPOST' and                                                  02/24/12
  76600 32177c                   if        pyiusr = 'ENTBATPOST' and                                                    02/24/12
  76700 32177c                             (parpcd = 5050 or parpcd = 5625)                                             01/23/12
  76800 32177c                   eval      Fdesc = %trim(pypfnm)                                                        03/15/12
  76900 32177c                   Endif                                                                                  01/23/12
  77000 32177                                                                                                           01/23/12
  77100      c                   if        ($code = ' ') or ($code = '1') or                                            12/09/03
  77200      c                             ($code = '2') or ($code = '5')                                               12/09/03
  77300 25900c                   IF        pyrst = ' '                                                                  05/05/04
  77400      c                   write     phpfdeta                                                                     11/10/03
  77500       *                                                                                                         05/05/04
  77600      c                   eval      parrst = 'Y'                                                                 12/02/03
  77700      c                   update    phppymtara                                                                   02/23/04
  77800       *                                                                                                         11/10/03
  77900      c                   exsr      @UpdateHeader                                                                12/02/03
  78000 25900c                   endif                                                                                  05/05/04
  78100      c                   endif                                                                                  12/09/03
  78200       *                                                                                                         02/08/10
  78300      c                   endif                                                                                  02/08/10
  78400       *                                                                                                         12/02/03
  78500      c     kphppymtar    reade     phppymtar                                                                    02/23/04
  78600      c                   enddo                                                                                  11/10/03
  78700       *                                                                                                         12/22/10
  78800      c                   endsr                                                                                  11/03/03
  78900       ********************************************************************                                      11/08/04
  79000       * Write Detail GL                                                                                         11/08/04
  79100       ********************************************************************                                      11/08/04
  79200      c     @WriteDetlGL  begsr                                                                                  11/08/04
  79300       *                                                                                                         11/08/04
  79400      c                   if        ($code=' ') or ($code='7') or ($code='8')                                    11/10/04
  79500      c     kphppymgl     setll     phppymgl                                                                     11/11/04







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   16

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  79600      c     kphppymgl     reade     phppymgl                                                                     11/11/04
  79700      c                   dow       not %eof(phppymgl)                                                           11/11/04
  79800       * Exclude dept charge - intercompany                                                                      08/30/07
  79900      c                   if        not (pypmtp = 'DC' and pyfac <> pyafac)      start dept chrg                 08/30/07
  80000 32623c                             and pglnum > 0                                                               05/10/13
  80100       *                                                                                                         05/10/13
  80200 33194c                   eval      $isGlArMisc = LoadIsArMisc(pglnum)                                           05/08/14
  80300 32808c                   eval      $GLDept = pgldept                                                            05/13/13
  80400 32808c                   eval      $GLAcct = %char(pglnum)                                                      05/10/13
  80500 32808c*                  eval      $GL# = %editc(pglnum:'X')                                                    05/10/13
  80600      c                   clear                   phppyrva                                                       03/13/07
  80700      c                   eval      ycrdt = $nDateIso                                                            03/15/07
  80800      c                   eval      ycrtm = $nTimeIso                                                            03/15/07
  80900 32808c                   eval      yssrc = $SourceSys                                                           05/13/13
  81000 32808c                   eval      ysmodnum = $Proc                                                             05/13/13
  81100 32808c                   eval      yscuser = $CreateUser                                                        05/13/13
  81200 32808c                   eval      ypmtid = pyinv#                                                              05/15/13
  81300      c                   eval      $year = %subst($sysdatea:3:2)                                                08/08/05
  81400      c                   eval      $month = %subst($sysdatea:5:2)                                               08/08/05
  81500      c                   eval      $day = %subst($sysdatea:7:2)                                                 08/08/05
  81600 31799c                   if        pypmtp = 'ET'                                                                04/02/12
  81700 31799c                   eval      yuplje# = 'WE' + $month + $day + $year                                       04/02/12
  81800 31799c                   else                                                                                   04/02/12
  81900      c                   select                                                                                 03/01/11
  82000      c                   when      ((pyttp = 'GL') or (pyttp = 'AG') or                                         03/01/11
  82100      c                              (pyttp = 'GLE')) and (pyfac = 400)                                          03/01/11
  82200      c                   eval      yuplje# = 'WT' + $month + 'EC' + $day                                        03/13/07
  82300      c                   when      ((pyttp = 'GL') or (pyttp = 'AG') or                                         03/01/11
  82400      c                              (pyttp = 'GLE')) and (pyfac <> 400)                                         03/01/11
  82500      c                   eval      yuplje# = 'AU' + $month + 'RC' + $day                                        03/13/07
  82600      c                   when      pyttp = 'GLHO'                                                               03/01/11
  82700      c                   eval      yuplje# = 'WH' + $month + 'RX' + $day                                        03/13/07
  82800      c                   endsl                                                                                  03/01/11
  82900 31799c                   endif                                                                                  04/02/12
  83000      c                   evalr     yuplco# = '00000' + %editc($GlHospFac:'X')                                   04/29/09
  83100      c                   eval      ygsid = %uns(yuplco#)                                                        03/22/07
  83200      c                   eval      ypyttp = pyttp                                                               03/15/07
  83300 32808c                   eval      ygldept = pgldept                                                            05/13/13
  83400      c                   eval      ygl# = pglnum                                                                03/15/07
  83500 32808c                   eval      yprj = pglproj                                                               05/13/13
  83600 32808c                   eval      yfund = pglfund                                                              05/13/13
  83700 32808c                   eval      yclass = pglclss                                                             05/13/13
  83800 32808c                   eval      ybupc = pglbupc                                                              05/13/13
  83900 32808c                   eval      yactid = pglactid                                                            05/13/13
  84000      c                   eval      ypmtp = pypmtp                                                               03/15/07
  84100      c                   eval      ypcct = pypcct                                                               03/15/07
  84200      c                   eval      yrfac = pyfac                                                                03/15/07
  84300      c                   eval      yrcpt = pyrcpt                                                               05/09/07
  84400      c                   eval      yrafac = pyafac                                                              03/15/07
  84500      c                   eval      yrcdpt = pycdpt                                                              03/15/07
  84600      c                   eval      yupldpt = $GLDept                                                            03/13/07
  84700      c                   eval      yuplact = $GLAcct                                                            03/13/07
  84800      c                   eval      yrst = pyrst                                                                 03/22/07







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   17

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  84900       * description                                                                                             03/15/07
  85000 32808c                   eval      yupldsc = getGLDesc(ygsid:ygldept:ygl#:                                      05/13/13
  85100 32808c                              pyiusr:$GLHospDeptInd:pyafac:pycdpt:                                        05/13/13
  85200 32808c                              $ZeroDept:pypmtp:pypcct:pglrcpt:pglmemo:                                    05/13/13
  85300 33194c                              yuplco#:ysumm:pybec)                                                        05/08/14
  85400       *                                                                                                         10/03/07
  85500 32808c                   if        ysumm = 'Y'                                                                  05/22/13
  85600 32808c                   eval      ytrdt = $curDateTime                                                         05/22/13
  85700 32808c                   else                                                                                   05/22/13
  85800 32808c                   eval      ytrdt = %timestamp(                                                          05/22/13
  85900 32808c                             %char(%date(pycdt:*iso):*iso) + '-' +                                        05/22/13
  86000 32808c                             %char(%time(pyctm:*iso):*iso) +'.000000')                                    05/22/13
  86100 32808c                   endif                                                                                  05/22/13
  86200      c                   evalr     $amt = %editc(pglamt:'X')                                                    11/11/04
  86300      c                   eval      $sign = '-'                                                                  11/11/04
  86400      c                   eval      yupld = 0 - pglamt                                                           03/15/07
  86500      c                   eval      ygamnt = yupld                                                               03/26/07
  86600      c                   write     phppyrva                                                                     03/15/07
  86700      c                   eval      $GLCount = $GLCount + 1                                                      03/26/07
  86800       * Debit Offset                                                                                            11/11/04
  86900       *  Added to handle Cafeteria non 1006286                                                                  12/11/06
  87000      c                   if        (pypmtp <> 'CO') and (pypmtp <> 'DC') and                                    02/08/07
  87100      c                             (pypmtp <> 'DS') and (pypmtp <> 'NP') and                                    05/31/07
  87200 32808c                             (pypmtp <> 'IA') and                                                         08/12/13
  87300 33194c                             ($isGlArMisc = *off)                                                         05/08/14
  87400      c                   eval      ygldept = $GLHospGLDept                                                      05/03/13
  87500      c                   eval      ygl# = $GLHospGLAcct                                                         05/03/13
  87600      c                   evalr     yupldpt = $GLHospGLDept                                                      05/03/13
  87700      c                   eval      yuplact = %char($GLHospGLAcct)                                               05/03/13
  87800      c                   evalr     $amt = %editc(pglamt:'X')                                                    11/11/04
  87900      c                   eval      $sign = ' '                                                                  11/11/04
  88000      c                   eval      yupld = pglamt                                                               03/20/07
  88100      c                   eval      ygamnt = yupld                                                               03/26/07
  88200 33106c                   clear                   yprj                                                           12/30/13
  88300 33106c                   clear                   yfund                                                          12/30/13
  88400 33106c                   clear                   yclass                                                         12/30/13
  88500 33106c                   clear                   ybupc                                                          12/30/13
  88600 33106c                   clear                   yactid                                                         12/30/13
  88700 32808c                   eval      yupldsc = getGLDesc(ygsid:ygldept:ygl#:                                      05/13/13
  88800 32808c                              pyiusr:$GLHospDeptInd:pyafac:pycdpt:                                        05/13/13
  88900 32808c                              $ZeroDept:pypmtp:pypcct:pglrcpt:pglmemo:                                    05/13/13
  89000 33194c                              yuplco#:ysumm:pybec)                                                        05/08/14
  89100      c                   write     phppyrva                                                                     03/20/07
  89200      c                   eval      $GLCount = $GLCount + 1                                                      03/26/07
  89300      c                   endif                                                                                  12/11/06
  89400      c                   endif                                                  end dept chrg                   08/30/07
  89500       * Update GL Payment                                                                                       11/11/04
  89600      c                   eval      pglbtdt = $nDateIso                                                          08/08/05
  89700      c                   update    phppymgla                                                                    11/11/04
  89800      c     kphppymgl     reade     phppymgl                                                                     11/11/04
  89900      c                   enddo                                                                                  11/11/04
  90000      c                   endif                                                                                  11/10/04
  90100       *                                                                                                         11/08/04







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   18

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  90200      c                   endsr                                                                                  06/07/06
  90300       ********************************************************************                                      12/02/03
  90400       * Update Pymt                                                                                             12/03/03
  90500       ********************************************************************                                      12/02/03
  90600      c     @UpdatePymt   begsr                                                                                  12/03/03
  90700       *                                                                                                         12/04/03
  90800      c                   if        ($code = ' ') or ($code = '1') or                                            12/09/03
  90900      c                             ($code = '2') or ($code = '5') or                                            11/17/04
  91000      c                             ($code = '7') or ($code = '8')                                               11/17/04
  91100      c                   if        ($code = '1') or ($code = '2') or                                            08/08/05
  91200      c                             ($code = '5')                                                                08/08/05
  91300      c                   eval      pydes_e = 'AR ' + %editc(pybt#:'X') + ' ' +                                  12/09/03
  91400      c                                       %editc(pypdt:'X')                                                  12/09/03
  91500      c                   endif                                                                                  12/09/03
  91600 25900c                   If        pyrst = ' '                                                                  05/04/04
  91700      c                   if        $TransType2 = 'AR'                                                           06/07/06
  91800      c                   eval      pybt# = $Batch_Out                                                           12/03/03
  91900 25900c                   endif                                                                                  11/17/04
  92000 25900c                   endif                                                                                  05/04/04
  92100      c                   endif                                                                                  05/04/04
  92200       *                                                                                                         10/04/06
  92300      c                   if        ($code = ' ')                                                                10/04/06
  92400      c                   eval      pypdt = $nDateIso                                                            10/04/06
  92500      c                   eval      pyptm = $nTimeIso                                                            10/04/06
  92600      c                   endif                                                                                  10/04/06
  92700       *                                                                                                         12/04/03
  92800      c                   update    phppymta                                                                     02/23/04
  92900       *                                                                                                         12/02/03
  93000      c                   endsr                                                                                  12/02/03
  93100       ********************************************************************                                      10/11/05
  93200       * CheckGL                                                                                                 10/11/05
  93300       ********************************************************************                                      10/11/05
  93400      c     @CheckGL      begsr                                                                                  10/11/05
  93500       *                                                                                                         10/11/05
  93600      c                   eval      $GLSumCount = 0                                                              03/22/07
  93700      c                   if        $GLCount > 0                                                                 03/15/07
  93800      c                   exsr      @SummarizeGL                                                                 03/15/07
  93900      c                   endif                                                                                  03/15/07
  94000       *                                                                                                         03/15/07
  94100 33397c***                close     phpupljw                                                                     06/10/15
  94200       *                                                                                                         10/11/05
  94300 32808c                   if        $GLSumCount > 0 and $PsLive = 'N'                                            05/16/13
  94400      c                   eval      $GLCnt = %editc($GLSumCount:'X')                                             03/15/07
  94500      c                   callp     RunGLUp($GLCnt)                                                              10/11/05
  94600      c                   endif                                                                                  10/11/05
  94700       *                                                                                                         03/15/07
  94800      c                   eval      $GLCount = 0                                                                 10/11/05
  94900       *                                                                                                         10/11/05
  95000      c                   endsr                                                                                  10/11/05
  95100       ********************************************************************                                      03/15/07
  95200       * Summarize GL                                                                                            03/15/07
  95300       ********************************************************************                                      03/15/07
  95400      c     @SummarizeGL  begsr                                                                                  03/15/07







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   19

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
  95500       *                                                                                                         03/15/07
  95600      c                   clear                   $rec                                                           03/26/07
  95700      c                   feod      phppyrv                                                                      03/26/07
  95800      c                   eval      $GLSumCnt = 0                                                                03/22/07
  95900       *                                                                                                         03/22/07
  96000      c     kphlpyrvb     setll     phlpyrvb                                                                     03/22/07
  96100      c     kphlpyrvb     reade     phlpyrvb                                                                     03/22/07
  96200      c                   dow       not %eof(phlpyrvb)                                                           03/22/07
  96300       *                                                                                                         03/22/07
  96400 33397c                   if        (a_ygsid <> ygsid) or                                                        06/10/15
  96500 33397c                             (a_ygldept <> ygldept) or                                                    06/10/15
  96600 32808c                             (a_ygl# <> ygl#) or                                                          05/13/13
  96700 32808c                             (a_yprj <> yprj) or                                                          05/13/13
  96800 32808c                             (a_yfund <> yfund) or                                                        05/13/13
  96900 32808c                             (a_yclass <> yclass) or                                                      05/13/13
  97000 32808c                             (a_ybupc <> ybupc) or                                                        05/13/13
  97100 32808c                             (a_yactid <> yactid) or                                                      05/13/13
  97200      c                             (a_yupldsc <> yupldsc) or                                                    04/28/09
  97300      c                             (a_yuplje# <> yuplje#)                                                       05/13/08
  97400 32808c                   exsr      @WriteToGL                                                                   05/14/13
  97500      c                   endif                                                                                  03/22/07
  97600      c                   exsr      @UpdateRec                                                                   03/22/07
  97700       *                                                                                                         03/22/07
  97800      c     kphlpyrvb     reade     phlpyrvb                                                                     03/22/07
  97900      c                   enddo                                                                                  03/22/07
  98000       *                                                                                                         03/15/07
  98100 32808c                   exsr      @WriteToGL                                                                   05/14/13
  98200       *                                                                                                         03/22/07
  98300      c                   endsr                                                                                  03/15/07
  98400       ********************************************************************                                      03/22/07
  98500       * Write To GL                                                                                             05/15/13
  98600       ********************************************************************                                      03/22/07
  98700      c     @WriteToGL    begsr                                                                                  05/14/13
  98800       *                                                                                                         03/22/07
  98900      c                   if        ($GLSumCnt > 0) and (a_ygamnt <> 0)                                          09/07/07
  99000      c                   eval      $GLSumCount = $GLSumCount + 1                                                03/22/07
  99100      c                   eval      $GLSumCnt = 0                                                                03/22/07
  99200 32808 * Add Batch# to description                                                                               08/07/13
  99300 32808c                   if        $GLSumBatch > 0                                                              08/07/13
  99400 33397c                   eval      $glDesc = AddBatchtoDesc(a_yuplco#:                                          06/10/15
  99500 32808c                                                     a_yupldsc:                                           08/07/13
  99600 32808c                                                     $GLSumBatch)                                         08/07/13
  99700 32808c                   eval      $receipt = '0'                                                               08/07/13
  99800 32808c                   else                                                                                   08/07/13
  99900 32808c                   eval      $glDesc = a_yupldsc                                                          08/07/13
 100000 32808c                   eval      $receipt = %char(a_yrcpt)                                                    08/07/13
 100100 32808c                   endif                                                                                  08/07/13
 100200 32808c                   if        $PsLive = 'N'                                                                05/22/13
 100300 32808 * Write to PHPUPLJW                                                                                       05/14/13
 100400 33397c***                clear                   phpupljwa                                                      06/10/15
 100500 33397c***                eval      upldte = a_yupldte                                                           06/10/15
 100600 33397c***                eval      uplje# = a_yuplje#                                                           06/10/15
 100700 33397c***                evalr     uplco# = '00000' + %trim(a_yuplco#)                                          06/10/15







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   20

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 100800 33397c***                evalr     upldpt = '00000' + %trim(a_yupldpt)                                          06/10/15
 100900 33397c***                evalr     uplact = '0000' + %trim(a_yuplact)                                           06/10/15
 101000 33397c***                eval      upldsc = $glDesc                                                             06/10/15
 101100 33397c***                eval      uplhrs = a_yuplhrs                                                           06/10/15
 101200 33397c***                if        a_ygamnt < 0                                                                 06/10/15
 101300 33397c***                eval      $a_lsign = '-'                                                               06/10/15
 101400 33397c***                else                                                                                   06/10/15
 101500 33397c***                eval      $a_lsign = ' '                                                               06/10/15
 101600 33397c***                endif                                                                                  06/10/15
 101700 33397c***                eval      upld = a_ygamnt                                                              06/10/15
 101800 33397c***                evalr     $a_lamt = %editc(a_ygamnt:'X')                                               06/10/15
 101900 33397c***                eval      upldol = a_upldol                                                            06/10/15
 102000 33397c***                eval      uplh = a_yuplh                                                               06/10/15
 102100 33397c***                write     phpupljwa                                                                    06/10/15
 102200 32808c                   else                                                                                   05/14/13
 102300 32808c                   eval      $savSqlcod = sqlcod                                                          07/31/13
 102400 32808c                   eval      $savSqlstt = sqlstt                                                          07/31/13
 102500 32808 * Write to GLPPSTRN                                                                                       05/14/13
 102600 32808c/exec sql                                                                                                 05/22/13
 102700 32808c+ insert into glppstrn                                                                                    05/22/13
 102800 32808c+   (psco, psdept, psacct, psprj, psfund, psclass,                                                        05/22/13
 102900 32808c+    psbupc, psactid, psamt, psqty, pstype, pssrc, psmodnum,                                              05/22/13
 103000 32808c+    psbatch, pstrdt, pssts, pspmtid, psdesc, psusrcmt,                                                   05/22/13
 103100 32808c+    psurl, pscuser, pscdt, psdocid, psrcpt)                                                              08/07/13
 103200 32808c+   values                                                                                                05/22/13
 103300 32808c+   (:a_ygsid, :a_ygldept, :a_ygl#, :a_yprj, :a_yfund, :a_yclass,                                         05/22/13
 103400 32808c+    :a_ybupc, :a_yactid, :a_ygamnt, 0, 'GL', :a_yssrc, :a_ysmodnum,                                      05/22/13
 103500 32808c+    :$GLSumBatch, :a_ytrdt, 'PENDING', :a_ypmtid, :$glDesc, null,                                        08/07/13
 103600 32808c+    null, :a_yscuser, :$curDateTime, :a_yuplje#, :$receipt)                                              08/07/13
 103700 32808c/end-exec                                                                                                 05/22/13
 103800 32808c                   eval      sqlcod = $savSqlcod                                                          07/31/13
 103900 32808c                   eval      sqlstt = $savSqlstt                                                          07/31/13
 104000 32808c                   endif                                                                                  05/14/13
 104100      c                   else                                                                                   10/05/07
 104200      c                   if        $GLSumBatch > 0                                                              10/05/07
 104300      c     $GLSumBatch   setll     phpglbtch                                                                    10/05/07
 104400      c     $GLSumBatch   reade     phpglbtch                                                                    10/05/07
 104500      c                   dow       not %eof(phpglbtch)                                                          10/05/07
 104600      c                   delete    phpglbtcha                                                                   10/05/07
 104700      c     $GLSumBatch   reade     phpglbtch                                                                    10/05/07
 104800      c                   enddo                                                                                  10/05/07
 104900      c                   endif                                                                                  10/05/07
 105000      c                   endif                                                                                  03/22/07
 105100       *                                                                                                         03/22/07
 105200      c                   clear                   $rec                                                           10/05/07
 105300      c                   eval      $GLSumBatch = 0                                                              10/04/07
 105400       *                                                                                                         10/04/07
 105500      c                   endsr                                                                                  03/22/07
 105600       ********************************************************************                                      03/22/07
 105700       * Update Record A_                                                                                        03/22/07
 105800       ********************************************************************                                      03/22/07
 105900      c     @UpdateRec    begsr                                                                                  03/22/07
 106000       *                                                                                                         03/22/07







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   21

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 106100      c                   eval      $GLSumCnt = $GLSumCnt + 1                                                    03/22/07
 106200 32808c                   eval      a_ycrdt = ycrdt                                                              05/13/13
 106300 32808c                   eval      a_ycrtm = ycrtm                                                              05/13/13
 106400 32808c                   eval      a_yssrc = yssrc                                                              05/13/13
 106500 32808c                   eval      a_ysmodnum = ysmodnum                                                        05/13/13
 106600 32808c                   eval      a_yscuser = yscuser                                                          05/13/13
 106700      c                   eval      a_ygsid = ygsid                                                              05/13/13
 106800 32808c                   eval      a_ygldept = ygldept                                                          05/13/13
 106900      c                   eval      a_ygl# = ygl#                                                                03/22/07
 107000 32808c                   eval      a_yprj = yprj                                                                05/13/13
 107100 32808c                   eval      a_yfund = yfund                                                              05/13/13
 107200 32808c                   eval      a_yclass = yclass                                                            05/13/13
 107300 32808c                   eval      a_ybupc = ybupc                                                              05/13/13
 107400 32808c                   eval      a_yactid = yactid                                                            05/13/13
 107500 32808c                   eval      a_ypmtid = ypmtid                                                            05/15/13
 107600 32808c                   eval      a_ytrdt = ytrdt                                                              05/15/13
 107700      c                   eval      a_ypmtp = ypmtp                                                              03/22/07
 107800      c                   eval      a_ypcct = ypcct                                                              03/22/07
 107900      c                   eval      a_yrfac = yrfac                                                              03/22/07
 108000 32808c                   eval      a_yrcpt = yrcpt                                                              08/07/13
 108100      c                   eval      a_yrafac = yrafac                                                            03/26/07
 108200      c                   eval      a_yupldte = yupldte                                                          03/22/07
 108300      c                   eval      a_yuplje# = yuplje#                                                          03/22/07
 108400      c                   eval      a_yuplco# = yuplco#                                                          03/22/07
 108500      c                   eval      a_yupldpt = yupldpt                                                          03/22/07
 108600      c                   eval      a_yuplact = yuplact                                                          03/22/07
 108700      c                   eval      a_yupldsc = yupldsc                                                          04/28/09
 108800      c                   eval      a_ygamnt = a_ygamnt + ygamnt                                                 03/22/07
 108900      c                   eval      a_yuplhrs = yuplhrs                                                          03/22/07
 109000      c                   eval      a_yuplh = yuplh                                                              03/22/07
 109100       * Link Receipt to Summary                                                                                 10/04/07
 109200      c                   if        ($GLSumBatch = 0) and (ysumm = 'Y')                                          10/04/07
 109300      c     *hival        setgt     phpglbtch                                                                    10/04/07
 109400      c                   readp(n)  phpglbtch                                                                    10/05/07
 109500      c                   if        not %eof(phpglbtch)                                                          10/04/07
 109600      c                   eval      $GLSumBatch = gbbatch + 1                                                    10/04/07
 109700      c                   else                                                                                   10/04/07
 109800      c                   eval      $GLSumBatch = 1                                                              10/04/07
 109900      c                   endif                                                                                  10/04/07
 110000      c                   endif                                                                                  10/04/07
 110100       *                                                                                                         10/04/07
 110200      c                   if        ysumm = 'Y'                                                                  10/04/07
 110300      c                   clear                   phpglbtcha                                                     10/04/07
 110400      c                   eval      gbbatch = $GLSumBatch                                                        10/04/07
 110500      c                   eval      gbfac = yrfac                                                                10/04/07
 110600      c                   eval      gbrcpt = yrcpt                                                               10/04/07
 110700 32808c                   eval      gbgldept = ygldept                                                           05/14/13
 110800      c                   eval      gbglnum = ygl#                                                               10/04/07
 110900 32808c                   eval      gbprj = yprj                                                                 05/14/13
 111000 32808c                   eval      gbfund = yfund                                                               05/14/13
 111100 32808c                   eval      gbclass = yclass                                                             05/14/13
 111200 32808c                   eval      gbbupc = ybupc                                                               05/14/13
 111300 32808c                   eval      gbactid = yactid                                                             05/14/13







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   22

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 111400      c                   write     phpglbtcha                                                                   10/04/07
 111500      c                   endif                                                                                  10/04/07
 111600       *                                                                                                         10/04/07
 111700      c                   endsr                                                                                  03/22/07
 111800       ********************************************************************                                      10/02/07
 111900       * Load GL Summary Values                                                                                  10/02/07
 112000       ********************************************************************                                      10/02/07
 112100      c     @LoadDescKeys begsr                                                                                  05/06/13
 112200       *                                                                                                         10/02/07
 112300 32808 * Load All Fac GL#s for Summary Required (GLSUM_GL#)                                                      05/06/13
 112400      c                   eval      $GLSumGLCnt = 0                                                              10/02/07
 112500      c                   eval      $glktp = 'WEBR'                                                              10/02/07
 112600      c                   eval      $glkv1 = 'GLSUM_GL#'                                                         10/02/07
 112700       *                                                                                                         10/02/07
 112800      c     kphldscpa     setll     phldscpa                                                                     10/02/07
 112900      c     kphldscpa     reade     phldscpa                                                                     10/02/07
 113000      c                   dow       not %eof(phldscpa)                                                           10/02/07
 113100      c                   if        dpmrst = 'A'                                                                 10/02/07
 113200      c                   monitor                                                                                10/02/07
 113300      c                   eval      $GLSumGLCnt = $GLSumGLCnt + 1                                                10/02/07
 113400      c                   eval      $GLSumGLFac($GLSumGLCnt) = dpmfid                                            10/02/07
 113500 32808c                   eval      $GLSumGLDept($GLSumGLCnt) = dpmfld2                                          05/06/13
 113600 32808c                   eval      $GLSumGLAcct($GLSumGLCnt) = dpmfld3                                          05/06/13
 113700      c                   on-error                                                                               10/02/07
 113800      c                   eval      $GLSumGLCnt = $GLSumGLCnt - 1                                                10/02/07
 113900      c                   endmon                                                                                 10/02/07
 114000      c                   endif                                                                                  10/02/07
 114100      c     kphldscpa     reade     phldscpa                                                                     10/02/07
 114200      c                   enddo                                                                                  10/02/07
 114300       *                                                                                                         10/02/07
 114400 32808 * Load All Fac Initialized by for Summary Required (GLSUM_INIT)                                           05/06/13
 114500      c                   eval      $GLSumInitCnt = 0                                                            10/02/07
 114600      c                   eval      $glktp = 'WEBR'                                                              10/02/07
 114700      c                   eval      $glkv1 = 'GLSUM_INIT'                                                        10/02/07
 114800       *                                                                                                         10/02/07
 114900      c     kphldscpa     setll     phldscpa                                                                     10/02/07
 115000      c     kphldscpa     reade     phldscpa                                                                     10/02/07
 115100      c                   dow       not %eof(phldscpa)                                                           10/02/07
 115200      c                   if        dpmrst = 'A'                                                                 10/02/07
 115300      c                   monitor                                                                                10/02/07
 115400      c                   eval      $GLSumInitCnt = $GLSumInitCnt + 1                                            10/02/07
 115500      c                   eval      $GLSumInitFac($GLSumInitCnt) = dpmfid                                        10/02/07
 115600      c                   eval      $GLSumInitBy($GLSumInitCnt) = dpmkv2                                         10/02/07
 115700      c                   on-error                                                                               10/02/07
 115800      c                   eval      $GLSumInitCnt = $GLSumInitCnt - 1                                            10/02/07
 115900      c                   endmon                                                                                 10/02/07
 116000      c                   endif                                                                                  10/02/07
 116100      c     kphldscpa     reade     phldscpa                                                                     10/02/07
 116200      c                   enddo                                                                                  10/02/07
 116300       *                                                                                                         05/06/13
 116400 32808 * Load GL Cash Default (GLCASHDFT)                                                                        05/06/13
 116500 32808c                   eval      $R_Fid = '***'                                                               05/06/13
 116600 32808c                   eval      $R_Ktp = 'WEBR'                                                              05/06/13







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   23

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 116700 32808c                   eval      $R_kv1 = 'GLCASHDFT'                                                         05/06/13
 116800 32808c                   callp     GetKeys($Dscp_rec:$R_Fid:$R_Ktp:$R_Kv1:                                      05/06/13
 116900 32808c                                     $R_Dtpt:$R_Car:$R_Kv2:$R_Odr:                                        05/06/13
 117000 32808c                                     $R_Ptwc:$R_Crwc:$R_Fiwc:$R_Err)                                      05/06/13
 117100 32808c                   if        ($R_Err = *off)                                                              05/06/13
 117200 32808 *                                                                                                         05/06/13
 117300 32808c                   if        (a_dpmfld2 <> ' ' and a_dpmfld3 <> ' ')                                      05/06/13
 117400 32808c                   monitor                                                                                05/06/13
 117500 32808c                   eval      $DftGLAcct = %uns(%trim(a_dpmfld3))                                          05/06/13
 117600 32808c                   eval      $DftGLDept = a_dpmfld2                                                       05/06/13
 117700 32808c                   on-error                                                                               05/06/13
 117800 32808c                   endmon                                                                                 05/06/13
 117900 32808c                   endif                                                                                  05/06/13
 118000 32808 *                                                                                                         05/06/13
 118100 32808c                   endif                                                                                  05/06/13
 118200 33194 *                                                                                                         05/08/14
 118300 33194 * Load All AR Misc GL Account (ARMISC)                                                                    05/08/14
 118400 33194c                   eval      $ArMiscCnt = 0                                                               05/08/14
 118500 33194c                   eval      $glktp = 'WEBR'                                                              05/08/14
 118600 33194c                   eval      $glkv1 = 'ARMISC'                                                            05/08/14
 118700 33194 *                                                                                                         05/08/14
 118800 33194c     kphldscpa     setll     phldscpa                                                                     05/08/14
 118900 33194c     kphldscpa     reade     phldscpa                                                                     05/08/14
 119000 33194c                   dow       not %eof(phldscpa)                                                           05/08/14
 119100 33194c                   if        dpmrst = 'A'                                                                 05/08/14
 119200 33194c                   monitor                                                                                05/08/14
 119300 33194c                   eval      $ArMiscCnt = $ArMiscCnt + 1                                                  05/08/14
 119400 33194c                   eval      $ArMiscAcct($ArMiscCnt) = dpmkv2                                             05/08/14
 119500 33194c                   on-error                                                                               05/08/14
 119600 33194c                   eval      $ArMiscCnt = $ArMiscCnt - 1                                                  05/08/14
 119700 33194c                   endmon                                                                                 05/08/14
 119800 33194c                   endif                                                                                  05/08/14
 119900 33194c     kphldscpa     reade     phldscpa                                                                     05/08/14
 120000 33194c                   enddo                                                                                  05/08/14
 120100 33194 *                                                                                                         05/08/14
 120200      c                   endsr                                                                                  05/06/13
 120300       ********************************************************************                                      02/08/10
 120400       * Check AR balance                                                                                        02/08/10
 120500       ********************************************************************                                      02/08/10
 120600      c     @CheckArBal   begsr                                                                                  02/08/10
 120700       *                                                                                                         02/08/10
 120800 33225c                   monitor                                                                                06/17/14
 120900 33225c                   eval      $paract = %dec(%trim(paract):11:0)                                           06/17/14
 121000 33225c                   on-error                                                                               06/17/14
 121100 33225c                   eval      $paract = 0                                                                  06/17/14
 121200 33225c                   endmon                                                                                 06/17/14
 121300       * Check AR balance on schedule payments                                                                   02/08/10
 121400      c                   if        (pyttp = 'AR' or pyttp = 'ARE') and                                          03/01/11
 121500      c                             (pychid = 0) and                                                             03/01/11
 121600      c                             (pycdt < pysdt) and (paramt > 0)                                             02/10/10
 121700 33225c                             and ($paract > 0)                                                            06/17/14
 121800       *  Check in PHPACCT for balance and final bill date                                                       02/10/10
 121900 33225c     $paract       chain     phpacct                                                                      06/17/14







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   24

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 122000      c                   if        %found(phpacct) and (adbdd > 0) and                                          02/08/10
 122100      c                             (ambal < paramt)                                                             02/12/10
 122200       *   Set description old history                                                                           02/10/10
 122300      c                   eval      pardes_e = 'Pmt amt: ' + %char(paramt)                                       02/12/10
 122400       *   Set new AR amount                                                                                     02/10/10
 122500      c                   select                                                                                 02/08/10
 122600      c                   when      ambal <= 0                                                                   02/12/10
 122700      c                   eval      $ArBalMod = paramt                                                           02/10/10
 122800      c                   eval      paramt = 0                                                                   02/08/10
 122900      c                   when      ambal > 0                                                                    02/08/10
 123000      c                   eval      $ArBalMod = (paramt - ambal)                                                 02/10/10
 123100      c                   eval      paramt = ambal                                                               02/08/10
 123200      c                   endsl                                                                                  02/08/10
 123300       *   Set description new history                                                                           02/10/10
 123400      c                   eval      pardes_e = %trim(pardes_e) +                                                 02/09/10
 123500      c                                        ' -> ' + %char(paramt)                                            02/12/10
 123600       *   Set new receipt amount                                                                                02/10/10
 123700      c                   if        pytot$ >= $ArBalMod                                                          02/09/10
 123800      c                   eval      pytot$ = pytot$ - $ArBalMod                                                  02/09/10
 123900      c                   else                                                                                   02/09/10
 124000      c                   eval      pytot$ = 0                                                                   02/09/10
 124100      c                   endif                                                                                  02/09/10
 124200       *   Add notes                                                                                             02/10/10
 124300      c                   if        paramt > 0                                                                   02/09/10
 124400      c                   eval      $ArNote = 'PAYMENT AMOUNT ADJUSTED BY ' +                                    02/12/10
 124500      c                             %char($ArBalMod) + ' ON RCPT# ' +                                            02/09/10
 124600      c                             %char(pyrcpt)                                                                02/09/10
 124700      c                   exsr      @addArNote                                                                   02/09/10
 124800      c                   else                                                                                   02/10/10
 124900      c                   eval      parrst = 'Y'                                                                 02/12/10
 125000      c                   update    phppymtara                                                                   02/10/10
 125100      c                   endif                                                                                  02/09/10
 125200      c                   endif                                                                                  02/08/10
 125300      c                   endif                                                                                  02/08/10
 125400       *                                                                                                         10/02/07
 125500      c                   endsr                                                                                  10/02/07
 125600       ********************************************************************                                      12/11/06
 125700       * Add AR Note                                                                                             02/09/10
 125800       ********************************************************************                                      12/11/06
 125900      c     @AddArNote    begsr                                                                                  02/09/10
 126000       *                                                                                                         12/11/06
 126100      c                   eval      $ArNoteCnt = $ArNoteCnt + 1                                                  02/09/10
 126200 25979c                   clear                   phpntwka                                                       02/09/10
 126300 25979c                   eval      ntfid = parfac                                                               02/09/10
 126400 33225c                   monitor                                                                                06/17/14
 126500 33225c                   eval      ntact = %dec(%trim(paract):11:0)                                             06/17/14
 126600 33225c                   on-error                                                                               06/17/14
 126700 33225c                   eval      ntact = 0                                                                    06/17/14
 126800 33225c                   endmon                                                                                 06/17/14
 126900 25979c                   eval      ntevdt = %uns(%char(%date:*iso0))                                            02/09/10
 127000 25979c                   eval      ntusr = pycusr                                                               02/09/10
 127100 25979c                   eval      nttyp = '590'                                                                02/09/10
 127200 25979c                   eval      ntln01 = $ArNote                                                             02/09/10







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   25

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 127300 25979c                   write     phpntwka                                                                     02/09/10
 127400       *                                                                                                         02/09/10
 127500      c                   endsr                                                                                  02/09/10
 127600       ********************************************************************                                      02/09/10
 127700       * Create Notes                                                                                            02/09/10
 127800       ********************************************************************                                      02/09/10
 127900      c     @CreateNotes  begsr                                                                                  02/09/10
 128000       *                                                                                                         02/09/10
 128100      c                   if        $ArNoteCnt > *zero                                                           02/09/10
 128200 25979c                   call      'PHRP615'                                                                    02/09/10
 128300 25979c                   parm                    #FileType                                                      02/09/10
 128400      c                   endif                                                                                  02/09/10
 128500       *                                                                                                         02/21/11
 128600      c                   endsr                                                                                  02/21/11
 128700       ********************************************************************                                      02/21/11
 128800       * Process ARE                                                                                             02/21/11
 128900       ********************************************************************                                      02/21/11
 129000      c     @ProcARE      begsr                                                                                  02/21/11
 129100       *                                                                                                         02/21/11
 129200      c                   select                                                                                 02/21/11
 129300      c                   when      ($orgSys = 'FAC') and ($owned = 'F')                                         04/02/12
 129400 31799c                   if        pypmtp <> 'ET'                                                               04/02/12
 129500      c                   exsr      @WriteDetlAR                                                                 02/21/11
 129600 31799c                   else                                                                                   04/02/12
 129700 31799c                   exsr      @UpdateArEt                                                                  04/02/12
 129800 31799c                   endif                                                                                  04/02/12
 129900      c                   when      $owned = 'T'                                                                 04/02/12
 130000      c                   exsr      @GetGLEntARE                                                                 02/21/11
 130100 33106c                   exsr      @UpdateArEt                                                                  12/20/13
 130200 33106c                   other                                                                                  12/20/13
 130300 33106c                   exsr      @UpdateArEt                                                                  12/20/13
 130400      c                   endsl                                                                                  02/21/11
 130500       *                                                                                                         02/21/11
 130600      c                   endsr                                                                                  02/21/11
 130700       ********************************************************************                                      02/21/11
 130800       * Get GL transactions for Enterprise ARE                                                                  02/21/11
 130900       ********************************************************************                                      02/21/11
 131000      c     @GetGLEntARE  begsr                                                                                  02/21/11
 131100       *                                                                                                         02/09/10
 131200      c                   if        ($code=' ') or ($code='7') or ($code='8')                                    02/21/11
 131300       *                                                                                                         02/21/11
 131400      c                   eval      $mode = 2                                                                    02/21/11
 131500      c                   eval      $rcpt_tran = pyrcpt                                                          02/21/11
 131600      c                   callp     ProcEntRcd($mode:$rcpt_tran:$orgSys:$team:                                   02/21/11
 131700 32808c                             $owned:$debitCo:$debitGLDept:$debitGLAcct:                                   05/13/13
 131800 32808c                             $creditCo:$creditGLDept:$creditGLAcct:                                       05/13/13
 131900 32808c                             $curDateTime:$pgmName)                                                       05/13/13
 132000       *                                                                                                         02/21/11
 132100 32808c                   if        ($debitCo > 0) and ($debitGLDept <> '0') and                                 07/31/13
 132200 32808c                             ($debitGLAcct <> '0') and                                                    07/31/13
 132300 32808c                             ($creditCo > 0) and                                                          07/31/13
 132400 32808c                             ($creditGLDept <> '0') and                                                   07/31/13
 132500 32808c                             ($creditGLAcct <> '0')                                                       07/31/13







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   26

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 132600      c                   clear                   phppyrva                                                       02/21/11
 132700      c                   eval      ycrdt = $nDateIso                                                            02/21/11
 132800      c                   eval      ycrtm = $nTimeIso                                                            02/21/11
 132900 32808c                   eval      yssrc = $SourceSys                                                           05/13/13
 133000 32808c                   eval      ysmodnum = $Proc                                                             05/13/13
 133100 32808c                   eval      yscuser = $CreateUser                                                        05/13/13
 133200      c                   eval      $year = %subst($sysdatea:3:2)                                                02/21/11
 133300      c                   eval      $month = %subst($sysdatea:5:2)                                               02/21/11
 133400      c                   eval      $day = %subst($sysdatea:7:2)                                                 02/21/11
 133500 31799c                   if        pypmtp = 'ET'                                                                04/02/12
 133600 31799c                   eval      yuplje# = 'WE' + $month + $day + $year                                       04/02/12
 133700 31799c                   else                                                                                   04/02/12
 133800      c                   eval      yuplje# = 'AU' + $month + 'RC' + $day                                        08/11/11
 133900 31799c                   endif                                                                                  04/02/12
 134000      c                   eval      ypyttp = pyttp                                                               02/21/11
 134100      c                   eval      ypmtp = pypmtp                                                               02/21/11
 134200      c                   eval      ypcct = pypcct                                                               02/21/11
 134300      c                   eval      yrfac = pyfac                                                                02/21/11
 134400      c                   eval      yrcpt = pyrcpt                                                               02/21/11
 134500      c                   eval      yrafac = pyafac                                                              02/21/11
 134600      c                   eval      yrcdpt = pycdpt                                                              02/21/11
 134700      c                   eval      yrst = pyrst                                                                 02/21/11
 134800      c                   evalr     yuplco# = '00000' + %editc($GlHospFac:'X')                                   02/21/11
 134900      c                   eval      ygsid = %uns(yuplco#)                                                        02/21/11
 135000       *Credit                                                                                                   02/21/11
 135100 32808c                   eval      ygldept = $creditGLDept                                                      05/13/13
 135200 32808c                   eval      ygl# = %uns($creditGLAcct)                                                   05/13/13
 135300 32808c***                eval      $GL# = %editc($creditGL:'X')                                                 05/13/13
 135400 32808c                   eval      yupldpt = $creditGLDept                                                      05/13/13
 135500 32808c                   eval      yuplact = $creditGLAcct                                                      05/13/13
 135600       *                                                                                                         02/21/11
 135700 32808c                   eval      yupldsc = getGLDesc(ygsid:ygldept:ygl#:                                      05/13/13
 135800 32808c                              pyiusr:$GLHospDeptInd:pyafac:pycdpt:                                        05/13/13
 135900 32808c                              $ZeroDept:pypmtp:pypcct:pyrcpt:pydes:                                       05/13/13
 136000 33194c                              yuplco#:ysumm:pybec)                                                        05/08/14
 136100 32808 *                                                                                                         05/24/13
 136200 32808c                   if        ysumm = 'Y'                                                                  05/24/13
 136300 32808c                   eval      ytrdt = $curDateTime                                                         05/24/13
 136400 32808c                   else                                                                                   05/24/13
 136500 32808c                   eval      ytrdt = %timestamp(                                                          05/24/13
 136600 32808c                             %char(%date(pycdt:*iso):*iso) + '-' +                                        05/24/13
 136700 32808c                             %char(%time(pyctm:*iso):*iso) +'.000000')                                    05/24/13
 136800 32808c                   endif                                                                                  05/24/13
 136900       *                                                                                                         02/21/11
 137000      c                   evalr     $amt = %editc(pytot$:'X')                                                    02/21/11
 137100      c                   eval      $sign = '-'                                                                  02/21/11
 137200      c                   eval      yupld = 0 - pytot$                                                           02/21/11
 137300      c                   eval      ygamnt = yupld                                                               02/21/11
 137400      c                   write     phppyrva                                                                     02/21/11
 137500      c                   eval      $GLCount = $GLCount + 1                                                      02/21/11
 137600       *Debit                                                                                                    02/21/11
 137700 32808c***                eval      $GL# = %editc($debitGL:'X')                                                  05/13/13
 137800 32808c                   eval      ygldept = $debitGLDept                                                       05/13/13







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   27

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 137900 32808c                   eval      ygl# = %uns($debitGLAcct)                                                    05/13/13
 138000 32808c                   evalr     yupldpt = $debitGLDept                                                       05/13/13
 138100 32808c                   eval      yuplact = $debitGLAcct                                                       05/13/13
 138200      c                   evalr     $amt = %editc(pytot$:'X')                                                    02/21/11
 138300      c                   eval      $sign = ' '                                                                  02/21/11
 138400      c                   eval      yupld = pytot$                                                               02/21/11
 138500      c                   eval      ygamnt = yupld                                                               02/21/11
 138600       *                                                                                                         02/21/11
 138700 32808c                   eval      yupldsc = getGLDesc(ygsid:ygldept:ygl#:                                      05/13/13
 138800 32808c                              pyiusr:$GLHospDeptInd:pyafac:pycdpt:                                        05/13/13
 138900 32808c                              $ZeroDept:pypmtp:pypcct:pyrcpt:pydes:                                       05/13/13
 139000 33194c                              yuplco#:ysumm:pybec)                                                        05/08/14
 139100      c                   write     phppyrva                                                                     02/21/11
 139200      c                   eval      $GLCount = $GLCount + 1                                                      02/21/11
 139300      c                   endif                                                  end dept chrg                   02/21/11
 139400      c                   endif                                                                                  02/21/11
 139500       *                                                                                                         02/21/11
 139600      c                   endsr                                                                                  02/09/10
 139700       ********************************************************************                                      12/20/13
 139800       * Update AR for Electronic Transfers (ET)                                                                 12/20/13
 139900       ********************************************************************                                      12/20/13
 140000      c     @UpdateArEt   begsr                                                                                  12/20/13
 140100       *                                                                                                         12/20/13
 140200      c                   if        ($code = ' ') or ($code = '1') or                                            04/02/12
 140300      c                             ($code = '2') or ($code = '5')                                               04/02/12
 140400      c     kphppymtar    setll     phppymtar                                                                    04/02/12
 140500      c     kphppymtar    reade     phppymtar                                                                    04/02/12
 140600       *                                                                                                         04/02/12
 140700      c                   dow       not %eof(phppymtar)                                                          04/02/12
 140800       *                                                                                                         04/02/12
 140900 33106c                   if        (pyrst = ' ') and (parrst = ' ')                                             12/20/13
 141000      c                   eval      parrst = 'Y'                                                                 04/02/12
 141100      c                   update    phppymtara                                                                   04/02/12
 141200 33106c                   else                                                                                   12/20/13
 141300 33106c                   unlock    phppymtar                                                                    12/20/13
 141400      c                   endif                                                                                  04/02/12
 141500       *                                                                                                         04/02/12
 141600      c     kphppymtar    reade     phppymtar                                                                    04/02/12
 141700      c                   enddo                                                                                  04/02/12
 141800      c                   endif                                                                                  04/02/12
 141900       *                                                                                                         04/02/12
 142000      c                   endsr                                                                                  04/02/12
 142100       ********************************************************************                                      12/22/10
 142200       * Exit                                                                                                    12/22/10
 142300       ********************************************************************                                      12/22/10
 142400      c     @Exit         begsr                                                                                  12/22/10
 142500       *                                                                                                         12/22/10
 142600      c                   eval      $Cmd = 'SNDPGMMSG2'                                                          12/11/06
 142700      c                   eval      $Val1 = 'MSG0001'                                                            12/11/06
 142800      c                   eval      $Val2 = 'PHGDEND'                                                            12/11/06
 142900      c                   eval      $Val3 = 'PHRP181 has processed ' +                                           12/11/06
 143000      c                                     %char($tcnt) + ' records'                                            12/11/06
 143100      c                   callp     Commands($Cmd:$Val1:$Val2:$Val3:$Val4:$Val5)                                 12/11/06







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   28

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 143200       *                                                                                                         12/11/06
 143300      c                   eval      *inlr = *on                                                                  12/11/06
 143400      c                   return                                                                                 12/11/06
 143500       *                                                                                                         12/11/06
 143600      c                   endsr                                                                                  12/11/06
 143700       ********************************************************************                                      11/20/12
 143800       * Check Enterprise                                                                                        11/20/12
 143900       ********************************************************************                                      11/20/12
 144000      c     @GetEntInfo   begsr                                                                                  11/20/12
 144100       *                                                                                                         11/20/12
 144200 33106c                   eval      $orgSys = *blanks                                                            12/13/13
 144300 33106c                   eval      $owned = *blanks                                                             12/13/13
 144400       *                                                                                                         12/13/13
 144500      c                   if        pyttp = 'ARE' or pyttp = 'GLE'                                               11/20/12
 144600       *                                                                                                         11/20/12
 144700      c                   eval      $mode = 1                                                                    11/20/12
 144800      c                   eval      $rcpt_tran = pyrcpt                                                          11/20/12
 144900      c                   callp     ProcEntRcd($mode:$rcpt_tran:$orgSys:$team:                                   11/20/12
 145000 32808c                             $owned:$debitCo:$debitGLDept:$debitGLAcct:                                   05/13/13
 145100 32808c                             $creditCo:$creditGLDept:$creditGLAcct:                                       05/13/13
 145200 32808c                             $curDateTime:$pgmName)                                                       05/13/13
 145300       *                                                                                                         11/20/12
 145400      c                   endif                                                                                  11/20/12
 145500       *                                                                                                         11/20/12
 145600      c                   endsr                                                                                  11/20/12
 145700       ********************************************************************                                      10/27/03
 145800       * Initialize                                                                                              10/27/03
 145900       ********************************************************************                                      10/27/03
 146000      c     *inzsr        begsr                                                                                  10/27/03
 146100       *                                                                                                         10/27/03
 146200      c                   eval      $nDateIso = %uns($SysDatea)                                                  08/08/05
 146300      c                   eval      $nTimeIso = %uns($Time6)                                                     08/08/05
 146400      c                   eval      $aDateTimeBank = $SysDatea + $aTimeBank                                      01/28/09
 146500       *                                                                                                         03/23/05
 146600      c                   eval      $curDateTime = %timestamp(                                                   12/23/10
 146700      c                             %char(%date($SysDatea:*iso0):*iso) + '-' +                                   12/23/10
 146800      c                             %char(%time($Time6:*iso0):*iso) +'.000000')                                  12/23/10
 146900       *                                                                                                         12/23/10
 147000      c                   eval      $Return = 'N'                                                                12/16/03
 147100      c                   eval      $GLCount = *zero                                                             11/11/04
 147200      c                   eval      $ARCount = *zero                                                             12/11/06
 147300       *                                                                                                         06/01/05
 147400 32808c                   in        *dtaara                                                                      05/14/13
 147500       *                                                                                                         05/14/13
 147600      c                   exsr      @LoadDescKeys                                                                05/06/13
 147700       *                                                                                                         10/02/07
 147800      c                   endsr                                                                                  04/21/08
 147900       ********************************************************************                                      10/03/07
 148000       * Get GL Description                                                                                      10/03/07
 148100       ********************************************************************                                      10/03/07
 148200      p getGLDesc       b                                                                                        10/03/07
 148300      d getGLDesc       pi            40a                                                                        02/20/09
 148400      d  pygsid                        3  0                                                                      10/03/07







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   29

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 148500 32808d  pygldept                     10a                                                                        05/10/13
 148600      d  pygl#                        11  0                                                                      10/03/07
 148700      d  ppyiusr                      10a                                                                        10/03/07
 148800      d  pHospDeptInd                  1n                                                                        10/03/07
 148900 33488d  ppyafac                      10  0                                                                      02/04/16
 149000 32808d  ppycdpt                      10a                                                                        05/10/13
 149100 32808d  pZeroDept                    10a                                                                        05/10/13
 149200      d  ppypmtp                       2a                                                                        10/03/07
 149300      d  ppypcct                      10a                                                                        10/03/07
 149400      d  ppglrcpt                     11  0                                                                      10/03/07
 149500      d  ppglmemo                     60a                                                                        10/03/07
 149600      d  pyuplco#                      5a                                                                        10/03/07
 149700      d  pysumm                        1a                                                                        10/04/07
 149800      d  ppybec                        3a                                                                        03/02/09
 149900 33194d***  ppyinv#                      30a                                                                     05/08/14
 150000       *                                                                                                         10/03/07
 150100      d rDesc           s             40a                                                                        02/20/09
 150200      d $descPmtType    s              3a                                                                        04/28/09
 150300      d*                                                                                                         05/13/13
 150400      d $mdesc          s             32a                                                                        05/13/13
 150500      d $macct          s             10a                                                                        05/14/13
 150600      d $mco#           s              3a                                                                        05/16/13
 150700 32808d*kfGLCo#         s                   like(mco#)                                                           05/13/13
 150800 32808d*kfGL#           s                   like(mgl#)                                                           05/13/13
 150900       *                                                                                                         10/03/07
 151000 32808c***  kglpmstr      klist                                                                                  05/13/13
 151100 32808c***                kfld                    kfGLCo#                                                        05/13/13
 151200 32808c***                kfld                    kfGL#                                                          05/13/13
 151300       *                                                                                                         10/03/07
 151400      c                   eval      rDesc = ppglmemo                                                             10/03/07
 151500      c                   eval      $mco# = %char(pygsid)                                                        05/16/13
 151600      c                   eval      $macct = %char(pygl#)                                                        05/14/13
 151700       *                                                                                                         04/28/09
 151800       * Payment Type - Checks (physical/electronic)                                                             04/28/09
 151900      c                   select                                                                                 04/28/09
 152000      c                   when      (ppypmtp = 'CK') and (ppybec = ' ')                                          04/28/09
 152100      c                   eval      $descPmtType = 'PCK'                                                         04/28/09
 152200      c                   when      (ppypmtp = 'CK') and (ppybec <> ' ')                                         04/28/09
 152300      c                   eval      $descPmtType = 'ECK'                                                         04/28/09
 152400      c                   other                                                                                  04/28/09
 152500      c                   eval      $descPmtType = ppypmtp + %subst(ppypcct:1:1)                                 04/28/09
 152600      c                   endsl                                                                                  04/28/09
 152700       *                                                                                                         04/29/09
 152800       * Build description                                                                                       04/28/09
 152900 33194c                   if        isSummaryReq(pygsid:pygldept:pygl#:ppyiusr)                                  05/08/14
 153000 33194c***                                       ppyinv#)                                                        05/08/14
 153100      c                   eval      pysumm = 'Y'                                                                 10/04/07
 153200      c                   select                                                                                 04/28/09
 153300      c                   when      pygsid = 400                                                                 04/28/09
 153400      c                   eval      rDesc = '400 ' +                                                             04/28/09
 153500      c                              %trim(ppycdpt) + ' '  + $descPmtType                                        04/28/09
 153600      c                   when      pHospDeptInd = *on                                                           04/28/09
 153700      c                   eval      rDesc = %char(ppyafac) + ' ' +                                               02/19/09







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   30

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 153800      c                              %trim(ppycdpt) + ' '  + $descPmtType                                        04/28/09
 153900      c                   other                                                                                  04/28/09
 154000      c                   eval      rDesc = %char(ppyafac) + ' ' +                                               02/19/09
 154100      c                              %trim(pZeroDept) + ' ' + $descPmtType                                       04/28/09
 154200      c                   endsl                                                                                  04/28/09
 154300      c                   else                                                                                   10/03/07
 154400      c                   eval      pysumm = 'N'                                                                 10/04/07
 154500      c                   select                                                                                 04/28/09
 154600      c                   when      pygsid = 400                                                                 04/28/09
 154700      c                   eval      rDesc = '400 ' + %trim(ppycdpt) + ' ' +                                      04/28/09
 154800      c                                     %char(ppglrcpt) + ' ' + ppglmemo                                     04/28/09
 154900      c                   when      ppglmemo <> *blanks                                                          04/28/09
 155000      c                   eval      rDesc = %char(ppglrcpt) + ' ' + ppglmemo                                     02/19/09
 155100      c                   other                                                                                  04/28/09
 155200 32808c***                eval      kfGLCo# = %uns(pyuplco#)                                                     05/13/13
 155300 32808c***                eval      kfGL# = pygl#                                                                05/13/13
 155400 32808c                   eval      $savSqlcod = sqlcod                                                          07/31/13
 155500 32808c                   eval      $savSqlstt = sqlstt                                                          07/31/13
 155600 32808c/exec sql                                                                                                 05/13/13
 155700 32808c+ select mdesc into :$mdesc                                                                               05/13/13
 155800 32808c+ from glpmstr                                                                                            05/13/13
 155900 32808c+ where mco# = :$mco# and mdept = :pygldept and macct = :$macct                                           05/16/13
 156000 32808c+ fetch first 1 row only                                                                                  05/13/13
 156100 32808c/end-exec                                                                                                 05/13/13
 156200 32808c                   eval      sqlcod = $savSqlcod                                                          07/31/13
 156300 32808c                   eval      sqlstt = $savSqlstt                                                          07/31/13
 156400      c                                                                                                          05/13/13
 156500 32808c***  kglpmstr      chain     glpmstra                                                                     05/13/13
 156600 32808c***                if        %found(glpmstr)                                                              05/13/13
 156700 32808c                   if        $mdesc <> ' '                                                                05/13/13
 156800 32808c                   eval      rDesc = %char(ppglrcpt) + ' ' + $mdesc                                       05/13/13
 156900      c                   else                                                                                   10/03/07
 157000      c                   eval      rDesc = %char(ppglrcpt)                                                      02/19/09
 157100      c                   endif                                                                                  10/03/07
 157200      c                   endsl                                                                                  04/28/09
 157300      c                   endif                                                                                  10/03/07
 157400       *                                                                                                         10/03/07
 157500      c                   return                  rDesc                                                          10/03/07
 157600       *                                                                                                         10/03/07
 157700      p getGLDesc       e                                                                                        10/03/07
 157800       ********************************************************************                                      10/02/07
 157900       * Is Summary Required                                                                                     10/02/07
 158000       ********************************************************************                                      10/02/07
 158100      p IsSummaryReq    b                                                                                        10/03/07
 158200      d IsSummaryReq    pi              n                                                                        10/02/07
 158300      d  pygsid                        3  0                                                                      10/03/07
 158400 32808d  pydept                       10a                                                                        05/06/13
 158500      d  pygl#                        11  0                                                                      10/03/07
 158600      d  ppyiusr                      10a                                                                        10/03/07
 158700 33194d***  ppyinv#                      30a                                                                     05/08/14
 158800       *                                                                                                         10/03/07
 158900      d rInd            s               n                                                                        10/03/07
 159000       *                                                                                                         10/03/07







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   31

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 159100      d x               s              5  0                                                                      10/03/07
 159200      d                 ds                                                                                       10/03/07
 159300      d $GLSrch                       43a                                                                        05/06/13
 159400      d  $GLSrchFac                         overlay($GLSrch:1) like($GLSumGLFac)                                 10/03/07
 159500 32808d  $GLSrchDept                        overlay($GLSrch:*Next)                                               05/06/13
 159600 32808d                                     like($GLSumGLDept)                                                   05/06/13
 159700 32808d  $GLSrchAcct                        overlay($GLSrch:*Next)                                               05/06/13
 159800 32808d                                     like($GLSumGLAcct)                                                   05/06/13
 159900      d                 ds                                                                                       10/03/07
 160000      d $InitSrch                     13a                                                                        10/03/07
 160100      d  $InitSrchFac                       overlay($InitSrch:1)                                                 10/03/07
 160200      d                                     like($GLSumInitFac)                                                  10/03/07
 160300      d  $InitSrchBy                        overlay($InitSrch:*Next)                                             10/03/07
 160400      d                                     like($GLSumInitBy)                                                   10/03/07
 160500       *                                                                                                         10/03/07
 160600      c                   eval      rInd = *off                                                                  10/03/07
 160700 32808 * Check Inv#                                                                                              05/22/13
 160800 33194c                   if        $isGlArMisc = *off                                                           05/08/14
 160900       * Check GL#                                                                                               10/03/07
 161000      c                   if        $GLSumGLCnt > 0                                                              10/03/07
 161100      c                   eval      $GLSrchFac = %char(pygsid)                                                   10/03/07
 161200      c                   eval      $GLSrchDept = pydept                                                         05/06/13
 161300      c                   eval      $GLSrchAcct = %char(pygl#)                                                   05/13/13
 161400      c                   eval      x = %lookup($GLSrch:$GLSumGL:1:$GLSumGLCnt)                                  10/03/07
 161500      c                   if        x > 0                                                                        10/03/07
 161600      c                   eval      rInd = *on                                                                   10/03/07
 161700      c                   else                                                                                   10/03/07
 161800      c                   eval      $GLSrchFac = '***'                                                           10/03/07
 161900      c                   eval      x = %lookup($GLSrch:$GLSumGL:1:$GLSumGLCnt)                                  10/03/07
 162000      c                   if        x > 0                                                                        10/03/07
 162100      c                   eval      rInd = *on                                                                   10/03/07
 162200      c                   endif                                                                                  10/03/07
 162300      c                   endif                                                                                  10/03/07
 162400      c                   endif                                                                                  10/03/07
 162500       * Check Initiated By                                                                                      10/03/07
 162600      c                   if        (rInd = *off) and ($GLSumInitCnt > 0) and                                    10/03/07
 162700      c                             (ppyiusr <> *blanks)                                                         10/03/07
 162800      c                   eval      $InitSrchFac = %char(pygsid)                                                 10/03/07
 162900      c                   eval      $InitSrchBy = ppyiusr                                                        10/03/07
 163000      c                   eval      x = %lookup($InitSrch:$GLSumInit:1:                                          10/03/07
 163100      c                              $GLSumInitCnt)                                                              10/03/07
 163200      c                   if        x > 0                                                                        10/03/07
 163300      c                   eval      rInd = *on                                                                   10/03/07
 163400      c                   else                                                                                   10/03/07
 163500      c                   eval      $InitSrchFac = '***'                                                         10/03/07
 163600      c                   eval      x = %lookup($InitSrch:$GLSumInit:1:                                          10/03/07
 163700      c                              $GLSumInitCnt)                                                              10/03/07
 163800      c                   if        x > 0                                                                        10/03/07
 163900      c                   eval      rInd = *on                                                                   10/03/07
 164000      c                   endif                                                                                  10/03/07
 164100      c                   endif                                                                                  10/03/07
 164200      c                   endif                                                                                  10/03/07
 164300       *                                                                                                         10/03/07







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   32

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 164400 32808c                   endif                                                                                  05/15/13
 164500       *                                                                                                         05/15/13
 164600      c                   return    rInd                                                                         10/03/07
 164700       *                                                                                                         10/03/07
 164800      p IsSummaryReq    e                                                                                        10/02/07
 164900       ********************************************************************                                      01/28/09
 165000       * Is Valid DB receipt                                                                                     02/08/10
 165100       ********************************************************************                                      01/28/09
 165200      p IsValidRcpt     b                                                                                        02/08/10
 165300      d IsValidRcpt     pi              n                                                                        02/08/10
 165400       *                                                                                                         01/28/09
 165500      d $nPyrsFound     s               n                                                                        01/28/09
 165600      d vInd            s               n                                                                        01/28/09
 165700       *                                                                                                         01/28/09
 165800      c     kphppyrs      klist                                                                                  01/28/09
 165900      c                   kfld                    pyfac                                                          01/28/09
 166000      c                   kfld                    pyrcpt                                                         01/28/09
 166100       *                                                                                                         01/28/09
 166200      c     kphppyrs_e    klist                                                                                  01/28/09
 166300      c                   kfld                    e_pyfac                                                        01/28/09
 166400      c                   kfld                    e_pyrcpt                                                       01/28/09
 166500       *                                                                                                         01/28/09
 166600      c                   eval      vInd = *on                                                                   01/28/09
 166700       *                                                                                                         01/28/09
 166800      c                   if        ($code = ' ') and (pypmtp = 'DB')                                            01/28/09
 166900       *                                                                                                         01/28/09
 167000      c                   eval      $nPyrsFound = *off                                                           01/28/09
 167100       *Find Response                                                                                            01/28/09
 167200      c                   if        pychid <> 0                                                                  01/28/09
 167300      c     pychid        setll     phlpymte                                                                     01/28/09
 167400      c     pychid        reade     phlpymte                                                                     01/28/09
 167500      c                   dow       not %eof(phlpymte)                                                           01/28/09
 167600      c     kphppyrs_e    chain     phppyrs                                                                      01/28/09
 167700      c                   if        %found(phppyrs)                                                              01/28/09
 167800      c                   eval      $nPyrsFound = *on                                                            01/28/09
 167900      c                   leave                                                                                  01/28/09
 168000      c                   endif                                                                                  01/28/09
 168100      c     pychid        reade     phlpymte                                                                     01/28/09
 168200      c                   enddo                                                                                  01/28/09
 168300      c                   else                                                                                   01/28/09
 168400      c     kphppyrs      chain     phppyrs                                                                      01/28/09
 168500      c                   if        %found(phppyrs)                                                              01/28/09
 168600      c                   eval      $nPyrsFound = *on                                                            01/28/09
 168700      c                   endif                                                                                  01/28/09
 168800      c                   endif                                                                                  01/28/09
 168900       *Check Date/Time                                                                                          01/28/09
 169000      c                   if        ($nPyrsFound = *on) and                                                      01/28/09
 169100      c                             (prtdat <> *blanks) and                                                      01/28/09
 169200      c                             (prttim <> *blanks)                                                          01/28/09
 169300      c                   monitor                                                                                01/28/09
 169400      c                   eval      $aDateTimeTran =                                                             01/28/09
 169500      c                              %char(%date(prtdat:*mdy0):*iso0) +                                          01/28/09
 169600      c                              prttim                                                                      01/28/09







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   33

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 169700      c                   if        $aDateTimeTran >= $aDateTimeBank                                             01/29/09
 169800      c                   eval      vInd = *off                                                                  01/28/09
 169900      c                   endif                                                                                  01/28/09
 170000      c                   on-error                                                                               01/28/09
 170100      c                   endmon                                                                                 01/28/09
 170200      c                   endif                                                                                  01/28/09
 170300       *                                                                                                         01/28/09
 170400      c                   endif                                                                                  01/28/09
 170500       *                                                                                                         01/28/09
 170600      c                   return    vInd                                                                         01/28/09
 170700       *                                                                                                         01/28/09
 170800      p IsValidRcpt     e                                                                                        02/08/10
 170900       ********************************************************************                                      04/29/09
 171000       * Add GL Summary Batch# to Description                                                                    04/29/09
 171100       ********************************************************************                                      04/29/09
 171200      p AddBatchtoDesc  b                                                                                        04/29/09
 171300 33397d AddBatchtoDesc  pi           256a                                                                        06/10/15
 171400 33397d  inCo#                         5a                                                                        06/10/15
 171500      d  inDesc                             like(a_yupldsc)                                                      04/29/09
 171600      d  inBatch                            like($GLSumBatch)                                                    04/29/09
 171700       *                                                                                                         04/29/09
 171800 33397d outDesc         s            256a                                                                        06/10/15
 171900      d lenBatch        s              5s 0                                                                      04/29/09
 172000      d lenDesc         s              5s 0                                                                      04/29/09
 172100      d maxLen          s              5s 0 inz(27)                                                              05/22/09
 172200      d blanks40        s             40a   inz(*blanks)                                                         05/22/09
 172300       *                                                                                                         04/29/09
 172400      c                   if        inCo# = '00400'                                                              05/07/09
 172500      c                   eval      lenBatch = %len(%trim(%char(inBatch)))                                       04/29/09
 172600      c                   eval      lenDesc = %len(%trim(inDesc))                                                04/29/09
 172700      c                   if        (lenBatch + lenDesc + 1) > maxLen                                            05/22/09
 172800      c                   eval      outDesc = %subst(inDesc:1:maxLen-1-lenBatch)                                 05/22/09
 172900      c                                       + ' ' + %char(inBatch) + blanks40                                  05/22/09
 173000      c                   else                                                                                   04/29/09
 173100      c                   eval      outDesc = %trim(inDesc) + ' ' +                                              04/29/09
 173200      c                                       %char(inBatch)                                                     04/29/09
 173300      c                   endif                                                                                  04/29/09
 173400      c                   else                                                                                   04/29/09
 173500      c                   eval      outDesc = %char(inBatch) + ' ' +                                             04/29/09
 173600      c                                      inDesc                                                              04/29/09
 173700      c                   endif                                                                                  04/29/09
 173800       *                                                                                                         04/29/09
 173900      c                   return    outDesc                                                                      04/29/09
 174000       *                                                                                                         04/29/09
 174100      p AddBatchtoDesc  e                                                                                        04/29/09
 174200 33194 ********************************************************************                                      05/08/14
 174300 33194 * Return if the GL Account# is an AR Misc Account#                                                        05/08/14
 174400 33194 ********************************************************************                                      05/08/14
 174500 33194p LoadIsArMisc    b                                                                                        05/08/14
 174600 33194d LoadIsArMisc    pi              n                                                                        05/08/14
 174700 33194d  glAccount#                   11  0                                      ygl#                            05/08/14
 174800 33194 *                                                                                                         05/08/14
 174900 33194d glActSrch       s                   like($ArMiscAcct)                                                    05/08/14







  5770WDS V7R3M0  160422                  SEU SOURCE LISTING                            04/09/24 09:56:23    IHCFAC       PAGE   34

  SOURCE FILE . . . . . . .  PH#LIBR/PHSSRC
  MEMBER  . . . . . . . . .  PHRP181

  SEQNBR*...+... 1 ...+... 2 ...+... 3 ...+... 4 ...+... 5 ...+... 6 ...+... 7 ...+... 8 ...+... 9 ...+... 0
 175000 33194d x               s              5  0                                                                      05/08/14
 175100 33194d rInd            s               n                                                                        05/08/14
 175200 33194 *                                                                                                         05/08/14
 175300 33194c                   eval      glActSrch = %char(glAccount#)                                                05/08/14
 175400 33194c                   eval      x = %lookup(glActSrch:$ArMisc:1:$ArMiscCnt)                                  05/08/14
 175500 33194c                   if        x > 0                                                                        05/08/14
 175600 33194c                   eval      rInd = *on                                                                   05/08/14
 175700 33194c                   else                                                                                   05/08/14
 175800 33194c                   eval      rInd = *off                                                                  05/08/14
 175900 33194c                   endif                                                                                  05/08/14
 176000 33194 *                                                                                                         05/08/14
 176100 33194c                   return    rInd                                                                         05/08/14
 176200 33194 *                                                                                                         05/08/14
 176300 33194p LoadIsArMisc    e                                                                                        05/08/14


                                  * * * *  E N D  O F  S O U R C E  * * * *











































