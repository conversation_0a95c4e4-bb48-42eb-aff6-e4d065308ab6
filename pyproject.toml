[project]
version = "2.0.0"
name = "genai_ram"
dynamic = ["dependencies"]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

# [tool.setuptools.packages.find]
# where = ["."]  # Search for packages in the root directory
# include = ["tools.*", "ui.*", "agents.*"]  # Include "genai" and its subpackages (e.g., genai.tools)

[tool.setuptools]
packages = ["genai_ram", "genai_ram.agents", "genai_ram.tools", "genai_ram.tools.chunkers" , "genai_ram.ui", "genai_ram.cobol_parser", "genai_ram.cobol_parser.preprocessor", "genai_ram.cobol_parser.ir", "genai_ram.cobol_parser.call_tree", "genai_ram.cobol_parser.call_tree.models", "genai_ram.cobol_parser.call_tree.analyzer", "genai_ram.cobol_parser.parser", "genai_ram.cobol_parser.parser.visitors"]

[tool.setuptools.package-dir] # List of packages to include in the distribution
"genai_ram" = "genai_ram"
"genai_ram.agents" = "agents"
"genai_ram.tools" = "tools"
"genai_ram.tools.chunkers" = "tools/chunkers"
"genai_ram.cobol_parser" = "tools/standalone/cobol_parser_cli/src"
"genai_ram.cobol_parser.preprocessor" = "tools/standalone/cobol_parser_cli/src/preprocessor"
"genai_ram.cobol_parser.ir" = "tools/standalone/cobol_parser_cli/src/ir"
"genai_ram.cobol_parser.call_tree" = "tools/standalone/cobol_parser_cli/src/call_tree"
"genai_ram.cobol_parser.call_tree.models" = "tools/standalone/cobol_parser_cli/src/call_tree/models"
"genai_ram.cobol_parser.call_tree.analyzer" = "tools/standalone/cobol_parser_cli/src/call_tree/analyzer"
"genai_ram.cobol_parser.parser" = "tools/standalone/cobol_parser_cli/src/parser"
"genai_ram.cobol_parser.parser.visitors" = "tools/standalone/cobol_parser_cli/src/parser/visitors"
"genai_ram.ui" = "ui"

[tool.setuptools.dynamic]
dependencies = {file = ["requirements.txt"]}