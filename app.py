import logging
import os
import time
import uuid

import streamlit as st
from dotenv import load_dotenv

# Import orchestrator for backend processing
from src.platform.agents.orchestrator import OrchestratorAgent
# Import helper functions
from ui.helpers import sync_orchestrator_state, start_conversion_process
# Import UI components
from ui.project_wizard import render_project_wizard
# Import rendering functions for different project types
from ui.renderers import (
    render_documentation_generation_tabs,
    render_microservice_conversion_tabs,
    render_data_discovery_tabs,
    render_dependency_analysis_tabs,
    render_knowledge_base_tabs
)
from ui.sidebar import render_sidebar

# Load environment variables
load_dotenv()

logger = logging.getLogger("tools.archive")

# Initialize session state
def init_session_state():
    """Initialize session state variables if they don't exist"""
    if "session_id" not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())

    if "messages" not in st.session_state:
        st.session_state.messages = []

    if "setup_complete" not in st.session_state:
        st.session_state.setup_complete = False
        
    if "wizard_step" not in st.session_state:
        st.session_state.wizard_step = 1

    if "project_type" not in st.session_state:
        st.session_state.project_type = None

    if "project_config" not in st.session_state:
        st.session_state.project_config = {}

    if "conversion_state" not in st.session_state:
        st.session_state.conversion_state = {
            "status": "idle",
            "current_agent": None,
            "progress": 0,
            "uploaded_files": [],
            "working_directory": None,
            "knowledge_base": {},
            "documentation": {},
            "generated_code": {},
            "errors": [],
            "logs": [],
            "agent_progress": {},
            "auto_refresh": True
        }

    # Initialize orchestrator if not already in session state
    if "orchestrator" not in st.session_state:
        st.session_state.orchestrator = OrchestratorAgent()

    # Check for existing state
    if "state_id" in st.session_state and st.session_state["state_id"] is not None:
        current_state = st.session_state.orchestrator.load_state_by_id(st.session_state.state_id)
    else:
        current_state = st.session_state.orchestrator.load_current_state()

    if current_state is not None and "idle" not in current_state["status"]:
        st.session_state.setup_complete = True
        st.session_state.conversion_state["status"] = current_state["status"]
        
        # Restore these critical fields from the persisted state
        if "working_directory" in current_state:
            st.session_state.conversion_state["working_directory"] = current_state["working_directory"]
        
        # Restore uploaded files from database
        # Check if uploaded_files is directly in the state
        if "uploaded_files" in current_state:
            st.session_state.conversion_state["uploaded_files"] = current_state["uploaded_files"]
        # Also check if it's stored in file_stats (our workaround)
        elif "file_stats" in current_state and isinstance(current_state["file_stats"], dict) and "uploaded_files" in current_state["file_stats"]:
            st.session_state.conversion_state["uploaded_files"] = current_state["file_stats"]["uploaded_files"]
        
        # Restore project type and configuration
        st.session_state.project_type = current_state.get("project_config", {}).get("project_type", "UNKNOWN")
        if "project_config" in current_state:
            st.session_state.project_config = current_state.get("project_config", {})

# Add custom CSS for dark theme compatibility
def load_css():
    """Load custom CSS styling"""
    st.markdown("""
    <style>
        /* Card styling */
        div.stButton > button {
            border-radius: 6px;
            font-weight: 500;
        }

        /* Info and container styling */
        div.stAlert {
            border-radius: 6px;
        }

        /* Text styling */
        h1, h2, h3, h4 {
            font-weight: 500;
            color: #4A90E2;
        }

        /* Input field styling */
        div.stTextInput > div > div > input,
        div.stTextArea > div > div > textarea {
            border-radius: 6px;
        }

        /* Expander styling */
        div.streamlit-expanderHeader {
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
        }

        /* Tabs styling */
        div.stTabs button[role="tab"] {
            background-color: transparent;
            color: #ffffff;
        }

        div.stTabs button[aria-selected="true"] {
            background-color: rgba(74, 144, 226, 0.2);
            border-bottom: 2px solid #4A90E2;
        }
        
        /* Fixed buttons at the bottom */
        .bottom-navbar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            z-index: 1000;
            backdrop-filter: blur(5px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
    """, unsafe_allow_html=True)

def main():
    """Main application entry point with controlled auto-refresh"""
    st.set_page_config(layout="wide")

    # Initialize session state
    init_session_state()
    
    # Critical fix: Check if uploaded_files needs to be extracted from file_stats
    if "file_stats" in st.session_state.conversion_state and isinstance(st.session_state.conversion_state["file_stats"], dict):
        logger.debug(f"DEBUG: file_stats keys: {list(st.session_state.conversion_state['file_stats'].keys())}")
        if "uploaded_files" in st.session_state.conversion_state["file_stats"]:
            # Extract uploaded_files from file_stats and store it in the main state
            logger.debug("DEBUG: Extracting uploaded_files from file_stats")
            st.session_state.conversion_state["uploaded_files"] = st.session_state.conversion_state["file_stats"]["uploaded_files"]
            logger.debug(f"DEBUG: Found {len(st.session_state.conversion_state['uploaded_files'])} files")
    
    # Also check knowledge_base.file_stats if needed
    if "knowledge_base" in st.session_state.conversion_state:
        kb = st.session_state.conversion_state["knowledge_base"]
        if isinstance(kb, dict) and "file_stats" in kb and isinstance(kb["file_stats"], dict):
            logger.debug(f"DEBUG: knowledge_base.file_stats keys: {list(kb['file_stats'].keys())}")
            if "uploaded_files" in kb["file_stats"]:
                logger.debug("DEBUG: Extracting uploaded_files from knowledge_base.file_stats")
                st.session_state.conversion_state["uploaded_files"] = kb["file_stats"]["uploaded_files"]
                logger.debug(f"DEBUG: Found {len(st.session_state.conversion_state['uploaded_files'])} files")

    # Load custom CSS
    load_css()

    # Render the sidebar first
    render_sidebar()

    # Check if we're in the transitioning state and need to start processing
    if st.session_state.conversion_state["status"] == "transitioning" and "pending_files_to_process" in st.session_state:
        # Start the actual conversion process
        start_conversion_process(st.session_state.pending_files_to_process, st)
        # Clean up the pending files
        del st.session_state.pending_files_to_process
        # Set status to running
        st.session_state.conversion_state["status"] = "running"

        # This shit is the only thing that worked to remove leftovers of project setup wizard from
        # the processing screen.
        # I have spent 3 hours, and a few hundred dollars for claude trying super hard to understand the issue,
        # but nothing worked.
        # This "solution" is something i have red deep inside internet...
        # And it scrolls to the bottom of the page, but at least no stale elements on the screen.
        # TODO: Fix this.
        for x in range(100):
            st.empty()
        st.markdown(" ")

        # Force rerun to refresh the UI completely
        st.rerun()
    # Check if processing has started (conversion status is running or completed)
    elif st.session_state.conversion_state["status"] in ["running", "completed"]:
        # Add synchronization with orchestrator state when processing is running
        sync_orchestrator_state()
        
        # Ensure working directory and uploaded files exist if we're in running/completed state
        if "working_directory" in st.session_state.conversion_state and st.session_state.conversion_state["working_directory"]:
            working_dir = st.session_state.conversion_state["working_directory"]
            logger.debug(f"DEBUG: Working directory from state: {working_dir}")
            logger.debug(f"DEBUG: Uploaded files from state: {st.session_state.conversion_state.get('uploaded_files', [])}")
            
            if not os.path.exists(working_dir):
                try:
                    # Recreate directory if it doesn't exist
                    os.makedirs(working_dir, exist_ok=True)
                    print(f"Recreated working directory: {working_dir}")
                    
                    # Also create uploads subdirectory
                    uploads_dir = os.path.join(working_dir, "uploads")
                    os.makedirs(uploads_dir, exist_ok=True)
                    print(f"Recreated uploads directory: {uploads_dir}")
                except Exception as e:
                    print(f"Error creating working directory: {str(e)}")
            else:
                # Ensure uploads subdirectory exists
                uploads_dir = os.path.join(working_dir, "uploads")
                if not os.path.exists(uploads_dir):
                    os.makedirs(uploads_dir, exist_ok=True)
                    print(f"Created missing uploads directory: {uploads_dir}")
                    
            # Fix uploaded file paths if necessary
            if "uploaded_files" in st.session_state.conversion_state and st.session_state.conversion_state["uploaded_files"]:
                fixed_paths = []
                for file_path in st.session_state.conversion_state["uploaded_files"]:
                    if isinstance(file_path, str):
                        # If path exists, keep it
                        if os.path.isfile(file_path):
                            fixed_paths.append(file_path)
                        # If path doesn't exist but should be in uploads dir
                        elif "/uploads/" not in file_path:
                            dir_path = os.path.dirname(file_path)
                            uploads_path = os.path.join(dir_path, "uploads", os.path.basename(file_path))
                            
                            # Check if the file exists in this location
                            if os.path.isfile(uploads_path):
                                print(f"Fixed path: {file_path} -> {uploads_path}")
                                fixed_paths.append(uploads_path)
                            else:
                                # Still include the fixed path even if the file doesn't exist yet
                                print(f"Updated path (file missing): {file_path} -> {uploads_path}")
                                fixed_paths.append(uploads_path)
                        else:
                            # Keep the original path even if file doesn't exist
                            fixed_paths.append(file_path)
                    else:
                        # For non-string paths (shouldn't happen), keep them
                        fixed_paths.append(file_path)
                
                # Replace with fixed paths
                st.session_state.conversion_state["uploaded_files"] = fixed_paths
                    
        # If processing has started, show the full application with tabs
        render_main_application()
    else:
        # Otherwise, show the wizard regardless of which step we're on
        render_project_wizard()



def sync_state():
    """
    Synchronizes the application state with the orchestrator state for running or waiting processes.

    This function checks if the current conversion state is active (running or waiting) or if a state ID
    is present, then attempts to synchronize with the orchestrator's state. If synchronization fails,
    an error message is displayed.
    """
    if st.session_state.conversion_state["status"] in ["running", "completed", "waiting"] or "state_id" in st.session_state:
        sync_orchestrator_state()

def render_main_application():
    """Render the main application after project setup is complete"""
    # Display project information in a banner
    st.markdown(f"""
    <div style="
        background-color: rgba(74, 144, 226, 0.1);
        border-left: 5px solid #4A90E2;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    ">
        <div>
            <h3 style="margin: 0; color: #4A90E2;">{st.session_state.project_config.get('project_name', 'Unnamed Project')}</h3>
            <p style="margin: 5px 0 0 0; color: rgba(255, 255, 255, 0.7);">Type: {st.session_state.project_type}</p>
        </div>
        <div>
            <p style="margin: 0; text-align: right; color: rgba(255, 255, 255, 0.7);">Created: {st.session_state.project_config.get('created_date', 'Unknown')}</p>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Create tabs appropriate for the project type
    tabs = create_tabs_for_project_type(st.session_state.project_type)

    # Populate tab content based on project type
    if st.session_state.project_type == "Documentation Generation":
        render_documentation_generation_tabs(tabs)
    elif st.session_state.project_type == "Full Code Conversion":
        render_microservice_conversion_tabs(tabs)
    elif st.session_state.project_type == "Data Discovery Project":
        render_data_discovery_tabs(tabs)
    elif st.session_state.project_type == "Dependency Analysis":
        render_dependency_analysis_tabs(tabs)
    elif st.session_state.project_type == "Knowledge Base Builder":
        render_knowledge_base_tabs(tabs)

    # Reset project button (fixed at bottom of page)
    st.markdown("""
    <div style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
    """, unsafe_allow_html=True)
    if st.button("Reset Project", key="reset_project_button"):
        st.session_state.setup_complete = False
        st.session_state.project_type = None
        st.session_state.project_config = {}
        st.session_state.wizard_step = 1
        # Reset the conversion state
        st.session_state.conversion_state = {
            "status": "idle",
            "current_agent": None,
            "progress": 0,
            "uploaded_files": [],
            "working_directory": None,
            "knowledge_base": {},
            "documentation": {},
            "generated_code": {},
            "errors": [],
            "logs": [],
            "agent_progress": {},
            "auto_refresh": True
        }
        st.session_state.orchestrator.reset()
        st.rerun()
    st.markdown("</div>", unsafe_allow_html=True)
    st.markdown(" ")

    # Add auto-refresh for running state only
    if st.session_state.conversion_state["auto_refresh"] and st.session_state.conversion_state["status"] == "running":
        # Auto refresh every 5 seconds when processing is running
        time.sleep(5)  # Shortened for better user experience
        st.rerun()


def create_tabs_for_project_type(project_type):
    """Create appropriate tabs based on project type"""
    if project_type == "Documentation Generation":
        return st.tabs(["⚙️ Process", "📁 Uploaded Files", "📊 Analysis", "📝 Documentation"])
    elif project_type == "Full Code Conversion":
        return st.tabs(["⚙️ Process", "📁 Uploaded Files", "📊 Analysis", "📝 Documentation", "💻 Generated Code"])
    elif project_type == "Data Discovery Project":
        return st.tabs(["⚙️ Process", "📁 Uploaded Files", "🔍 Data Fields", "📊 Data Structures", "📝 Documentation"])
    elif project_type == "Dependency Analysis":
        return st.tabs(["⚙️ Process", "📁 Uploaded Files", "📊 Call Graph", "🔍 Data Flow", "📝 Documentation"])
    elif project_type == "Knowledge Base Builder":
        return st.tabs(["⚙️ Process", "📁 Uploaded Files", "🧠 Knowledge Base", "💬 Chat", "📝 Documentation"])
    else:
        return st.tabs(["⚙️ Process", "📁 Uploaded Files", "📊 Analysis"])

if __name__ == "__main__":
    main()