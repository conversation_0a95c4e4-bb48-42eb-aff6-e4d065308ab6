import sys
import logging
import json
import helpers.test_states as states
from src.platform.agents.base_agent import AgentInput
from src.platform.agents.knowledge_miner import KnowledgeMinerAgent


def main():

    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("documentation_agent_test")

    states.current_state["knowledge_base"].update(states.knowledge_base_updates_from_code_preprocessor)
    states.current_state["knowledge_base"].update(states.knowledge_base_updates_from_cobol_overview)

    agent_input = AgentInput(
        working_directory=states.paths.working_dir_path,
        knowledge_base=states.current_state["knowledge_base"]
    )

    knowledge_miner = KnowledgeMinerAgent()
    result = knowledge_miner.process(agent_input)
    logger.info(f"Knowledge miner successfully finished.")
    logger.info(json.dumps(result.knowledge_base_updates, indent=2))
    return 0 if result else 1


if __name__ == "__main__":
    sys.exit(main())
