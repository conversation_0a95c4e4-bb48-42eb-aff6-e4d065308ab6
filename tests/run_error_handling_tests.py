#!/usr/bin/env python3
"""
Test runner for error handling scenarios.
This script runs all the error handling tests to ensure the fixes work correctly.
"""
import sys
import os
import subprocess
import logging
from pathlib import Path

# Add project root to Python path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_command(cmd, description):
    """Run a command and return success status."""
    logger.info(f"Running {description}...")
    try:
        result = subprocess.run(
            cmd,
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
            check=False
        )
        
        if result.returncode == 0:
            logger.info(f"✅ {description} passed")
            if result.stdout:
                logger.debug(f"Output: {result.stdout}")
            return True
        else:
            logger.error(f"❌ {description} failed")
            if result.stdout:
                logger.error(f"Output: {result.stdout}")
            if result.stderr:
                logger.error(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ {description} failed with exception: {str(e)}")
        return False


def run_error_handling_tests():
    """Run all error handling tests."""
    logger.info("🧪 Running Error Handling Tests")
    logger.info("=" * 50)
    
    test_files = [
        "tests/agents/test_code_reviewer_error_handling.py",
        "tests/plugins/test_cobol_preprocessor_error_handling.py", 
        "tests/plugins/test_cobol_chunker_error_handling.py",
        "tests/integration/test_workflow_error_handling.py"
    ]
    
    results = []
    
    for test_file in test_files:
        if os.path.exists(test_file):
            cmd = [sys.executable, "-m", "pytest", test_file, "-v", "--tb=short"]
            success = run_command(cmd, f"Error handling tests in {test_file}")
            results.append((test_file, success))
        else:
            logger.warning(f"⚠️  Test file not found: {test_file}")
            results.append((test_file, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 Test Results Summary")
    logger.info("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_file, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{status}: {test_file}")
        if success:
            passed += 1
        else:
            failed += 1
    
    logger.info(f"\nTotal: {len(results)} test files")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    
    if failed == 0:
        logger.info("🎉 All error handling tests passed!")
        return True
    else:
        logger.error(f"💥 {failed} test file(s) failed")
        return False


def run_specific_error_scenarios():
    """Run tests for specific error scenarios mentioned in the log."""
    logger.info("\n🎯 Running Specific Error Scenario Tests")
    logger.info("=" * 50)
    
    scenarios = [
        {
            "name": "IR File Generation Failure",
            "test": "tests/plugins/test_cobol_preprocessor_error_handling.py::TestCobolPreprocessorErrorHandling::test_ir_generation_failure_with_fallback"
        },
        {
            "name": "Missing IR File Chunking",
            "test": "tests/plugins/test_cobol_chunker_error_handling.py::TestCobolChunkerErrorHandling::test_missing_ir_file_fallback"
        },
        {
            "name": "Code Reviewer Missing Knowledge Base",
            "test": "tests/agents/test_code_reviewer_error_handling.py::TestCodeReviewerErrorHandling::test_missing_knowledge_base"
        },
        {
            "name": "Workflow Cascading Failure Prevention",
            "test": "tests/integration/test_workflow_error_handling.py::TestWorkflowErrorHandling::test_cascading_failure_prevention"
        }
    ]
    
    results = []
    
    for scenario in scenarios:
        cmd = [sys.executable, "-m", "pytest", scenario["test"], "-v", "--tb=short"]
        success = run_command(cmd, scenario["name"])
        results.append((scenario["name"], success))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("🎯 Specific Scenario Results")
    logger.info("=" * 50)
    
    for name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{status}: {name}")
    
    return all(success for _, success in results)


def run_coverage_analysis():
    """Run tests with coverage analysis."""
    logger.info("\n📈 Running Coverage Analysis")
    logger.info("=" * 50)
    
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/agents/test_code_reviewer_error_handling.py",
        "tests/plugins/test_cobol_preprocessor_error_handling.py",
        "tests/plugins/test_cobol_chunker_error_handling.py",
        "--cov=src.platform.agents.code_reviewer",
        "--cov=src.plugins.legacy.cobol.tools.preprocessor",
        "--cov=src.plugins.legacy.cobol.tools.chunkers.cobol_chunker",
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov/error_handling",
        "-v"
    ]
    
    success = run_command(cmd, "Coverage analysis for error handling")
    
    if success:
        logger.info("📊 Coverage report generated in htmlcov/error_handling/")
    
    return success


def main():
    """Main test runner function."""
    logger.info("🚀 Starting Error Handling Test Suite")
    logger.info("This test suite validates the fixes for the errors found in the log")
    
    all_passed = True
    
    # Run all error handling tests
    if not run_error_handling_tests():
        all_passed = False
    
    # Run specific error scenarios
    if not run_specific_error_scenarios():
        all_passed = False
    
    # Run coverage analysis
    if not run_coverage_analysis():
        logger.warning("⚠️  Coverage analysis failed, but continuing...")
    
    # Final result
    logger.info("\n" + "=" * 50)
    if all_passed:
        logger.info("🎉 ALL TESTS PASSED! Error handling fixes are working correctly.")
        logger.info("The issues from the original log should now be resolved.")
        return 0
    else:
        logger.error("💥 SOME TESTS FAILED! Please review the failures above.")
        return 1


if __name__ == "__main__":
    exit(main())
