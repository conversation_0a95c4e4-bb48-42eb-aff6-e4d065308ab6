"""
Unit tests for Plugin<PERSON>oader.
"""
import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock
from src.platform.plugins.plugin_loader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get_plugin_loader
from config.plugin_registry import PluginConfig, PluginType


class TestPluginLoader:
    """Test cases for PluginLoader."""
    
    @pytest.fixture
    def plugin_loader(self):
        """Create PluginLoader instance."""
        return PluginLoader()
    
    @pytest.fixture
    def mock_language_plugin(self):
        """Create mock language plugin."""
        plugin = Mock()
        plugin.get_name.return_value = "test_language"
        plugin.get_language_name.return_value = "test_language"
        plugin.get_supported_extensions.return_value = [".test"]
        plugin.can_process_file.return_value = True
        plugin.initialize.return_value = True
        
        # Mock detector
        detector = Mock()
        detector.get_confidence_score.return_value = 0.8
        plugin.get_detector.return_value = detector
        
        return plugin
    
    @pytest.fixture
    def mock_target_plugin(self):
        """Create mock target plugin."""
        plugin = Mock()
        plugin.get_name.return_value = "test_target"
        plugin.get_target_name.return_value = "test_target"
        plugin.get_supported_languages.return_value = ["test_language"]
        plugin.can_handle_language.return_value = True
        plugin.initialize.return_value = True
        return plugin
    
    @pytest.fixture
    def mock_plugin_config(self):
        """Create mock plugin configuration."""
        return PluginConfig(
            name="test_plugin",
            display_name="Test Plugin",
            description="Test plugin for unit tests",
            plugin_type=PluginType.LANGUAGE,
            module_path="test.module",
            class_name="TestPlugin",
            version="1.0.0",
            author="Test Author",
            dependencies=[],
            supported_versions=["1.0"],
            enabled=True
        )
    
    def test_initialization(self, plugin_loader):
        """Test PluginLoader initialization."""
        assert plugin_loader._language_plugins == {}
        assert plugin_loader._target_plugins == {}
        assert plugin_loader._loaded_plugins == {}
    
    @patch('src.platform.plugins.plugin_loader.PLUGIN_REGISTRY')
    def test_load_all_plugins_success(self, mock_registry, plugin_loader, mock_plugin_config, mock_language_plugin):
        """Test loading all plugins successfully."""
        mock_registry.items.return_value = [("test_plugin", mock_plugin_config)]
        
        with patch('importlib.import_module') as mock_import:
            mock_module = Mock()
            mock_module.TestPlugin = Mock(return_value=mock_language_plugin)
            mock_import.return_value = mock_module
            
            plugin_loader.load_all_plugins()
            
            assert "test_plugin" in plugin_loader._loaded_plugins
            assert "test_plugin" in plugin_loader._language_plugins
    
    @patch('src.platform.plugins.plugin_loader.PLUGIN_REGISTRY')
    def test_load_all_plugins_disabled(self, mock_registry, plugin_loader, mock_plugin_config):
        """Test skipping disabled plugins."""
        mock_plugin_config.enabled = False
        mock_registry.items.return_value = [("test_plugin", mock_plugin_config)]
        
        plugin_loader.load_all_plugins()
        
        assert "test_plugin" not in plugin_loader._loaded_plugins
    
    def test_load_plugin_success(self, plugin_loader, mock_plugin_config, mock_language_plugin):
        """Test loading a single plugin successfully."""
        with patch('importlib.import_module') as mock_import:
            mock_module = Mock()
            mock_module.TestPlugin = Mock(return_value=mock_language_plugin)
            mock_import.return_value = mock_module
            
            result = plugin_loader.load_plugin("test_plugin", mock_plugin_config)
            
            assert result == mock_language_plugin
            assert "test_plugin" in plugin_loader._loaded_plugins
            assert "test_plugin" in plugin_loader._language_plugins
    
    def test_load_plugin_import_error(self, plugin_loader, mock_plugin_config):
        """Test handling import errors during plugin loading."""
        with patch('importlib.import_module', side_effect=ImportError("Module not found")):
            with pytest.raises(ImportError):
                plugin_loader.load_plugin("test_plugin", mock_plugin_config)
    
    def test_get_language_plugins(self, plugin_loader, mock_language_plugin):
        """Test getting language plugins."""
        plugin_loader._language_plugins["test_language"] = mock_language_plugin
        
        plugins = plugin_loader.get_language_plugins()
        
        assert "test_language" in plugins
        assert plugins["test_language"] == mock_language_plugin
    
    def test_get_target_plugins(self, plugin_loader, mock_target_plugin):
        """Test getting target plugins."""
        plugin_loader._target_plugins["test_target"] = mock_target_plugin
        
        plugins = plugin_loader.get_target_plugins()
        
        assert "test_target" in plugins
        assert plugins["test_target"] == mock_target_plugin
    
    def test_get_available_languages(self, plugin_loader, mock_language_plugin):
        """Test getting available languages."""
        plugin_loader._language_plugins["test_language"] = mock_language_plugin
        
        languages = plugin_loader.get_available_languages()
        
        assert "test_language" in languages
    
    def test_get_available_targets(self, plugin_loader, mock_target_plugin):
        """Test getting available targets."""
        plugin_loader._target_plugins["test_target"] = mock_target_plugin
        
        targets = plugin_loader.get_available_targets()
        
        assert "test_target" in targets
    
    def test_detect_language_success(self, plugin_loader, mock_language_plugin):
        """Test successful language detection."""
        plugin_loader._language_plugins["test_language"] = mock_language_plugin
        
        result = plugin_loader.detect_language("test content", "test.file")
        
        assert result == "test_language"
        mock_language_plugin.get_detector.assert_called_once()
    
    def test_detect_language_low_confidence(self, plugin_loader, mock_language_plugin):
        """Test language detection with low confidence."""
        detector = Mock()
        detector.get_confidence_score.return_value = 0.3  # Below threshold
        mock_language_plugin.get_detector.return_value = detector
        
        plugin_loader._language_plugins["test_language"] = mock_language_plugin
        
        result = plugin_loader.detect_language("test content", "test.file")
        
        assert result is None
    
    def test_detect_language_no_detector(self, plugin_loader, mock_language_plugin):
        """Test language detection when plugin has no detector."""
        mock_language_plugin.get_detector.return_value = None
        plugin_loader._language_plugins["test_language"] = mock_language_plugin
        
        result = plugin_loader.detect_language("test content", "test.file")
        
        assert result is None
    
    def test_can_process_file(self, plugin_loader, mock_language_plugin):
        """Test checking if plugins can process a file."""
        plugin_loader._language_plugins["test_language"] = mock_language_plugin
        
        result = plugin_loader.can_process_file("test.file")
        
        assert result == ["test_language"]
        mock_language_plugin.can_process_file.assert_called_once_with("test.file")
    
    def test_can_process_file_exception(self, plugin_loader, mock_language_plugin):
        """Test handling exceptions during file processing check."""
        mock_language_plugin.can_process_file.side_effect = Exception("Test error")
        plugin_loader._language_plugins["test_language"] = mock_language_plugin
        
        result = plugin_loader.can_process_file("test.file")
        
        assert result == []
    
    def test_get_plugin_loader_singleton(self):
        """Test that get_plugin_loader returns singleton instance."""
        loader1 = get_plugin_loader()
        loader2 = get_plugin_loader()
        
        assert loader1 is loader2
        assert isinstance(loader1, PluginLoader)
