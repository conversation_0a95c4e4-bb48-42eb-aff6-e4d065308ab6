"""
Unit tests for Java Spring Plugin refactored implementation.
Tests the new architecture with delegation to specialized classes.
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock

from src.plugins.targets.java_spring.plugin import JavaSpringCodeGenerator
from src.plugins.targets.java_spring.tools.project_manager.core import JavaProjectManager


class TestJavaSpringPluginRefactoredImplementation:
    """Test Java Spring plugin refactored implementation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.code_generator = JavaSpringCodeGenerator()
        self.project_manager = JavaProjectManager()

        # Mock dependencies for the refactored implementation
        self.code_generator.knowledge_db = Mock()
        self.code_generator.tools = Mock()
        self.code_generator.dependency_analyzer = Mock()
        self.code_generator.state_manager = Mock()
        self.code_generator.performance_tracker = Mock()
        self.code_generator.react_coordinator = Mock()

        # Mock performance tracker methods
        self.code_generator.performance_tracker.start_processing = Mock(return_value=0.0)
        self.code_generator.performance_tracker.end_processing = Mock(return_value=1.0)
        self.code_generator.performance_tracker.log_delegation_performance = Mock()
        self.code_generator.performance_tracker.log_performance_metrics = Mock()
        self.code_generator.performance_tracker.start_chunk_processing = Mock(return_value=0.0)
        self.code_generator.performance_tracker.end_chunk_processing_success = Mock(return_value=0.1)
        self.code_generator.performance_tracker.end_chunk_processing_failure = Mock(return_value=0.1)
        self.code_generator.performance_tracker.get_chunk_performance_metrics = Mock(return_value={})
        self.code_generator.performance_tracker.get_failure_performance_metrics = Mock(return_value={})

    def test_code_generator_generates_files_from_business_logic(self):
        """Test that code generator creates files using the React agent workflow."""
        # Arrange - Mock the React workflow to return success
        self.code_generator.state_manager.load_or_initialize_generation_state = Mock(return_value={
            "completed": True,
            "current_phase": "finalization"
        })
        self.code_generator.state_manager.is_generation_complete = Mock(return_value=True)
        self.code_generator.react_coordinator.should_continue_generation = Mock(return_value=False)
        self.code_generator.react_coordinator.create_success_result = Mock(return_value={
            "success": True,
            "message": "Successfully generated Java Spring Boot project",
            "artifacts": {
                "java_project_path": os.path.join(self.temp_dir, "java_project"),
                "generated_files": ["Application.java", "Service.java"]
            }
        })

        source_analysis = {
            "knowledge_base": {
                "analyzed_chunks": [
                    {"program_id": "CBACT01C", "chunk_name": "ENTRY-PARAGRAPH"},
                    {"program_id": "CBACT01C", "chunk_name": "ACCTFILE-OPEN"}
                ]
            }
        }

        config = {
            "working_directory": self.temp_dir,
            "target_technology": "java_spring"
        }

        # Act
        result = self.code_generator.generate_code(source_analysis, config)

        # Assert
        assert result["success"] is True
        assert "artifacts" in result
        assert "java_project_path" in result["artifacts"]

    def test_code_generator_handles_empty_knowledge_base(self):
        """Test that code generator handles empty knowledge base gracefully."""
        # Arrange - Mock the React workflow to handle empty knowledge base
        self.code_generator.state_manager.load_or_initialize_generation_state = Mock(return_value={
            "completed": True,
            "current_phase": "finalization"
        })
        self.code_generator.state_manager.is_generation_complete = Mock(return_value=True)
        self.code_generator.react_coordinator.should_continue_generation = Mock(return_value=False)
        self.code_generator.react_coordinator.create_success_result = Mock(return_value={
            "success": True,
            "message": "Successfully generated Java Spring Boot project",
            "artifacts": {
                "java_project_path": os.path.join(self.temp_dir, "java_project")
            }
        })

        source_analysis = {"knowledge_base": {}}
        config = {"working_directory": self.temp_dir}

        # Act
        result = self.code_generator.generate_code(source_analysis, config)

        # Assert
        assert result["success"] is True
        assert "artifacts" in result

    def test_java_code_generator_delegation(self):
        """Test that the plugin properly delegates to JavaCodeGenerator."""
        # Arrange
        chunk_info = {"program_id": "CBACT01C", "chunk_name": "ENTRY-PARAGRAPH"}
        generation_state = {}

        # Mock the JavaCodeGenerator
        self.code_generator.java_code_generator.generate_chunk_code = Mock(return_value="public class TestService {}")
        self.code_generator.knowledge_db.get_chunk_documentation = Mock(return_value={
            "program_id": "CBACT01C",
            "chunk_name": "ENTRY-PARAGRAPH",
            "code": "TEST CODE"
        })
        self.code_generator.dependency_analyzer.get_chunk_dependencies = Mock(return_value={})
        self.code_generator.java_project_manager.save_java_code_to_file = Mock(return_value={"file_path": "/test/path"})
        self.code_generator.java_code_generator.validate_java_code = Mock(return_value={"valid": True})
        self.code_generator.tools.get_language_mappings = Mock(return_value={})

        from src.platform.agents.base_agent import AgentInput
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})

        # Act
        result = self.code_generator._generate_chunk_code(chunk_info, generation_state, input_data)

        # Assert
        assert result["success"] is True
        self.code_generator.java_code_generator.generate_chunk_code.assert_called_once()

    def test_code_generator_creates_project_structure(self):
        """Test that code generator creates project structure through delegation."""
        # Arrange - Mock the React workflow and project manager
        self.code_generator.state_manager.load_or_initialize_generation_state = Mock(return_value={
            "completed": True,
            "current_phase": "finalization"
        })
        self.code_generator.state_manager.is_generation_complete = Mock(return_value=True)
        self.code_generator.react_coordinator.should_continue_generation = Mock(return_value=False)
        self.code_generator.react_coordinator.create_success_result = Mock(return_value={
            "success": True,
            "message": "Successfully generated Java Spring Boot project",
            "artifacts": {
                "java_project_path": os.path.join(self.temp_dir, "java_project")
            }
        })

        # Mock the project manager
        self.code_generator.java_project_manager.ensure_project_structure = Mock()

        source_analysis = {
            "knowledge_base": {
                "analyzed_chunks": [
                    {"program_id": "TEST", "chunk_name": "CHUNK"}
                ]
            }
        }

        config = {"working_directory": self.temp_dir}

        # Act
        result = self.code_generator.generate_code(source_analysis, config)

        # Assert
        self.code_generator.java_project_manager.ensure_project_structure.assert_called_once()
        assert result["success"] is True

    def test_project_manager_creates_basic_structure(self):
        """Test that project manager creates basic Maven structure."""
        # Arrange
        project_dir = os.path.join(self.temp_dir, "test-project")

        # Act
        self.project_manager.ensure_project_structure(project_dir)

        # Also create the POM.xml file
        config = {"group_id": "com.test", "java_version": "17"}
        self.project_manager.generate_pom_xml(project_dir, config)

        # Assert - Check that basic directory structure exists
        assert os.path.exists(project_dir)
        assert os.path.exists(os.path.join(project_dir, "src", "main", "java"))
        assert os.path.exists(os.path.join(project_dir, "src", "main", "resources"))
        assert os.path.exists(os.path.join(project_dir, "src", "test", "java"))

        # Check pom.xml exists
        pom_path = os.path.join(project_dir, "pom.xml")
        assert os.path.exists(pom_path)

    def test_project_manager_generate_pom_xml(self):
        """Test POM.xml generation with custom configuration."""
        # Arrange
        project_dir = os.path.join(self.temp_dir, "custom-project")
        os.makedirs(project_dir, exist_ok=True)

        config = {
            "group_id": "com.custom",
            "java_version": "11",
            "spring_boot_version": "2.7.0"
        }

        # Act
        pom_file_path = self.project_manager.generate_pom_xml(project_dir, config)

        # Read the generated POM content
        with open(pom_file_path, 'r') as f:
            pom_content = f.read()

        # Assert
        assert "<groupId>com.custom</groupId>" in pom_content
        assert "<artifactId>cobol-conversion</artifactId>" in pom_content  # Template uses hardcoded artifact ID
        assert "<maven.compiler.source>11</maven.compiler.source>" in pom_content
        assert "<spring-boot.version>2.7.0</spring-boot.version>" in pom_content

    def test_project_manager_handles_creation_failure(self):
        """Test that project manager handles creation failures gracefully."""
        # Arrange - use invalid path to cause failure
        project_dir = "/invalid/path/that/does/not/exist/test-project"

        # Act & Assert - Should raise an exception or handle gracefully
        try:
            self.project_manager.ensure_project_structure(project_dir)
            # If no exception, that's also acceptable behavior
            assert True
        except Exception:
            # Exception is expected for invalid paths
            assert True

    def test_code_generator_handles_exception_gracefully(self):
        """Test that code generator handles exceptions in the React workflow."""
        # Arrange - Mock the React workflow to simulate an error
        self.code_generator.state_manager.load_or_initialize_generation_state = Mock(side_effect=Exception("Test error"))
        self.code_generator.react_coordinator.create_error_result = Mock(return_value={
            "success": False,
            "error": "Test error",
            "total_time": 1.0
        })

        source_analysis = {
            "knowledge_base": {
                "analyzed_chunks": [
                    {"program_id": "TEST", "chunk_name": "CHUNK"}
                ]
            }
        }

        config = {"working_directory": self.temp_dir}

        # Act
        result = self.code_generator.generate_code(source_analysis, config)

        # Assert - should handle error gracefully
        assert result["success"] is False
        assert "error" in result

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
