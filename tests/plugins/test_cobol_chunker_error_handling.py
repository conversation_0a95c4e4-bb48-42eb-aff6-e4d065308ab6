"""
Unit tests for COBOL chunker error handling scenarios.
"""
import pytest
import os
import tempfile
import json
from unittest.mock import Mock, patch

from src.plugins.legacy.cobol.tools.chunkers.cobol_chunker import CobolChunker


class TestCobolChunkerErrorHandling:
    """Test cases for COBOL chunker error handling."""

    @pytest.fixture
    def chunker(self):
        """Create CobolChunker instance for testing."""
        return CobolChunker()

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    def sample_cobol_file(self, temp_dir):
        """Create a sample COBOL file for testing."""
        cobol_content = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. TEST-PROGRAM.

       ENVIRONMENT DIVISION.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 WS-COUNTER PIC 9(3) VALUE 0.

       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY 'Hello World'.
           STOP RUN.
        """

        cobol_file = os.path.join(temp_dir, "TEST-PROGRAM.cbl")
        with open(cobol_file, 'w', encoding='utf-8') as f:
            f.write(cobol_content)

        return cobol_file

    @pytest.fixture
    def sample_ir_file(self, temp_dir):
        """Create a sample IR JSON file for testing."""
        ir_data = {
            "metadata": {
                "module_id": "TEST-PROGRAM",
                "file_name": "TEST-PROGRAM.cbl"
            },
            "nodes": [
                {
                    "uuid": "test_module",
                    "type": "CobolModule",
                    "module_id": "TEST-PROGRAM",
                    "file_name": "TEST-PROGRAM.cbl",
                    "full_text": "full program text"
                },
                {
                    "uuid": "test_id_div",
                    "type": "CobolIdentificationDivision",
                    "module_id": "TEST-PROGRAM",
                    "full_text": "IDENTIFICATION DIVISION.\nPROGRAM-ID. TEST-PROGRAM."
                },
                {
                    "uuid": "test_proc_div",
                    "type": "CobolProcedureDivision",
                    "module_id": "TEST-PROGRAM",
                    "full_text": "PROCEDURE DIVISION.\nMAIN-PARA.\n    DISPLAY 'Hello World'.\n    STOP RUN."
                }
            ],
            "relationships": []
        }

        json_dir = os.path.join(temp_dir, "json")
        os.makedirs(json_dir, exist_ok=True)
        ir_file = os.path.join(json_dir, "TEST-PROGRAM.json")

        with open(ir_file, 'w', encoding='utf-8') as f:
            json.dump(ir_data, f, indent=2)

        return ir_file

    def test_missing_ir_file_fallback(self, chunker, sample_cobol_file, temp_dir):
        """Test that chunker creates fallback chunks when IR file is missing."""
        output_dir = os.path.join(temp_dir, "chunks")

        # Call chunker without creating IR file
        result = chunker.chunk_file(sample_cobol_file, output_dir)

        # Should return fallback chunks
        assert len(result) > 0
        assert all(chunk.get('metadata', {}).get('fallback') is True for chunk in result)

        # Check that chunk files were created
        assert os.path.exists(output_dir)
        chunk_files = os.listdir(output_dir)
        assert len(chunk_files) > 0

    def test_fallback_chunks_division_detection(self, chunker, temp_dir):
        """Test that fallback chunking correctly detects COBOL divisions."""
        cobol_content = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. TEST-PROG.

       ENVIRONMENT DIVISION.
       CONFIGURATION SECTION.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 WS-VAR PIC X(10).

       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY 'Test'.
           STOP RUN.
        """

        cobol_file = os.path.join(temp_dir, "TEST-PROG.cbl")
        with open(cobol_file, 'w', encoding='utf-8') as f:
            f.write(cobol_content)

        output_dir = os.path.join(temp_dir, "chunks")

        result = chunker.chunk_file(cobol_file, output_dir)

        # Should detect all four divisions
        chunk_types = [chunk['chunk_type'] for chunk in result]
        expected_types = [
            'IDENTIFICATION_DIVISION',
            'ENVIRONMENT_DIVISION',
            'DATA_DIVISION',
            'PROCEDURE_DIVISION'
        ]

        for expected_type in expected_types:
            assert expected_type in chunk_types

    def test_corrupted_ir_file_fallback(self, chunker, sample_cobol_file, temp_dir):
        """Test handling of corrupted IR files."""
        # Create corrupted IR file
        json_dir = os.path.join(os.path.dirname(sample_cobol_file), "json")
        os.makedirs(json_dir, exist_ok=True)
        ir_file = os.path.join(json_dir, "TEST-PROGRAM.json")

        with open(ir_file, 'w') as f:
            f.write("{ invalid json content")

        output_dir = os.path.join(temp_dir, "chunks")

        result = chunker.chunk_file(sample_cobol_file, output_dir)

        # Should fall back to source-based chunking
        assert len(result) > 0

    def test_empty_ir_file(self, chunker, sample_cobol_file, temp_dir):
        """Test handling of empty IR files."""
        # Create empty IR file
        json_dir = os.path.join(os.path.dirname(sample_cobol_file), "json")
        os.makedirs(json_dir, exist_ok=True)
        ir_file = os.path.join(json_dir, "TEST-PROGRAM.json")

        with open(ir_file, 'w') as f:
            json.dump({}, f)

        output_dir = os.path.join(temp_dir, "chunks")

        result = chunker.chunk_file(sample_cobol_file, output_dir)

        # Should handle empty IR gracefully
        assert isinstance(result, list)

    def test_ir_file_without_nodes(self, chunker, sample_cobol_file, temp_dir):
        """Test handling of IR files without nodes."""
        # Create IR file without nodes
        json_dir = os.path.join(os.path.dirname(sample_cobol_file), "json")
        os.makedirs(json_dir, exist_ok=True)
        ir_file = os.path.join(json_dir, "TEST-PROGRAM.json")

        ir_data = {
            "metadata": {"module_id": "TEST-PROGRAM"},
            "relationships": []
        }

        with open(ir_file, 'w') as f:
            json.dump(ir_data, f)

        output_dir = os.path.join(temp_dir, "chunks")

        result = chunker.chunk_file(sample_cobol_file, output_dir)

        # Should handle missing nodes gracefully
        assert isinstance(result, list)

    def test_file_read_permission_error(self, chunker, temp_dir):
        """Test handling of file read permission errors."""
        cobol_file = os.path.join(temp_dir, "test.cbl")
        output_dir = os.path.join(temp_dir, "chunks")

        # Mock file operations to raise permission error
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            result = chunker.chunk_file(cobol_file, output_dir)

            assert result == []

    def test_output_directory_creation_failure(self, chunker, sample_cobol_file, temp_dir):
        """Test handling of output directory creation failures."""
        output_dir = os.path.join(temp_dir, "chunks")

        # Mock os.makedirs to raise permission error
        with patch('os.makedirs', side_effect=PermissionError("Cannot create directory")):
            result = chunker.chunk_file(sample_cobol_file, output_dir)

            assert result == []

    def test_chunk_file_write_failure(self, chunker, sample_cobol_file, temp_dir, sample_ir_file):
        """Test handling of chunk file write failures."""
        output_dir = os.path.join(temp_dir, "chunks")

        # Mock file write operations to fail
        original_open = open
        def mock_open(*args, **kwargs):
            if 'w' in args or kwargs.get('mode', '') == 'w':
                raise PermissionError("Cannot write file")
            return original_open(*args, **kwargs)

        with patch('builtins.open', side_effect=mock_open):
            result = chunker.chunk_file(sample_cobol_file, output_dir)

            # Should still return chunk info even if file writing fails
            assert isinstance(result, list)

    def test_malformed_cobol_content_fallback(self, chunker, temp_dir):
        """Test fallback chunking with malformed COBOL content."""
        malformed_content = """
        This is not valid COBOL
        No proper divisions
        Just random text
        """

        cobol_file = os.path.join(temp_dir, "malformed.cbl")
        with open(cobol_file, 'w') as f:
            f.write(malformed_content)

        output_dir = os.path.join(temp_dir, "chunks")

        result = chunker.chunk_file(cobol_file, output_dir)

        # Should handle malformed content gracefully
        assert isinstance(result, list)
        # May have no chunks or minimal chunks depending on content

    def test_unicode_encoding_issues(self, chunker, temp_dir):
        """Test handling of unicode encoding issues."""
        # Create file with problematic encoding
        cobol_file = os.path.join(temp_dir, "unicode_test.cbl")

        # Write content with mixed encodings
        with open(cobol_file, 'wb') as f:
            f.write(b'\xff\xfe')  # BOM
            f.write("IDENTIFICATION DIVISION.\n".encode('utf-16le'))
            f.write(b'\x80\x81\x82')  # Invalid UTF-8 bytes

        output_dir = os.path.join(temp_dir, "chunks")

        result = chunker.chunk_file(cobol_file, output_dir)

        # Should handle encoding issues gracefully
        assert isinstance(result, list)

    @pytest.mark.unit
    def test_error_recovery_and_logging(self, chunker, sample_cobol_file, temp_dir):
        """Test error recovery mechanisms and proper logging."""
        output_dir = os.path.join(temp_dir, "chunks")

        with patch('src.plugins.legacy.cobol.tools.chunkers.cobol_chunker.logger') as mock_logger:
            # Test with missing IR file to trigger fallback
            result = chunker.chunk_file(sample_cobol_file, output_dir)

            # Should have logged the IR file not found error
            assert mock_logger.error.called

            # Should have logged fallback chunk creation
            assert mock_logger.info.called

            # Should still return results
            assert isinstance(result, list)
            assert len(result) > 0
