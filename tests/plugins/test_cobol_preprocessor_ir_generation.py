"""
Comprehensive tests for COBOL preprocessor IR file generation.
Tests the complete flow from preprocessing to IR file creation.
"""
import pytest
import os
import json
import tempfile
from unittest.mock import Mock, patch, MagicMock

from src.plugins.legacy.cobol.tools.preprocessor import CobolPreprocessor


class TestCobolPreprocessorIRGeneration:
    """Test cases for COBOL preprocessor IR file generation."""

    @pytest.fixture
    def preprocessor(self):
        """Create CobolPreprocessor instance."""
        return CobolPreprocessor()

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    def sample_cobol_file(self, temp_dir):
        """Create a sample COBOL file for testing."""
        cobol_content = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CBACT01C.
       
       ENVIRONMENT DIVISION.
       
       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 WS-COUNTER PIC 9(3) VALUE 0.
       01 WS-MESSAGE PIC X(50) VALUE 'Hello World'.
       
       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY WS-MESSAGE.
           ADD 1 TO WS-COUNTER.
           STOP RUN.
        """
        
        cobol_file = os.path.join(temp_dir, "CBACT01C.cbl")
        with open(cobol_file, 'w', encoding='utf-8') as f:
            f.write(cobol_content)
        
        return cobol_file

    def test_successful_ir_generation(self, preprocessor, sample_cobol_file, temp_dir):
        """Test successful IR file generation through the complete flow."""
        output_file = os.path.join(temp_dir, "CBACT01C_preprocessed.cbl")
        
        # Mock the orchestrator to succeed in IR generation
        with patch('src.plugins.legacy.cobol.tools.preprocessor.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.process_files.return_value = (True, 0)  # Success
            mock_orchestrator.copybook_expander.expand_source.return_value = "expanded content"
            mock_orchestrator.text_normalizer.normalize.return_value = "normalized content"
            mock_orchestrator.neo4j_connector.graph = None  # Disable Neo4j
            mock_orchestrator_class.return_value = mock_orchestrator
            
            # Mock the temp directory to create a fake IR file
            with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
                mock_temp_dir.return_value.__enter__.return_value = temp_dir
                
                # Create a fake IR file in the temp directory
                ir_file_path = os.path.join(temp_dir, "CBACT01C.json")
                fake_ir_data = {
                    "metadata": {
                        "module_id": "CBACT01C",
                        "file_name": "CBACT01C.cbl",
                        "fallback": False
                    },
                    "nodes": [
                        {
                            "uuid": "CBACT01C_module",
                            "type": "CobolModule",
                            "module_id": "CBACT01C",
                            "file_name": "CBACT01C.cbl"
                        }
                    ],
                    "relationships": []
                }
                
                with open(ir_file_path, 'w') as f:
                    json.dump(fake_ir_data, f)
                
                # Create a fake preprocessed file
                preprocessed_file_path = os.path.join(temp_dir, "CBACT01C_preprocessed.cob")
                with open(preprocessed_file_path, 'w') as f:
                    f.write("normalized content")
                
                result = preprocessor.preprocess_file(sample_cobol_file, output_file)
                
                # Should succeed
                assert result is True
                
                # Check that IR file was created in the correct location
                json_dir = os.path.join(os.path.dirname(output_file), "json")
                final_ir_file = os.path.join(json_dir, "CBACT01C_preprocessed.json")
                assert os.path.exists(final_ir_file)
                
                # Verify IR file content
                with open(final_ir_file, 'r') as f:
                    ir_data = json.load(f)
                
                assert ir_data["metadata"]["module_id"] == "CBACT01C"
                assert ir_data["metadata"]["fallback"] is False

    def test_ir_generation_with_parser_components(self, preprocessor, sample_cobol_file, temp_dir):
        """Test that the orchestrator components are properly called."""
        output_file = os.path.join(temp_dir, "CBACT01C_preprocessed.cbl")
        
        with patch('src.plugins.legacy.cobol.tools.preprocessor.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.process_files.return_value = (True, 0)
            mock_orchestrator.copybook_expander.expand_source.return_value = "expanded content"
            mock_orchestrator.text_normalizer.normalize.return_value = "normalized content"
            mock_orchestrator.neo4j_connector.graph = None
            mock_orchestrator_class.return_value = mock_orchestrator
            
            with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
                mock_temp_dir.return_value.__enter__.return_value = temp_dir
                
                # Create required files
                ir_file_path = os.path.join(temp_dir, "CBACT01C.json")
                with open(ir_file_path, 'w') as f:
                    json.dump({"metadata": {"module_id": "CBACT01C"}}, f)
                
                preprocessed_file_path = os.path.join(temp_dir, "CBACT01C_preprocessed.cob")
                with open(preprocessed_file_path, 'w') as f:
                    f.write("normalized content")
                
                result = preprocessor.preprocess_file(sample_cobol_file, output_file)
                
                # Verify that the orchestrator components were called
                mock_orchestrator.copybook_expander.expand_source.assert_called_once()
                mock_orchestrator.text_normalizer.normalize.assert_called_once()
                mock_orchestrator.process_files.assert_called_once()
                
                assert result is True

    def test_ir_generation_failure_creates_fallback(self, preprocessor, sample_cobol_file, temp_dir):
        """Test that fallback IR is created when main IR generation fails."""
        output_file = os.path.join(temp_dir, "CBACT01C_preprocessed.cbl")
        
        with patch('src.plugins.legacy.cobol.tools.preprocessor.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.process_files.return_value = (False, 1)  # Failure
            mock_orchestrator.copybook_expander.expand_source.return_value = "expanded content"
            mock_orchestrator.text_normalizer.normalize.return_value = "normalized content"
            mock_orchestrator.neo4j_connector.graph = None
            mock_orchestrator_class.return_value = mock_orchestrator
            
            with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
                mock_temp_dir.return_value.__enter__.return_value = temp_dir
                
                # Create preprocessed file but no IR file (simulating failure)
                preprocessed_file_path = os.path.join(temp_dir, "CBACT01C_preprocessed.cob")
                with open(preprocessed_file_path, 'w') as f:
                    f.write("normalized content")
                
                result = preprocessor.preprocess_file(sample_cobol_file, output_file)
                
                # Should still succeed due to fallback
                assert result is True
                
                # Check that fallback IR file was created
                json_dir = os.path.join(os.path.dirname(output_file), "json")
                final_ir_file = os.path.join(json_dir, "CBACT01C_preprocessed.json")
                assert os.path.exists(final_ir_file)
                
                # Verify fallback IR structure
                with open(final_ir_file, 'r') as f:
                    ir_data = json.load(f)
                
                assert ir_data["metadata"]["fallback"] is True
                assert ir_data["metadata"]["module_id"] == "CBACT01C"

    def test_call_tree_and_rekt_analysis_execution(self, preprocessor, sample_cobol_file, temp_dir):
        """Test that call tree and REKT analysis are executed after IR generation."""
        output_file = os.path.join(temp_dir, "CBACT01C_preprocessed.cbl")
        
        with patch('src.plugins.legacy.cobol.tools.preprocessor.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.process_files.return_value = (True, 0)
            mock_orchestrator.copybook_expander.expand_source.return_value = "expanded content"
            mock_orchestrator.text_normalizer.normalize.return_value = "normalized content"
            mock_orchestrator.neo4j_connector.graph = None
            mock_orchestrator_class.return_value = mock_orchestrator
            
            # Mock the analysis methods
            with patch.object(preprocessor, '_generate_call_tree_analysis', return_value=True) as mock_call_tree:
                with patch.object(preprocessor, '_generate_flow_diagram', return_value=True) as mock_flow_diagram:
                    with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
                        mock_temp_dir.return_value.__enter__.return_value = temp_dir
                        
                        # Create required files
                        ir_file_path = os.path.join(temp_dir, "CBACT01C.json")
                        with open(ir_file_path, 'w') as f:
                            json.dump({"metadata": {"module_id": "CBACT01C"}}, f)
                        
                        preprocessed_file_path = os.path.join(temp_dir, "CBACT01C_preprocessed.cob")
                        with open(preprocessed_file_path, 'w') as f:
                            f.write("normalized content")
                        
                        result = preprocessor.preprocess_file(sample_cobol_file, output_file)
                        
                        # Verify that both analysis methods were called
                        mock_call_tree.assert_called_once()
                        mock_flow_diagram.assert_called_once()
                        
                        assert result is True

    def test_ir_file_validation_and_structure(self, preprocessor, sample_cobol_file, temp_dir):
        """Test that generated IR files have the correct structure and are valid JSON."""
        output_file = os.path.join(temp_dir, "CBACT01C_preprocessed.cbl")
        
        # Test with fallback IR creation (easier to control)
        with patch('src.plugins.legacy.cobol.tools.preprocessor.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.process_files.return_value = (False, 1)  # Force fallback
            mock_orchestrator.copybook_expander.expand_source.return_value = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CBACT01C.
       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 WS-VAR PIC X(10).
       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY 'Test'.
           STOP RUN.
            """
            mock_orchestrator.text_normalizer.normalize.return_value = mock_orchestrator.copybook_expander.expand_source.return_value
            mock_orchestrator.neo4j_connector.graph = None
            mock_orchestrator_class.return_value = mock_orchestrator
            
            with patch('tempfile.TemporaryDirectory') as mock_temp_dir:
                mock_temp_dir.return_value.__enter__.return_value = temp_dir
                
                # Create preprocessed file
                preprocessed_file_path = os.path.join(temp_dir, "CBACT01C_preprocessed.cob")
                with open(preprocessed_file_path, 'w') as f:
                    f.write(mock_orchestrator.copybook_expander.expand_source.return_value)
                
                result = preprocessor.preprocess_file(sample_cobol_file, output_file)
                
                assert result is True
                
                # Verify IR file structure
                json_dir = os.path.join(os.path.dirname(output_file), "json")
                final_ir_file = os.path.join(json_dir, "CBACT01C_preprocessed.json")
                assert os.path.exists(final_ir_file)
                
                with open(final_ir_file, 'r') as f:
                    ir_data = json.load(f)
                
                # Validate required structure
                assert "metadata" in ir_data
                assert "nodes" in ir_data
                assert "relationships" in ir_data
                
                # Validate metadata
                metadata = ir_data["metadata"]
                assert "module_id" in metadata
                assert "file_name" in metadata
                assert "fallback" in metadata
                
                # Validate nodes
                assert len(ir_data["nodes"]) > 0
                module_node = ir_data["nodes"][0]
                assert "uuid" in module_node
                assert "type" in module_node
                assert module_node["type"] == "CobolModule"
                
                # Check for division nodes (should be created by fallback)
                division_types = [node["type"] for node in ir_data["nodes"] if "Division" in node["type"]]
                expected_divisions = ["CobolIdentificationDivision", "CobolDataDivision", "CobolProcedureDivision"]
                for expected in expected_divisions:
                    assert expected in division_types
