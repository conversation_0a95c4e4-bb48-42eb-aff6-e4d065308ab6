"""
Unit tests for COBOL preprocessor error handling scenarios.
"""
import pytest
import os
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock

from src.plugins.legacy.cobol.tools.preprocessor import CobolPreprocessor


class TestCobolPreprocessorErrorHandling:
    """Test cases for COBOL preprocessor error handling."""

    @pytest.fixture
    def preprocessor(self):
        """Create CobolPreprocessor instance for testing."""
        return CobolPreprocessor()

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    def sample_cobol_file(self, temp_dir):
        """Create a sample COBOL file for testing."""
        cobol_content = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. TEST-PROGRAM.

       ENVIRONMENT DIVISION.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 WS-COUNTER PIC 9(3) VALUE 0.

       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY 'Hello World'.
           STOP RUN.
        """

        cobol_file = os.path.join(temp_dir, "TEST-PROGRAM.cbl")
        with open(cobol_file, 'w', encoding='utf-8') as f:
            f.write(cobol_content)

        return cobol_file

    def test_ir_generation_failure_with_fallback(self, preprocessor, sample_cobol_file, temp_dir):
        """Test that fallback IR file is created when IR generation fails."""
        output_file = os.path.join(temp_dir, "TEST-PROGRAM_preprocessed.cbl")

        # Mock the orchestrator to fail IR generation
        with patch('src.plugins.legacy.cobol.tools.preprocessor.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.process_files.return_value = (False, ["IR generation failed"])
            mock_orchestrator.copybook_expander.expand_source.return_value = "expanded content"
            mock_orchestrator.text_normalizer.normalize.return_value = "normalized content"
            mock_orchestrator_class.return_value = mock_orchestrator

            result = preprocessor.preprocess_file(sample_cobol_file, output_file)

            # Should still succeed due to fallback
            assert result is True

            # Check that fallback IR file was created
            json_dir = os.path.join(os.path.dirname(output_file), "json")
            ir_file = os.path.join(json_dir, "TEST-PROGRAM_preprocessed.json")
            assert os.path.exists(ir_file)

            # Verify fallback IR structure
            with open(ir_file, 'r') as f:
                ir_data = json.load(f)

            assert ir_data["metadata"]["fallback"] is True
            assert ir_data["metadata"]["module_id"] == "TEST-PROGRAM"
            assert len(ir_data["nodes"]) > 0

    def test_fallback_ir_creation_with_divisions(self, preprocessor, temp_dir):
        """Test that fallback IR correctly identifies COBOL divisions."""
        cobol_content = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. TEST-PROG.

       ENVIRONMENT DIVISION.
       CONFIGURATION SECTION.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 WS-VAR PIC X(10).

       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY 'Test'.
           STOP RUN.
        """

        ir_file = os.path.join(temp_dir, "TEST-PROG.json")

        preprocessor._create_fallback_ir_file(ir_file, "TEST-PROG", cobol_content)

        assert os.path.exists(ir_file)

        with open(ir_file, 'r') as f:
            ir_data = json.load(f)

        # Check that all divisions were identified
        division_types = [node["type"] for node in ir_data["nodes"] if "Division" in node["type"]]
        expected_divisions = [
            "CobolIdentificationDivision",
            "CobolEnvironmentDivision",
            "CobolDataDivision",
            "CobolProcedureDivision"
        ]

        for expected in expected_divisions:
            assert expected in division_types

    def test_preprocessor_file_not_found(self, preprocessor, temp_dir):
        """Test handling of non-existent input files."""
        non_existent_file = os.path.join(temp_dir, "non-existent.cbl")
        output_file = os.path.join(temp_dir, "output.cbl")

        result = preprocessor.preprocess_file(non_existent_file, output_file)

        assert result is False

    def test_preprocessor_permission_error(self, preprocessor, sample_cobol_file, temp_dir):
        """Test handling of permission errors during file operations."""
        output_file = os.path.join(temp_dir, "output.cbl")

        # Mock file operations to raise permission error
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            result = preprocessor.preprocess_file(sample_cobol_file, output_file)

            assert result is False

    def test_orchestrator_initialization_failure(self, preprocessor, sample_cobol_file, temp_dir):
        """Test handling of orchestrator initialization failures."""
        output_file = os.path.join(temp_dir, "output.cbl")

        # Mock orchestrator to raise exception during initialization
        with patch('src.plugins.legacy.cobol.tools.preprocessor.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator_class.side_effect = Exception("Orchestrator init failed")

            result = preprocessor.preprocess_file(sample_cobol_file, output_file)

            assert result is False

    def test_copybook_expansion_failure(self, preprocessor, sample_cobol_file, temp_dir):
        """Test handling of copybook expansion failures."""
        output_file = os.path.join(temp_dir, "output.cbl")

        with patch('src.plugins.legacy.cobol.tools.preprocessor.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.copybook_expander.expand_source.side_effect = Exception("Copybook expansion failed")
            mock_orchestrator_class.return_value = mock_orchestrator

            result = preprocessor.preprocess_file(sample_cobol_file, output_file)

            assert result is False

    def test_text_normalization_failure(self, preprocessor, sample_cobol_file, temp_dir):
        """Test handling of text normalization failures."""
        output_file = os.path.join(temp_dir, "output.cbl")

        with patch('src.plugins.legacy.cobol.tools.preprocessor.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.copybook_expander.expand_source.return_value = "expanded content"
            mock_orchestrator.text_normalizer.normalize.side_effect = Exception("Normalization failed")
            mock_orchestrator_class.return_value = mock_orchestrator

            result = preprocessor.preprocess_file(sample_cobol_file, output_file)

            assert result is False

    def test_fallback_ir_json_write_failure(self, preprocessor, temp_dir):
        """Test handling of JSON write failures during fallback IR creation."""
        ir_file = os.path.join(temp_dir, "test.json")

        # Mock json.dump to raise an exception
        with patch('json.dump', side_effect=Exception("JSON write failed")):
            # Should not raise exception, just log error
            preprocessor._create_fallback_ir_file(ir_file, "TEST", "content")

            # File should not exist due to the failure
            assert not os.path.exists(ir_file)

    def test_malformed_cobol_content_fallback(self, preprocessor, temp_dir):
        """Test fallback IR creation with malformed COBOL content."""
        malformed_content = """
        This is not valid COBOL content
        No divisions here
        Just random text
        """

        ir_file = os.path.join(temp_dir, "malformed.json")

        preprocessor._create_fallback_ir_file(ir_file, "MALFORMED", malformed_content)

        assert os.path.exists(ir_file)

        with open(ir_file, 'r') as f:
            ir_data = json.load(f)

        # Should still create basic structure even with malformed content
        assert ir_data["metadata"]["module_id"] == "MALFORMED"
        assert ir_data["metadata"]["fallback"] is True
        assert len(ir_data["nodes"]) >= 1  # At least the module node

    @pytest.mark.unit
    def test_error_logging_and_recovery(self, preprocessor, sample_cobol_file, temp_dir):
        """Test that errors are properly logged and recovery mechanisms work."""
        output_file = os.path.join(temp_dir, "output.cbl")

        with patch('src.plugins.legacy.cobol.tools.preprocessor.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            # First call fails, but fallback should work
            mock_orchestrator.process_files.return_value = (False, ["Processing failed"])
            mock_orchestrator.copybook_expander.expand_source.return_value = "expanded"
            mock_orchestrator.text_normalizer.normalize.return_value = "normalized"
            mock_orchestrator_class.return_value = mock_orchestrator

            with patch('src.plugins.legacy.cobol.tools.preprocessor.logger') as mock_logger:
                result = preprocessor.preprocess_file(sample_cobol_file, output_file)

                # Should succeed with fallback
                assert result is True

                # Should have logged the error
                assert mock_logger.error.called

                # Should have logged fallback creation
                assert mock_logger.info.called
