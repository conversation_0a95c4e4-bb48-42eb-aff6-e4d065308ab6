"""
Comprehensive tests for the refactored Java Spring plugin.
Tests the separation of concerns and delegation to specialized classes.
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os
import json
import inspect
from typing import Dict, Any

from src.plugins.targets.java_spring.plugin import (
    JavaSpringCodeGenerator,
    JavaSpringPlugin
)
from src.plugins.targets.java_spring.tools.project_manager.core import JavaProjectManager
from src.platform.agents.base_agent import AgentInput


class TestJavaSpringCodeGeneratorRefactored(unittest.TestCase):
    """Test the refactored JavaSpringCodeGenerator class."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.code_generator = JavaSpringCodeGenerator()

        # Mock dependencies
        self.code_generator.knowledge_db = Mock()
        self.code_generator.tools = Mock()
        self.code_generator.java_project_manager = Mock()
        self.code_generator.java_code_generator = Mock()
        self.code_generator.dependency_analyzer = Mock()
        self.code_generator.state_manager = Mock()
        self.code_generator.performance_tracker = Mock()
        self.code_generator.react_coordinator = Mock()

        # Mock performance tracker methods
        self.code_generator.performance_tracker.start_chunk_processing = Mock(return_value=0.0)
        self.code_generator.performance_tracker.end_chunk_processing_success = Mock(return_value=0.1)
        self.code_generator.performance_tracker.end_chunk_processing_failure = Mock(return_value=0.1)
        self.code_generator.performance_tracker.log_delegation_performance = Mock()
        self.code_generator.performance_tracker.get_chunk_performance_metrics = Mock(return_value={})
        self.code_generator.performance_tracker.get_failure_performance_metrics = Mock(return_value={})

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_initialization_creates_all_dependencies(self):
        """Test that initialization creates all required dependencies."""
        generator = JavaSpringCodeGenerator()

        # Check that all required components are initialized
        self.assertIsNotNone(generator.knowledge_db)
        self.assertIsNotNone(generator.java_project_manager)
        self.assertIsNotNone(generator.java_code_generator)
        self.assertIsNotNone(generator.dependency_analyzer)
        self.assertIsNotNone(generator.state_manager)
        self.assertIsNotNone(generator.performance_tracker)
        self.assertIsNotNone(generator.react_coordinator)
        self.assertIsNotNone(generator.logger)

    def test_generate_code_delegates_to_react_loop(self):
        """Test that generate_code properly orchestrates the React loop."""
        source_analysis = {"knowledge_base": {}}
        config = {"working_directory": self.temp_dir}

        # Mock the React loop components
        self.code_generator.state_manager.load_or_initialize_generation_state = Mock(return_value={
            "completed": True,
            "current_phase": "finalization"
        })
        self.code_generator.state_manager.is_generation_complete = Mock(return_value=True)
        self.code_generator.react_coordinator.should_continue_generation = Mock(return_value=False)
        self.code_generator.react_coordinator.create_success_result = Mock(return_value={
            "success": True,
            "message": "Successfully generated Java Spring Boot project",
            "artifacts": {"java_project_path": self.temp_dir}
        })
        self.code_generator.performance_tracker.start_processing = Mock(return_value=0.0)
        self.code_generator.performance_tracker.end_processing = Mock(return_value=1.0)

        result = self.code_generator.generate_code(source_analysis, config)

        # Verify successful completion
        self.assertTrue(result["success"])
        self.assertIn("Successfully generated Java Spring Boot project", result["message"])

    def test_chunk_code_generation_delegates_properly(self):
        """Test that chunk code generation delegates to JavaCodeGenerator."""
        chunk_info = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK"}
        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})

        # Mock dependencies
        chunk_doc = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK", "code": "TEST CODE"}
        self.code_generator.knowledge_db.get_chunk_documentation = Mock(return_value=chunk_doc)
        self.code_generator.dependency_analyzer.get_chunk_dependencies = Mock(return_value={})
        self.code_generator.java_code_generator.generate_chunk_code = Mock(return_value="public class TestClass {}")
        self.code_generator.tools.get_language_mappings = Mock(return_value={})
        self.code_generator.java_project_manager.save_java_code_to_file = Mock(return_value={"file_path": "/test/path"})
        self.code_generator.java_code_generator.validate_java_code = Mock(return_value={"valid": True})

        result = self.code_generator._generate_chunk_code(chunk_info, generation_state, input_data)

        # Verify delegation occurred
        self.code_generator.java_code_generator.generate_chunk_code.assert_called_once()
        self.assertTrue(result["success"])
        self.assertIn("java_code", result)

    def test_database_operations_delegate_to_knowledge_db(self):
        """Test that database operations delegate to KnowledgeDatabase."""
        # Test get_chunk_documentation delegation (this method exists in the current implementation)
        self.code_generator.knowledge_db.get_chunk_documentation = Mock(return_value={"test": "data"})
        result = self.code_generator.knowledge_db.get_chunk_documentation("TEST_PROG", "TEST_CHUNK")
        self.code_generator.knowledge_db.get_chunk_documentation.assert_called_once_with("TEST_PROG", "TEST_CHUNK")

        # Test that the knowledge_db is properly used
        self.assertIsNotNone(self.code_generator.knowledge_db)

    def test_file_operations_delegate_to_project_manager(self):
        """Test that file operations delegate to JavaProjectManager."""
        java_code = "public class TestClass {}"
        chunk_info = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK"}
        java_project_dir = self.temp_dir

        self.code_generator.java_project_manager.save_java_code_to_file = Mock(return_value={"file_path": "/test/path"})

        result = self.code_generator.java_project_manager.save_java_code_to_file(java_code, chunk_info, java_project_dir)

        self.code_generator.java_project_manager.save_java_code_to_file.assert_called_once()
        self.assertEqual(result["file_path"], "/test/path")

    def test_validation_delegates_to_code_generator(self):
        """Test that validation delegates to JavaCodeGenerator."""
        java_code = "public class TestClass {}"
        chunk_doc = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK"}

        self.code_generator.java_code_generator.validate_java_code = Mock(return_value={"valid": True})

        result = self.code_generator.java_code_generator.validate_java_code(java_code, chunk_doc)

        self.code_generator.java_code_generator.validate_java_code.assert_called_once_with(java_code, chunk_doc)
        self.assertTrue(result["valid"])

    def test_error_handling_preserves_original_behavior(self):
        """Test that error handling fails explicitly as in original implementation."""
        chunk_info = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK"}
        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})

        # Mock JavaCodeGenerator to return empty string (failure case)
        self.code_generator.knowledge_db.get_chunk_documentation = Mock(return_value={"test": "data", "code": "test"})
        self.code_generator.dependency_analyzer.get_chunk_dependencies = Mock(return_value={})
        self.code_generator.java_code_generator.generate_chunk_code = Mock(return_value="")  # Empty = failure

        result = self.code_generator._generate_chunk_code(chunk_info, generation_state, input_data)

        # Verify explicit failure (no fallback)
        self.assertFalse(result["success"])
        self.assertIn("JavaCodeGenerator returned empty code", result["error"])

    def test_no_fallback_methods_exist(self):
        """Test that no fallback or backward compatibility methods exist."""
        # Check that fallback methods don't exist
        self.assertFalse(hasattr(self.code_generator, '_basic_generate'))
        self.assertFalse(hasattr(self.code_generator, '_fallback_generation'))
        self.assertFalse(hasattr(self.code_generator, '_graceful_degradation'))

        # Check that validation methods don't exist in plugin
        self.assertFalse(hasattr(self.code_generator, '_validate_java_against_cobol'))
        self.assertFalse(hasattr(self.code_generator, '_extract_cobol_perform_statements'))


class TestJavaProjectManagerRefactored(unittest.TestCase):
    """Test the refactored JavaProjectManager class."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.project_manager = JavaProjectManager()

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_create_project_structure_delegates_properly(self):
        """Test that project structure creation delegates to core JavaProjectManager."""
        # Test that the project manager has the expected methods
        self.assertTrue(hasattr(self.project_manager, 'ensure_project_structure'))
        self.assertTrue(hasattr(self.project_manager, 'save_java_code_to_file'))
        self.assertTrue(hasattr(self.project_manager, 'finalize_project'))

        # Test that the project manager is properly initialized
        self.assertIsNotNone(self.project_manager)

    def test_no_fallback_methods_exist(self):
        """Test that no fallback methods exist in project manager."""
        # Check that fallback methods don't exist
        self.assertFalse(hasattr(self.project_manager, '_basic_create_structure'))
        self.assertFalse(hasattr(self.project_manager, '_generate_pom_xml'))


class TestJavaSpringPluginIntegration(unittest.TestCase):
    """Integration tests for the complete plugin."""

    def setUp(self):
        """Set up test fixtures."""
        self.plugin = JavaSpringPlugin()

    def test_plugin_components_are_properly_initialized(self):
        """Test that all plugin components are properly initialized."""
        self.assertIsNotNone(self.plugin._code_generator)
        self.assertIsNotNone(self.plugin._project_manager)
        self.assertIsNotNone(self.plugin._documentation_generator)

    def test_plugin_uses_external_classes_not_internal_implementations(self):
        """Test that plugin uses external classes rather than implementing them internally."""
        from src.plugins.targets.java_spring.tools.project_manager.core import JavaProjectManager as CoreJavaProjectManager
        from src.plugins.targets.java_spring.tools.documentation_generator import JavaSpringDocumentationGenerator

        # Verify that the plugin uses the external classes
        self.assertIsInstance(self.plugin._project_manager, CoreJavaProjectManager)
        self.assertIsInstance(self.plugin._documentation_generator, JavaSpringDocumentationGenerator)

        # Verify that these are NOT defined as classes in the plugin.py file (they should be imported)
        from src.plugins.targets.java_spring import plugin
        import inspect

        # Get all classes defined in the plugin module (not imported)
        defined_classes = [name for name, obj in inspect.getmembers(plugin, inspect.isclass)
                          if obj.__module__ == plugin.__name__]

        # These should not be defined in the plugin module itself
        self.assertNotIn('JavaProjectManager', defined_classes)
        self.assertNotIn('JavaSpringDocumentationGenerator', defined_classes)

        # The plugin should use delegation, not internal implementations
        self.assertTrue(True)  # This test passes if we get here without errors

    def test_plugin_delegation_works(self):
        """Test that plugin properly delegates to its components."""
        code_generator = self.plugin.get_code_generator()
        project_manager = self.plugin.get_project_manager()
        doc_generator = self.plugin.get_documentation_generator()

        self.assertIsInstance(code_generator, JavaSpringCodeGenerator)
        self.assertEqual(code_generator, self.plugin._code_generator)

    def test_validation_delegates_to_code_generator(self):
        """Test that validation delegates to the code generator."""
        with patch.object(self.plugin._code_generator, 'java_code_generator') as mock_java_gen:
            mock_java_gen.validate_java_code = Mock(return_value={"valid": True})

            result = self.plugin.validate_against_source(
                "COBOL CODE", "Java code", "cobol", "TEST_PROG", "TEST_CHUNK"
            )

            mock_java_gen.validate_java_code.assert_called_once()
            self.assertTrue(result["valid"])

    def test_performance_metrics_tracking(self):
        """Test that performance metrics are properly tracked."""
        code_generator = self.plugin.get_code_generator()

        # Check that performance tracker exists and has the expected methods
        self.assertIsNotNone(code_generator.performance_tracker)
        self.assertTrue(hasattr(code_generator, 'get_performance_metrics'))

        # Check initial metrics
        initial_metrics = code_generator.get_performance_metrics()
        self.assertIsInstance(initial_metrics, dict)

    def test_comprehensive_logging_is_present(self):
        """Test that comprehensive logging is implemented."""
        code_generator = self.plugin.get_code_generator()

        # Check that logging and performance tracking exist
        self.assertIsNotNone(code_generator.logger)
        self.assertIsNotNone(code_generator.performance_tracker)
        self.assertTrue(hasattr(code_generator, 'get_performance_metrics'))

    def test_no_hardcoded_prompts_in_plugin(self):
        """Test that no hardcoded prompts exist in the plugin file."""
        import inspect
        from src.plugins.targets.java_spring import plugin

        # Get the source code of the plugin module
        source = inspect.getsource(plugin)

        # Check for common prompt patterns
        prompt_indicators = [
            "Generate a complete Java",
            "You are an expert",
            "**Program ID:**",
            "**COBOL Code:**",
            "```cobol",
            "```java"
        ]

        for indicator in prompt_indicators:
            self.assertNotIn(indicator, source, f"Found hardcoded prompt indicator: {indicator}")

    def test_plugin_file_structure_is_clean(self):
        """Test that the plugin file only contains coordination logic."""
        from src.plugins.targets.java_spring import plugin

        # Check that only the expected classes are defined
        expected_classes = ['JavaSpringCodeGenerator', 'JavaSpringPlugin']

        # Get all classes defined in the module
        defined_classes = [name for name, obj in inspect.getmembers(plugin, inspect.isclass)
                          if obj.__module__ == plugin.__name__]

        self.assertEqual(set(defined_classes), set(expected_classes))

        # Check that no business logic methods are implemented directly
        business_logic_methods = [
            '_generate_pom_xml',
            '_basic_create_structure',
            '_validate_java_against_cobol',
            '_extract_cobol_perform_statements'
        ]

        for method_name in business_logic_methods:
            self.assertFalse(hasattr(plugin.JavaSpringCodeGenerator, method_name),
                           f"Found business logic method {method_name} in plugin class")


if __name__ == '__main__':
    unittest.main()
