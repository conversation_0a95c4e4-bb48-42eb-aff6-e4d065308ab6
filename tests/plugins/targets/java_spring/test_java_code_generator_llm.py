"""
Tests for JavaCodeGenerator with real LLM invocation.
Tests the actual code generation and mapping extraction functionality.
"""
import pytest
import tempfile
import os
import json
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

from src.plugins.targets.java_spring.agents.code_generator import JavaCodeGenerator
from src.platform.tools.knowledge_database import KnowledgeDatabase
from src.platform.tools.code_generator_tools import CodeGeneratorTools
import llm_settings


class TestJavaCodeGeneratorLLM:
    """Test JavaCodeGenerator with real LLM invocation."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield tmpdir

    @pytest.fixture
    def knowledge_db(self, temp_dir):
        """Create test knowledge database."""
        db_path = os.path.join(temp_dir, "test_knowledge.db")
        return KnowledgeDatabase(db_path)

    @pytest.fixture
    def code_generator_tools(self, knowledge_db, temp_dir):
        """Create code generator tools."""
        return CodeGeneratorTools(knowledge_db, temp_dir)

    @pytest.fixture
    def java_code_generator(self, knowledge_db):
        """Create JavaCodeGenerator with real LLM."""
        return JavaCodeGenerator(llm_settings.llm, knowledge_db)

    def test_generate_chunk_code_with_real_llm(self, java_code_generator, code_generator_tools):
        """Test generate_chunk_code with real LLM invocation."""
        # Prepare realistic COBOL chunk
        chunk_doc = {
            "program_id": "CBPAY01C",
            "chunk_name": "CALCULATE-PAY",
            "business_name": "Payroll Calculation",
            "business_description": "Calculates employee payroll based on hours and rate",
            "code": """
            CALCULATE-PAY.
                COMPUTE WS-GROSS-PAY = WS-HOURS-WORKED * WS-HOURLY-RATE
                IF WS-HOURS-WORKED > 40
                    COMPUTE WS-OVERTIME-HOURS = WS-HOURS-WORKED - 40
                    COMPUTE WS-OVERTIME-PAY = WS-OVERTIME-HOURS * WS-HOURLY-RATE * 1.5
                    ADD WS-OVERTIME-PAY TO WS-GROSS-PAY
                END-IF
                COMPUTE WS-NET-PAY = WS-GROSS-PAY - WS-DEDUCTIONS.
            """
        }

        dependencies = {
            "dependent_services": [],
            "required_imports": ["java.math.BigDecimal"],
            "data_structures": []
        }

        generation_state = {}

        # Act - invoke real LLM
        java_code = java_code_generator.generate_chunk_code(
            chunk_doc, dependencies, generation_state, code_generator_tools
        )

        # Assert - verify real Java code was generated
        assert java_code is not None
        assert len(java_code) > 100  # Should be substantial code

        # Verify Java structure
        assert "class" in java_code
        assert "public" in java_code

        # Verify Spring Boot patterns
        spring_annotations = ["@Service", "@Component", "@RestController"]
        assert any(annotation in java_code for annotation in spring_annotations)

        # Verify business logic concepts are present
        business_concepts = ["pay", "calculate", "hours", "rate"]
        java_lower = java_code.lower()
        assert any(concept in java_lower for concept in business_concepts)

        # Verify method structure
        assert "(" in java_code and ")" in java_code
        assert "{" in java_code and "}" in java_code

        print(f"Generated Java code ({len(java_code)} chars):")
        print(java_code[:500] + "..." if len(java_code) > 500 else java_code)

    def test_mapping_extraction_from_real_llm_response(self, java_code_generator, code_generator_tools):
        """Test mapping extraction from real LLM response."""
        chunk_doc = {
            "program_id": "CBCUST01",
            "chunk_name": "VALIDATE-CUSTOMER",
            "business_name": "Customer Validation",
            "business_description": "Validates customer information",
            "code": """
            VALIDATE-CUSTOMER.
                IF WS-CUSTOMER-ID = SPACES
                    MOVE 'INVALID' TO WS-STATUS
                ELSE
                    MOVE 'VALID' TO WS-STATUS
                END-IF.
            """
        }

        # Act - generate code with real LLM
        java_code = java_code_generator.generate_chunk_code(
            chunk_doc, {}, {}, code_generator_tools
        )

        # Verify mappings were saved
        mappings = code_generator_tools.get_language_mappings("CBCUST01", "cobol", "java", "VALIDATE-CUSTOMER")

        # Assert mappings exist
        assert mappings is not None

        # Log results for verification
        print(f"Generated code length: {len(java_code)}")
        print(f"Mappings type: {type(mappings)}")
        print(f"Mappings content: {mappings}")

    def test_code_validation_with_real_generated_code(self, java_code_generator, code_generator_tools):
        """Test code validation with real generated Java code."""
        chunk_doc = {
            "program_id": "CBORD01C",
            "chunk_name": "PROCESS-ORDER",
            "business_name": "Order Processing",
            "business_description": "Processes customer orders",
            "code": """
            PROCESS-ORDER.
                MOVE WS-ORDER-ID TO ORDER-REC-ID
                READ ORDER-FILE
                IF ORDER-FOUND
                    PERFORM VALIDATE-ORDER
                    IF WS-VALID-ORDER = 'Y'
                        PERFORM UPDATE-INVENTORY
                        PERFORM CALCULATE-TOTAL
                    END-IF
                END-IF.
            """
        }

        # Generate code
        java_code = java_code_generator.generate_chunk_code(
            chunk_doc, {}, {}, code_generator_tools
        )

        # Validate the generated code
        validation_result = java_code_generator.validate_java_code(java_code, chunk_doc)

        # Assert validation results
        assert validation_result is not None
        assert isinstance(validation_result, dict)
        assert "valid" in validation_result

        # Log validation results
        print(f"Validation result: {validation_result}")

        # If validation failed, log the issues
        if not validation_result.get("valid", False):
            print(f"Validation errors: {validation_result.get('errors', [])}")
            print(f"Validation warnings: {validation_result.get('warnings', [])}")

    def test_complex_cobol_logic_generation(self, java_code_generator, code_generator_tools):
        """Test generation of complex COBOL business logic."""
        chunk_doc = {
            "program_id": "CBLOAN01",
            "chunk_name": "CALCULATE-INTEREST",
            "business_name": "Loan Interest Calculation",
            "business_description": "Calculates compound interest for loan payments",
            "code": """
            CALCULATE-INTEREST.
                MOVE WS-PRINCIPAL TO WS-TEMP-PRINCIPAL
                PERFORM VARYING WS-MONTH FROM 1 BY 1
                    UNTIL WS-MONTH > WS-LOAN-TERM
                    COMPUTE WS-MONTHLY-INTEREST =
                        WS-TEMP-PRINCIPAL * WS-INTEREST-RATE / 12
                    ADD WS-MONTHLY-INTEREST TO WS-TOTAL-INTEREST
                    ADD WS-MONTHLY-INTEREST TO WS-TEMP-PRINCIPAL
                END-PERFORM
                COMPUTE WS-TOTAL-AMOUNT = WS-PRINCIPAL + WS-TOTAL-INTEREST.
            """
        }

        dependencies = {
            "dependent_services": ["InterestCalculationService"],
            "required_imports": ["java.math.BigDecimal", "java.math.RoundingMode"],
            "data_structures": ["LoanDetails", "InterestCalculation"]
        }

        # Act
        java_code = java_code_generator.generate_chunk_code(
            chunk_doc, dependencies, {}, code_generator_tools
        )

        # Assert complex logic was generated
        assert java_code is not None
        assert len(java_code) > 200  # Should be substantial for complex logic

        # Verify financial calculation concepts
        financial_concepts = ["interest", "principal", "calculate", "loan"]
        java_lower = java_code.lower()
        assert any(concept in java_lower for concept in financial_concepts)

        # Verify loop structure (for PERFORM VARYING)
        loop_indicators = ["for", "while", "loop"]
        assert any(indicator in java_lower for indicator in loop_indicators)

        print(f"Complex logic generated ({len(java_code)} chars)")

    def test_error_handling_in_code_generation(self, java_code_generator, code_generator_tools):
        """Test error handling when LLM fails or returns invalid response."""
        # Test with minimal/invalid chunk data
        invalid_chunk_doc = {
            "program_id": "",
            "chunk_name": "",
            "business_name": "",
            "business_description": "",
            "code": ""
        }

        # Act
        java_code = java_code_generator.generate_chunk_code(
            invalid_chunk_doc, {}, {}, code_generator_tools
        )

        # Assert - should handle gracefully
        # Even with invalid input, should return some result (empty string is acceptable)
        assert java_code is not None
        assert isinstance(java_code, str)

        print(f"Error handling test - returned: '{java_code}'")

    def test_prompt_construction_and_context_handling(self, java_code_generator):
        """Test prompt construction and context size handling."""
        # Create a large chunk to test context handling
        large_code = "LARGE-PROCEDURE.\n" + "    MOVE 'TEST' TO WS-FIELD.\n" * 1000

        chunk_doc = {
            "program_id": "CBLARGE01",
            "chunk_name": "LARGE-PROCEDURE",
            "business_name": "Large Procedure",
            "business_description": "A very large procedure for testing context limits",
            "code": large_code
        }

        # Test prompt building
        variables_mapping = {"WS-FIELD": "testField"}
        existing_mappings = {"PREV-PROC": "previousProcedure"}
        java_structures = [{"name": "TestEntity", "fields": ["id", "name"]}]

        prompt = java_code_generator._build_enhanced_java_generation_prompt(
            chunk_doc, {}, {}, variables_mapping, existing_mappings, java_structures
        )

        # Assert prompt was constructed
        assert prompt is not None
        assert len(prompt) > 0
        assert "CBLARGE01" in prompt
        assert "LARGE-PROCEDURE" in prompt

        # Test token estimation
        estimated_tokens = java_code_generator._estimate_token_count(prompt)
        assert estimated_tokens > 0
        assert isinstance(estimated_tokens, int)

        print(f"Prompt length: {len(prompt)} chars, estimated tokens: {estimated_tokens}")

    @pytest.mark.slow
    def test_multiple_chunk_generation_consistency(self, java_code_generator, code_generator_tools):
        """Test consistency across multiple chunk generations."""
        chunks = [
            {
                "program_id": "CBTEST01",
                "chunk_name": "INIT-PROCESS",
                "business_name": "Initialize Process",
                "business_description": "Initialize processing variables",
                "code": "INIT-PROCESS.\n    MOVE ZEROS TO WS-COUNTERS.\n    MOVE 'Y' TO WS-ACTIVE-FLAG."
            },
            {
                "program_id": "CBTEST01",
                "chunk_name": "MAIN-PROCESS",
                "business_name": "Main Process",
                "business_description": "Main processing logic",
                "code": "MAIN-PROCESS.\n    PERFORM INIT-PROCESS.\n    PERFORM PROCESS-RECORDS.\n    PERFORM CLEANUP-PROCESS."
            }
        ]

        generated_codes = []

        for chunk_doc in chunks:
            java_code = java_code_generator.generate_chunk_code(
                chunk_doc, {}, {}, code_generator_tools
            )
            generated_codes.append(java_code)

            # Verify each generation
            assert java_code is not None
            assert len(java_code) > 0
            assert "class" in java_code

        # Verify consistency in naming patterns
        for code in generated_codes:
            # Should follow similar patterns
            assert "public" in code
            # Should have Spring annotations
            spring_annotations = ["@Service", "@Component", "@RestController"]
            assert any(annotation in code for annotation in spring_annotations)

        print(f"Generated {len(generated_codes)} consistent code chunks")
