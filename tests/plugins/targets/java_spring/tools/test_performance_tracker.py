"""
Tests for JavaPerformanceTracker.
"""
import unittest
import time
from unittest.mock import patch

from src.plugins.targets.java_spring.tools.performance_tracker import JavaPerformanceTracker


class TestJavaPerformanceTracker(unittest.TestCase):
    """Test the JavaPerformanceTracker class."""

    def setUp(self):
        """Set up test fixtures."""
        self.tracker = JavaPerformanceTracker()

    def test_initialization(self):
        """Test tracker initialization."""
        metrics = self.tracker.get_performance_metrics()
        
        self.assertEqual(metrics["total_chunks_processed"], 0)
        self.assertEqual(metrics["successful_generations"], 0)
        self.assertEqual(metrics["failed_generations"], 0)
        self.assertEqual(metrics["total_processing_time"], 0.0)
        self.assertEqual(metrics["average_chunk_time"], 0.0)

    def test_start_and_end_processing(self):
        """Test processing time tracking."""
        start_time = self.tracker.start_processing()
        self.assertIsInstance(start_time, float)
        
        # Simulate some processing time
        time.sleep(0.01)
        
        total_time = self.tracker.end_processing(start_time)
        self.assertGreater(total_time, 0)
        
        metrics = self.tracker.get_performance_metrics()
        self.assertGreater(metrics["total_processing_time"], 0)

    def test_chunk_processing_success(self):
        """Test successful chunk processing tracking."""
        start_time = self.tracker.start_chunk_processing()
        
        # Simulate processing time
        time.sleep(0.01)
        
        chunk_time = self.tracker.end_chunk_processing_success(start_time)
        self.assertGreater(chunk_time, 0)
        
        metrics = self.tracker.get_performance_metrics()
        self.assertEqual(metrics["total_chunks_processed"], 1)
        self.assertEqual(metrics["successful_generations"], 1)
        self.assertEqual(metrics["failed_generations"], 0)
        self.assertGreater(metrics["average_chunk_time"], 0)

    def test_chunk_processing_failure(self):
        """Test failed chunk processing tracking."""
        start_time = self.tracker.start_chunk_processing()
        
        # Simulate processing time
        time.sleep(0.01)
        
        chunk_time = self.tracker.end_chunk_processing_failure(start_time)
        self.assertGreater(chunk_time, 0)
        
        metrics = self.tracker.get_performance_metrics()
        self.assertEqual(metrics["total_chunks_processed"], 1)
        self.assertEqual(metrics["successful_generations"], 0)
        self.assertEqual(metrics["failed_generations"], 1)
        self.assertEqual(metrics["average_chunk_time"], 0.0)  # No successful chunks

    def test_multiple_chunk_processing(self):
        """Test tracking multiple chunk processing operations."""
        # Process 3 successful chunks
        for i in range(3):
            start_time = self.tracker.start_chunk_processing()
            time.sleep(0.01)
            self.tracker.end_chunk_processing_success(start_time)
        
        # Process 1 failed chunk
        start_time = self.tracker.start_chunk_processing()
        time.sleep(0.01)
        self.tracker.end_chunk_processing_failure(start_time)
        
        metrics = self.tracker.get_performance_metrics()
        self.assertEqual(metrics["total_chunks_processed"], 4)
        self.assertEqual(metrics["successful_generations"], 3)
        self.assertEqual(metrics["failed_generations"], 1)
        self.assertGreater(metrics["average_chunk_time"], 0)

    def test_get_performance_summary(self):
        """Test getting performance summary."""
        # Process some chunks
        start_time = self.tracker.start_chunk_processing()
        time.sleep(0.01)
        self.tracker.end_chunk_processing_success(start_time)
        
        start_time = self.tracker.start_chunk_processing()
        time.sleep(0.01)
        self.tracker.end_chunk_processing_failure(start_time)
        
        summary = self.tracker.get_performance_summary(1.5)
        
        self.assertEqual(summary["total_time_seconds"], 1.5)
        self.assertEqual(summary["chunks_processed"], 2)
        self.assertEqual(summary["chunks_successful"], 1)
        self.assertEqual(summary["chunks_failed"], 1)
        self.assertEqual(summary["success_rate_percent"], 50.0)
        self.assertGreater(summary["average_chunk_time_seconds"], 0)

    def test_get_chunk_performance_metrics(self):
        """Test getting chunk-specific performance metrics."""
        chunk_time = 2.5
        java_code_length = 1000
        mappings_count = 15
        
        metrics = self.tracker.get_chunk_performance_metrics(
            chunk_time, java_code_length, mappings_count
        )
        
        self.assertEqual(metrics["chunk_processing_time_seconds"], 2.5)
        self.assertEqual(metrics["java_code_length"], 1000)
        self.assertEqual(metrics["mappings_count"], 15)
        self.assertEqual(metrics["code_generation_rate_chars_per_second"], 400.0)

    def test_get_partial_performance_metrics(self):
        """Test getting partial performance metrics."""
        metrics = self.tracker.get_partial_performance_metrics(5.0, 3)
        
        self.assertEqual(metrics["partial_time_seconds"], 5.0)
        self.assertEqual(metrics["iterations_completed"], 3)
        self.assertEqual(metrics["average_iteration_time"], 5.0 / 3)

    def test_get_failure_performance_metrics(self):
        """Test getting failure performance metrics."""
        # Process some chunks first
        start_time = self.tracker.start_chunk_processing()
        self.tracker.end_chunk_processing_success(start_time)
        
        metrics = self.tracker.get_failure_performance_metrics(3.0)
        
        self.assertEqual(metrics["failed_after_seconds"], 3.0)
        self.assertEqual(metrics["chunks_processed_before_failure"], 1)
        self.assertEqual(metrics["successful_chunks_before_failure"], 1)

    def test_log_performance_metrics(self):
        """Test logging performance metrics."""
        # Process some chunks to have data to log
        start_time = self.tracker.start_chunk_processing()
        self.tracker.end_chunk_processing_success(start_time)

        # Test that the method runs without error
        # (Actual logging verification is difficult to test reliably)
        try:
            self.tracker.log_performance_metrics()
            # If we get here without exception, the test passes
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"log_performance_metrics raised an exception: {e}")

    def test_log_delegation_performance(self):
        """Test logging delegation performance."""
        start_time = time.time() - 0.1  # Simulate 0.1 second operation

        # Test that the method runs without error
        # (Actual logging verification is difficult to test reliably)
        try:
            self.tracker.log_delegation_performance("test_operation", start_time, True)
            self.tracker.log_delegation_performance("test_operation_failed", start_time, False)
            # If we get here without exception, the test passes
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"log_delegation_performance raised an exception: {e}")

    def test_reset_metrics(self):
        """Test resetting performance metrics."""
        # Process some chunks first
        start_time = self.tracker.start_chunk_processing()
        self.tracker.end_chunk_processing_success(start_time)
        
        # Verify metrics are not zero
        metrics = self.tracker.get_performance_metrics()
        self.assertGreater(metrics["total_chunks_processed"], 0)
        
        # Reset metrics
        self.tracker.reset_metrics()
        
        # Verify metrics are reset
        metrics = self.tracker.get_performance_metrics()
        self.assertEqual(metrics["total_chunks_processed"], 0)
        self.assertEqual(metrics["successful_generations"], 0)
        self.assertEqual(metrics["failed_generations"], 0)
        self.assertEqual(metrics["total_processing_time"], 0.0)
        self.assertEqual(metrics["average_chunk_time"], 0.0)

    def test_get_success_rate(self):
        """Test getting success rate."""
        # No chunks processed
        self.assertEqual(self.tracker.get_success_rate(), 0.0)
        
        # Process some chunks
        start_time = self.tracker.start_chunk_processing()
        self.tracker.end_chunk_processing_success(start_time)
        
        start_time = self.tracker.start_chunk_processing()
        self.tracker.end_chunk_processing_success(start_time)
        
        start_time = self.tracker.start_chunk_processing()
        self.tracker.end_chunk_processing_failure(start_time)
        
        # 2 successful out of 3 total = 66.67%
        success_rate = self.tracker.get_success_rate()
        self.assertAlmostEqual(success_rate, 66.67, places=1)

    def test_get_failure_rate(self):
        """Test getting failure rate."""
        # No chunks processed
        self.assertEqual(self.tracker.get_failure_rate(), 0.0)
        
        # Process some chunks
        start_time = self.tracker.start_chunk_processing()
        self.tracker.end_chunk_processing_success(start_time)
        
        start_time = self.tracker.start_chunk_processing()
        self.tracker.end_chunk_processing_failure(start_time)
        
        # 1 failed out of 2 total = 50%
        failure_rate = self.tracker.get_failure_rate()
        self.assertEqual(failure_rate, 50.0)

    def test_average_chunk_time_calculation(self):
        """Test average chunk time calculation with multiple successful chunks."""
        # Process chunks with known times
        times = [0.1, 0.2, 0.3]
        
        for expected_time in times:
            start_time = time.time() - expected_time
            self.tracker.end_chunk_processing_success(start_time)
        
        metrics = self.tracker.get_performance_metrics()
        # Average should be approximately (0.1 + 0.2 + 0.3) / 3 = 0.2
        self.assertAlmostEqual(metrics["average_chunk_time"], 0.2, places=1)


if __name__ == "__main__":
    unittest.main()
