"""
Tests for JavaDependencyAnalyzer.
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os
import json
from typing import Dict, Any

from src.plugins.targets.java_spring.tools.dependency_analyzer import JavaDependencyAnalyzer
from src.platform.agents.base_agent import AgentInput


class TestJavaDependencyAnalyzer(unittest.TestCase):
    """Test the JavaDependencyAnalyzer class."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.analyzer = JavaDependencyAnalyzer()

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.DependencyAnalyzer')
    def test_analyze_dependencies_success(self, mock_dependency_analyzer_class, mock_knowledge_db_class):
        """Test successful dependency analysis."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_dependency_analyzer = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db
        mock_dependency_analyzer_class.return_value = mock_dependency_analyzer

        # Configure mock returns
        mock_knowledge_db.get_database_statistics.return_value = {"total_chunks": 5}
        mock_knowledge_db.get_all_chunks.return_value = [
            {"program_id": "TEST", "chunk_name": "CHUNK1", "business_name": "Test Business"}
        ]
        mock_knowledge_db.get_analyzed_chunks.return_value = [
            {
                "program_id": "TEST",
                "chunk_name": "CHUNK1",
                "business_name": "Test Business",
                "business_description": "Test Description"
            }
        ]
        mock_dependency_analyzer.get_sorted_chunks_for_program.return_value = [
            {"chunk_name": "CHUNK1"}
        ]

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        # Create JSON directory and file for validation
        json_dir = os.path.join(self.temp_dir, "preprocessed", "cobol", "json")
        os.makedirs(json_dir, exist_ok=True)
        json_file = os.path.join(json_dir, "TEST.json")
        with open(json_file, 'w') as f:
            json.dump({"nodes": [{"name": "test"}]}, f)

        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})

        # Execute
        result = analyzer.analyze_dependencies(generation_state, input_data)

        # Verify result
        self.assertTrue(result["success"])
        self.assertIn("generation_order", result)
        self.assertIn("analyzed_chunks", result)
        self.assertEqual(len(result["generation_order"]), 1)

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    def test_analyze_dependencies_no_analyzed_chunks(self, mock_knowledge_db_class):
        """Test dependency analysis when no analyzed chunks are found."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db

        # Configure mock returns - no analyzed chunks
        mock_knowledge_db.get_database_statistics.return_value = {"total_chunks": 0}
        mock_knowledge_db.get_all_chunks.return_value = []
        mock_knowledge_db.get_analyzed_chunks.return_value = []

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})

        # Execute
        result = analyzer.analyze_dependencies(generation_state, input_data)

        # Verify result
        self.assertFalse(result["success"])
        self.assertIn("COBOL analysis required", result["error"])

    def test_validate_json_files_missing_directory(self):
        """Test JSON file validation when directory is missing."""
        analyzed_chunks = [{"program_id": "TEST", "chunk_name": "CHUNK1"}]

        result = self.analyzer._validate_json_files(analyzed_chunks, self.temp_dir)

        self.assertFalse(result["success"])
        self.assertIn("COBOL preprocessing required", result["error"])

    def test_validate_json_files_missing_files(self):
        """Test JSON file validation when files are missing."""
        # Create JSON directory but no files
        json_dir = os.path.join(self.temp_dir, "preprocessed", "cobol", "json")
        os.makedirs(json_dir, exist_ok=True)

        analyzed_chunks = [{"program_id": "TEST", "chunk_name": "CHUNK1"}]

        result = self.analyzer._validate_json_files(analyzed_chunks, self.temp_dir)

        self.assertFalse(result["success"])
        self.assertIn("missing_files", result)
        self.assertIn("TEST.json", result["missing_files"])

    def test_validate_json_files_invalid_content(self):
        """Test JSON file validation with invalid content."""
        # Create JSON directory and invalid file
        json_dir = os.path.join(self.temp_dir, "preprocessed", "cobol", "json")
        os.makedirs(json_dir, exist_ok=True)
        json_file = os.path.join(json_dir, "TEST.json")
        with open(json_file, 'w') as f:
            f.write("invalid json")

        analyzed_chunks = [{"program_id": "TEST", "chunk_name": "CHUNK1"}]

        result = self.analyzer._validate_json_files(analyzed_chunks, self.temp_dir)

        self.assertFalse(result["success"])
        self.assertIn("invalid_files", result)

    def test_validate_json_files_empty_nodes(self):
        """Test JSON file validation with empty nodes array."""
        # Create JSON directory and file with empty nodes
        json_dir = os.path.join(self.temp_dir, "preprocessed", "cobol", "json")
        os.makedirs(json_dir, exist_ok=True)
        json_file = os.path.join(json_dir, "TEST.json")
        with open(json_file, 'w') as f:
            json.dump({"nodes": []}, f)

        analyzed_chunks = [{"program_id": "TEST", "chunk_name": "CHUNK1"}]

        result = self.analyzer._validate_json_files(analyzed_chunks, self.temp_dir)

        self.assertFalse(result["success"])
        self.assertIn("invalid_files", result)

    def test_validate_json_files_success(self):
        """Test successful JSON file validation."""
        # Create JSON directory and valid file
        json_dir = os.path.join(self.temp_dir, "preprocessed", "cobol", "json")
        os.makedirs(json_dir, exist_ok=True)
        json_file = os.path.join(json_dir, "TEST.json")
        with open(json_file, 'w') as f:
            json.dump({"nodes": [{"name": "test"}]}, f)

        analyzed_chunks = [{"program_id": "TEST", "chunk_name": "CHUNK1"}]

        result = self.analyzer._validate_json_files(analyzed_chunks, self.temp_dir)

        self.assertTrue(result["success"])
        self.assertIn("validated_programs", result)
        self.assertIn("TEST", result["validated_programs"])

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    def test_get_chunk_dependencies_success(self, mock_knowledge_db_class):
        """Test getting chunk dependencies successfully."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db

        # Configure mock returns
        mock_knowledge_db.get_chunk_by_name.return_value = {
            "analysis_status": "complete",
            "business_name": "Test Business Function",
            "business_description": "Test Description",
            "chunk_name": "CHUNK2"
        }

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        chunk_info = {"program_id": "TEST", "chunk_name": "CHUNK1"}
        generation_state = {
            "dependency_graph": {
                "TEST.CHUNK1": ["TEST.CHUNK2"]
            }
        }

        # Execute
        result = analyzer.get_chunk_dependencies(chunk_info, generation_state)

        # Verify result
        self.assertIn("dependent_services", result)
        self.assertIn("input_types", result)
        self.assertIn("output_types", result)
        self.assertEqual(len(result["dependent_services"]), 1)

        # Verify dependency service details
        service = result["dependent_services"][0]
        self.assertEqual(service["class_name"], "TestBusinessFunctionService")
        self.assertEqual(service["method_name"], "testBusinessFunction")
        self.assertEqual(service["business_name"], "Test Business Function")
        self.assertEqual(service["cobol_chunk"], "CHUNK2")
        self.assertEqual(service["package"], "com.generated.cobol.service")

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    def test_get_chunk_dependencies_missing_dependency_graph(self, mock_knowledge_db_class):
        """Test get_chunk_dependencies raises error when dependency_graph is missing."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        chunk_info = {"program_id": "TEST", "chunk_name": "CHUNK1"}
        generation_state = {}  # Missing dependency_graph

        # Execute and verify exception
        with self.assertRaises(ValueError) as context:
            analyzer.get_chunk_dependencies(chunk_info, generation_state)

        self.assertIn("dependency_graph not found", str(context.exception))

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    def test_get_chunk_dependencies_empty_dependency_graph(self, mock_knowledge_db_class):
        """Test get_chunk_dependencies raises error when dependency_graph is empty."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        chunk_info = {"program_id": "TEST", "chunk_name": "CHUNK1"}
        generation_state = {"dependency_graph": {}}  # Empty dependency_graph

        # Execute and verify exception
        with self.assertRaises(ValueError) as context:
            analyzer.get_chunk_dependencies(chunk_info, generation_state)

        self.assertIn("dependency_graph is empty", str(context.exception))

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    def test_get_chunk_dependencies_no_dependencies_for_chunk(self, mock_knowledge_db_class):
        """Test get_chunk_dependencies when chunk has no dependencies."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        chunk_info = {"program_id": "TEST", "chunk_name": "CHUNK1"}
        generation_state = {
            "dependency_graph": {
                "TEST.CHUNK2": ["TEST.CHUNK3"]  # Different chunk has dependencies
            }
        }

        # Execute
        result = analyzer.get_chunk_dependencies(chunk_info, generation_state)

        # Verify result - should return empty dependencies
        self.assertIn("dependent_services", result)
        self.assertIn("input_types", result)
        self.assertIn("output_types", result)
        self.assertEqual(len(result["dependent_services"]), 0)

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    def test_get_chunk_dependencies_dependency_not_found_in_db(self, mock_knowledge_db_class):
        """Test get_chunk_dependencies when dependency chunk is not found in database."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db

        # Configure mock to return None (chunk not found)
        mock_knowledge_db.get_chunk_by_name.return_value = None

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        chunk_info = {"program_id": "TEST", "chunk_name": "CHUNK1"}
        generation_state = {
            "dependency_graph": {
                "TEST.CHUNK1": ["TEST.CHUNK2"]
            }
        }

        # Execute
        result = analyzer.get_chunk_dependencies(chunk_info, generation_state)

        # Verify result - should return empty dependencies since chunk not found
        self.assertEqual(len(result["dependent_services"]), 0)

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    def test_get_chunk_dependencies_incomplete_analysis(self, mock_knowledge_db_class):
        """Test get_chunk_dependencies when dependency chunk analysis is incomplete."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db

        # Configure mock to return chunk with incomplete analysis
        mock_knowledge_db.get_chunk_by_name.return_value = {
            "analysis_status": "pending",  # Not complete
            "business_name": "Test Business Function",
            "business_description": "Test Description"
        }

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        chunk_info = {"program_id": "TEST", "chunk_name": "CHUNK1"}
        generation_state = {
            "dependency_graph": {
                "TEST.CHUNK1": ["TEST.CHUNK2"]
            }
        }

        # Execute
        result = analyzer.get_chunk_dependencies(chunk_info, generation_state)

        # Verify result - should return empty dependencies since analysis incomplete
        self.assertEqual(len(result["dependent_services"]), 0)

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    def test_get_chunk_dependencies_no_business_name(self, mock_knowledge_db_class):
        """Test get_chunk_dependencies when dependency chunk has no business name."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db

        # Configure mock to return chunk without business name
        mock_knowledge_db.get_chunk_by_name.return_value = {
            "analysis_status": "complete",
            "business_name": "",  # Empty business name
            "business_description": "Test Description"
        }

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        chunk_info = {"program_id": "TEST", "chunk_name": "CHUNK1"}
        generation_state = {
            "dependency_graph": {
                "TEST.CHUNK1": ["TEST.CHUNK2"]
            }
        }

        # Execute
        result = analyzer.get_chunk_dependencies(chunk_info, generation_state)

        # Verify result - should return empty dependencies since no business name
        self.assertEqual(len(result["dependent_services"]), 0)

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.DependencyAnalyzer')
    def test_build_program_dependency_graph_success(self, mock_dependency_analyzer_class):
        """Test building dependency graph for a program successfully."""
        # Setup mocks
        mock_dependency_analyzer = Mock()
        mock_dependency_analyzer_class.return_value = mock_dependency_analyzer

        # Configure mock to return dependencies
        mock_dependency_analyzer._build_dependency_graph.return_value = (
            {"CHUNK1": {"CHUNK2"}, "CHUNK2": set()},  # dependencies
            {"CHUNK1": "uuid1", "CHUNK2": "uuid2"}     # name_to_uuid
        )

        # Create JSON directory and file
        json_dir = os.path.join(self.temp_dir, "preprocessed", "cobol", "json")
        os.makedirs(json_dir, exist_ok=True)
        json_file = os.path.join(json_dir, "TEST.json")
        with open(json_file, 'w') as f:
            json.dump({
                "nodes": [
                    {"uuid": "uuid1", "paragraph_name": "CHUNK1"},
                    {"uuid": "uuid2", "paragraph_name": "CHUNK2"}
                ]
            }, f)

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        analyzed_chunks = [
            {"program_id": "TEST", "chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK1"},
            {"program_id": "TEST", "chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK2"}
        ]

        # Execute
        result = analyzer._build_program_dependency_graph("TEST", self.temp_dir, analyzed_chunks)

        # Verify result - should map short names to full chunk names
        self.assertIn("TEST.TEST_PROC_UNNAMED-SECTION_SECT_CHUNK1", result)
        self.assertEqual(result["TEST.TEST_PROC_UNNAMED-SECTION_SECT_CHUNK1"], ["TEST.TEST_PROC_UNNAMED-SECTION_SECT_CHUNK2"])

    def test_build_program_dependency_graph_missing_json(self):
        """Test building dependency graph when JSON file is missing."""
        analyzed_chunks = [{"program_id": "TEST", "chunk_name": "CHUNK1"}]

        # Execute
        result = self.analyzer._build_program_dependency_graph("TEST", self.temp_dir, analyzed_chunks)

        # Verify result - should return empty dict
        self.assertEqual(result, {})

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.DependencyAnalyzer')
    def test_analyze_dependencies_with_dependency_graph(self, mock_dependency_analyzer_class, mock_knowledge_db_class):
        """Test analyze_dependencies builds proper dependency graph."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_dependency_analyzer = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db
        mock_dependency_analyzer_class.return_value = mock_dependency_analyzer

        # Configure mock returns
        mock_knowledge_db.get_database_statistics.return_value = {"total_chunks": 2}
        mock_knowledge_db.get_all_chunks.return_value = [
            {"program_id": "TEST", "chunk_name": "CHUNK1", "business_name": "Test Business"}
        ]
        mock_knowledge_db.get_analyzed_chunks.return_value = [
            {
                "program_id": "TEST",
                "chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK1",
                "business_name": "Test Business",
                "business_description": "Test Description"
            },
            {
                "program_id": "TEST",
                "chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK2",
                "business_name": "Another Business",
                "business_description": "Another Description"
            }
        ]
        mock_dependency_analyzer.get_sorted_chunks_for_program.return_value = [
            {"chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK1"},
            {"chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK2"}
        ]
        mock_dependency_analyzer._build_dependency_graph.return_value = (
            {"CHUNK1": {"CHUNK2"}},  # dependencies
            {"CHUNK1": "uuid1", "CHUNK2": "uuid2"}  # name_to_uuid
        )

        # Create JSON directory and file for validation
        json_dir = os.path.join(self.temp_dir, "preprocessed", "cobol", "json")
        os.makedirs(json_dir, exist_ok=True)
        json_file = os.path.join(json_dir, "TEST.json")
        with open(json_file, 'w') as f:
            json.dump({
                "nodes": [
                    {"uuid": "uuid1", "paragraph_name": "CHUNK1"},
                    {"uuid": "uuid2", "paragraph_name": "CHUNK2"}
                ]
            }, f)

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})

        # Execute
        result = analyzer.analyze_dependencies(generation_state, input_data)

        # Verify result
        self.assertTrue(result["success"])
        self.assertIn("dependency_graph", result)
        self.assertIn("generation_order", result)

        # Verify dependency graph is not empty
        dependency_graph = result["dependency_graph"]
        self.assertGreater(len(dependency_graph), 0)

        # Verify generation order
        generation_order = result["generation_order"]
        self.assertEqual(len(generation_order), 2)

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.DependencyAnalyzer')
    def test_analyze_dependencies_updates_generation_state(self, mock_dependency_analyzer_class, mock_knowledge_db_class):
        """Test that analyze_dependencies properly updates the generation_state parameter."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_dependency_analyzer = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db
        mock_dependency_analyzer_class.return_value = mock_dependency_analyzer

        # Configure mock returns
        mock_knowledge_db.get_database_statistics.return_value = {"total_chunks": 1}
        mock_knowledge_db.get_all_chunks.return_value = [
            {"program_id": "TEST", "chunk_name": "CHUNK1", "business_name": "Test Business"}
        ]
        mock_knowledge_db.get_analyzed_chunks.return_value = [
            {
                "program_id": "TEST",
                "chunk_name": "CHUNK1",
                "business_name": "Test Business",
                "business_description": "Test Description"
            }
        ]
        mock_dependency_analyzer.get_sorted_chunks_for_program.return_value = [
            {"chunk_name": "CHUNK1"}
        ]
        mock_dependency_analyzer._build_dependency_graph.return_value = (
            {"CHUNK1": set()},  # dependencies
            {"CHUNK1": "uuid1"}  # name_to_uuid
        )

        # Create JSON directory and file for validation
        json_dir = os.path.join(self.temp_dir, "preprocessed", "cobol", "json")
        os.makedirs(json_dir, exist_ok=True)
        json_file = os.path.join(json_dir, "TEST.json")
        with open(json_file, 'w') as f:
            json.dump({
                "nodes": [
                    {"uuid": "uuid1", "paragraph_name": "CHUNK1"}
                ]
            }, f)

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        # Start with empty generation_state
        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})

        # Execute
        result = analyzer.analyze_dependencies(generation_state, input_data)

        # Verify result is successful
        self.assertTrue(result["success"])

        # Verify that generation_state was updated with the dependency graph
        self.assertIn("dependency_graph", generation_state)
        self.assertIn("generation_order", generation_state)
        self.assertIn("analyzed_chunks", generation_state)

        # Verify the dependency graph is accessible for get_chunk_dependencies
        self.assertIsInstance(generation_state["dependency_graph"], dict)
        self.assertIsInstance(generation_state["generation_order"], list)
        self.assertIsInstance(generation_state["analyzed_chunks"], list)

    def test_convert_business_name_to_java_class(self):
        """Test business name to Java class name conversion."""
        result = self.analyzer._convert_business_name_to_java_class("Customer Account Management")
        self.assertEqual(result, "CustomerAccountManagementService")

        result = self.analyzer._convert_business_name_to_java_class("process-payment")
        self.assertEqual(result, "ProcessPaymentService")

    def test_convert_business_name_to_java_method(self):
        """Test business name to Java method name conversion."""
        result = self.analyzer._convert_business_name_to_java_method("Customer Account Management")
        self.assertEqual(result, "customerAccountManagement")

        result = self.analyzer._convert_business_name_to_java_method("process-payment")
        self.assertEqual(result, "processPayment")

        result = self.analyzer._convert_business_name_to_java_method("")
        self.assertEqual(result, "process")

    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.KnowledgeDatabase')
    @patch('src.plugins.targets.java_spring.tools.dependency_analyzer.DependencyAnalyzer')
    def test_integration_analyze_then_get_dependencies(self, mock_dependency_analyzer_class, mock_knowledge_db_class):
        """Integration test: analyze_dependencies followed by get_chunk_dependencies should work."""
        # Setup mocks
        mock_knowledge_db = Mock()
        mock_dependency_analyzer = Mock()
        mock_knowledge_db_class.return_value = mock_knowledge_db
        mock_dependency_analyzer_class.return_value = mock_dependency_analyzer

        # Configure mock returns for analyze_dependencies
        mock_knowledge_db.get_database_statistics.return_value = {"total_chunks": 2}
        mock_knowledge_db.get_all_chunks.return_value = [
            {"program_id": "TEST", "chunk_name": "CHUNK1", "business_name": "Test Business"}
        ]
        mock_knowledge_db.get_analyzed_chunks.return_value = [
            {
                "program_id": "TEST",
                "chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK1",
                "business_name": "Test Business Function",
                "business_description": "Test Description"
            },
            {
                "program_id": "TEST",
                "chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK2",
                "business_name": "Another Business Function",
                "business_description": "Another Description"
            }
        ]
        mock_dependency_analyzer.get_sorted_chunks_for_program.return_value = [
            {"chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK1"},
            {"chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK2"}
        ]
        mock_dependency_analyzer._build_dependency_graph.return_value = (
            {"CHUNK1": {"CHUNK2"}, "CHUNK2": set()},  # CHUNK1 depends on CHUNK2
            {"CHUNK1": "uuid1", "CHUNK2": "uuid2"}
        )

        # Configure mock returns for get_chunk_dependencies
        def mock_get_chunk_by_name(program_id, chunk_name):
            if chunk_name == "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK2":
                return {
                    "analysis_status": "complete",
                    "business_name": "Another Business Function",
                    "business_description": "Another Description",
                    "chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK2"
                }
            return None

        mock_knowledge_db.get_chunk_by_name.side_effect = mock_get_chunk_by_name

        # Create JSON directory and file for validation
        json_dir = os.path.join(self.temp_dir, "preprocessed", "cobol", "json")
        os.makedirs(json_dir, exist_ok=True)
        json_file = os.path.join(json_dir, "TEST.json")
        with open(json_file, 'w') as f:
            json.dump({
                "nodes": [
                    {"uuid": "uuid1", "paragraph_name": "CHUNK1"},
                    {"uuid": "uuid2", "paragraph_name": "CHUNK2"}
                ]
            }, f)

        # Create new analyzer instance to use mocked classes
        analyzer = JavaDependencyAnalyzer()

        # Step 1: Analyze dependencies
        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})

        analyze_result = analyzer.analyze_dependencies(generation_state, input_data)

        # Verify analyze_dependencies succeeded
        self.assertTrue(analyze_result["success"])
        self.assertIn("dependency_graph", generation_state)

        # Step 2: Get chunk dependencies using the updated generation_state
        chunk_info = {"program_id": "TEST", "chunk_name": "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK1"}

        dependencies_result = analyzer.get_chunk_dependencies(chunk_info, generation_state)

        # Verify get_chunk_dependencies succeeded and found dependencies
        self.assertIn("dependent_services", dependencies_result)
        self.assertEqual(len(dependencies_result["dependent_services"]), 1)

        # Verify the dependency details
        service = dependencies_result["dependent_services"][0]
        self.assertEqual(service["class_name"], "AnotherBusinessFunctionService")
        self.assertEqual(service["method_name"], "anotherBusinessFunction")
        self.assertEqual(service["business_name"], "Another Business Function")
        self.assertEqual(service["cobol_chunk"], "TEST_PROC_UNNAMED-SECTION_SECT_CHUNK2")


if __name__ == "__main__":
    unittest.main()
