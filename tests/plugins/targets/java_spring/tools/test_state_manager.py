"""
Tests for JavaStateManager.
"""
import unittest
import tempfile
import os
import json
import datetime
from typing import Dict, Any

from src.plugins.targets.java_spring.tools.state_manager import JavaStateManager


class TestJavaStateManager(unittest.TestCase):
    """Test the JavaStateManager class."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.state_manager = JavaStateManager()

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_load_or_initialize_generation_state_new(self):
        """Test initializing a new generation state."""
        result = self.state_manager.load_or_initialize_generation_state(self.temp_dir)

        # Verify initial state structure
        self.assertFalse(result["completed"])
        self.assertEqual(result["current_phase"], "analysis")
        self.assertEqual(result["analyzed_programs"], [])
        self.assertIsNone(result["dependency_graph"])
        self.assertEqual(result["generation_order"], [])
        self.assertEqual(result["generated_chunks"], [])
        self.assertEqual(result["failed_chunks"], [])
        self.assertEqual(result["current_chunk_index"], 0)
        self.assertEqual(result["java_classes_mapping"], {})
        self.assertEqual(result["iteration_count"], 0)
        self.assertIn("last_updated", result)

    def test_load_or_initialize_generation_state_existing(self):
        """Test loading an existing generation state."""
        # Create existing state file
        existing_state = {
            "completed": False,
            "current_phase": "generation",
            "generated_chunks": ["TEST.CHUNK1"],
            "current_chunk_index": 1
        }
        state_file = os.path.join(self.temp_dir, "generation_state.json")
        with open(state_file, 'w') as f:
            json.dump(existing_state, f)

        result = self.state_manager.load_or_initialize_generation_state(self.temp_dir)

        # Verify loaded state
        self.assertEqual(result["current_phase"], "generation")
        self.assertEqual(result["generated_chunks"], ["TEST.CHUNK1"])
        self.assertEqual(result["current_chunk_index"], 1)

    def test_load_or_initialize_generation_state_corrupted_file(self):
        """Test handling corrupted state file."""
        # Create corrupted state file
        state_file = os.path.join(self.temp_dir, "generation_state.json")
        with open(state_file, 'w') as f:
            f.write("invalid json")

        result = self.state_manager.load_or_initialize_generation_state(self.temp_dir)

        # Should initialize new state when file is corrupted
        self.assertEqual(result["current_phase"], "analysis")
        self.assertEqual(result["current_chunk_index"], 0)

    def test_save_generation_state(self):
        """Test saving generation state."""
        state = {
            "completed": False,
            "current_phase": "generation",
            "generated_chunks": ["TEST.CHUNK1"]
        }

        self.state_manager.save_generation_state(state, self.temp_dir)

        # Verify file was created and contains correct data
        state_file = os.path.join(self.temp_dir, "generation_state.json")
        self.assertTrue(os.path.exists(state_file))

        with open(state_file, 'r') as f:
            saved_state = json.load(f)

        self.assertEqual(saved_state["current_phase"], "generation")
        self.assertEqual(saved_state["generated_chunks"], ["TEST.CHUNK1"])

    def test_update_generation_state_analyze_dependencies(self):
        """Test updating state after analyze_dependencies action."""
        state = {"iteration_count": 0}
        action = {"type": "analyze_dependencies"}
        result = {
            "success": True,
            "dependency_graph": {"TEST.CHUNK1": ["TEST.CHUNK2"]},
            "generation_order": [{"program_id": "TEST", "chunk_name": "CHUNK1"}],
            "analyzed_chunks": [{"program_id": "TEST", "chunk_name": "CHUNK1"}]
        }

        self.state_manager.update_generation_state(state, action, result)

        self.assertEqual(state["dependency_graph"], result["dependency_graph"])
        self.assertEqual(state["generation_order"], result["generation_order"])
        self.assertEqual(state["analyzed_programs"], result["analyzed_chunks"])
        self.assertEqual(state["iteration_count"], 1)
        self.assertIn("last_updated", state)

    def test_update_generation_state_generate_chunk_success(self):
        """Test updating state after successful chunk generation."""
        state = {
            "iteration_count": 0,
            "generated_chunks": [],
            "java_classes_mapping": {},
            "current_chunk_index": 0
        }
        action = {
            "type": "generate_chunk",
            "chunk_info": {"program_id": "TEST", "chunk_name": "CHUNK1"}
        }
        result = {
            "success": True,
            "java_class_info": {"class_name": "TestService"},
            "mappings": {"cobol_var": "javaVar"}
        }

        self.state_manager.update_generation_state(state, action, result)

        self.assertIn("TEST.CHUNK1", state["generated_chunks"])
        self.assertIn("TEST.CHUNK1", state["java_classes_mapping"])
        self.assertEqual(state["current_chunk_index"], 1)
        self.assertEqual(state["iteration_count"], 1)

    def test_update_generation_state_generate_chunk_failure(self):
        """Test updating state after failed chunk generation."""
        state = {
            "iteration_count": 0,
            "failed_chunks": [],
            "current_chunk_index": 0
        }
        action = {
            "type": "generate_chunk",
            "chunk_info": {"program_id": "TEST", "chunk_name": "CHUNK1"}
        }
        result = {
            "success": False,
            "error": "Generation failed"
        }

        self.state_manager.update_generation_state(state, action, result)

        self.assertEqual(len(state["failed_chunks"]), 1)
        self.assertEqual(state["failed_chunks"][0]["chunk_id"], "TEST.CHUNK1")
        self.assertEqual(state["failed_chunks"][0]["error"], "Generation failed")
        self.assertEqual(state["current_chunk_index"], 1)

    def test_update_generation_state_complete(self):
        """Test updating state after complete action."""
        state = {"completed": False}
        action = {"type": "complete"}
        result = {"success": True}

        self.state_manager.update_generation_state(state, action, result)

        self.assertTrue(state["completed"])

    def test_transition_to_generation(self):
        """Test transitioning to generation phase."""
        state = {"current_phase": "analysis"}

        result = self.state_manager.transition_to_generation(state)

        self.assertTrue(result["success"])
        self.assertEqual(result["phase"], "generation")
        self.assertEqual(state["current_phase"], "generation")

    def test_transition_to_finalization(self):
        """Test transitioning to finalization phase."""
        state = {"current_phase": "generation"}

        result = self.state_manager.transition_to_finalization(state)

        self.assertTrue(result["success"])
        self.assertEqual(result["phase"], "finalization")
        self.assertEqual(state["current_phase"], "finalization")

    def test_mark_finalization_complete(self):
        """Test marking finalization as complete."""
        state = {"current_phase": "finalization"}

        result = self.state_manager.mark_finalization_complete(state)

        self.assertTrue(result["success"])
        self.assertEqual(result["phase"], "finalization")
        self.assertTrue(result["finalization_complete"])
        self.assertTrue(state["finalization_complete"])

    def test_should_restart_due_to_context(self):
        """Test context restart check."""
        # Currently returns False - placeholder implementation
        result = self.state_manager.should_restart_due_to_context()
        self.assertFalse(result)

    def test_get_generation_summary(self):
        """Test getting generation summary."""
        state = {
            "current_phase": "generation",
            "completed": False,
            "generation_order": [{"chunk": 1}, {"chunk": 2}],
            "generated_chunks": ["TEST.CHUNK1"],
            "failed_chunks": [],
            "current_chunk_index": 1,
            "iteration_count": 5,
            "last_updated": "2023-01-01T00:00:00",
            "finalization_complete": False
        }

        summary = self.state_manager.get_generation_summary(state)

        self.assertEqual(summary["current_phase"], "generation")
        self.assertFalse(summary["completed"])
        self.assertEqual(summary["total_chunks"], 2)
        self.assertEqual(summary["generated_chunks"], 1)
        self.assertEqual(summary["failed_chunks"], 0)
        self.assertEqual(summary["current_chunk_index"], 1)
        self.assertEqual(summary["iteration_count"], 5)
        self.assertEqual(summary["last_updated"], "2023-01-01T00:00:00")
        self.assertFalse(summary["finalization_complete"])

    def test_is_generation_complete(self):
        """Test checking if generation is complete."""
        state_incomplete = {"completed": False}
        state_complete = {"completed": True}

        self.assertFalse(self.state_manager.is_generation_complete(state_incomplete))
        self.assertTrue(self.state_manager.is_generation_complete(state_complete))

    def test_get_next_chunk_to_generate(self):
        """Test getting next chunk to generate."""
        state = {
            "current_chunk_index": 1,
            "generation_order": [
                {"program_id": "TEST", "chunk_name": "CHUNK1"},
                {"program_id": "TEST", "chunk_name": "CHUNK2"},
                {"program_id": "TEST", "chunk_name": "CHUNK3"}
            ]
        }

        # Should return second chunk (index 1)
        next_chunk = self.state_manager.get_next_chunk_to_generate(state)
        self.assertEqual(next_chunk["chunk_name"], "CHUNK2")

        # Test when no more chunks
        state["current_chunk_index"] = 3
        next_chunk = self.state_manager.get_next_chunk_to_generate(state)
        self.assertIsNone(next_chunk)

    def test_get_failed_chunks(self):
        """Test getting failed chunks."""
        state = {
            "failed_chunks": [
                {"chunk_id": "TEST.CHUNK1", "error": "Error 1"},
                {"chunk_id": "TEST.CHUNK2", "error": "Error 2"}
            ]
        }

        failed_chunks = self.state_manager.get_failed_chunks(state)
        self.assertEqual(len(failed_chunks), 2)
        self.assertEqual(failed_chunks[0]["chunk_id"], "TEST.CHUNK1")

    def test_get_generated_chunks(self):
        """Test getting generated chunks."""
        state = {
            "generated_chunks": ["TEST.CHUNK1", "TEST.CHUNK2"]
        }

        generated_chunks = self.state_manager.get_generated_chunks(state)
        self.assertEqual(len(generated_chunks), 2)
        self.assertIn("TEST.CHUNK1", generated_chunks)

    def test_get_java_classes_mapping(self):
        """Test getting Java classes mapping."""
        state = {
            "java_classes_mapping": {
                "TEST.CHUNK1": {"class_name": "TestService1"},
                "TEST.CHUNK2": {"class_name": "TestService2"}
            }
        }

        mapping = self.state_manager.get_java_classes_mapping(state)
        self.assertEqual(len(mapping), 2)
        self.assertEqual(mapping["TEST.CHUNK1"]["class_name"], "TestService1")


if __name__ == "__main__":
    unittest.main()
