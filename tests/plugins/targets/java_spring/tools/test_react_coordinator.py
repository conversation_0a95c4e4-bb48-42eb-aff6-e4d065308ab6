"""
Tests for JavaReactCoordinator.
"""
import unittest
from unittest.mock import Mock, patch

from src.plugins.targets.java_spring.tools.react_coordinator import JavaReactCoordinator
from src.platform.agents.base_agent import AgentInput


class TestJavaReactCoordinator(unittest.TestCase):
    """Test the JavaReactCoordinator class."""

    def setUp(self):
        """Set up test fixtures."""
        self.coordinator = JavaReactCoordinator()

    def test_analyze_and_decide_action_analysis_phase_no_order(self):
        """Test action decision in analysis phase when no generation order exists."""
        generation_state = {
            "current_phase": "analysis",
            "generation_order": []
        }
        input_data = AgentInput(working_directory="/test", knowledge_base={})

        action = self.coordinator.analyze_and_decide_action(generation_state, input_data)

        self.assertEqual(action["type"], "analyze_dependencies")
        self.assertIn("Analyze COBOL chunks", action["description"])

    def test_analyze_and_decide_action_analysis_phase_with_order(self):
        """Test action decision in analysis phase when generation order exists."""
        generation_state = {
            "current_phase": "analysis",
            "generation_order": [{"program_id": "TEST", "chunk_name": "CHUNK1"}]
        }
        input_data = AgentInput(working_directory="/test", knowledge_base={})

        action = self.coordinator.analyze_and_decide_action(generation_state, input_data)

        self.assertEqual(action["type"], "transition_to_generation")
        self.assertIn("Move to code generation", action["description"])

    def test_analyze_and_decide_action_generation_phase_with_chunks(self):
        """Test action decision in generation phase with chunks to generate."""
        generation_state = {
            "current_phase": "generation",
            "current_chunk_index": 0,
            "generation_order": [
                {"program_id": "TEST", "chunk_name": "CHUNK1"},
                {"program_id": "TEST", "chunk_name": "CHUNK2"}
            ]
        }
        input_data = AgentInput(working_directory="/test", knowledge_base={})

        action = self.coordinator.analyze_and_decide_action(generation_state, input_data)

        self.assertEqual(action["type"], "generate_chunk")
        self.assertIn("Generate Java code for chunk TEST.CHUNK1", action["description"])
        self.assertEqual(action["chunk_info"]["program_id"], "TEST")
        self.assertEqual(action["chunk_info"]["chunk_name"], "CHUNK1")

    def test_analyze_and_decide_action_generation_phase_no_chunks(self):
        """Test action decision in generation phase with no more chunks."""
        generation_state = {
            "current_phase": "generation",
            "current_chunk_index": 2,
            "generation_order": [
                {"program_id": "TEST", "chunk_name": "CHUNK1"},
                {"program_id": "TEST", "chunk_name": "CHUNK2"}
            ]
        }
        input_data = AgentInput(working_directory="/test", knowledge_base={})

        action = self.coordinator.analyze_and_decide_action(generation_state, input_data)

        self.assertEqual(action["type"], "transition_to_finalization")
        self.assertIn("Move to project finalization", action["description"])

    def test_analyze_and_decide_action_finalization_phase_not_complete(self):
        """Test action decision in finalization phase when not complete."""
        generation_state = {
            "current_phase": "finalization",
            "finalization_complete": False
        }
        input_data = AgentInput(working_directory="/test", knowledge_base={})

        action = self.coordinator.analyze_and_decide_action(generation_state, input_data)

        self.assertEqual(action["type"], "finalize_project")
        self.assertIn("Finalize Java project", action["description"])

    def test_analyze_and_decide_action_finalization_phase_complete(self):
        """Test action decision in finalization phase when complete."""
        generation_state = {
            "current_phase": "finalization",
            "finalization_complete": True
        }
        input_data = AgentInput(working_directory="/test", knowledge_base={})

        action = self.coordinator.analyze_and_decide_action(generation_state, input_data)

        self.assertEqual(action["type"], "complete")
        self.assertIn("Mark generation as completed", action["description"])

    def test_analyze_and_decide_action_unknown_phase(self):
        """Test action decision with unknown phase."""
        generation_state = {
            "current_phase": "unknown_phase"
        }
        input_data = AgentInput(working_directory="/test", knowledge_base={})

        action = self.coordinator.analyze_and_decide_action(generation_state, input_data)

        self.assertEqual(action["type"], "error")
        self.assertIn("Unknown phase: unknown_phase", action["description"])

    def test_should_continue_generation_completed(self):
        """Test should continue when generation is completed."""
        generation_state = {"completed": True}
        
        result = self.coordinator.should_continue_generation(generation_state, 5, 50)
        
        self.assertFalse(result)

    def test_should_continue_generation_max_iterations(self):
        """Test should continue when max iterations reached."""
        generation_state = {"completed": False}
        
        result = self.coordinator.should_continue_generation(generation_state, 50, 50)
        
        self.assertFalse(result)

    def test_should_continue_generation_normal(self):
        """Test should continue under normal conditions."""
        generation_state = {"completed": False}
        
        result = self.coordinator.should_continue_generation(generation_state, 10, 50)
        
        self.assertTrue(result)

    def test_validate_action_result_success(self):
        """Test validating successful action result."""
        action = {"type": "generate_chunk"}
        result = {"success": True}
        
        is_valid = self.coordinator.validate_action_result(action, result)
        
        self.assertTrue(is_valid)

    def test_validate_action_result_critical_error(self):
        """Test validating action result with critical error."""
        action = {"type": "analyze_dependencies"}
        result = {
            "success": False,
            "error": "COBOL analysis required before Java generation"
        }
        
        is_valid = self.coordinator.validate_action_result(action, result)
        
        self.assertFalse(is_valid)

    def test_validate_action_result_non_critical_error(self):
        """Test validating action result with non-critical error."""
        action = {"type": "generate_chunk"}
        result = {
            "success": False,
            "error": "Some non-critical error"
        }
        
        is_valid = self.coordinator.validate_action_result(action, result)
        
        self.assertTrue(is_valid)

    def test_get_generation_progress(self):
        """Test getting generation progress."""
        generation_state = {
            "generation_order": [{"chunk": 1}, {"chunk": 2}, {"chunk": 3}],
            "current_chunk_index": 2,
            "generated_chunks": ["TEST.CHUNK1"],
            "failed_chunks": [],
            "current_phase": "generation",
            "completed": False
        }
        
        progress = self.coordinator.get_generation_progress(generation_state)
        
        self.assertEqual(progress["total_chunks"], 3)
        self.assertEqual(progress["current_index"], 2)
        self.assertEqual(progress["generated_chunks"], 1)
        self.assertEqual(progress["failed_chunks"], 0)
        self.assertAlmostEqual(progress["progress_percentage"], 66.67, places=1)
        self.assertEqual(progress["current_phase"], "generation")
        self.assertFalse(progress["completed"])

    def test_get_generation_progress_no_chunks(self):
        """Test getting generation progress with no chunks."""
        generation_state = {
            "generation_order": [],
            "current_chunk_index": 0,
            "generated_chunks": [],
            "failed_chunks": [],
            "current_phase": "analysis",
            "completed": False
        }
        
        progress = self.coordinator.get_generation_progress(generation_state)
        
        self.assertEqual(progress["total_chunks"], 0)
        self.assertEqual(progress["progress_percentage"], 0)

    def test_log_action_execution(self):
        """Test logging action execution."""
        action = {"type": "generate_chunk", "description": "Generate test chunk"}

        # Test that the method runs without error
        # (Actual logging verification is difficult to test reliably)
        try:
            self.coordinator.log_action_execution(action)
            # If we get here without exception, the test passes
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"log_action_execution raised an exception: {e}")

    def test_log_iteration_start(self):
        """Test logging iteration start."""
        generation_state = {
            "generation_order": [{"chunk": 1}, {"chunk": 2}],
            "current_chunk_index": 1,
            "generated_chunks": [],
            "failed_chunks": [],
            "current_phase": "generation",
            "completed": False
        }

        # Test that the method runs without error
        # (Actual logging verification is difficult to test reliably)
        try:
            self.coordinator.log_iteration_start(5, generation_state)
            # If we get here without exception, the test passes
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"log_iteration_start raised an exception: {e}")

    def test_log_generation_summary_completed(self):
        """Test logging generation summary for completed generation."""
        generation_state = {
            "completed": True,
            "generation_order": [{"chunk": 1}, {"chunk": 2}],
            "current_chunk_index": 2,
            "generated_chunks": ["TEST.CHUNK1", "TEST.CHUNK2"],
            "failed_chunks": [],
            "current_phase": "finalization"
        }

        # Test that the method runs without error
        # (Actual logging verification is difficult to test reliably)
        try:
            self.coordinator.log_generation_summary(generation_state, 5.5, 10)
            # If we get here without exception, the test passes
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"log_generation_summary raised an exception: {e}")

    def test_create_success_result(self):
        """Test creating success result."""
        generation_state = {
            "generation_order": [{"chunk": 1}, {"chunk": 2}],
            "current_chunk_index": 2,
            "generated_chunks": ["TEST.CHUNK1", "TEST.CHUNK2"],
            "failed_chunks": []
        }
        
        result = self.coordinator.create_success_result(generation_state, "/java/project", 5.5)
        
        self.assertTrue(result["success"])
        self.assertIn("Successfully generated", result["message"])
        self.assertEqual(result["artifacts"]["java_project_path"], "/java/project")
        self.assertEqual(result["performance_metrics"]["total_time_seconds"], 5.5)
        self.assertEqual(result["performance_metrics"]["chunks_processed"], 2)
        self.assertEqual(result["performance_metrics"]["chunks_failed"], 0)

    def test_create_partial_result(self):
        """Test creating partial result."""
        generation_state = {
            "generation_order": [{"chunk": 1}, {"chunk": 2}],
            "current_chunk_index": 1,
            "generated_chunks": ["TEST.CHUNK1"],
            "failed_chunks": []
        }
        
        result = self.coordinator.create_partial_result(generation_state, "/java/project", 3.0, 25)
        
        self.assertFalse(result["success"])
        self.assertIn("incomplete after 25 iterations", result["message"])
        self.assertEqual(result["artifacts"]["java_project_path"], "/java/project")
        self.assertEqual(result["performance_metrics"]["partial_time_seconds"], 3.0)
        self.assertEqual(result["performance_metrics"]["iterations_completed"], 25)

    def test_create_error_result(self):
        """Test creating error result."""
        result = self.coordinator.create_error_result("Test error message", 2.5)
        
        self.assertFalse(result["success"])
        self.assertIn("Test error message", result["message"])
        self.assertIn("Test error message", result["errors"])
        self.assertEqual(result["performance_metrics"]["failed_after_seconds"], 2.5)

    def test_create_critical_error_result(self):
        """Test creating critical error result."""
        original_result = {
            "error": "COBOL analysis required",
            "reason": "No analyzed chunks",
            "missing_files": ["test.json"],
            "invalid_files": ["bad.json"]
        }
        
        result = self.coordinator.create_critical_error_result(original_result, 1.5)
        
        self.assertFalse(result["success"])
        self.assertEqual(result["error"], "COBOL analysis required")
        self.assertEqual(result["reason"], "No analyzed chunks")
        self.assertEqual(result["missing_files"], ["test.json"])
        self.assertEqual(result["invalid_files"], ["bad.json"])
        self.assertEqual(result["generated_files"], [])
        self.assertEqual(result["processing_time"], 1.5)


if __name__ == "__main__":
    unittest.main()
