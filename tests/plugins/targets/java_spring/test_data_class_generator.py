"""
Unit tests for JavaDataClassGenerator.
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock

from src.plugins.targets.java_spring.agents.data_class_generator import JavaDataClassGenerator
from src.platform.tools.knowledge_database import KnowledgeDatabase


class TestJavaDataClassGenerator:
    """Test cases for JavaDataClassGenerator."""

    @pytest.fixture
    def mock_knowledge_db(self):
        """Create a mock knowledge database."""
        db = Mo<PERSON>(spec=KnowledgeDatabase)
        db.chunk_manager = Mock()
        return db

    @pytest.fixture
    def data_class_generator(self, mock_knowledge_db):
        """Create a JavaDataClassGenerator instance."""
        return JavaDataClassGenerator(mock_knowledge_db)

    @pytest.fixture
    def sample_copybook_data(self):
        """Sample copybook data for testing."""
        return {
            "program_id": "TESTPROG",
            "chunk_name": "CUSTOMER-RECORD",
            "chunk_type": "DATA_DIVISION_FILE_SECTION",
            "code": "01 CUSTOMER-RECORD.\n   05 CUST-ID PIC 9(6).\n   05 CUST-NAME PIC X(30)."
        }

    @pytest.fixture
    def sample_variables(self):
        """Sample variable definitions for testing."""
        return [
            {
                "name": "CUST-ID",
                "data_type": "PIC 9(6)",
                "level": 5,
                "length": 6,
                "business_name": "Customer ID",
                "description": "Unique customer identifier",
                "is_signed": 0,
                "decimals": 0
            },
            {
                "name": "CUST-NAME",
                "data_type": "PIC X(30)",
                "level": 5,
                "length": 30,
                "business_name": "Customer Name",
                "description": "Customer full name",
                "is_signed": 0,
                "decimals": 0
            }
        ]

    def test_get_copybooks_from_database(self, data_class_generator, mock_knowledge_db):
        """Test querying copybooks from database."""
        # Mock database response
        mock_copybooks = [
            {"program_id": "PROG1", "chunk_name": "RECORD1", "chunk_type": "DATA_DIVISION_FILE_SECTION"},
            {"program_id": "PROG2", "chunk_name": "RECORD2", "chunk_type": "DATA_DIVISION_WS_SECTION"}
        ]
        mock_knowledge_db.get_chunks_by_type.return_value = mock_copybooks

        # Test the method
        result = data_class_generator._get_copybooks_from_database()

        # Verify calls
        assert mock_knowledge_db.get_chunks_by_type.call_count == 2
        mock_knowledge_db.get_chunks_by_type.assert_any_call("DATA_DIVISION_FILE_SECTION")
        mock_knowledge_db.get_chunks_by_type.assert_any_call("DATA_DIVISION_WS_SECTION")

        # Verify result
        assert len(result) == 4  # 2 calls * 2 results each
        assert result[0]["program_id"] == "PROG1"

    def test_determine_if_entity_class_with_dli(self, data_class_generator, mock_knowledge_db, sample_copybook_data):
        """Test entity class determination with DLI patterns that specifically reference the copybook."""
        # Mock chunks with DLI patterns that specifically reference CUSTOMER-RECORD
        mock_chunks = [
            {
                "chunk_name": "MAIN-LOGIC",
                "code": "EXEC DLI GU USING PCB (DBDCU01-PCB-NUM) SEGMENT (CUSCH00) WHERE (CUFCM00K = KEY3-CM00) INTO (CUSTOMER-RECORD)"
            },
            {
                "chunk_name": "DB-ACCESS",
                "code": "EXEC DLI ISRT USING PCB (DBDAM01-PCB-NUM1) SEGMENT (AMSAM00) FROM (CUSTOMER-RECORD)"
            }
        ]
        mock_knowledge_db.chunk_manager.get_chunks_by_program.return_value = mock_chunks

        result = data_class_generator._determine_if_entity_class(sample_copybook_data)

        assert result is True

    def test_determine_if_entity_class_with_db2(self, data_class_generator, mock_knowledge_db, sample_copybook_data):
        """Test entity class determination with DB2 patterns that specifically reference the copybook."""
        # Mock chunks with DB2 patterns that specifically reference CUSTOMER-RECORD
        mock_chunks = [
            {
                "chunk_name": "SQL-LOGIC",
                "code": "EXEC SQL SELECT EVTRT_MNTH_TO_RETN INTO :CUSTOMER-RECORD FROM TCH_EVENT_RETN_OPT WHERE EVTRT_CLIENT_NBR = :EVTRT-CLIENT-NBR"
            },
            {
                "chunk_name": "DB-UPDATE",
                "code": "EXEC SQL DECLARE INSERT-CURSOR CURSOR FOR SELECT INSRD_FORM_NUMBER FROM TCH_INSRT_DETAIL WHERE INSRD_CLIENT_ID = :CUSTOMER-RECORD-ID"
            }
        ]
        mock_knowledge_db.chunk_manager.get_chunks_by_program.return_value = mock_chunks

        result = data_class_generator._determine_if_entity_class(sample_copybook_data)

        assert result is True

    def test_determine_if_entity_class_no_db_patterns(self, data_class_generator, mock_knowledge_db, sample_copybook_data):
        """Test entity class determination without database patterns."""
        # Mock chunks without database patterns
        mock_chunks = [
            {"chunk_name": "CALC-LOGIC", "code": "COMPUTE TOTAL = AMOUNT1 + AMOUNT2"},
            {"chunk_name": "DISPLAY-LOGIC", "code": "DISPLAY 'Processing complete'"}
        ]
        mock_knowledge_db.chunk_manager.get_chunks_by_program.return_value = mock_chunks

        result = data_class_generator._determine_if_entity_class(sample_copybook_data)

        assert result is False

    def test_extract_data_structure_from_copybook(self, data_class_generator, mock_knowledge_db,
                                                sample_copybook_data):
        """Test extracting 01-level data structures from copybook by parsing COBOL code."""
        # Create sample COBOL code with 01-level structure
        cobol_code = """
        01  CUSTOMER-RECORD.
            05 CUST-ID                        PIC 9(6).
            05 CUST-NAME                      PIC X(30).
            05 CUST-ADDRESS.
                10 ADDR-LINE1                 PIC X(25).
                10 ADDR-LINE2                 PIC X(25).
                10 CITY                       PIC X(20).
        """

        # Update sample copybook data with COBOL code
        sample_copybook_data["code"] = cobol_code

        # Mock business name mappings
        mock_knowledge_db.get_data_definitions_by_program.return_value = [
            {"name": "CUST-ID", "business_name": "Customer ID", "description": "Unique customer identifier"},
            {"name": "CUST-NAME", "business_name": "Customer Name", "description": "Customer full name"}
        ]

        result = data_class_generator._extract_data_structure_from_copybook(sample_copybook_data)

        assert result is not None
        assert isinstance(result, list)
        assert len(result) == 1  # One 01-level structure

        structure = result[0]
        assert structure["name"] == "CUSTOMER-RECORD"
        assert structure["program_id"] == "TESTPROG"
        assert len(structure["variables"]) == 6  # 01-level + 5 subordinate fields
        assert structure["variables"][0]["name"] == "CUSTOMER-RECORD"
        assert structure["variables"][1]["name"] == "CUST-ID"
        assert structure["variables"][1]["data_type"] == "PIC 9(6)"
        assert structure["variables"][2]["name"] == "CUST-NAME"
        assert structure["variables"][2]["data_type"] == "PIC X(30)"

    def test_parse_cobol_data_structure(self, data_class_generator):
        """Test parsing COBOL code to extract field definitions."""
        cobol_code = """
        01  FD-ACCTFILE-REC.
            05 FD-ACCT-ID                        PIC 9(11).
            05 FD-ACCT-DATA                      PIC X(289).
            05 FD-BALANCE                        PIC S9(7)V9(2).
            05 FD-STATUS                         PIC X.
        """

        result = data_class_generator._parse_cobol_data_structure(cobol_code)

        assert len(result) == 5

        # Check 01-level structure
        assert result[0]["name"] == "FD-ACCTFILE-REC"
        assert result[0]["level"] == 1
        assert result[0]["data_type"] == "GROUP"

        # Check subordinate fields
        assert result[1]["name"] == "FD-ACCT-ID"
        assert result[1]["level"] == 5
        assert result[1]["data_type"] == "PIC 9(11)"
        assert result[1]["length"] == 11

        assert result[2]["name"] == "FD-ACCT-DATA"
        assert result[2]["level"] == 5
        assert result[2]["data_type"] == "PIC X(289)"
        assert result[2]["length"] == 289

        assert result[3]["name"] == "FD-BALANCE"
        assert result[3]["level"] == 5
        assert result[3]["data_type"] == "PIC S9(7)V9(2)"
        assert result[3]["length"] == 9
        assert result[3]["decimals"] == 2
        assert result[3]["is_signed"] == True

    def test_parse_pic_clause(self, data_class_generator):
        """Test parsing various PIC clause formats."""
        test_cases = [
            ("X(30)", {"data_type": "PIC X(30)", "length": 30, "decimals": 0, "is_signed": False}),
            ("9(11)", {"data_type": "PIC 9(11)", "length": 11, "decimals": 0, "is_signed": False}),
            ("S9(7)V9(2)", {"data_type": "PIC S9(7)V9(2)", "length": 9, "decimals": 2, "is_signed": True}),
            ("X", {"data_type": "PIC X", "length": 1, "decimals": 0, "is_signed": False}),
            ("999", {"data_type": "PIC 999", "length": 3, "decimals": 0, "is_signed": False}),
        ]

        for pic_clause, expected in test_cases:
            result = data_class_generator._parse_pic_clause(pic_clause)
            assert result["data_type"] == expected["data_type"]
            assert result["length"] == expected["length"]
            assert result["decimals"] == expected["decimals"]
            assert result["is_signed"] == expected["is_signed"]

    def test_group_variables_by_specific_level(self, data_class_generator):
        """Test grouping variables by specific COBOL level."""
        variables = [
            {"name": "RECORD-01", "level": 1, "line_number": 1},
            {"name": "GROUP-A", "level": 5, "line_number": 2},
            {"name": "FIELD-A1", "level": 10, "line_number": 3},
            {"name": "FIELD-A2", "level": 10, "line_number": 4},
            {"name": "GROUP-B", "level": 5, "line_number": 5},
            {"name": "FIELD-B1", "level": 10, "line_number": 6},
            {"name": "FIELD-B2", "level": 10, "line_number": 7},
            {"name": "FIELD-B3", "level": 10, "line_number": 8},
        ]

        result = data_class_generator._group_variables_by_specific_level(variables, 5)

        assert len(result) == 2
        assert "GROUP-A" in result
        assert "GROUP-B" in result

        # GROUP-A should have 3 fields (itself + 2 subordinates)
        assert len(result["GROUP-A"]) == 3
        assert result["GROUP-A"][0]["name"] == "GROUP-A"
        assert result["GROUP-A"][1]["name"] == "FIELD-A1"
        assert result["GROUP-A"][2]["name"] == "FIELD-A2"

        # GROUP-B should have 4 fields (itself + 3 subordinates)
        assert len(result["GROUP-B"]) == 4
        assert result["GROUP-B"][0]["name"] == "GROUP-B"
        assert result["GROUP-B"][1]["name"] == "FIELD-B1"

    def test_group_variables_by_01_level(self, data_class_generator, sample_copybook_data):
        """Test grouping variables by 01-level structures."""
        # Create test variables with multiple 01-level structures
        test_variables = [
            {"name": "CUSTOMER-RECORD", "level": 1, "line_number": 1},
            {"name": "CUST-ID", "level": 5, "line_number": 2},
            {"name": "CUST-NAME", "level": 5, "line_number": 3},
            {"name": "ADDRESS-RECORD", "level": 1, "line_number": 4},
            {"name": "ADDR-LINE1", "level": 5, "line_number": 5},
            {"name": "ADDR-LINE2", "level": 5, "line_number": 6},
            {"name": "CITY", "level": 5, "line_number": 7},
        ]

        result = data_class_generator._group_variables_by_01_level(test_variables, sample_copybook_data)

        assert len(result) == 2  # Two 01-level structures

        # First structure
        customer_structure = result[0]
        assert customer_structure["name"] == "CUSTOMER-RECORD"
        assert len(customer_structure["variables"]) == 3  # 01-level + 2 subordinates

        # Second structure
        address_structure = result[1]
        assert address_structure["name"] == "ADDRESS-RECORD"
        assert len(address_structure["variables"]) == 4  # 01-level + 3 subordinates

    def test_determine_if_entity_class_for_structure(self, data_class_generator, mock_knowledge_db, sample_copybook_data):
        """Test entity class determination for specific 01-level structure."""
        # Create a data structure for testing
        data_structure = {
            "name": "CUSTOMER-RECORD",
            "program_id": "TESTPROG",
            "variables": [{"name": "CUST-ID", "level": 5}]
        }

        # Mock chunks with DLI patterns that specifically reference CUSTOMER-RECORD
        mock_chunks = [
            {
                "chunk_name": "MAIN-LOGIC",
                "code": "EXEC DLI GU USING PCB (DBDCU01-PCB-NUM) SEGMENT (CUSCH00) WHERE (CUFCM00K = KEY3-CM00) INTO (CUSTOMER-RECORD)"
            }
        ]
        mock_knowledge_db.chunk_manager.get_chunks_by_program.return_value = mock_chunks

        result = data_class_generator._determine_if_entity_class_for_structure(data_structure, sample_copybook_data)

        assert result is True

    def test_group_variables_by_level(self, data_class_generator, sample_variables):
        """Test grouping variables by COBOL level."""
        # Add more variables with different levels
        extended_variables = sample_variables + [
            {"name": "ADDR-LINE1", "level": 10, "data_type": "PIC X(25)"},
            {"name": "ADDR-LINE2", "level": 10, "data_type": "PIC X(25)"},
            {"name": "CITY", "level": 10, "data_type": "PIC X(20)"},
            {"name": "STATE", "level": 10, "data_type": "PIC X(2)"},
            {"name": "ZIP", "level": 10, "data_type": "PIC 9(5)"}
        ]

        result = data_class_generator._group_variables_by_level(extended_variables)

        assert 5 in result  # Level 5 variables
        assert 10 in result  # Level 10 variables
        assert len(result[5]) == 2  # 2 level-5 variables
        assert len(result[10]) == 5  # 5 level-10 variables

    def test_get_variables_business_names(self, data_class_generator, sample_variables):
        """Test getting business names for variables."""
        data_structure = {"variables": sample_variables}

        result = data_class_generator._get_variables_business_names(data_structure)

        assert "CUST-ID" in result
        assert result["CUST-ID"] == "Customer ID"
        assert "CUST-NAME" in result
        assert result["CUST-NAME"] == "Customer Name"

    def test_determine_if_needs_string_constructor_file_section(self, data_class_generator):
        """Test string constructor determination for FILE_SECTION."""
        data_structure = {
            "chunk_type": "DATA_DIVISION_FILE_SECTION",
            "code": "FD CUSTOMER-FILE"
        }

        result = data_class_generator._determine_if_needs_string_constructor(data_structure)

        assert result is True

    def test_determine_if_needs_string_constructor_with_read(self, data_class_generator):
        """Test string constructor determination with READ operations."""
        data_structure = {
            "chunk_type": "DATA_DIVISION_WS_SECTION",
            "code": "READ CUSTOMER-FILE INTO CUSTOMER-RECORD"
        }

        result = data_class_generator._determine_if_needs_string_constructor(data_structure)

        assert result is True

    def test_determine_if_needs_string_constructor_no_file_ops(self, data_class_generator):
        """Test string constructor determination without file operations."""
        data_structure = {
            "chunk_type": "DATA_DIVISION_WS_SECTION",
            "code": "COMPUTE TOTAL = AMOUNT1 + AMOUNT2"
        }

        result = data_class_generator._determine_if_needs_string_constructor(data_structure)

        assert result is False

    @patch('llm_settings.invoke_llm')
    def test_generate_java_class_with_llm(self, mock_llm, data_class_generator, sample_variables):
        """Test Java class generation with LLM."""
        # Mock LLM response
        mock_llm.return_value = """
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerRecord {
    private Integer custId;
    private String custName;
}
```
"""

        data_structure = {
            "name": "CustomerRecord",
            "variables": sample_variables,
            "chunk_type": "DATA_DIVISION_FILE_SECTION"
        }

        result = data_class_generator._generate_java_class_with_llm(data_structure, False)

        assert result is not None
        assert "class CustomerRecord" in result
        # Check for customer ID field (may be custId or customerId)
        assert ("private Integer custId" in result or "private Integer customerId" in result)
        # Check for customer name field (may be custName or customerName)
        assert ("private String custName" in result or "private String customerName" in result)

    def test_extract_java_code_from_response(self, data_class_generator):
        """Test extracting Java code from LLM response."""
        response = """
Here's the Java class:

```java
@Data
public class TestClass {
    private String field1;
    private Integer field2;
}
```

This class represents the COBOL structure.
"""

        result = data_class_generator._extract_java_code_from_response(response)

        assert "@Data" in result
        assert "class TestClass" in result
        assert "private String field1" in result

    def test_extract_class_name_from_java_code(self, data_class_generator):
        """Test extracting class name from Java code."""
        java_code = """
@Data
@NoArgsConstructor
public class CustomerRecord {
    private String name;
}
"""

        result = data_class_generator._extract_class_name_from_java_code(java_code)

        assert result == "CustomerRecord"

    def test_check_dli_copybook_usage_with_into_pattern(self, data_class_generator):
        """Test DLI copybook usage detection with INTO pattern."""
        code = "EXEC DLI GU USING PCB (DBDCU01-PCB-NUM) SEGMENT (CUSCH00) WHERE (CUFCM00K = KEY3-CM00) INTO (CUSTOMER-RECORD)"
        copybook_name = "CUSTOMER-RECORD"
        chunk_name = "TEST-CHUNK"

        result = data_class_generator._check_dli_copybook_usage(code, copybook_name, chunk_name)

        assert len(result) == 1
        assert "DLI GU operation with copybook 'CUSTOMER-RECORD'" in result[0]

    def test_check_dli_copybook_usage_with_from_pattern(self, data_class_generator):
        """Test DLI copybook usage detection with FROM pattern."""
        code = "EXEC DLI ISRT USING PCB (DBDAM01-PCB-NUM1) SEGMENT (AMSAM00) FROM (CUSTOMER-RECORD)"
        copybook_name = "CUSTOMER-RECORD"
        chunk_name = "TEST-CHUNK"

        result = data_class_generator._check_dli_copybook_usage(code, copybook_name, chunk_name)

        assert len(result) == 1
        assert "DLI ISRT operation with copybook 'CUSTOMER-RECORD'" in result[0]

    def test_check_dli_copybook_usage_with_segment_pattern(self, data_class_generator):
        """Test DLI copybook usage detection with segment pattern."""
        code = "EXEC DLI GU USING PCB (DBDAM01-PCB-NUM1) SEGMENT (CUSTOMER-RECORD) WHERE (AMFAM00K = AM00-ACCOUNT-KEY)"
        copybook_name = "CUSTOMER-RECORD"
        chunk_name = "TEST-CHUNK"

        result = data_class_generator._check_dli_copybook_usage(code, copybook_name, chunk_name)

        assert len(result) == 1
        assert "DLI GU operation with segment 'CUSTOMER-RECORD'" in result[0]

    def test_check_sql_copybook_usage_with_select_into(self, data_class_generator):
        """Test SQL copybook usage detection with SELECT INTO pattern."""
        code = "EXEC SQL SELECT EVTRT_MNTH_TO_RETN INTO :CUSTOMER-RECORD FROM TCH_EVENT_RETN_OPT WHERE EVTRT_CLIENT_NBR = :EVTRT-CLIENT-NBR"
        copybook_name = "CUSTOMER-RECORD"
        chunk_name = "TEST-CHUNK"

        result = data_class_generator._check_sql_copybook_usage(code, copybook_name, chunk_name)

        assert len(result) == 1
        assert "SQL SELECT INTO copybook 'CUSTOMER-RECORD'" in result[0]

    def test_check_sql_copybook_usage_with_host_variable(self, data_class_generator):
        """Test SQL copybook usage detection with host variable pattern."""
        code = "EXEC SQL INSERT INTO CUSTOMER_TABLE VALUES (:CUSTOMER-RECORD-ID, :CUSTOMER-RECORD-NAME)"
        copybook_name = "CUSTOMER-RECORD"
        chunk_name = "TEST-CHUNK"

        result = data_class_generator._check_sql_copybook_usage(code, copybook_name, chunk_name)

        assert len(result) == 1
        assert "SQL operation with copybook fields from 'CUSTOMER-RECORD'" in result[0]

    def test_check_sql_copybook_usage_with_cursor(self, data_class_generator):
        """Test SQL copybook usage detection with cursor pattern."""
        code = "EXEC SQL DECLARE INSERT-CURSOR CURSOR FOR SELECT INSRD_FORM_NUMBER FROM TCH_INSRT_DETAIL WHERE INSRD_CLIENT_ID = :CUSTOMER-RECORD-ID"
        copybook_name = "CUSTOMER-RECORD"
        chunk_name = "TEST-CHUNK"

        result = data_class_generator._check_sql_copybook_usage(code, copybook_name, chunk_name)

        assert len(result) == 1
        assert "SQL operation with copybook fields from 'CUSTOMER-RECORD'" in result[0]

    def test_check_dli_copybook_usage_no_match(self, data_class_generator):
        """Test DLI copybook usage detection with no matching patterns."""
        code = "EXEC DLI GU USING PCB (DBDCU01-PCB-NUM) SEGMENT (OTHER-RECORD) INTO (DIFFERENT-AREA)"
        copybook_name = "CUSTOMER-RECORD"
        chunk_name = "TEST-CHUNK"

        result = data_class_generator._check_dli_copybook_usage(code, copybook_name, chunk_name)

        assert len(result) == 0

    def test_check_sql_copybook_usage_no_match(self, data_class_generator):
        """Test SQL copybook usage detection with no matching patterns."""
        code = "EXEC SQL SELECT * FROM CUSTOMER_TABLE WHERE ID = :OTHER-RECORD-ID"
        copybook_name = "CUSTOMER-RECORD"
        chunk_name = "TEST-CHUNK"

        result = data_class_generator._check_sql_copybook_usage(code, copybook_name, chunk_name)

        assert len(result) == 0

    @patch('llm_settings.invoke_llm')
    def test_generate_data_classes_from_copybooks_integration(self, mock_llm, data_class_generator,
                                                            mock_knowledge_db, sample_copybook_data, sample_variables):
        """Test full integration of data class generation."""
        # Add level 1 variable to create a proper 01-level structure
        sample_variables_with_01 = [
            {
                "name": "CUSTOMER-RECORD",
                "data_type": "GROUP",
                "level": 1,
                "length": 0,
                "business_name": "Customer Record",
                "description": "Main customer record structure",
                "is_signed": 0,
                "decimals": 0,
                "line_number": 1
            }
        ] + sample_variables

        # Setup mocks - return empty for first call, sample for second call to simulate single copybook
        mock_knowledge_db.get_chunks_by_type.side_effect = [[], [sample_copybook_data]]
        mock_knowledge_db.get_data_definitions_by_program.return_value = sample_variables_with_01
        mock_knowledge_db.chunk_manager.get_chunks_by_program.return_value = []

        mock_llm.return_value = """
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerRecord {
    private Integer custId;
    private String custName;
}
```
"""

        # Mock tools
        mock_tools = Mock()

        result = data_class_generator.generate_data_classes_from_copybooks(mock_tools)

        assert result["success"] is True
        assert len(result["generated_classes"]) == 1
        assert result["generated_classes"][0]["name"] == "CUSTOMER-RECORD"
        assert result["generated_classes"][0]["is_entity"] is False

    def test_large_structure_chunking(self, data_class_generator, sample_copybook_data):
        """Test that large 01-level structures (>40 fields) are properly chunked."""
        # Create a large structure with 50 fields
        large_variables = [
            {"name": "LARGE-RECORD", "level": 1, "line_number": 1}
        ]

        # Add 50 subordinate fields
        for i in range(1, 51):
            large_variables.append({
                "name": f"FIELD-{i:02d}",
                "level": 5,
                "data_type": "PIC X(10)",
                "length": 10,
                "line_number": i + 1
            })

        # Create data structure
        large_structure = {
            "name": "LARGE-RECORD",
            "program_id": "TESTPROG",
            "variables": large_variables,
            "chunk_type": "DATA_DIVISION_WS_SECTION",
            "code": "01 LARGE-RECORD.",
            "original_copybook": "TEST-COPYBOOK"
        }

        # Test chunking
        result = data_class_generator._generate_java_classes_for_structure(
            large_structure, sample_copybook_data, False, None
        )

        # Should generate multiple classes due to chunking
        assert len(result["classes"]) > 1

        # Verify chunking logic
        total_fields = 0
        for class_info in result["classes"]:
            field_count = class_info.get("field_count", 0)
            assert field_count <= 40  # Each chunk should have <= 40 fields
            total_fields += field_count

        # Total fields should match original (51 including the 01-level)
        assert total_fields == 51
