"""
Comprehensive tests for the refactored Java Spring plugin.
Tests the separation of concerns and delegation to specialized classes.
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os
import time
from typing import Dict, Any

from src.plugins.targets.java_spring.plugin import (
    JavaSpringCodeGenerator,
    JavaSpringPlugin
)
from src.platform.agents.base_agent import AgentInput


class TestJavaSpringCodeGeneratorRefactored(unittest.TestCase):
    """Test the refactored JavaSpringCodeGenerator with delegation architecture."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.code_generator = JavaSpringCodeGenerator()

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_initialization_creates_specialized_tools(self):
        """Test that initialization creates all specialized tool instances."""
        # Verify all specialized tools are initialized
        self.assertIsNotNone(self.code_generator.dependency_analyzer)
        self.assertIsNotNone(self.code_generator.state_manager)
        self.assertIsNotNone(self.code_generator.performance_tracker)
        self.assertIsNotNone(self.code_generator.react_coordinator)
        self.assertIsNotNone(self.code_generator.java_project_manager)
        self.assertIsNotNone(self.code_generator.java_code_generator)

        # Verify tool types
        from src.plugins.targets.java_spring.tools.dependency_analyzer import JavaDependencyAnalyzer
        from src.plugins.targets.java_spring.tools.state_manager import JavaStateManager
        from src.plugins.targets.java_spring.tools.performance_tracker import JavaPerformanceTracker
        from src.plugins.targets.java_spring.tools.react_coordinator import JavaReactCoordinator

        self.assertIsInstance(self.code_generator.dependency_analyzer, JavaDependencyAnalyzer)
        self.assertIsInstance(self.code_generator.state_manager, JavaStateManager)
        self.assertIsInstance(self.code_generator.performance_tracker, JavaPerformanceTracker)
        self.assertIsInstance(self.code_generator.react_coordinator, JavaReactCoordinator)

    def test_generate_code_delegates_properly(self):
        """Test that generate_code method delegates to specialized tools."""
        # Mock the specialized tools directly on the instance
        self.code_generator.performance_tracker = Mock()
        self.code_generator.state_manager = Mock()
        self.code_generator.react_coordinator = Mock()
        self.code_generator.java_project_manager = Mock()

        # Configure mock returns
        self.code_generator.performance_tracker.start_processing.return_value = time.time()
        self.code_generator.performance_tracker.end_processing.return_value = 1.0
        self.code_generator.performance_tracker.log_delegation_performance = Mock()
        self.code_generator.performance_tracker.log_performance_metrics = Mock()

        self.code_generator.state_manager.load_or_initialize_generation_state.return_value = {"completed": True}
        self.code_generator.state_manager.is_generation_complete.return_value = True
        self.code_generator.state_manager.should_restart_due_to_context.return_value = False

        self.code_generator.react_coordinator.should_continue_generation.return_value = False
        self.code_generator.react_coordinator.create_success_result.return_value = {"success": True}
        self.code_generator.react_coordinator.log_generation_summary = Mock()

        source_analysis = {"knowledge_base": {}}
        config = {"working_directory": self.temp_dir}

        # Execute
        result = self.code_generator.generate_code(source_analysis, config)

        # Verify delegations
        self.code_generator.performance_tracker.start_processing.assert_called_once()
        self.code_generator.state_manager.load_or_initialize_generation_state.assert_called_once()
        self.code_generator.react_coordinator.should_continue_generation.assert_called()
        self.code_generator.state_manager.is_generation_complete.assert_called()

    def test_execute_action_delegates_to_appropriate_tools(self):
        """Test that _execute_action delegates to the correct specialized tools."""
        # Mock the specialized tools
        self.code_generator.dependency_analyzer = Mock()
        self.code_generator.state_manager = Mock()
        self.code_generator.react_coordinator = Mock()
        self.code_generator.performance_tracker = Mock()

        # Configure mock returns
        self.code_generator.dependency_analyzer.analyze_dependencies.return_value = {"success": True}
        self.code_generator.state_manager.transition_to_generation.return_value = {"success": True}
        self.code_generator.state_manager.transition_to_finalization.return_value = {"success": True}
        self.code_generator.react_coordinator.log_action_execution = Mock()
        self.code_generator.performance_tracker.log_delegation_performance = Mock()

        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})
        generation_state = {}

        # Test analyze_dependencies action
        action = {"type": "analyze_dependencies", "description": "Test"}
        result = self.code_generator._execute_action(action, generation_state, input_data)
        self.code_generator.dependency_analyzer.analyze_dependencies.assert_called_once_with(generation_state, input_data)
        self.assertEqual(result["success"], True)

        # Test transition_to_generation action
        action = {"type": "transition_to_generation", "description": "Test"}
        result = self.code_generator._execute_action(action, generation_state, input_data)
        self.code_generator.state_manager.transition_to_generation.assert_called_once_with(generation_state)
        self.assertEqual(result["success"], True)

        # Test transition_to_finalization action
        action = {"type": "transition_to_finalization", "description": "Test"}
        result = self.code_generator._execute_action(action, generation_state, input_data)
        self.code_generator.state_manager.transition_to_finalization.assert_called_once_with(generation_state)
        self.assertEqual(result["success"], True)

    def test_generate_chunk_code_delegates_to_specialized_tools(self):
        """Test that _generate_chunk_code delegates to specialized tools."""
        # Mock the specialized tools and dependencies
        self.code_generator.performance_tracker = Mock()
        self.code_generator.dependency_analyzer = Mock()
        self.code_generator.java_code_generator = Mock()
        self.code_generator.java_project_manager = Mock()
        self.code_generator.knowledge_db = Mock()
        self.code_generator.tools = Mock()

        # Configure mock returns
        self.code_generator.performance_tracker.start_chunk_processing.return_value = time.time()
        self.code_generator.performance_tracker.end_chunk_processing_success.return_value = 1.0
        self.code_generator.performance_tracker.get_chunk_performance_metrics.return_value = {"time": 1.0}
        self.code_generator.performance_tracker.log_delegation_performance = Mock()

        self.code_generator.knowledge_db.get_chunk_documentation.return_value = {
            "program_id": "TEST", "chunk_name": "CHUNK", "code": "test code"
        }
        self.code_generator.dependency_analyzer.get_chunk_dependencies.return_value = {"dependent_services": []}
        self.code_generator.java_code_generator.generate_chunk_code.return_value = "public class TestService {}"
        self.code_generator.java_code_generator.validate_java_code.return_value = {"valid": True}
        self.code_generator.java_project_manager.save_java_code_to_file.return_value = {"file_path": "/test/path"}
        self.code_generator.tools.get_language_mappings.return_value = {"mapping": "test"}

        chunk_info = {"program_id": "TEST", "chunk_name": "CHUNK"}
        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})

        # Execute
        result = self.code_generator._generate_chunk_code(chunk_info, generation_state, input_data)

        # Verify delegations
        self.code_generator.performance_tracker.start_chunk_processing.assert_called_once()
        self.code_generator.knowledge_db.get_chunk_documentation.assert_called_once_with("TEST", "CHUNK")
        self.code_generator.dependency_analyzer.get_chunk_dependencies.assert_called_once_with(chunk_info, generation_state)
        self.code_generator.java_code_generator.generate_chunk_code.assert_called_once()
        self.code_generator.java_project_manager.save_java_code_to_file.assert_called_once()
        self.code_generator.java_code_generator.validate_java_code.assert_called_once()

        # Verify result
        self.assertTrue(result["success"])
        self.assertIn("java_code", result)
        self.assertIn("performance_metrics", result)

    def test_finalize_project_delegates_to_specialized_tools(self):
        """Test that _finalize_project delegates to specialized tools."""
        # Mock the specialized tools
        self.code_generator.java_project_manager = Mock()
        self.code_generator.state_manager = Mock()
        self.code_generator.performance_tracker = Mock()

        # Configure mock returns
        self.code_generator.state_manager.mark_finalization_complete.return_value = {"success": True, "finalization_complete": True}
        self.code_generator.performance_tracker.log_delegation_performance = Mock()

        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})

        # Execute
        result = self.code_generator._finalize_project(generation_state, input_data)

        # Verify delegations
        self.code_generator.java_project_manager.finalize_project.assert_called_once()
        self.code_generator.state_manager.mark_finalization_complete.assert_called_once_with(generation_state)

        # Verify result
        self.assertTrue(result["success"])
        self.assertTrue(result["finalization_complete"])

    @patch('src.plugins.targets.java_spring.tools.project_manager.core.get_template_manager')
    def test_project_manager_finalize_project_with_correct_arguments(self, mock_get_template_manager):
        """Test that finalize_project calls generate_application_class with required arguments."""
        from src.plugins.targets.java_spring.tools.project_manager.core import JavaProjectManager

        # Setup mock template manager
        mock_template_manager = Mock()
        mock_get_template_manager.return_value = mock_template_manager

        # Create project manager instance
        project_manager = JavaProjectManager()

        # Mock all the file generator methods to avoid template issues
        project_manager.file_generator.generate_application_class = Mock(return_value="/path/to/Application.java")
        project_manager.file_generator.generate_application_properties = Mock(return_value="/path/to/application.properties")
        project_manager.file_generator.generate_pom_xml = Mock(return_value="/path/to/pom.xml")
        project_manager.file_generator.generate_gitignore = Mock(return_value="/path/to/.gitignore")
        project_manager.file_generator.generate_base_service_class = Mock(return_value="/path/to/BaseService.java")
        project_manager.file_generator.generate_logback_config = Mock(return_value="/path/to/logback.xml")
        project_manager.file_generator.generate_readme = Mock(return_value="/path/to/README.md")

        # Execute finalize_project
        java_project_dir = os.path.join(self.temp_dir, "java_project")
        os.makedirs(java_project_dir, exist_ok=True)
        generation_state = {}

        # This should not raise an error about missing arguments
        project_manager.finalize_project(java_project_dir, generation_state)

        # Verify that generate_application_class was called with the correct arguments
        project_manager.file_generator.generate_application_class.assert_called_once_with(
            java_project_dir,
            project_manager.default_package,  # package_name
            "CobolConversionApplication"       # class_name
        )

    def test_get_performance_metrics_delegates_to_tracker(self):
        """Test that get_performance_metrics delegates to performance tracker."""
        # Mock the performance tracker
        self.code_generator.performance_tracker = Mock()
        expected_metrics = {"total_chunks": 5, "successful": 4, "failed": 1}
        self.code_generator.performance_tracker.get_performance_metrics.return_value = expected_metrics

        # Execute
        result = self.code_generator.get_performance_metrics()

        # Verify delegation and result
        self.code_generator.performance_tracker.get_performance_metrics.assert_called_once()
        self.assertEqual(result, expected_metrics)


class TestJavaSpringPluginRefactored(unittest.TestCase):
    """Test the refactored JavaSpringPlugin."""

    def setUp(self):
        """Set up test fixtures."""
        self.plugin = JavaSpringPlugin()

    def test_plugin_uses_refactored_code_generator(self):
        """Test that plugin uses the refactored JavaSpringCodeGenerator."""
        code_generator = self.plugin.get_code_generator()
        self.assertIsInstance(code_generator, JavaSpringCodeGenerator)

        # Verify the code generator has specialized tools
        self.assertIsNotNone(code_generator.dependency_analyzer)
        self.assertIsNotNone(code_generator.state_manager)
        self.assertIsNotNone(code_generator.performance_tracker)
        self.assertIsNotNone(code_generator.react_coordinator)

    def test_plugin_delegation_to_code_generator(self):
        """Test that plugin properly delegates validation to code generator."""
        # Mock the code generator's java_code_generator
        mock_java_code_generator = Mock()
        mock_java_code_generator.validate_java_code.return_value = {"valid": True}
        self.plugin._code_generator.java_code_generator = mock_java_code_generator

        # Execute validation
        result = self.plugin.validate_against_source(
            "COBOL code", "Java code", "cobol", "TEST_PROG", "TEST_CHUNK"
        )

        # Verify delegation
        mock_java_code_generator.validate_java_code.assert_called_once()
        self.assertTrue(result["valid"])


if __name__ == "__main__":
    unittest.main()
