"""
Tests for logging and performance of the refactored Java Spring plugin.
Verifies comprehensive logging and performance improvements from separation of concerns.
"""
import unittest
from unittest.mock import Mock, patch, call
import tempfile
import time
import logging
from io import StringIO

from src.plugins.targets.java_spring.plugin import JavaSpringCodeGenerator
from src.platform.agents.base_agent import AgentInput


class TestJavaSpringLogging(unittest.TestCase):
    """Test comprehensive logging in the refactored plugin."""

    def setUp(self):
        """Set up test fixtures with logging capture."""
        self.temp_dir = tempfile.mkdtemp()
        self.code_generator = JavaSpringCodeGenerator()
        
        # Set up logging capture
        self.log_stream = StringIO()
        self.log_handler = logging.StreamHandler(self.log_stream)
        self.log_handler.setLevel(logging.DEBUG)
        
        # Add handler to logger
        self.code_generator.logger.addHandler(self.log_handler)
        self.code_generator.logger.setLevel(logging.DEBUG)
        
        # Mock dependencies
        self.code_generator.knowledge_db = Mock()
        self.code_generator.tools = Mock()
        self.code_generator.java_project_manager = Mock()
        self.code_generator.java_code_generator = Mock()
        self.code_generator.dependency_analyzer = Mock()
        self.code_generator.performance_tracker = Mock()

        # Mock performance tracker methods
        self.code_generator.performance_tracker.start_chunk_processing = Mock(return_value=0.0)
        self.code_generator.performance_tracker.end_chunk_processing_success = Mock(return_value=0.1)
        self.code_generator.performance_tracker.end_chunk_processing_failure = Mock(return_value=0.1)
        self.code_generator.performance_tracker.log_delegation_performance = Mock()
        self.code_generator.performance_tracker.get_chunk_performance_metrics = Mock(return_value={})
        self.code_generator.performance_tracker.get_failure_performance_metrics = Mock(return_value={})

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        self.code_generator.logger.removeHandler(self.log_handler)

    def test_comprehensive_logging_in_chunk_generation(self):
        """Test that chunk generation includes comprehensive logging."""
        chunk_info = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK"}
        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})
        
        # Mock successful generation
        chunk_doc = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK", "code": "TEST CODE"}
        self.code_generator.knowledge_db.get_chunk_documentation = Mock(return_value=chunk_doc)
        self.code_generator.dependency_analyzer.get_chunk_dependencies = Mock(return_value={"dependent_services": []})
        self.code_generator.java_code_generator.generate_chunk_code = Mock(return_value="public class TestClass {}")
        self.code_generator.tools.get_language_mappings = Mock(return_value={"test": "mapping"})
        self.code_generator.java_project_manager.save_java_code_to_file = Mock(return_value={"file_path": "/test/path"})
        self.code_generator.java_code_generator.validate_java_code = Mock(return_value={"valid": True})
        
        # Execute
        result = self.code_generator._generate_chunk_code(chunk_info, generation_state, input_data)
        
        # Check logs
        log_output = self.log_stream.getvalue()
        
        # Verify comprehensive logging
        self.assertIn("Starting code generation for chunk TEST_PROG.TEST_CHUNK", log_output)
        self.assertIn("Retrieving documentation for TEST_PROG.TEST_CHUNK", log_output)
        self.assertIn("Found documentation with", log_output)
        self.assertIn("Analyzing dependencies for TEST_PROG.TEST_CHUNK", log_output)
        self.assertIn("Found 0 dependencies", log_output)
        self.assertIn("Delegating code generation to JavaCodeGenerator", log_output)
        self.assertIn("Generated 25 characters of Java code", log_output)
        self.assertIn("Retrieving COBOL-Java mappings", log_output)
        self.assertIn("Found 1 mappings", log_output)
        self.assertIn("Saving Java code to file", log_output)
        self.assertIn("Saved Java class to: /test/path", log_output)
        self.assertIn("Validating generated Java code", log_output)
        self.assertIn("Code validation passed", log_output)
        self.assertIn("Successfully completed code generation", log_output)

    def test_error_logging_is_comprehensive(self):
        """Test that error cases include comprehensive logging."""
        chunk_info = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK"}
        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})
        
        # Mock failure case
        self.code_generator.knowledge_db.get_chunk_documentation = Mock(return_value=None)
        
        # Execute
        result = self.code_generator._generate_chunk_code(chunk_info, generation_state, input_data)
        
        # Check error logging
        log_output = self.log_stream.getvalue()
        self.assertIn("No documentation found for TEST_PROG.TEST_CHUNK", log_output)
        self.assertFalse(result["success"])

    def test_debug_logging_includes_detailed_info(self):
        """Test that debug logging includes detailed information."""
        chunk_info = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK", "business_name": "Test Business"}
        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})
        
        # Mock successful generation
        chunk_doc = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK", "code": "COBOL CODE HERE"}
        self.code_generator.knowledge_db.get_chunk_documentation = Mock(return_value=chunk_doc)
        self.code_generator.dependency_analyzer.get_chunk_dependencies = Mock(return_value={"dependent_services": ["service1", "service2"]})
        self.code_generator.java_code_generator.generate_chunk_code = Mock(return_value="public class TestClass {}")
        self.code_generator.tools.get_language_mappings = Mock(return_value={})
        self.code_generator.java_project_manager.save_java_code_to_file = Mock(return_value={"file_path": "/test/path"})
        self.code_generator.java_code_generator.validate_java_code = Mock(return_value={"valid": False, "errors": ["test error"]})
        
        # Execute
        result = self.code_generator._generate_chunk_code(chunk_info, generation_state, input_data)
        
        # Check debug logging
        log_output = self.log_stream.getvalue()
        self.assertIn("Chunk info:", log_output)
        self.assertIn("Found documentation with 15 characters of code", log_output)
        self.assertIn("Found 2 dependencies", log_output)
        self.assertIn("Found 0 mappings", log_output)
        self.assertIn("Code validation failed", log_output)

    def test_logging_preserves_original_behavior(self):
        """Test that logging doesn't change the original behavior."""
        chunk_info = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK"}
        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})
        
        # Mock JavaCodeGenerator returning empty string (failure)
        self.code_generator.knowledge_db.get_chunk_documentation = Mock(return_value={"test": "data", "code": "test"})
        self.code_generator.dependency_analyzer.get_chunk_dependencies = Mock(return_value={})
        self.code_generator.java_code_generator.generate_chunk_code = Mock(return_value="")  # Empty = failure
        
        # Execute
        result = self.code_generator._generate_chunk_code(chunk_info, generation_state, input_data)
        
        # Verify explicit failure (original behavior preserved)
        self.assertFalse(result["success"])
        self.assertIn("JavaCodeGenerator returned empty code", result["error"])
        
        # Verify error was logged
        log_output = self.log_stream.getvalue()
        self.assertIn("JavaCodeGenerator returned empty code", log_output)


class TestJavaSpringPerformance(unittest.TestCase):
    """Test performance improvements from separation of concerns."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.code_generator = JavaSpringCodeGenerator()
        
        # Mock dependencies for performance testing
        self.code_generator.knowledge_db = Mock()
        self.code_generator.tools = Mock()
        self.code_generator.java_project_manager = Mock()
        self.code_generator.java_code_generator = Mock()
        self.code_generator.dependency_analyzer = Mock()
        self.code_generator.performance_tracker = Mock()

        # Mock performance tracker methods
        self.code_generator.performance_tracker.start_chunk_processing = Mock(return_value=0.0)
        self.code_generator.performance_tracker.end_chunk_processing_success = Mock(return_value=0.1)
        self.code_generator.performance_tracker.end_chunk_processing_failure = Mock(return_value=0.1)
        self.code_generator.performance_tracker.log_delegation_performance = Mock()
        self.code_generator.performance_tracker.get_chunk_performance_metrics = Mock(return_value={})
        self.code_generator.performance_tracker.get_failure_performance_metrics = Mock(return_value={})

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_delegation_reduces_method_call_overhead(self):
        """Test that delegation doesn't add significant overhead."""
        chunk_info = {"program_id": "TEST_PROG", "chunk_name": "TEST_CHUNK"}
        generation_state = {}
        input_data = AgentInput(working_directory=self.temp_dir, knowledge_base={})
        
        # Mock fast responses
        self.code_generator.knowledge_db.get_chunk_documentation = Mock(return_value={"test": "data", "code": "test"})
        self.code_generator.dependency_analyzer.get_chunk_dependencies = Mock(return_value={})
        self.code_generator.java_code_generator.generate_chunk_code = Mock(return_value="public class TestClass {}")
        self.code_generator.tools.get_language_mappings = Mock(return_value={})
        self.code_generator.java_project_manager.save_java_code_to_file = Mock(return_value={"file_path": "/test/path"})
        self.code_generator.java_code_generator.validate_java_code = Mock(return_value={"valid": True})
        
        # Measure execution time
        start_time = time.time()
        result = self.code_generator._generate_chunk_code(chunk_info, generation_state, input_data)
        end_time = time.time()
        
        # Verify performance (should be very fast with mocks)
        execution_time = end_time - start_time
        self.assertLess(execution_time, 0.1)  # Should complete in less than 100ms
        self.assertTrue(result["success"])

    def test_memory_usage_is_reasonable(self):
        """Test that the refactored plugin doesn't use excessive memory."""
        import sys
        import gc

        # Get initial memory usage
        initial_objects = len(gc.get_objects())
        
        # Create multiple instances to test memory usage
        generators = []
        for i in range(10):
            generator = JavaSpringCodeGenerator()
            generator.knowledge_db = Mock()
            generator.tools = Mock()
            generator.java_project_manager = Mock()
            generator.java_code_generator = Mock()
            generators.append(generator)
        
        # Memory usage should be reasonable
        final_objects = len(gc.get_objects())
        # Should not create excessive objects (allowing for some overhead)
        self.assertLess(final_objects - initial_objects, 1000)

    def test_no_circular_dependencies(self):
        """Test that there are no circular dependencies between components."""
        # This test ensures that the separation of concerns doesn't create circular imports
        generator = JavaSpringCodeGenerator()
        
        # Verify that components can be created independently
        self.assertIsNotNone(generator.knowledge_db)
        self.assertIsNotNone(generator.java_project_manager)
        self.assertIsNotNone(generator.java_code_generator)
        
        # Verify that components don't hold references to the main generator
        # (This would indicate a circular dependency)
        self.assertFalse(hasattr(generator.knowledge_db, 'code_generator'))
        self.assertFalse(hasattr(generator.java_project_manager, 'code_generator'))


if __name__ == '__main__':
    # Import gc for memory testing
    import gc
    unittest.main()
