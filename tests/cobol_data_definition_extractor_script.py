import os
import json
import logging

from src.plugins.legacy.cobol.tools.data_extractors.core import CobolDataDefinitionExtractor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_data_extractor")

def test_data_extractor():
    """
    Test the COBOL data definition extractor on multiple sample data division chunks.
    """
    # Create sample data division chunks
    sample_chunks = [
        # Basic data structures
        {
            "chunk_type": "DATA_DIVISION_WS_SECTION",
            "chunk_name": "CUSTOMER_DATA",
            "code": """
      * Customer Information Structure
       01  CUSTOMER-DATA.
           05  CUST-ID              PIC 9(6).
           05  CUST-NAME.
               10  CUST-FIRST-NAME  PIC X(20).
               10  CUST-LAST-NAME   PIC X(30).
           05  CUST-ADDRESS.
               10  CUST-STREET      PIC X(40).
               10  CUST-CITY        PIC X(20).
               10  CUST-STATE       PIC XX.
               10  CUST-ZIP         PIC X(10).
           05  CUST-CONTACT         OCCURS 3 TIMES.
               10  CONTACT-TYPE     PIC X.
                   88  CONTACT-PHONE        VALUE 'P'.
                   88  CONTACT-EMAIL        VALUE 'E'.
                   88  CONTACT-SOCIAL       VALUE 'S'.
               10  CONTACT-VALUE    PIC X(50).
           05  CUST-BALANCE         PIC S9(7)V99 COMP-3.
           05  CUST-STATUS          PIC X.
               88  CUST-ACTIVE      VALUE 'A'.
               88  CUST-INACTIVE    VALUE 'I'.
               88  CUST-SUSPENDED   VALUE 'S'.
           05  CUST-JOINED-DATE     PIC 9(8).
           05  FILLER               PIC X(20).
            """,
            "metadata": {
                "source_file": "customer.cbl"
            }
        },
        # REDEFINES example
        {
            "chunk_type": "DATA_DIVISION_WS_SECTION",
            "chunk_name": "ACCOUNT_DATA",
            "code": """
      * Account Balance Structure with REDEFINES
       01  ACCOUNT-DATA.
           05  ACCT-ID              PIC 9(8).
           05  ACCT-TYPE            PIC X.
               88  ACCT-CHECKING    VALUE 'C'.
               88  ACCT-SAVINGS     VALUE 'S'.
               88  ACCT-CREDIT      VALUE 'R'.
           05  ACCT-BALANCE         PIC S9(7)V99 COMP-3.
           05  ACCT-LAST-ACTIVITY   PIC 9(8).
           05  ACCT-STATUS REDEFINES ACCT-LAST-ACTIVITY PIC X(8).
            """,
            "metadata": {
                "source_file": "account.cbl"
            }
        },
        # 77-level variables and EQU directives
        {
            "chunk_type": "DATA_DIVISION_WS_SECTION",
            "chunk_name": "CONSTANTS",
            "code": """
      * Constants and standalone fields
       77  MAX-ACCOUNTS            PIC 9(2) VALUE 10.
       77  SERVICE-CHARGE-RATE     PIC 9V99 VALUE 1.50.
       77  INTEREST-RATE           PIC 9V999 VALUE 0.025.

       QWE     EQU     123
       MAX-REC-SIZE    EQU     32760
       RECORD-EOF      EQU     'EOF'
       STATUS-OK       EQU     0
        """,
            "metadata": {
                "source_file": "constants.cbl"
            }
        },
        # OCCURS with DEPENDING ON example
        {
            "chunk_type": "DATA_DIVISION_WS_SECTION",
            "chunk_name": "TRANSACTION_DATA",
            "code": """
      * Transaction data with variable-length array
       01  TRANSACTION-TABLE.
           05  TRANS-COUNT         PIC 9(3) COMP.
           05  TRANS-ENTRY         OCCURS 1 TO 500 TIMES
                                   DEPENDING ON TRANS-COUNT.
               10  TRANS-ID        PIC 9(8).
               10  TRANS-DATE      PIC 9(8).
               10  TRANS-AMOUNT    PIC S9(9)V99 COMP-3.
               10  TRANS-TYPE      PIC XX.
                   88  TRANS-DEBIT  VALUE 'DB'.
                   88  TRANS-CREDIT VALUE 'CR'.
                   88  TRANS-ADJUST VALUE 'AD'.
        """,
            "metadata": {
                "source_file": "transaction.cbl"
            }
        },
        # SYNC and USAGE example
        {
            "chunk_type": "DATA_DIVISION_WS_SECTION",
            "chunk_name": "CALCULATION_DATA",
            "code": """
      * Calculation data with various COMP types
       01  CALC-AREA.
           05  CALC-BINARY-FULL    PIC S9(8) COMP SYNC.
           05  CALC-BINARY-HALF    PIC S9(4) COMP-4.
           05  CALC-PACKED         PIC S9(15)V99 COMP-3.
           05  CALC-FLOAT          COMP-1.
           05  CALC-DOUBLE         COMP-2.
        """,
            "metadata": {
                "source_file": "calculations.cbl"
            }
        }
    ]

    # Create the extractor
    extractor = CobolDataDefinitionExtractor()

    # Process each sample chunk
    all_results = []

    for i, chunk in enumerate(sample_chunks):
        logger.info(f"\n\n===== Processing Sample Chunk {i+1}: {chunk['chunk_name']} =====")

        # Process the chunk
        program_id = f"TEST-PROG-{i+1}"
        data_items = extractor.process_chunk(chunk, program_id)

        logger.info(f"Extracted {len(data_items)} data items from {chunk['chunk_name']}")
        all_results.extend(data_items)

        # Print detailed information for each item
        for j, item in enumerate(data_items):
            logger.info(f"\n--- Item {j+1}: {item.get('name', 'Unknown')} ---")

            # Print all fields in the item
            for field, value in sorted(item.items()):
                if field != 'description':  # Print description last as it may be long
                    logger.info(f"{field}: {value}")

            # Print description last
            if 'description' in item:
                logger.info(f"description: {item['description']}")

    logger.info(f"\n\n===== SUMMARY =====")
    logger.info(f"Total items extracted: {len(all_results)}")

    # Count by item type
    item_types = {}
    for item in all_results:
        item_type = item.get('item_type', 'unknown')
        item_types[item_type] = item_types.get(item_type, 0) + 1

    logger.info("Items by type:")
    for item_type, count in item_types.items():
        logger.info(f"  {item_type}: {count}")

    # Count by data type
    data_types = {}
    for item in all_results:
        data_type = item.get('data_type', 'unknown')
        data_types[data_type] = data_types.get(data_type, 0) + 1

    logger.info("Items by data type:")
    for data_type, count in sorted(data_types.items()):
        logger.info(f"  {data_type}: {count}")

    return all_results

if __name__ == "__main__":
    results = test_data_extractor()

    # Export results to JSON file for inspection
    output_dir = os.path.dirname(os.path.abspath(__file__))
    output_path = os.path.join(output_dir, "extracted_cobol_data.json")

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)

    print(f"\nExtracted data saved to: {output_path}")