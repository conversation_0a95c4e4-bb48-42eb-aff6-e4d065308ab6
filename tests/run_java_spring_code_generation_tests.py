#!/usr/bin/env python3
"""
Test runner for Java Spring code generation tests.
Runs comprehensive tests for the code generation functionality with real LLM invocation.
"""
import sys
import os
import pytest
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def run_code_generation_tests():
    """Run all code generation tests."""
    print("🚀 Running Java Spring Code Generation Tests")
    print("=" * 60)
    
    # Test files to run
    test_files = [
        "tests/plugins/targets/java_spring/test_code_generation_integration.py",
        "tests/plugins/targets/java_spring/test_java_code_generator_llm.py"
    ]
    
    # Check if test files exist
    missing_files = []
    for test_file in test_files:
        if not os.path.exists(test_file):
            missing_files.append(test_file)
    
    if missing_files:
        print(f"❌ Missing test files: {missing_files}")
        return False
    
    # Run tests with pytest
    pytest_args = [
        "-v",  # Verbose output
        "-s",  # Don't capture output
        "--tb=short",  # Short traceback format
        "--durations=10",  # Show 10 slowest tests
        "-x",  # Stop on first failure
    ]
    
    # Add test files
    pytest_args.extend(test_files)
    
    print(f"Running pytest with args: {pytest_args}")
    print("-" * 60)
    
    # Run the tests
    exit_code = pytest.main(pytest_args)
    
    print("-" * 60)
    if exit_code == 0:
        print("✅ All tests passed!")
    else:
        print(f"❌ Tests failed with exit code: {exit_code}")
    
    return exit_code == 0

def run_quick_tests():
    """Run quick tests without LLM invocation."""
    print("🏃 Running Quick Tests (No LLM)")
    print("=" * 60)
    
    pytest_args = [
        "-v",
        "-s", 
        "--tb=short",
        "-m", "not slow and not integration",  # Skip slow and integration tests
        "tests/plugins/targets/java_spring/"
    ]
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("✅ Quick tests passed!")
    else:
        print(f"❌ Quick tests failed with exit code: {exit_code}")
    
    return exit_code == 0

def run_integration_tests():
    """Run integration tests with real LLM."""
    print("🔗 Running Integration Tests (With LLM)")
    print("=" * 60)
    
    pytest_args = [
        "-v",
        "-s",
        "--tb=short", 
        "-m", "integration",  # Only integration tests
        "tests/plugins/targets/java_spring/"
    ]
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("✅ Integration tests passed!")
    else:
        print(f"❌ Integration tests failed with exit code: {exit_code}")
    
    return exit_code == 0

def run_specific_test(test_name):
    """Run a specific test by name."""
    print(f"🎯 Running Specific Test: {test_name}")
    print("=" * 60)
    
    pytest_args = [
        "-v",
        "-s",
        "--tb=short",
        "-k", test_name,  # Run tests matching the name
        "tests/plugins/targets/java_spring/"
    ]
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print(f"✅ Test '{test_name}' passed!")
    else:
        print(f"❌ Test '{test_name}' failed with exit code: {exit_code}")
    
    return exit_code == 0

def check_test_environment():
    """Check if the test environment is properly set up."""
    print("🔍 Checking Test Environment")
    print("=" * 60)
    
    # Check if required modules can be imported
    required_modules = [
        "src.plugins.targets.java_spring.plugin",
        "src.plugins.targets.java_spring.agents.code_generator",
        "src.platform.tools.knowledge_database",
        "src.platform.tools.code_generator_tools",
        "llm_settings"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ Missing modules: {missing_modules}")
        print("Please ensure all dependencies are installed and the project structure is correct.")
        return False
    
    print("\n✅ Test environment is ready!")
    return True

def main():
    """Main test runner."""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "check":
            success = check_test_environment()
        elif command == "quick":
            success = check_test_environment() and run_quick_tests()
        elif command == "integration":
            success = check_test_environment() and run_integration_tests()
        elif command == "all":
            success = check_test_environment() and run_code_generation_tests()
        elif command.startswith("test_"):
            success = check_test_environment() and run_specific_test(command)
        else:
            print(f"Unknown command: {command}")
            print("Usage: python run_java_spring_code_generation_tests.py [check|quick|integration|all|test_name]")
            return 1
    else:
        # Default: run all tests
        success = check_test_environment() and run_code_generation_tests()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
