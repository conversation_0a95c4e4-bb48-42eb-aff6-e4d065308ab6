"""
Pytest configuration and fixtures for the RAM2 test suite.
"""
import os
import sys
import tempfile
import shutil
import pytest
from unittest.mock import Mock, MagicMock
from pathlib import Path

# Add project root to Python path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.platform.agents.base_agent import AgentInput, AgentOutput
from src.platform.tools.knowledge_database import KnowledgeDatabase


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def mock_llm():
    """Mock LLM for testing without API calls."""
    mock = Mock()
    mock.invoke.return_value = "Mock LLM response"
    return mock


@pytest.fixture
def mock_knowledge_db():
    """Mock knowledge database for testing."""
    mock = Mock(spec=KnowledgeDatabase)
    # Mock all the methods from KnowledgeDatabase (updated for refactored interface)
    mock.get_chunk_by_name.return_value = {}
    mock.get_chunks_by_program.return_value = []
    mock.get_cobol_java_mappings.return_value = {}
    mock.save_cobol_java_mapping.return_value = None
    mock.get_java_data_structures.return_value = []
    mock.save_java_data_structure.return_value = None
    mock.insert_program.return_value = None
    mock.insert_chunks.return_value = None
    mock.update_chunk_analysis.return_value = None
    mock.get_program_details.return_value = {}
    mock.get_data_definitions_by_program.return_value = []
    mock.search_data_definitions.return_value = []
    mock.get_all_programs.return_value = []
    mock.get_cobol_structure_hierarchy.return_value = {}
    mock.get_structure_hierarchy.return_value = {}
    mock.save_data_definition.return_value = None
    mock.insert_cobol_data_definitions.return_value = None
    return mock


@pytest.fixture
def sample_agent_input(temp_dir):
    """Sample AgentInput for testing."""
    return AgentInput(
        working_directory=temp_dir,
        knowledge_base={
            "file_stats": {"total_files": 1, "languages": {"cobol": 1}},
            "organized_directory": temp_dir
        }
    )


@pytest.fixture
def sample_cobol_code():
    """Sample COBOL code for testing."""
    return """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. SAMPLE-PROG.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 CUSTOMER-ID PIC 9(5).
       01 CUSTOMER-NAME PIC X(30).

       PROCEDURE DIVISION.
       MAIN-PARA.
           MOVE 12345 TO CUSTOMER-ID.
           MOVE 'JOHN DOE' TO CUSTOMER-NAME.
           DISPLAY 'Customer: ' CUSTOMER-NAME.
           STOP RUN.
    """


@pytest.fixture
def sample_chunk_doc():
    """Sample chunk documentation for testing."""
    return {
        "program_id": "SAMPLE-PROG",
        "chunk_name": "MAIN-PARA",
        "business_name": "Customer Processing",
        "business_description": "Process customer information",
        "functional_spec": "Display customer details",
        "input_parameters": [
            {
                "name": "CUSTOMER-ID",
                "type": "variable",
                "business_name": "Customer ID",
                "description": "Unique customer identifier"
            }
        ],
        "output_parameters": [
            {
                "name": "CUSTOMER-NAME",
                "type": "variable",
                "business_name": "Customer Name",
                "description": "Customer full name"
            }
        ],
        "code": "MOVE 12345 TO CUSTOMER-ID."
    }


@pytest.fixture
def mock_neo4j_connector():
    """Mock Neo4j connector for testing."""
    mock = Mock()
    mock.get_nodes_perform_call_relations.return_value = []
    mock.close.return_value = None
    return mock


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, temp_dir):
    """Set up test environment variables."""
    monkeypatch.setenv("OUT_DIR", temp_dir)
    monkeypatch.setenv("OPENAI_API_KEY", "test-key")


@pytest.fixture
def mock_file_system(temp_dir):
    """Create mock file system structure for testing."""
    # Create test directories
    organized_dir = os.path.join(temp_dir, "organized")
    cobol_dir = os.path.join(organized_dir, "cobol")
    os.makedirs(cobol_dir, exist_ok=True)

    # Create test files
    test_cobol_file = os.path.join(cobol_dir, "TEST.cbl")
    with open(test_cobol_file, 'w') as f:
        f.write("""
       IDENTIFICATION DIVISION.
       PROGRAM-ID. TEST.
       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY 'Hello World'.
           STOP RUN.
        """)

    return {
        "organized_dir": organized_dir,
        "cobol_dir": cobol_dir,
        "test_file": test_cobol_file
    }


# Skip tests that require external services by default
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "requires_neo4j: mark test as requiring Neo4j database"
    )
    config.addinivalue_line(
        "markers", "requires_llm: mark test as requiring LLM API access"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to skip external service tests by default."""
    skip_neo4j = pytest.mark.skip(reason="Neo4j not available")
    skip_llm = pytest.mark.skip(reason="LLM API not available")

    for item in items:
        if "requires_neo4j" in item.keywords:
            if not config.getoption("--run-neo4j", default=False):
                item.add_marker(skip_neo4j)
        if "requires_llm" in item.keywords:
            if not config.getoption("--run-llm", default=False):
                item.add_marker(skip_llm)


def pytest_addoption(parser):
    """Add custom command line options."""
    parser.addoption(
        "--run-neo4j", action="store_true", default=False,
        help="Run tests that require Neo4j database"
    )
    parser.addoption(
        "--run-llm", action="store_true", default=False,
        help="Run tests that require LLM API access"
    )
