#!/usr/bin/env python

import os
import sys
import logging
import argparse
import pytest
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)


@pytest.mark.skip(reason="COBOL preprocessor depends on missing tools.standalone module")
def test_cobol_call_tree(input_file=None, project_name=None):
    """
    Test the COBOL preprocessor with call tree analysis integration.

    Args:
        input_file: Path to the COBOL file to process (relative to project root)
        project_name: Name of the project folder to use in output path
    """
    try:
        from src.plugins.legacy.cobol.tools.preprocessor import CobolPreprocessor

        # Initialize the preprocessor
        logger.info("Initializing COBOL preprocessor")
        preprocessor = CobolPreprocessor()

        # Default values
        if input_file is None:
            input_file = "code_samples/CBACT01C.cbl"

        if project_name is None:
            project_name = "test_call_tree"

        # Convert input path to absolute if needed
        if not os.path.isabs(input_file):
            input_file = os.path.join(PROJECT_ROOT, input_file)

        # Get file name for output
        file_name = os.path.basename(input_file)

        # Output path
        output_dir = os.path.join(PROJECT_ROOT, "out", project_name, "preprocessed", "cobol")
        output_file = os.path.join(output_dir, file_name)

        # Make sure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Process the file
        logger.info(f"Processing {input_file} -> {output_file} with call tree analysis")
        result = preprocessor.preprocess_file(input_file, output_file)

        if result:
            logger.info("Preprocessing with call tree analysis completed successfully")

            # Check if flow diagram was created (REKT still runs after call tree)
            flow_dir = os.path.join(output_dir, "rekt")
            if os.path.exists(flow_dir):
                logger.info(f"REKT analysis output directory found: {flow_dir}")

                # List generated files
                for root, dirs, files in os.walk(flow_dir):
                    rel_path = os.path.relpath(root, flow_dir)
                    if rel_path != ".":
                        logger.info(f"Files in {rel_path}:")
                    else:
                        logger.info("Files in root:")
                    for file in files:
                        logger.info(f"  - {file}")
            else:
                logger.warning(f"REKT analysis output directory not found: {flow_dir}")

            # Check JSON output directory for IR files
            json_dir = os.path.join(output_dir, "json")
            if os.path.exists(json_dir):
                logger.info(f"IR JSON directory found: {json_dir}")

                # List generated JSON files
                for file in os.listdir(json_dir):
                    if file.endswith(".json"):
                        logger.info(f"  - {file}")
            else:
                logger.warning(f"IR JSON directory not found: {json_dir}")

            # Use assertions for pytest compatibility
            assert True, "Call tree analysis completed successfully"
            return True
        else:
            logger.error("Preprocessing with call tree analysis failed")
            assert False, "Preprocessing with call tree analysis should not fail"

    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)
        assert False, f"Test failed with exception: {str(e)}"


def main():
    """Parse command line arguments and run the test."""
    parser = argparse.ArgumentParser(description="Test COBOL preprocessor with call tree analysis")
    parser.add_argument("-f", "--file", help="Path to the COBOL file to process")
    parser.add_argument("-p", "--project", help="Project name for output directory")
    parser.add_argument("--debug", action="store_true", help="Set logging level to DEBUG")
    args = parser.parse_args()

    # Set debug logging level if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")

    result = test_cobol_call_tree(args.file, args.project)
    return 0 if result else 1


if __name__ == "__main__":
    sys.exit(main())