Based on the following description of a COBOL program and its functionality, generate a JSON object with the recap information fields:

- Block: the logical system domain or business area the function belongs to.
- Application: the name of the broader system or application containing the program.
- MainframeProgram: {{module_id}}.
- FunctionalElement: the main function, use case, or process the program implements.
- Version: version number (use "1.0" if unknown).
- DocumentStatus: status of the document (use "Draft" if unknown).


Descriptions available:

{{context_elements_text}}


Please respond in valid JSON format only, like the following:

```json
{
  "Block:": "Customer Management",
  "Application:": "Mainframe CRM System",
  "Mainframe Program:": "CUS1234C",
  "Functional Element:": "Customer Address Validation",
  "Version:": "1.0",
  "Document Status:": "Draft"
}
```

nothing else.