Based on the following description of procedure/chunk of a COBOL program and its functionality, generate a JSON object with the recap information fields:

- Program: {{module_id}}.
- Procedure/Chunk: {{chunk_name}}
- Generated: {{recap_time_stamp}}
- Document status: status of the document (use "Draft" if unknown).
- Document owner:: owner of the generated document (use 'NA' if unknown)


Descriptions available:

{{context_elements_text}}


Please respond in valid JSON format only, like the following:

```json
{
  "Program:": "CUS1234C",
  "Procedure/Chunk:": "1000-ACCTFILE-GET-NEXT",
  "Generated:": "025-05-21 00:17:59",
  "Document status:": "Draft",
  "Document owner:": "NA"
}
```

nothing else.