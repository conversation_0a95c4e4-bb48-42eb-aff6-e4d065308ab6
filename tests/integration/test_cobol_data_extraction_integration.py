"""
Integration tests for COBOL data extraction through the code preprocessor.
Tests that the LLM analyzer is properly invoked and results are saved to database.
"""
import pytest
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
import json

from src.platform.agents.code_preprocessor import CodePreprocessorAgent
from src.platform.agents.base_agent import AgentInput
from src.platform.tools.knowledge_database import KnowledgeDatabase
from src.platform.plugins.plugin_loader import get_plugin_loader


class TestCobolDataExtractionIntegration:
    """Integration tests for COBOL data extraction through code preprocessor."""

    @pytest.fixture
    def temp_workspace(self):
        """Create a temporary workspace for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def knowledge_db(self, temp_workspace):
        """Create a test knowledge database."""
        db_path = os.path.join(temp_workspace, "test_knowledge.db")
        return KnowledgeDatabase(db_path)

    @pytest.fixture
    def sample_cobol_files(self, temp_workspace):
        """Create sample COBOL files for testing."""
        # Create organized directory structure
        organized_dir = os.path.join(temp_workspace, "organized")
        cobol_dir = os.path.join(organized_dir, "cobol")
        os.makedirs(cobol_dir, exist_ok=True)

        # Sample COBOL program with DATA DIVISION
        cobol_content = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. TESTPROG.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 WS-CUSTOMER-RECORD.
           05 WS-CUSTOMER-ID       PIC 9(6).
           05 WS-CUSTOMER-NAME     PIC X(30).
           05 WS-CUSTOMER-BALANCE  PIC S9(7)V99 COMP-3.
           05 WS-CUSTOMER-STATUS   PIC X.
               88 ACTIVE-CUSTOMER  VALUE 'A'.
               88 INACTIVE-CUSTOMER VALUE 'I'.
       01 WS-COUNTERS.
           05 WS-RECORD-COUNT      PIC 9(5) VALUE ZERO.
           05 WS-ERROR-COUNT       PIC 9(3) VALUE ZERO.

       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY 'Processing customers'.
           STOP RUN.
        """

        cobol_file = os.path.join(cobol_dir, "TESTPROG.cbl")
        with open(cobol_file, 'w', encoding='utf-8') as f:
            f.write(cobol_content)

        # File info structure that code_preprocessor expects
        file_info = [{
            "filename": "TESTPROG.cbl",
            "language": "cobol",
            "relative_path": "TESTPROG.cbl",
            "organized_path": cobol_file
        }]

        return {
            "organized_dir": organized_dir,
            "cobol_dir": cobol_dir,
            "cobol_file": cobol_file,
            "file_info": file_info
        }

    @pytest.fixture
    def mock_chunker_with_data_division(self):
        """Mock chunker that returns DATA_DIVISION chunks."""
        mock_chunker = Mock()
        mock_chunker.chunk_file.return_value = [
            {
                'chunk_type': 'DATA_DIVISION_WORKING_STORAGE',
                'chunk_name': 'WS-CUSTOMER-RECORD',
                'code': """
       01 WS-CUSTOMER-RECORD.
           05 WS-CUSTOMER-ID       PIC 9(6).
           05 WS-CUSTOMER-NAME     PIC X(30).
           05 WS-CUSTOMER-BALANCE  PIC S9(7)V99 COMP-3.
           05 WS-CUSTOMER-STATUS   PIC X.
               88 ACTIVE-CUSTOMER  VALUE 'A'.
               88 INACTIVE-CUSTOMER VALUE 'I'.
                """,
                'metadata': {'section': 'WORKING-STORAGE'}
            },
            {
                'chunk_type': 'DATA_DIVISION_WORKING_STORAGE',
                'chunk_name': 'WS-COUNTERS',
                'code': """
       01 WS-COUNTERS.
           05 WS-RECORD-COUNT      PIC 9(5) VALUE ZERO.
           05 WS-ERROR-COUNT       PIC 9(3) VALUE ZERO.
                """,
                'metadata': {'section': 'WORKING-STORAGE'}
            },
            {
                'chunk_type': 'PROCEDURE_DIVISION',
                'chunk_name': 'MAIN-PARA',
                'code': """
       MAIN-PARA.
           DISPLAY 'Processing customers'.
           STOP RUN.
                """,
                'metadata': {'section': 'PROCEDURE'}
            }
        ]
        return mock_chunker

    @pytest.fixture
    def mock_preprocessor(self):
        """Mock preprocessor that succeeds."""
        mock_preprocessor = Mock()
        mock_preprocessor.preprocess_file.return_value = True
        return mock_preprocessor

    def test_cobol_analyzer_invoked_for_data_division_chunks(self, temp_workspace, sample_cobol_files,
                                                           mock_chunker_with_data_division, mock_preprocessor):
        """Test that COBOL analyzer is invoked for DATA_DIVISION chunks."""
        # Setup
        agent = CodePreprocessorAgent()

        # Mock the plugin system to return our mocked components
        with patch('src.platform.plugins.plugin_loader.get_plugin_loader') as mock_get_loader:
            mock_plugin_loader = Mock()
            mock_language_plugin = Mock()
            mock_analyzer = Mock()

            # Configure the analyzer to return data items
            mock_analyzer.can_extract_data_definitions.return_value = True
            mock_analyzer.analyze.return_value = {
                'file_path': 'WS-CUSTOMER-RECORD',
                'language': 'cobol',
                'data_items': [
                    {
                        'name': 'WS-CUSTOMER-ID',
                        'program_id': 'TESTPROG',
                        'level': 5,
                        'data_type': 'NUMERIC',
                        'length': 6,
                        'item_type': 'variable',
                        'business_name': 'Customer ID',
                        'description': 'Customer identification number'
                    },
                    {
                        'name': 'WS-CUSTOMER-NAME',
                        'program_id': 'TESTPROG',
                        'level': 5,
                        'data_type': 'ALPHANUMERIC',
                        'length': 30,
                        'item_type': 'variable',
                        'business_name': 'Customer Name',
                        'description': 'Customer full name'
                    }
                ],
                'chunk_type': 'DATA_DIVISION'
            }

            mock_language_plugin.get_preprocessor.return_value = mock_preprocessor
            mock_language_plugin.get_chunker.return_value = mock_chunker_with_data_division
            mock_language_plugin.get_analyzer.return_value = mock_analyzer

            mock_plugin_loader.get_available_languages.return_value = ['cobol']
            mock_plugin_loader.get_language_plugin.return_value = mock_language_plugin
            mock_get_loader.return_value = mock_plugin_loader

            # Create agent input
            agent_input = AgentInput(
                working_directory=temp_workspace,
                knowledge_base={
                    "organized_directory": sample_cobol_files["organized_dir"],
                    "file_info": sample_cobol_files["file_info"]
                }
            )

            # Execute
            result = agent.process(agent_input)

            # Verify
            assert result.success, f"Agent processing failed: {result.errors}"

            # Verify analyzer was called for DATA_DIVISION chunks
            assert mock_analyzer.can_extract_data_definitions.called
            assert mock_analyzer.analyze.called

            # Verify it was called with the right chunk types
            analyze_calls = mock_analyzer.analyze.call_args_list
            # The analyzer should be called for each DATA_DIVISION chunk
            # Check that it was called at least twice (for the 2 DATA_DIVISION chunks)
            data_division_calls = [call for call in analyze_calls
                                 if len(call[0]) > 0]  # Check that calls were made with content
            assert len(data_division_calls) >= 2, f"Should have called analyzer for at least 2 chunks, got {len(data_division_calls)}"

    def test_data_extraction_results_saved_to_database(self, temp_workspace, sample_cobol_files,
                                                     mock_chunker_with_data_division, mock_preprocessor):
        """Test that data extraction results are properly saved to the database."""
        # Setup
        agent = CodePreprocessorAgent()

        # Use real database for this test
        db_path = os.path.join(temp_workspace, "test_knowledge.db")

        with patch('src.platform.plugins.plugin_loader.get_plugin_loader') as mock_get_loader:
            mock_plugin_loader = Mock()
            mock_language_plugin = Mock()
            mock_analyzer = Mock()

            # Configure the analyzer to return realistic data items
            mock_analyzer.can_extract_data_definitions.return_value = True
            mock_analyzer.analyze.return_value = {
                'file_path': 'test_chunk',
                'language': 'cobol',
                'data_items': [
                    {
                        'name': 'WS-CUSTOMER-ID',
                        'program_id': 'TESTPROG',
                        'level': 5,
                        'data_type': 'NUMERIC',
                        'length': 6,
                        'item_type': 'variable',
                        'business_name': 'Customer ID',
                        'description': 'Customer identification number',
                        'source_file_name': 'TESTPROG.cbl',
                        'top_parent_datastructure_name': 'WS-CUSTOMER-RECORD'
                    }
                ],
                'chunk_type': 'DATA_DIVISION'
            }

            mock_language_plugin.get_preprocessor.return_value = mock_preprocessor
            mock_language_plugin.get_chunker.return_value = mock_chunker_with_data_division
            mock_language_plugin.get_analyzer.return_value = mock_analyzer

            mock_plugin_loader.get_available_languages.return_value = ['cobol']
            mock_plugin_loader.get_language_plugin.return_value = mock_language_plugin
            mock_get_loader.return_value = mock_plugin_loader

            # Create agent input
            agent_input = AgentInput(
                working_directory=temp_workspace,
                knowledge_base={
                    "organized_directory": sample_cobol_files["organized_dir"],
                    "file_info": sample_cobol_files["file_info"]
                }
            )

            # Execute
            result = agent.process(agent_input)

            # Verify processing succeeded
            assert result.success, f"Agent processing failed: {result.errors}"

            # Verify data was saved to database
            # The KnowledgeDatabase uses the default path: out/knowledge_base.sqlite3
            out_dir = os.path.join(os.getcwd(), "out")
            default_db_path = os.path.join(out_dir, "knowledge_base.sqlite3")

            # Check if database file exists
            assert os.path.exists(default_db_path), f"Database file not found at {default_db_path}"

            # Debug: Check what's in the database directly
            import sqlite3
            with sqlite3.connect(default_db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM variables WHERE program_id = ?', ('TESTPROG',))
                count = cursor.fetchone()[0]
                print(f"DEBUG: Found {count} variables for TESTPROG in database")

                cursor.execute('SELECT name, data_type FROM variables WHERE program_id = ?', ('TESTPROG',))
                raw_data = cursor.fetchall()
                print(f"DEBUG: Raw data: {raw_data}")

            kb = KnowledgeDatabase(default_db_path)
            retrieved_data = kb.get_data_definitions_by_program('TESTPROG')

            assert len(retrieved_data) > 0, f"No data definitions found in database. Database path: {default_db_path}"

            # Verify the data content
            customer_id_item = next((item for item in retrieved_data if item['name'] == 'WS-CUSTOMER-ID'), None)
            assert customer_id_item is not None, "WS-CUSTOMER-ID not found in database"
            assert customer_id_item['data_type'] == 'NUMERIC'
            assert customer_id_item['length'] == 6

    def test_llm_analyzer_integration_with_real_plugin(self, temp_workspace, sample_cobol_files):
        """Test integration with real COBOL plugin and LLM analyzer."""
        # Setup
        agent = CodePreprocessorAgent()

        # Create a mock chunker that returns DATA_DIVISION chunks
        mock_chunker = Mock()
        mock_chunker.chunk_file.return_value = [
            {
                'chunk_type': 'DATA_DIVISION_WORKING_STORAGE',
                'chunk_name': 'WS-CUSTOMER-RECORD',
                'code': """
       01 WS-CUSTOMER-RECORD.
           05 WS-CUSTOMER-ID       PIC 9(6).
           05 WS-CUSTOMER-NAME     PIC X(30).
                """,
                'metadata': {'section': 'WORKING-STORAGE'}
            }
        ]

        # Mock preprocessor
        mock_preprocessor = Mock()
        mock_preprocessor.preprocess_file.return_value = True

        # Use real plugin loader but mock the chunker and preprocessor
        with patch('src.platform.plugins.plugin_loader.get_plugin_loader') as mock_get_loader:
            # Get the real plugin loader and load plugins
            real_plugin_loader = get_plugin_loader()
            real_plugin_loader.load_all_plugins()

            # Get the real COBOL plugin
            cobol_plugin = real_plugin_loader.get_language_plugin('cobol')
            assert cobol_plugin is not None, "COBOL plugin not loaded"

            # Replace chunker and preprocessor with mocks
            cobol_plugin.get_chunker = Mock(return_value=mock_chunker)
            cobol_plugin.get_preprocessor = Mock(return_value=mock_preprocessor)

            # Configure mock plugin loader to return our modified plugin
            mock_plugin_loader = Mock()
            mock_plugin_loader.get_available_languages.return_value = ['cobol']
            mock_plugin_loader.get_language_plugin.return_value = cobol_plugin
            mock_get_loader.return_value = mock_plugin_loader

            # Create agent input
            agent_input = AgentInput(
                working_directory=temp_workspace,
                knowledge_base={
                    "organized_directory": sample_cobol_files["organized_dir"],
                    "file_info": sample_cobol_files["file_info"]
                }
            )

            # Execute
            result = agent.process(agent_input)

            # Verify
            assert result.success, f"Agent processing failed: {result.errors}"

            # Verify that the real analyzer was used and data was extracted
            db_path = os.path.join(temp_workspace, "knowledge.db")
            if os.path.exists(db_path):
                kb = KnowledgeDatabase(db_path)
                retrieved_data = kb.get_data_definitions_by_program('TESTPROG')

                # Should have extracted some data items
                assert len(retrieved_data) >= 0, "Data extraction should have been attempted"

    def test_analyzer_not_invoked_for_non_data_division_chunks(self, temp_workspace, sample_cobol_files):
        """Test that analyzer is not invoked for non-DATA_DIVISION chunks."""
        # Setup
        agent = CodePreprocessorAgent()

        # Mock chunker that returns only PROCEDURE_DIVISION chunks
        mock_chunker = Mock()
        mock_chunker.chunk_file.return_value = [
            {
                'chunk_type': 'PROCEDURE_DIVISION',
                'chunk_name': 'MAIN-PARA',
                'code': """
       MAIN-PARA.
           DISPLAY 'Processing customers'.
           STOP RUN.
                """,
                'metadata': {'section': 'PROCEDURE'}
            }
        ]

        mock_preprocessor = Mock()
        mock_preprocessor.preprocess_file.return_value = True

        with patch('src.platform.plugins.plugin_loader.get_plugin_loader') as mock_get_loader:
            mock_plugin_loader = Mock()
            mock_language_plugin = Mock()
            mock_analyzer = Mock()

            # Configure the analyzer
            mock_analyzer.can_extract_data_definitions.return_value = False  # Should return False for PROCEDURE_DIVISION

            mock_language_plugin.get_preprocessor.return_value = mock_preprocessor
            mock_language_plugin.get_chunker.return_value = mock_chunker
            mock_language_plugin.get_analyzer.return_value = mock_analyzer

            mock_plugin_loader.get_available_languages.return_value = ['cobol']
            mock_plugin_loader.get_language_plugin.return_value = mock_language_plugin
            mock_get_loader.return_value = mock_plugin_loader

            # Create agent input
            agent_input = AgentInput(
                working_directory=temp_workspace,
                knowledge_base={
                    "organized_directory": sample_cobol_files["organized_dir"],
                    "file_info": sample_cobol_files["file_info"]
                }
            )

            # Execute
            result = agent.process(agent_input)

            # Verify
            assert result.success, f"Agent processing failed: {result.errors}"

            # Verify analyzer was checked but not called for analysis
            assert mock_analyzer.can_extract_data_definitions.called
            # analyze should not be called since can_extract_data_definitions returned False
            assert not mock_analyzer.analyze.called

    def test_error_handling_in_data_extraction(self, temp_workspace, sample_cobol_files):
        """Test error handling when data extraction fails."""
        # Setup
        agent = CodePreprocessorAgent()

        mock_chunker = Mock()
        mock_chunker.chunk_file.return_value = [
            {
                'chunk_type': 'DATA_DIVISION_WORKING_STORAGE',
                'chunk_name': 'WS-CUSTOMER-RECORD',
                'code': """
       01 WS-CUSTOMER-RECORD.
           05 WS-CUSTOMER-ID       PIC 9(6).
                """,
                'metadata': {'section': 'WORKING-STORAGE'}
            }
        ]

        mock_preprocessor = Mock()
        mock_preprocessor.preprocess_file.return_value = True

        with patch('src.platform.plugins.plugin_loader.get_plugin_loader') as mock_get_loader:
            mock_plugin_loader = Mock()
            mock_language_plugin = Mock()
            mock_analyzer = Mock()

            # Configure the analyzer to raise an exception
            mock_analyzer.can_extract_data_definitions.return_value = True
            mock_analyzer.analyze.side_effect = Exception("LLM analyzer failed")

            mock_language_plugin.get_preprocessor.return_value = mock_preprocessor
            mock_language_plugin.get_chunker.return_value = mock_chunker
            mock_language_plugin.get_analyzer.return_value = mock_analyzer

            mock_plugin_loader.get_available_languages.return_value = ['cobol']
            mock_plugin_loader.get_language_plugin.return_value = mock_language_plugin
            mock_get_loader.return_value = mock_plugin_loader

            # Create agent input
            agent_input = AgentInput(
                working_directory=temp_workspace,
                knowledge_base={
                    "organized_directory": sample_cobol_files["organized_dir"],
                    "file_info": sample_cobol_files["file_info"]
                }
            )

            # Execute
            result = agent.process(agent_input)

            # Verify that processing continues despite the error
            assert result.success, "Agent should handle data extraction errors gracefully"

            # Verify analyzer was attempted
            assert mock_analyzer.analyze.called
