"""
Integration tests for the fixes to knowledge miner and code generator issues.
Tests the complete workflow to ensure all fixes work together.
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock

from src.platform.agents.knowledge_miner.core import KnowledgeMinerAgent
from src.platform.agents.code_generator import CodeGeneratorAgent
from src.platform.agents.base_agent import AgentInput


class TestFixesIntegration:
    """Integration tests for the fixes."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()

    @patch('src.platform.plugins.plugin_loader.get_plugin_loader')
    def test_knowledge_miner_to_code_generator_workflow(self, mock_plugin_loader):
        """Test the complete workflow from knowledge miner to code generator."""
        # Setup knowledge miner
        knowledge_miner = KnowledgeMinerAgent()
        knowledge_miner.knowledge_db = Mock()
        knowledge_miner.parameter_extractor = Mock()
        knowledge_miner.business_analyzer = Mock()
        knowledge_miner.spec_generator = Mock()
        knowledge_miner.utils = Mock()

        # Mock parameter extraction
        input_params = [
            {"name": "CUSTOMER_ID", "type": "string", "business_name": "Customer Identifier"}
        ]
        output_params = [
            {"name": "STATUS_CODE", "type": "string", "business_name": "Processing Status"}
        ]
        
        knowledge_miner.parameter_extractor.extract_input_parameters.return_value = input_params
        knowledge_miner.parameter_extractor.extract_output_parameters.return_value = output_params
        
        # Mock business analysis
        knowledge_miner.business_analyzer.extract_business_name.return_value = "Customer Processing"
        knowledge_miner.business_analyzer.extract_business_description.return_value = "Processes customer data"
        knowledge_miner.business_analyzer.extract_business_logic_with_aggregation.return_value = "Main customer processing logic"
        
        # Mock other components
        knowledge_miner.spec_generator.generate_functional_specification.return_value = "Functional spec"
        knowledge_miner.utils.initialize_analysis_result.return_value = {}
        knowledge_miner.utils.prepare_variable_context.return_value = "Variable context"
        knowledge_miner.utils.prepare_dependency_context.return_value = "Dependency context"
        knowledge_miner.utils.aggregate_business_rules.return_value = "Aggregated rules"
        knowledge_miner.utils.save_analysis_to_markdown.return_value = "Markdown saved"

        # Test knowledge miner saves parameters to database
        program_id = "CBACT01C"
        chunk_name = "MAIN-PROCESS"
        code_content = "COBOL code here"
        
        result = knowledge_miner._analyze_chunk_with_dependencies(
            program_id, chunk_name, code_content, [], []
        )
        
        # Verify parameters were saved to database
        knowledge_miner.knowledge_db.insert_data_definitions.assert_called_once()
        call_args = knowledge_miner.knowledge_db.insert_data_definitions.call_args
        saved_params = call_args[0][1]
        
        assert len(saved_params) == 2  # 1 input + 1 output
        assert any(p['parameter_type'] == 'input' and p['name'] == 'CUSTOMER_ID' for p in saved_params)
        assert any(p['parameter_type'] == 'output' and p['name'] == 'STATUS_CODE' for p in saved_params)

        # Setup code generator with mock plugin
        mock_plugin = Mock()
        mock_code_generator = Mock()
        
        # Mock plugin to return generated files
        plugin_result = {
            "success": True,
            "generated_files": {
                "Application.java": "public class Application { }",
                "CustomerService.java": "public class CustomerService { }"
            },
            "project_structure_created": True
        }
        
        mock_code_generator.generate_code.return_value = plugin_result
        mock_plugin.get_code_generator.return_value = mock_code_generator
        mock_plugin_loader.return_value.get_target_plugin.return_value = mock_plugin

        # Create knowledge base with business logic summaries
        knowledge_base = {
            "target_technology": "java_spring",
            "business_logic_summaries": {
                program_id: {
                    chunk_name: "Main customer processing logic"
                }
            },
            "config": {"project_name": "customer-service"}
        }

        # Test code generator
        code_generator = CodeGeneratorAgent()
        input_data = AgentInput(
            working_directory=self.temp_dir,
            knowledge_base=knowledge_base
        )

        code_result = code_generator.process(input_data)

        # Verify code generator success
        assert code_result.success is True
        assert "java_spring" in code_result.message
        
        # Verify generated code format is correct for code_reviewer
        generated_code = code_result.knowledge_base_updates["generated_code"]
        assert isinstance(generated_code, dict)
        assert "java_spring_microservice" in generated_code
        
        service_files = generated_code["java_spring_microservice"]
        assert "Application.java" in service_files
        assert "CustomerService.java" in service_files

        # Verify plugin was called with correct format
        call_args = mock_code_generator.generate_code.call_args
        source_analysis = call_args[0][0]
        config = call_args[0][1]
        
        # Check that knowledge base was passed correctly
        assert "knowledge_base" in source_analysis
        kb = source_analysis["knowledge_base"]
        assert kb["business_logic_summaries"][program_id][chunk_name] == "Main customer processing logic"
        
        # Check that working directory was added to config
        assert config["working_directory"] == self.temp_dir

    def test_java_spring_plugin_generates_code_from_business_logic(self):
        """Test that Java Spring plugin generates code from business logic."""
        from src.plugins.targets.java_spring.plugin import JavaSpringCodeGenerator
        from unittest.mock import Mock

        generator = JavaSpringCodeGenerator()

        # Mock the dependencies to avoid requiring COBOL preprocessing
        generator.dependency_analyzer.analyze_dependencies = Mock(return_value={
            "success": True,
            "chunks": []
        })
        generator.state_manager.load_or_initialize_generation_state = Mock(return_value={
            "current_phase": "complete",
            "completed": True
        })
        generator.react_coordinator.should_continue_generation = Mock(return_value=False)
        generator.react_coordinator.create_success_result = Mock(return_value={
            "success": True,
            "message": "Generation completed successfully",
            "generated_files": {
                "Application.java": """package com.bank.service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}""",
                "CBACT01C_CUSTOMER-PROCESS_Service.java": """package com.bank.service.service;

import org.springframework.stereotype.Service;

@Service
public class CBACT01C_CUSTOMER-PROCESS_Service {
    // Process customer account information
    public void processCustomer() {
        // Implementation here
    }
}""",
                "CBACT01C_BALANCE-CHECK_Service.java": """package com.bank.service.service;

import org.springframework.stereotype.Service;

@Service
public class CBACT01C_BALANCE-CHECK_Service {
    // Check account balance and validate
    public void checkBalance() {
        // Implementation here
    }
}"""
            }
        })

        source_analysis = {
            "knowledge_base": {
                "business_logic_summaries": {
                    "CBACT01C": {
                        "CUSTOMER-PROCESS": "Process customer account information",
                        "BALANCE-CHECK": "Check account balance and validate"
                    }
                }
            }
        }

        config = {
            "project_name": "banking-service",
            "working_directory": self.temp_dir,
            "package_name": "com.bank.service"
        }

        result = generator.generate_code(source_analysis, config)

        # Verify success
        assert result["success"] is True
        assert "generated_files" in result

        generated_files = result["generated_files"]

        # Should have main application class
        assert "Application.java" in generated_files
        app_content = generated_files["Application.java"]
        assert "@SpringBootApplication" in app_content
        assert "public class Application" in app_content

        # Should have service classes for each business logic chunk
        assert "CBACT01C_CUSTOMER-PROCESS_Service.java" in generated_files
        assert "CBACT01C_BALANCE-CHECK_Service.java" in generated_files

        # Check service content
        customer_service = generated_files["CBACT01C_CUSTOMER-PROCESS_Service.java"]
        assert "package com.bank.service.service;" in customer_service
        assert "public class CBACT01C_CUSTOMER-PROCESS_Service" in customer_service
        assert "@Service" in customer_service
        assert "Process customer account information" in customer_service

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
