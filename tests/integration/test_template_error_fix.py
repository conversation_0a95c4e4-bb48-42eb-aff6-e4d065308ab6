"""
Integration test to verify the template error fix.
Tests that the original "'list' object has no attribute 'items'" error is fixed.
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch
from src.plugins.targets.java_spring.plugin import JavaSpringPlugin


class TestTemplateErrorFix:
    """Test that the original template error is fixed."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield tmpdir

    def test_original_error_is_fixed(self, temp_dir):
        """Test that the original error scenario now works."""
        plugin = JavaSpringPlugin()
        code_generator = plugin.get_code_generator()

        # Mock the dependencies to avoid requiring COBOL preprocessing
        code_generator.dependency_analyzer.analyze_dependencies = Mock(return_value={
            "success": True,
            "chunks": []
        })
        code_generator.state_manager.load_or_initialize_generation_state = Mock(return_value={
            "current_phase": "complete",
            "completed": True
        })
        code_generator.react_coordinator.should_continue_generation = Mock(return_value=False)
        code_generator.react_coordinator.create_success_result = Mock(return_value={
            "success": True,
            "message": "Generation completed successfully",
            "generated_files": ["test_file.java"]
        })

        # This is the exact scenario that would have caused the original error:
        # custom_properties as a list instead of a dictionary
        source_analysis = {
            "knowledge_base": {
                "business_logic_summaries": {
                    "TEST_PROG": {
                        "TEST_CHUNK": "Test business logic"
                    }
                }
            }
        }

        config = {
            "project_name": "test-project",
            "working_directory": temp_dir,
            "package_name": "com.test.app",
            "custom_properties": ["prop1", "prop2"],  # This would cause the original error
            "custom_config": "invalid_config"  # This would also cause template errors
        }

        # This should NOT raise "'list' object has no attribute 'items'" error
        result = code_generator.generate_code(source_analysis, config)

        # Verify the result is successful
        assert result["success"] is True
        assert "generated_files" in result
        assert len(result["generated_files"]) > 0

    def test_mappings_extraction_handles_lists(self):
        """Test that mappings extraction handles lists correctly."""
        from src.plugins.targets.java_spring.agents.code_generator import JavaCodeGenerator

        # Create a code generator instance
        mock_llm = Mock()
        mock_knowledge_db = Mock()
        code_generator = JavaCodeGenerator(mock_llm, mock_knowledge_db)

        # Test response that returns mappings as a list (the original error case)
        response_with_list = """
        ```json
        [{"cobol_var1": "javaVar1"}, {"cobol_var2": "javaVar2"}]
        ```
        """

        # This should NOT crash and should return a dictionary
        mappings = code_generator._extract_mappings_from_response(response_with_list)

        # Verify it's a dictionary and we can call .items() on it
        assert isinstance(mappings, dict)

        # This should not raise an AttributeError
        for key, value in mappings.items():
            assert isinstance(key, str)
            assert isinstance(value, str)

    def test_template_validation_fixes_data_types(self):
        """Test that template validation fixes invalid data types."""
        from src.plugins.targets.java_spring.tools.project_manager.file_generator import ProjectFileGenerator

        # Create a file generator instance
        mock_template_manager = Mock()
        generator = ProjectFileGenerator(
            template_manager=mock_template_manager,
            default_package="com.test.app",
            default_group_id="com.test",
            default_artifact_id="test-app",
            default_version="1.0.0",
            java_version="17",
            spring_boot_version="3.2.0"
        )

        # Test context with invalid data types that would cause template errors
        invalid_context = {
            "app_name": "test-app",
            "custom_properties": ["prop1", "prop2"],  # Should be dict
            "custom_config": "invalid_string",  # Should be dict
            "server_port": 8080
        }

        # This should fix the invalid data types
        validated_context = generator._validate_template_context(invalid_context)

        # Verify the fixes
        assert isinstance(validated_context["custom_properties"], dict)
        assert isinstance(validated_context["custom_config"], dict)
        assert validated_context["app_name"] == "test-app"  # Valid data preserved
        assert validated_context["server_port"] == 8080  # Valid data preserved

    def test_code_generator_template_validation(self):
        """Test that code generator template validation works."""
        from src.plugins.targets.java_spring.agents.code_generator import JavaCodeGenerator

        mock_llm = Mock()
        mock_knowledge_db = Mock()
        code_generator = JavaCodeGenerator(mock_llm, mock_knowledge_db)

        # Test context with invalid data types
        invalid_context = {
            "chunk_docs": ["not", "a", "dict"],  # Should be dict
            "existing_mappings": ["invalid", "list"],  # Should be dict
            "java_structures": "not_a_list"  # Should be list
        }

        # This should fix the invalid data types
        validated_context = code_generator._validate_template_context(invalid_context)

        # Verify the fixes
        assert isinstance(validated_context["chunk_docs"], dict)
        assert isinstance(validated_context["existing_mappings"], dict)
        assert isinstance(validated_context["java_structures"], list)

    def test_template_paths_are_corrected(self, temp_dir):
        """Test that template paths no longer include incorrect prefixes."""
        from src.plugins.targets.java_spring.tools.project_manager.file_generator import ProjectFileGenerator

        mock_template_manager = Mock()
        mock_template_manager.render_template.return_value = "# Generated content"

        generator = ProjectFileGenerator(
            template_manager=mock_template_manager,
            default_package="com.test.app",
            default_group_id="com.test",
            default_artifact_id="test-app",
            default_version="1.0.0",
            java_version="17",
            spring_boot_version="3.2.0"
        )

        # Create required directories
        resources_dir = os.path.join(temp_dir, "src/main/resources")
        os.makedirs(resources_dir, exist_ok=True)

        # This should call render_template with the corrected path
        generator.generate_application_properties(temp_dir)

        # Verify the correct template path is used (corrected path without prefix)
        mock_template_manager.render_template.assert_called_once()
        call_args = mock_template_manager.render_template.call_args
        assert call_args[0][0] == "application.properties.j2"


if __name__ == "__main__":
    pytest.main([__file__])
