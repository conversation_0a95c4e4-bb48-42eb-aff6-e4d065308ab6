"""
Integration test for COBOL preprocessor to verify complete IR generation flow.
This test verifies that the preprocessor actually creates IR files that can be used by the chunker.
"""
import pytest
import os
import json
import tempfile

from src.plugins.legacy.cobol.tools.preprocessor import CobolPreprocessor
from src.plugins.legacy.cobol.tools.chunkers.cobol_chunker import CobolChunker


class TestCobolPreprocessorIntegration:
    """Integration tests for COBOL preprocessor IR generation."""

    @pytest.fixture
    def preprocessor(self):
        """Create CobolPreprocessor instance."""
        return CobolPreprocessor()

    @pytest.fixture
    def chunker(self):
        """Create CobolChunker instance."""
        return CobolChunker()

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    def sample_cobol_file(self, temp_dir):
        """Create a sample COBOL file for testing."""
        cobol_content = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CBACT01C.

       ENVIRONMENT DIVISION.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 WS-COUNTER PIC 9(3) VALUE 0.
       01 WS-MESSAGE PIC X(50) VALUE 'Hello World'.

       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY WS-MESSAGE.
           ADD 1 TO WS-COUNTER.
           STOP RUN.
        """

        cobol_file = os.path.join(temp_dir, "CBACT01C.cbl")
        with open(cobol_file, 'w', encoding='utf-8') as f:
            f.write(cobol_content)

        return cobol_file

    def test_complete_preprocessing_and_chunking_flow(self, preprocessor, chunker, sample_cobol_file, temp_dir):
        """Test the complete flow from preprocessing to chunking using generated IR files."""
        # Set up output paths
        output_file = os.path.join(temp_dir, "CBACT01C_preprocessed.cbl")

        # Run preprocessing
        result = preprocessor.preprocess_file(sample_cobol_file, output_file)

        # Verify preprocessing succeeded
        assert result is True
        assert os.path.exists(output_file)

        # Verify IR file was created
        json_dir = os.path.join(os.path.dirname(output_file), "json")
        ir_file = os.path.join(json_dir, "CBACT01C_preprocessed.json")
        assert os.path.exists(ir_file)

        # Verify IR file is valid JSON
        with open(ir_file, 'r') as f:
            ir_data = json.load(f)

        # Verify IR structure
        assert "metadata" in ir_data
        assert "nodes" in ir_data
        assert "relationships" in ir_data
        assert ir_data["metadata"]["module_id"] == "CBACT01C"

        # Test chunking with the generated IR file
        chunks_dir = os.path.join(temp_dir, "chunks")
        os.makedirs(chunks_dir, exist_ok=True)

        # Run chunker
        chunk_results = chunker.chunk_file(output_file, chunks_dir)

        # Verify chunking succeeded
        assert len(chunk_results) > 0

        # Verify chunks were created
        for chunk_result in chunk_results:
            # The chunker creates files with specific naming patterns
            chunk_file = chunk_result.get('path')
            if chunk_file and os.path.exists(chunk_file):
                # Verify chunk has content
                with open(chunk_file, 'r') as f:
                    content = f.read()
                    assert len(content.strip()) > 0
            else:
                # Check if files exist in chunks directory with expected patterns
                chunk_files = [f for f in os.listdir(chunks_dir) if f.endswith('.txt')]
                assert len(chunk_files) > 0, f"No chunk files found in {chunks_dir}"

    def test_ir_file_contains_expected_structure(self, preprocessor, sample_cobol_file, temp_dir):
        """Test that the generated IR file contains the expected COBOL structure."""
        output_file = os.path.join(temp_dir, "CBACT01C_preprocessed.cbl")

        # Run preprocessing
        result = preprocessor.preprocess_file(sample_cobol_file, output_file)
        assert result is True

        # Check IR file
        json_dir = os.path.join(os.path.dirname(output_file), "json")
        ir_file = os.path.join(json_dir, "CBACT01C_preprocessed.json")

        with open(ir_file, 'r') as f:
            ir_data = json.load(f)

        # Check for expected COBOL divisions
        node_types = [node["type"] for node in ir_data["nodes"]]

        # Should have at least a module node
        assert "CobolModule" in node_types

        # If it's a fallback IR, should have division nodes
        if ir_data["metadata"].get("fallback", False):
            expected_divisions = [
                "CobolIdentificationDivision",
                "CobolDataDivision",
                "CobolProcedureDivision"
            ]

            for division in expected_divisions:
                assert division in node_types, f"Missing {division} in IR nodes"

    def test_preprocessor_handles_missing_copybooks_gracefully(self, preprocessor, temp_dir):
        """Test that preprocessor handles missing copybooks gracefully."""
        # Create COBOL file with COPY statement
        cobol_content = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. TESTCOPY.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       COPY NONEXISTENT.
       01 WS-VAR PIC X(10).

       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY 'Test'.
           STOP RUN.
        """

        cobol_file = os.path.join(temp_dir, "TESTCOPY.cbl")
        with open(cobol_file, 'w', encoding='utf-8') as f:
            f.write(cobol_content)

        output_file = os.path.join(temp_dir, "TESTCOPY_preprocessed.cbl")

        # Should still succeed even with missing copybook
        result = preprocessor.preprocess_file(cobol_file, output_file)
        assert result is True

        # Should still create IR file
        json_dir = os.path.join(os.path.dirname(output_file), "json")
        ir_file = os.path.join(json_dir, "TESTCOPY_preprocessed.json")
        assert os.path.exists(ir_file)

    def test_call_tree_and_rekt_analysis_execution(self, preprocessor, sample_cobol_file, temp_dir):
        """Test that call tree and REKT analysis are executed and produce output."""
        output_file = os.path.join(temp_dir, "CBACT01C_preprocessed.cbl")

        # Run preprocessing
        result = preprocessor.preprocess_file(sample_cobol_file, output_file)
        assert result is True

        # Check that REKT analysis output directory was created
        rekt_dir = os.path.join(os.path.dirname(output_file), "rekt")
        # Note: REKT analysis might not create files if the COBOL program is too simple
        # but the directory should be created
        assert os.path.exists(rekt_dir)

    def test_preprocessor_version_property(self, preprocessor):
        """Test that preprocessor has a version property."""
        version = preprocessor.version
        assert isinstance(version, str)
        assert len(version) > 0
        # Should be in semantic version format
        assert "." in version

    def test_preprocessor_encoding_property(self, preprocessor):
        """Test that preprocessor has an encoding property."""
        encoding = preprocessor.encoding
        assert isinstance(encoding, str)
        assert encoding == "utf-8"
