"""
Integration tests for workflow error handling scenarios.
"""
import pytest
import os
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock

from src.platform.agents.orchestrator import OrchestratorAgent
from src.platform.agents.code_preprocessor import CodePreprocessorAgent
from src.platform.agents.knowledge_miner.core import KnowledgeMinerAgent
from src.platform.agents.code_reviewer import CodeReviewerAgent
from src.platform.agents.base_agent import AgentInput


class TestWorkflowErrorHandling:
    """Integration tests for error handling across the workflow."""

    @pytest.fixture
    def temp_working_dir(self):
        """Create temporary working directory with sample files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create directory structure
            organized_dir = os.path.join(temp_dir, "organized", "cobol")
            os.makedirs(organized_dir, exist_ok=True)

            # Create sample COBOL file
            cobol_content = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CBACT01C.

       ENVIRONMENT DIVISION.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 WS-COUNTER PIC 9(3) VALUE 0.

       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY 'Hello World'.
           STOP RUN.
            """

            cobol_file = os.path.join(organized_dir, "CBACT01C.cbl")
            with open(cobol_file, 'w', encoding='utf-8') as f:
                f.write(cobol_content)

            yield temp_dir

    @pytest.fixture
    def mock_knowledge_db(self):
        """Create mock knowledge database."""
        mock_db = Mock()
        mock_db.insert_program.return_value = None
        mock_db.insert_chunk.return_value = None
        mock_db.get_program_details.return_value = None
        return mock_db

    def test_preprocessor_ir_failure_recovery(self, temp_working_dir, mock_knowledge_db):
        """Test that workflow continues when IR generation fails but fallback works."""
        # Create organized directory structure
        organized_dir = os.path.join(temp_working_dir, "organized")
        cobol_dir = os.path.join(organized_dir, "cobol")
        os.makedirs(cobol_dir, exist_ok=True)

        # Create a sample COBOL file
        cobol_file = os.path.join(cobol_dir, "TESTPROG.cbl")
        with open(cobol_file, 'w') as f:
            f.write("IDENTIFICATION DIVISION.\nPROGRAM-ID. TESTPROG.\n")

        # Create agent input with proper structure
        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={
                "organized_directory": organized_dir,
                "file_info": [{
                    "filename": "TESTPROG.cbl",
                    "language": "cobol",
                    "relative_path": "TESTPROG.cbl",
                    "organized_path": cobol_file
                }]
            }
        )

        # Mock the COBOL preprocessor to fail IR generation but succeed with fallback
        with patch('src.platform.agents.code_preprocessor.KnowledgeDatabase') as mock_kb_class:
            mock_kb_class.return_value = mock_knowledge_db

            with patch('src.plugins.legacy.cobol.tools.parsers.cobol_parser_cli.src.orchestrator.Orchestrator') as mock_orchestrator_class:
                mock_orchestrator = Mock()
                mock_orchestrator.process_files.return_value = (False, ["IR generation failed"])
                mock_orchestrator.copybook_expander.expand_source.return_value = "expanded content"
                mock_orchestrator.text_normalizer.normalize.return_value = "normalized content"
                mock_orchestrator_class.return_value = mock_orchestrator

                # Run code preprocessor
                preprocessor = CodePreprocessorAgent()
                result = preprocessor.process(input_data)

                # Should succeed with fallback chunks
                assert result.success is True
                assert "preprocessing completed" in result.message.lower()

    def test_chunker_fallback_when_ir_missing(self, temp_working_dir, mock_knowledge_db):
        """Test that chunker creates fallback chunks when IR files are missing."""
        # Create a minimal organized directory structure
        organized_dir = os.path.join(temp_working_dir, "organized")
        cobol_dir = os.path.join(organized_dir, "cobol")
        os.makedirs(cobol_dir, exist_ok=True)

        # Create a simple COBOL file
        test_file = os.path.join(cobol_dir, "test.cbl")
        with open(test_file, 'w') as f:
            f.write("IDENTIFICATION DIVISION.\nPROGRAM-ID. TEST.\nPROCEDURE DIVISION.\nDISPLAY 'HELLO'.\nSTOP RUN.")

        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={
                "organized_directory": organized_dir,
                "file_info": [{
                    "filename": "test.cbl",
                    "language": "cobol",
                    "relative_path": "test.cbl",
                    "organized_path": test_file
                }]
            }
        )

        with patch('src.platform.agents.code_preprocessor.KnowledgeDatabase') as mock_kb_class:
            mock_kb_class.return_value = mock_knowledge_db

            # Run preprocessor without creating IR files
            preprocessor = CodePreprocessorAgent()
            result = preprocessor.process(input_data)

            # Should succeed with fallback chunks
            assert result.success is True

            # Verify that chunks were created (even if fallback)
            # The exact number depends on the fallback implementation
            assert isinstance(result.knowledge_base_updates, dict)

    def test_knowledge_miner_with_missing_json_files(self, temp_working_dir, mock_knowledge_db):
        """Test knowledge miner handling of missing JSON files."""
        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={}
        )

        with patch('src.platform.agents.knowledge_miner.core.KnowledgeDatabase') as mock_kb_class:
            mock_kb_class.return_value = mock_knowledge_db

            # Mock the knowledge database to return programs but no JSON data
            mock_knowledge_db.search_programs.return_value = [
                {"program_id": "CBACT01C", "language": "cobol"}
            ]

            knowledge_miner = KnowledgeMinerAgent()
            result = knowledge_miner.process(input_data)

            # Should complete successfully even without JSON files
            assert result.success is True
            assert "dependency-based analysis" in result.message

    def test_code_reviewer_missing_generated_code(self, temp_working_dir):
        """Test code reviewer handling of missing generated code."""
        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={"generated_code": {}}  # Empty generated code
        )

        code_reviewer = CodeReviewerAgent()
        result = code_reviewer.process(input_data)

        # Should fail gracefully with clear error message
        assert result.success is False
        assert "No generated code found to review" in result.message
        assert "Missing generated_code in knowledge base" in result.errors

    def test_orchestrator_agent_failure_handling(self, temp_working_dir):
        """Test orchestrator handling of agent failures."""
        config = {
            "project_name": "test_project",
            "project_type": "Full Code Conversion",
            "microservice_options": {
                "java_version": "Java 17",
                "framework": "Spring Boot"
            }
        }

        workflow = ["code_preprocessor", "knowledge_miner", "code_reviewer"]

        with patch('src.platform.agents.orchestrator.OrchestratorAgent._initialize_agents') as mock_init_agents:
            # Create mock agents where one fails
            mock_preprocessor = Mock()
            mock_preprocessor.set_up = Mock()
            mock_preprocessor.report_progress = Mock()
            mock_preprocessor.process.return_value = Mock(
                success=True,
                message="Preprocessing completed",
                knowledge_base_updates={},
                artifacts={}
            )

            mock_knowledge_miner = Mock()
            mock_knowledge_miner.set_up = Mock()
            mock_knowledge_miner.report_progress = Mock()
            mock_knowledge_miner.process.return_value = Mock(
                success=False,
                message="Knowledge mining failed",
                knowledge_base_updates={},
                artifacts={},
                errors=["JSON file not found"]
            )

            mock_code_reviewer = Mock()
            mock_code_reviewer.set_up = Mock()
            mock_code_reviewer.report_progress = Mock()
            mock_code_reviewer.process.return_value = Mock(
                success=False,
                message="No generated code found",
                knowledge_base_updates={},
                artifacts={},
                errors=["Missing generated_code"]
            )

            mock_agents = {
                "code_preprocessor": mock_preprocessor,
                "knowledge_miner": mock_knowledge_miner,
                "code_reviewer": mock_code_reviewer
            }
            mock_init_agents.return_value = mock_agents

            orchestrator = OrchestratorAgent()
            orchestrator.set_workflow(workflow)
            orchestrator.set_config(config)

            # Mock the error handler to continue processing
            orchestrator._handle_error = Mock(return_value="continue")

            # Run workflow synchronously for testing
            orchestrator._run_workflow(temp_working_dir)
            result = orchestrator.current_state

            # Should handle agent failures gracefully
            assert result is not None

    def test_end_to_end_error_recovery(self, temp_working_dir):
        """Test end-to-end error recovery across multiple agents."""
        config = {
            "project_name": "test_project",
            "project_type": "Full Code Conversion",
            "microservice_options": {
                "java_version": "Java 17",
                "framework": "Spring Boot"
            }
        }

        # Test with minimal workflow
        workflow = ["code_preprocessor"]

        with patch('src.platform.agents.orchestrator.OrchestratorAgent._initialize_agents') as mock_init_agents:
            # Create mock agent that succeeds with fallback
            mock_preprocessor = Mock()
            mock_preprocessor.set_up = Mock()
            mock_preprocessor.report_progress = Mock()
            mock_preprocessor.process.return_value = Mock(
                success=True,
                message="Preprocessing completed with fallback",
                knowledge_base_updates={"preprocessing_stats": {"cobol": 1}},
                artifacts={}
            )

            mock_agents = {"code_preprocessor": mock_preprocessor}
            mock_init_agents.return_value = mock_agents

            orchestrator = OrchestratorAgent()
            orchestrator.set_workflow(workflow)
            orchestrator.set_config(config)

            # Run workflow synchronously for testing
            orchestrator._run_workflow(temp_working_dir)
            result = orchestrator.current_state

            # Should complete successfully with fallback mechanisms
            assert result is not None
            assert result["status"] == "completed"

    @pytest.mark.integration
    def test_cascading_failure_prevention(self, temp_working_dir):
        """Test that failures in one agent don't cascade to break the entire workflow."""
        # This test ensures that the error handling improvements prevent
        # the cascading failures seen in the original log

        config = {
            "project_name": "test_project",
            "project_type": "Full Code Conversion"
        }

        workflow = ["code_preprocessor", "knowledge_miner"]

        with patch('src.platform.agents.orchestrator.OrchestratorAgent._initialize_agents') as mock_init_agents:
            # Create agents where first succeeds with fallback, second handles missing data
            mock_preprocessor = Mock()
            mock_preprocessor.set_up = Mock()
            mock_preprocessor.report_progress = Mock()
            mock_preprocessor.process.return_value = Mock(
                success=True,
                message="Preprocessing completed with fallback",
                knowledge_base_updates={"preprocessing_stats": {"cobol": 1}},
                artifacts={}
            )

            mock_knowledge_miner = Mock()
            mock_knowledge_miner.set_up = Mock()
            mock_knowledge_miner.report_progress = Mock()
            mock_knowledge_miner.process.return_value = Mock(
                success=True,
                message="Knowledge mining completed (no JSON data found)",
                knowledge_base_updates={},
                artifacts={}
            )

            mock_agents = {
                "code_preprocessor": mock_preprocessor,
                "knowledge_miner": mock_knowledge_miner
            }
            mock_init_agents.return_value = mock_agents

            orchestrator = OrchestratorAgent()
            orchestrator.set_workflow(workflow)
            orchestrator.set_config(config)

            # Run workflow synchronously for testing
            orchestrator._run_workflow(temp_working_dir)
            result = orchestrator.current_state

            # Both agents should have been called despite the first having issues
            assert mock_preprocessor.process.called
            assert mock_knowledge_miner.process.called

            # Workflow should complete
            assert result is not None
