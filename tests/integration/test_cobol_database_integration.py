"""
Integration tests for COBOL data extraction and database storage.
Tests the complete flow from code analysis to database persistence.
"""
import pytest
import os
import tempfile
import shutil
import sqlite3
from unittest.mock import Mock, patch

from src.platform.tools.knowledge_database import KnowledgeDatabase
from src.plugins.legacy.cobol.plugin import <PERSON><PERSON><PERSON><PERSON>yzer
from src.platform.agents.code_preprocessor import CodePreprocessorAgent
from src.platform.agents.base_agent import AgentInput


class TestCobolDatabaseIntegration:
    """Integration tests for COBOL data extraction and database storage."""

    @pytest.fixture
    def temp_workspace(self):
        """Create a temporary workspace for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def test_database(self, temp_workspace):
        """Create a test database."""
        db_path = os.path.join(temp_workspace, "test_knowledge.db")
        return KnowledgeDatabase(db_path)

    @pytest.fixture
    def cobol_analyzer(self):
        """Create COBOL analyzer instance."""
        return CobolAnalyzer()

    def test_cobol_analyzer_data_extraction_and_database_storage(self, test_database, cobol_analyzer):
        """Test that COBOL analyzer extracts data and stores it correctly in database."""
        # Sample COBOL DATA DIVISION content
        cobol_content = """
       01 WS-CUSTOMER-RECORD.
           05 WS-CUSTOMER-ID       PIC 9(6).
           05 WS-CUSTOMER-NAME     PIC X(30).
           05 WS-CUSTOMER-BALANCE  PIC S9(7)V99 COMP-3.
           05 WS-CUSTOMER-STATUS   PIC X.
               88 ACTIVE-CUSTOMER  VALUE 'A'.
               88 INACTIVE-CUSTOMER VALUE 'I'.
       01 WS-COUNTERS.
           05 WS-RECORD-COUNT      PIC 9(5) VALUE ZERO.
           05 WS-ERROR-COUNT       PIC 9(3) VALUE ZERO.
        """
        
        # Execute analysis
        result = cobol_analyzer.analyze(cobol_content, 'TESTPROG.cbl')
        
        # Verify analysis result structure
        assert 'data_items' in result
        assert result['language'] == 'cobol'
        assert result['chunk_type'] == 'DATA_DIVISION'
        
        data_items = result['data_items']
        assert len(data_items) > 0, "Should extract data items"
        
        # Verify data item structure
        for item in data_items:
            assert 'name' in item
            assert 'data_type' in item
            assert 'program_id' in item
            assert item['program_id'] == 'TESTPROG'
        
        # Store in database
        test_database.insert_cobol_data_definitions('TESTPROG', data_items)
        
        # Retrieve from database
        retrieved_items = test_database.get_data_definitions_by_program('TESTPROG')
        
        # Verify database storage
        assert len(retrieved_items) == len(data_items), f"Expected {len(data_items)} items, got {len(retrieved_items)}"
        
        # Verify specific data items
        item_names = [item['name'] for item in retrieved_items]
        assert 'WS-CUSTOMER-ID' in item_names
        assert 'WS-CUSTOMER-NAME' in item_names
        assert 'WS-RECORD-COUNT' in item_names
        
        # Verify data types are preserved
        customer_id = next(item for item in retrieved_items if item['name'] == 'WS-CUSTOMER-ID')
        assert customer_id['data_type'] == 'NUMERIC'
        assert customer_id['length'] == 6
        
        customer_name = next(item for item in retrieved_items if item['name'] == 'WS-CUSTOMER-NAME')
        assert customer_name['data_type'] == 'ALPHANUMERIC'
        assert customer_name['length'] == 30

    def test_database_schema_compatibility(self, test_database):
        """Test that the database schema supports all required COBOL data fields."""
        # Create sample data item with all possible fields
        sample_item = {
            'name': 'WS-TEST-FIELD',
            'program_id': 'TESTPROG',
            'level': 5,
            'data_type': 'NUMERIC',
            'length': 10,
            'default_value': '0',
            'item_type': 'variable',
            'business_name': 'Test Field',
            'description': 'A test field for schema validation',
            'parent_name': 'WS-PARENT',
            'source_file_name': 'TESTPROG.cbl',
            'top_parent_datastructure_name': 'WS-PARENT',
            'possible_values': ['A', 'B', 'C'],
            'is_signed': False,
            'is_filler': False,
            'is_equ': False,
            'occurs': None,
            'redefines': None,
            'storage_type': 'DISPLAY',
            'decimals': 2
        }
        
        # Insert into database
        test_database.insert_cobol_data_definitions('TESTPROG', [sample_item])
        
        # Retrieve and verify all fields are preserved
        retrieved_items = test_database.get_data_definitions_by_program('TESTPROG')
        assert len(retrieved_items) == 1
        
        retrieved_item = retrieved_items[0]
        
        # Verify core fields
        assert retrieved_item['name'] == 'WS-TEST-FIELD'
        assert retrieved_item['program_id'] == 'TESTPROG'
        assert retrieved_item['level'] == 5
        assert retrieved_item['data_type'] == 'NUMERIC'
        assert retrieved_item['length'] == 10
        assert retrieved_item['business_name'] == 'Test Field'
        
        # Verify boolean fields are handled correctly
        assert retrieved_item['is_signed'] == False
        assert retrieved_item['is_filler'] == False
        assert retrieved_item['is_equ'] == False

    def test_multiple_programs_data_isolation(self, test_database, cobol_analyzer):
        """Test that data from different programs is properly isolated."""
        # Create data for first program
        cobol_content1 = """
       01 WS-PROGRAM1-DATA.
           05 WS-FIELD1 PIC X(10).
        """
        
        result1 = cobol_analyzer.analyze(cobol_content1, 'PROG1.cbl')
        test_database.insert_cobol_data_definitions('PROG1', result1['data_items'])
        
        # Create data for second program
        cobol_content2 = """
       01 WS-PROGRAM2-DATA.
           05 WS-FIELD2 PIC 9(5).
        """
        
        result2 = cobol_analyzer.analyze(cobol_content2, 'PROG2.cbl')
        test_database.insert_cobol_data_definitions('PROG2', result2['data_items'])
        
        # Verify data isolation
        prog1_items = test_database.get_data_definitions_by_program('PROG1')
        prog2_items = test_database.get_data_definitions_by_program('PROG2')
        
        # Each program should only see its own data
        prog1_names = [item['name'] for item in prog1_items]
        prog2_names = [item['name'] for item in prog2_items]
        
        assert 'WS-FIELD1' in prog1_names
        assert 'WS-FIELD1' not in prog2_names
        assert 'WS-FIELD2' in prog2_names
        assert 'WS-FIELD2' not in prog1_names

    def test_database_search_functionality(self, test_database, cobol_analyzer):
        """Test database search functionality for data definitions."""
        # Create test data
        cobol_content = """
       01 WS-CUSTOMER-RECORD.
           05 WS-CUSTOMER-ID       PIC 9(6).
           05 WS-CUSTOMER-NAME     PIC X(30).
           05 WS-ACCOUNT-BALANCE   PIC S9(7)V99.
        """
        
        result = cobol_analyzer.analyze(cobol_content, 'TESTPROG.cbl')
        test_database.insert_cobol_data_definitions('TESTPROG', result['data_items'])
        
        # Test search by name
        search_results = test_database.search_data_definitions('CUSTOMER')
        customer_items = [item for item in search_results if 'CUSTOMER' in item['name']]
        assert len(customer_items) >= 2  # Should find CUSTOMER-ID and CUSTOMER-NAME
        
        # Test search by business name (if implemented)
        search_results = test_database.search_data_definitions('Customer')
        assert len(search_results) >= 0  # Should handle case-insensitive search

    def test_end_to_end_code_preprocessor_database_flow(self, temp_workspace):
        """Test complete end-to-end flow from code preprocessor to database."""
        # Setup test environment
        organized_dir = os.path.join(temp_workspace, "organized")
        cobol_dir = os.path.join(organized_dir, "cobol")
        os.makedirs(cobol_dir, exist_ok=True)
        
        # Create COBOL file
        cobol_content = """
       IDENTIFICATION DIVISION.
       PROGRAM-ID. ENDTOEND.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01 WS-TEST-DATA.
           05 WS-TEST-ID    PIC 9(5).
           05 WS-TEST-NAME  PIC X(20).

       PROCEDURE DIVISION.
       MAIN-PARA.
           DISPLAY 'Test program'.
           STOP RUN.
        """
        
        cobol_file = os.path.join(cobol_dir, "ENDTOEND.cbl")
        with open(cobol_file, 'w', encoding='utf-8') as f:
            f.write(cobol_content)
        
        file_info = [{
            "filename": "ENDTOEND.cbl",
            "language": "cobol",
            "relative_path": "ENDTOEND.cbl",
            "organized_path": cobol_file
        }]
        
        # Mock the plugin system components
        mock_chunker = Mock()
        mock_chunker.chunk_file.return_value = [
            {
                'chunk_type': 'DATA_DIVISION_WORKING_STORAGE',
                'chunk_name': 'WS-TEST-DATA',
                'code': """
       01 WS-TEST-DATA.
           05 WS-TEST-ID    PIC 9(5).
           05 WS-TEST-NAME  PIC X(20).
                """,
                'metadata': {'section': 'WORKING-STORAGE'}
            }
        ]
        
        mock_preprocessor = Mock()
        mock_preprocessor.preprocess_file.return_value = True
        
        # Use real COBOL analyzer
        from src.plugins.legacy.cobol.plugin import CobolAnalyzer
        real_analyzer = CobolAnalyzer()
        
        with patch('src.platform.plugins.plugin_loader.get_plugin_loader') as mock_get_loader:
            mock_plugin_loader = Mock()
            mock_language_plugin = Mock()
            
            mock_language_plugin.get_preprocessor.return_value = mock_preprocessor
            mock_language_plugin.get_chunker.return_value = mock_chunker
            mock_language_plugin.get_analyzer.return_value = real_analyzer
            
            mock_plugin_loader.get_available_languages.return_value = ['cobol']
            mock_plugin_loader.get_language_plugin.return_value = mock_language_plugin
            mock_get_loader.return_value = mock_plugin_loader
            
            # Execute code preprocessor
            agent = CodePreprocessorAgent()
            agent_input = AgentInput(
                working_directory=temp_workspace,
                knowledge_base={
                    "organized_directory": organized_dir,
                    "file_info": file_info
                }
            )
            
            result = agent.process(agent_input)
            
            # Verify processing succeeded
            assert result.success, f"Code preprocessor failed: {result.errors}"
            
            # Verify data was stored in database
            db_path = os.path.join(temp_workspace, "knowledge.db")
            if os.path.exists(db_path):
                kb = KnowledgeDatabase(db_path)
                retrieved_data = kb.get_data_definitions_by_program('ENDTOEND')
                
                # Should have extracted data items
                assert len(retrieved_data) >= 0, "Should have attempted data extraction"
                
                # If data was extracted, verify it
                if len(retrieved_data) > 0:
                    item_names = [item['name'] for item in retrieved_data]
                    # Should contain the fields we defined
                    expected_fields = ['WS-TEST-ID', 'WS-TEST-NAME']
                    found_fields = [field for field in expected_fields if field in item_names]
                    assert len(found_fields) > 0, f"Should find some expected fields, got: {item_names}"
