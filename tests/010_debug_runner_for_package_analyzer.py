import sys
import logging
import json
import shutil
import helpers.test_paths as paths
from src.platform.agents.base_agent import AgentInput
from src.platform.agents.package_analyzer import PackageAnalyzerAgent

def main():

    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("documentation_agent_test")

    current_state = {
        "status": "idle",
        "current_agent": None,
        "progress": 0,
        "working_directory": None,
        "knowledge_base": {},
        "documentation": {},
        "generated_code": {},
        "errors": [],
        "logs": [],
        "start_time": None,
        "agent_progress": {}
    }

    shutil.copy(paths.cobol_path, paths.working_dir_path)
    shutil.copy(paths.copybook_path, paths.working_dir_path)

    agent_input = AgentInput(
        working_directory=paths.working_dir_path,
        knowledge_base=current_state["knowledge_base"]
    )
    package_analyzer = PackageAnalyzerAgent()
    result = package_analyzer.process(agent_input)
    current_state["knowledge_base"].update(result.knowledge_base_updates)
    logger.info(json.dumps(current_state["knowledge_base"], indent=2))
    return 0 if result else 1

if __name__ == "__main__":
    sys.exit(main())
