import helpers.test_paths as paths
import tests.helpers.test_states as states
import tests.helpers.test_knowledge_miner_updates as miner
import logging
import json

from src.platform.agents.base_agent import AgentInput
from src.platform.agents.documentation_gen import DocumentationGeneratorAgent


def main():

    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("documentation_agent_test")

    states.current_state["knowledge_base"].update(states.knowledge_base_updates_from_code_preprocessor)
    states.current_state["knowledge_base"].update(states.knowledge_base_updates_from_cobol_overview)
    states.current_state["knowledge_base"].update(miner.knowledge_base_updates)

    agent_input = AgentInput(
        working_directory=paths.working_dir_path,
        knowledge_base=states.current_state["knowledge_base"]
    )
    documentation_generator = DocumentationGeneratorAgent()
    result = documentation_generator.process(agent_input)
    logger.info(json.dumps(result.knowledge_base_updates, indent=2))

    return 0 if result else 1


if __name__ == "__main__":
    exit(main())
