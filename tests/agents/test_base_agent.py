"""
Unit tests for BaseAgent class.
"""
import pytest
import os
import tempfile
from unittest.mock import Mock, patch
from src.platform.agents.base_agent import BaseAgent, AgentInput, AgentOutput


class _TestableAgent(BaseAgent):
    """Concrete implementation of BaseAgent for testing."""

    def process(self, input_data: AgentInput) -> AgentOutput:
        return AgentOutput(
            success=True,
            message="Test successful",
            knowledge_base_updates={"test": "data"},
            artifacts={"test_artifact": "value"}
        )

    def set_up(self, config: dict) -> None:
        pass


class TestBaseAgent:
    """Test cases for BaseAgent class."""

    def test_agent_initialization(self):
        """Test agent initialization."""
        agent = _TestableAgent("test_agent")
        assert agent.name == "test_agent"
        assert agent.tools == []
        assert agent.logger is not None

    def test_agent_initialization_with_out_dir(self, temp_dir, monkeypatch):
        """Test agent initialization with custom output directory."""
        monkeypatch.setenv("OUT_DIR", temp_dir)
        agent = _TestableAgent("test_agent")
        assert agent.out_dir == temp_dir

    def test_agent_initialization_creates_out_dir(self, monkeypatch):
        """Test agent creates output directory if it doesn't exist."""
        with tempfile.TemporaryDirectory() as temp_dir:
            out_dir = os.path.join(temp_dir, "new_out_dir")
            monkeypatch.setenv("OUT_DIR", out_dir)

            agent = _TestableAgent("test_agent")
            assert os.path.exists(out_dir)
            assert agent.out_dir == out_dir

    def test_agent_initialization_handles_permission_error(self, monkeypatch):
        """Test agent handles permission errors when creating output directory."""
        # Use a path that would cause permission error
        monkeypatch.setenv("OUT_DIR", "/root/restricted")

        with patch('os.makedirs', side_effect=PermissionError("Permission denied")):
            agent = _TestableAgent("test_agent")
            assert agent.out_dir is None

    def test_add_tool(self):
        """Test adding tools to agent."""
        agent = _TestableAgent("test_agent")
        mock_tool = Mock()

        agent.add_tool(mock_tool)
        assert mock_tool in agent.tools
        assert len(agent.tools) == 1

    def test_get_tools(self):
        """Test getting tools from agent."""
        agent = _TestableAgent("test_agent")
        mock_tool1 = Mock()
        mock_tool2 = Mock()

        agent.add_tool(mock_tool1)
        agent.add_tool(mock_tool2)

        tools = agent.get_tools()
        assert len(tools) == 2
        assert mock_tool1 in tools
        assert mock_tool2 in tools

    def test_ask_user(self):
        """Test ask_user method."""
        agent = _TestableAgent("test_agent")
        response = agent.ask_user("Test question?")
        assert response == "User response placeholder"

    def test_process_method(self, sample_agent_input):
        """Test process method implementation."""
        agent = _TestableAgent("test_agent")
        result = agent.process(sample_agent_input)

        assert isinstance(result, AgentOutput)
        assert result.success is True
        assert result.message == "Test successful"
        assert result.knowledge_base_updates == {"test": "data"}
        assert result.artifacts == {"test_artifact": "value"}


class TestAgentInput:
    """Test cases for AgentInput model."""

    def test_agent_input_creation(self, temp_dir):
        """Test AgentInput creation."""
        knowledge_base = {"test": "data"}
        agent_input = AgentInput(
            working_directory=temp_dir,
            knowledge_base=knowledge_base
        )

        assert agent_input.working_directory == temp_dir
        assert agent_input.knowledge_base == knowledge_base

    def test_agent_input_validation(self):
        """Test AgentInput validation."""
        # Test with invalid data types
        with pytest.raises(Exception):  # Pydantic validation error
            AgentInput(
                working_directory=123,  # Should be string
                knowledge_base="invalid"  # Should be dict
            )


class TestAgentOutput:
    """Test cases for AgentOutput model."""

    def test_agent_output_creation(self):
        """Test AgentOutput creation."""
        output = AgentOutput(
            success=True,
            message="Test message",
            knowledge_base_updates={"key": "value"},
            artifacts={"artifact": "data"},
            errors=["error1", "error2"]
        )

        assert output.success is True
        assert output.message == "Test message"
        assert output.knowledge_base_updates == {"key": "value"}
        assert output.artifacts == {"artifact": "data"}
        assert output.errors == ["error1", "error2"]

    def test_agent_output_defaults(self):
        """Test AgentOutput default values."""
        output = AgentOutput(
            success=False,
            message="Test message"
        )

        assert output.success is False
        assert output.message == "Test message"
        assert output.knowledge_base_updates == {}
        assert output.artifacts == {}
        assert output.errors == []

    def test_agent_output_validation(self):
        """Test AgentOutput validation."""
        # Test with invalid data types
        with pytest.raises(Exception):  # Pydantic validation error
            AgentOutput(
                success="invalid",  # Should be bool
                message=123  # Should be string
            )
