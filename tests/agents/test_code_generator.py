"""
Unit tests for CodeGeneratorAgent - Plugin Architecture.
"""
import pytest
import json
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from src.platform.agents.code_generator import CodeGeneratorAgent
from src.platform.agents.base_agent import AgentInput, AgentOutput


class TestCodeGeneratorAgent:
    """Test cases for CodeGeneratorAgent using plugin architecture."""

    @pytest.fixture
    def mock_plugin_loader(self):
        """Create mock plugin loader."""
        loader = Mock()

        # Mock target plugin
        target_plugin = Mock()
        target_plugin.get_name.return_value = "java_spring"
        target_plugin.can_handle_language.return_value = True

        # Mock code generator component
        code_generator = Mock()
        code_generator.generate_code.return_value = {
            "success": True,
            "generated_files": {
                "Application.java": "package com.test; public class Application {}",
                "Controller.java": "package com.test; public class Controller {}"
            },
            "message": "Code generation completed"
        }
        target_plugin.get_code_generator.return_value = code_generator

        loader.get_target_plugins.return_value = {"java_spring": target_plugin}
        loader.get_target_plugin.return_value = target_plugin

        return loader

    @pytest.fixture
    def agent(self):
        """Create CodeGeneratorAgent instance."""
        with patch('src.platform.agents.code_generator.llm_settings') as mock_llm_settings:
            mock_llm_settings.llm = Mock()
            with patch('src.platform.tools.utils.template_manager.get_template_manager') as mock_template_manager:
                mock_template_manager.return_value = Mock()
                return CodeGeneratorAgent()

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    def sample_agent_input(self, temp_dir):
        """Sample agent input for testing."""
        return AgentInput(
            working_directory=temp_dir,
            knowledge_base={
                "target_technology": "java_spring",
                "package_name": "com.test.generated",
                "output_directory": os.path.join(temp_dir, "output"),
                "source_language": "cobol"
            }
        )

    def test_agent_initialization(self, agent):
        """Test agent initialization."""
        assert agent.name == "code_generator"
        assert agent.llm is not None
        assert agent.template_manager is not None

    def test_set_up(self, agent):
        """Test set_up method."""
        config = {"target_technology": "java_spring"}

        # Should not raise any exceptions
        agent.set_up(config)
        assert True

    def test_process_plugin_based_generation(self, agent, sample_agent_input, mock_plugin_loader):
        """Test code generation using plugin architecture."""
        # Mock the plugin loader import inside the process method
        with patch('src.platform.plugins.plugin_loader.get_plugin_loader', return_value=mock_plugin_loader):
            # Set target technology and required parameters in knowledge base
            sample_agent_input.knowledge_base = {
                "target_technology": "java_spring",
                "source_analysis": {"test": "analysis_data"},
                "config": {"test": "config_data"}
            }

            result = agent.process(sample_agent_input)

        assert isinstance(result, AgentOutput)
        assert result.success is True
        assert "java_spring" in result.message

        # Verify plugin was called
        mock_plugin_loader.get_target_plugin.assert_called_with("java_spring")
        target_plugin = mock_plugin_loader.get_target_plugin.return_value
        code_generator = target_plugin.get_code_generator.return_value

        # Verify that generate_code was called with the new format
        # First argument should be source_analysis with knowledge_base wrapper
        # Second argument should be config with working_directory added
        call_args = code_generator.generate_code.call_args
        source_analysis_arg = call_args[0][0]
        config_arg = call_args[0][1]

        # Check source_analysis format
        assert "knowledge_base" in source_analysis_arg
        knowledge_base = source_analysis_arg["knowledge_base"]
        assert knowledge_base["target_technology"] == "java_spring"
        assert knowledge_base["source_analysis"] == {"test": "analysis_data"}
        # Config should have the original data plus working_directory
        assert knowledge_base["config"]["test"] == "config_data"
        assert "working_directory" in knowledge_base["config"]

        # Check config format
        assert config_arg["test"] == "config_data"
        assert "working_directory" in config_arg

    def test_process_unsupported_target(self, agent, sample_agent_input, mock_plugin_loader):
        """Test processing with unsupported target technology."""
        # Mock no target plugin available
        mock_plugin_loader.get_target_plugin.return_value = None

        with patch('src.platform.plugins.plugin_loader.get_plugin_loader', return_value=mock_plugin_loader):
            sample_agent_input.knowledge_base = {"target_technology": "unsupported_tech"}

            result = agent.process(sample_agent_input)

        assert isinstance(result, AgentOutput)
        assert result.success is False
        assert "No plugin available" in result.message

    def test_process_plugin_generation_failure(self, agent, sample_agent_input, mock_plugin_loader):
        """Test handling plugin generation failure."""
        # Mock plugin generation failure
        target_plugin = mock_plugin_loader.get_target_plugin.return_value
        code_generator = target_plugin.get_code_generator.return_value
        code_generator.generate_code.side_effect = Exception("Generation failed")

        with patch('src.platform.plugins.plugin_loader.get_plugin_loader', return_value=mock_plugin_loader):
            sample_agent_input.knowledge_base = {"target_technology": "java_spring"}

            result = agent.process(sample_agent_input)

        assert isinstance(result, AgentOutput)
        assert result.success is False
        assert "Generation failed" in result.message

    def test_plugin_integration(self, agent, mock_plugin_loader):
        """Test integration with plugin system."""
        # Test that agent properly integrates with plugin loader
        # This test verifies the mock setup is correct
        target_plugin = mock_plugin_loader.get_target_plugin.return_value

        assert target_plugin is not None
        assert target_plugin.can_handle_language("cobol") is True

        code_generator = target_plugin.get_code_generator.return_value
        assert code_generator is not None

    def test_process_with_missing_parameters(self, agent, sample_agent_input, mock_plugin_loader):
        """Test code generation with missing source_analysis and config parameters."""
        # Mock the plugin loader import inside the process method
        with patch('src.platform.plugins.plugin_loader.get_plugin_loader', return_value=mock_plugin_loader):
            # Set only target technology, missing source_analysis and config
            sample_agent_input.knowledge_base = {"target_technology": "java_spring"}

            result = agent.process(sample_agent_input)

        assert isinstance(result, AgentOutput)
        assert result.success is True
        assert "java_spring" in result.message

        # Verify plugin was called
        target_plugin = mock_plugin_loader.get_target_plugin.return_value
        code_generator = target_plugin.get_code_generator.return_value

        # Verify that generate_code was called with the new format
        call_args = code_generator.generate_code.call_args
        source_analysis_arg = call_args[0][0]
        config_arg = call_args[0][1]

        # Check source_analysis format - should have knowledge_base wrapper
        assert "knowledge_base" in source_analysis_arg
        knowledge_base = source_analysis_arg["knowledge_base"]
        assert knowledge_base["target_technology"] == "java_spring"

        # Check config format - should have working_directory
        assert "working_directory" in config_arg


