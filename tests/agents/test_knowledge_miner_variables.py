"""
Unit tests for Knowledge Miner variable saving functionality.
Tests the fix for variables not being written to the database.
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock

from src.platform.agents.knowledge_miner.core import KnowledgeMinerAgent
from src.platform.agents.base_agent import AgentInput


class TestKnowledgeMinerVariables:
    """Test knowledge miner variable saving functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.agent = KnowledgeMinerAgent()
        
        # Mock the knowledge database
        self.mock_knowledge_db = Mock()
        self.agent.knowledge_db = self.mock_knowledge_db
        
        # Mock the components
        self.agent.parameter_extractor = Mock()
        self.agent.business_analyzer = Mock()
        self.agent.spec_generator = Mock()
        self.agent.utils = Mock()

    def test_save_parameters_to_database_with_input_and_output(self):
        """Test that input and output parameters are saved to database."""
        # Arrange
        program_id = "TEST_PROGRAM"
        chunk_name = "TEST_CHUNK"
        
        input_parameters = [
            {"name": "INPUT_VAR1", "type": "string", "business_name": "Customer ID"},
            {"name": "INPUT_VAR2", "type": "number", "business_name": "Account Balance"}
        ]
        
        output_parameters = [
            {"name": "OUTPUT_VAR1", "type": "string", "business_name": "Status Message"},
            {"name": "OUTPUT_VAR2", "type": "boolean", "business_name": "Success Flag"}
        ]
        
        # Act
        self.agent._save_parameters_to_database(program_id, chunk_name, input_parameters, output_parameters)
        
        # Assert
        self.mock_knowledge_db.insert_data_definitions.assert_called_once()
        call_args = self.mock_knowledge_db.insert_data_definitions.call_args
        
        # Check program_id
        assert call_args[0][0] == program_id
        
        # Check parameters
        saved_parameters = call_args[0][1]
        assert len(saved_parameters) == 4
        
        # Check input parameters
        input_params = [p for p in saved_parameters if p['parameter_type'] == 'input']
        assert len(input_params) == 2
        assert input_params[0]['name'] == 'INPUT_VAR1'
        assert input_params[0]['business_name'] == 'Customer ID'
        assert input_params[0]['program_id'] == program_id
        assert input_params[0]['chunk_name'] == chunk_name
        
        # Check output parameters
        output_params = [p for p in saved_parameters if p['parameter_type'] == 'output']
        assert len(output_params) == 2
        assert output_params[0]['name'] == 'OUTPUT_VAR1'
        assert output_params[0]['business_name'] == 'Status Message'

    def test_save_parameters_to_database_with_empty_parameters(self):
        """Test that no database call is made when parameters are empty."""
        # Arrange
        program_id = "TEST_PROGRAM"
        chunk_name = "TEST_CHUNK"
        input_parameters = []
        output_parameters = []
        
        # Act
        self.agent._save_parameters_to_database(program_id, chunk_name, input_parameters, output_parameters)
        
        # Assert
        self.mock_knowledge_db.insert_data_definitions.assert_not_called()

    def test_save_parameters_to_database_handles_missing_business_name(self):
        """Test that parameters without business_name use the name field."""
        # Arrange
        program_id = "TEST_PROGRAM"
        chunk_name = "TEST_CHUNK"
        
        input_parameters = [
            {"name": "INPUT_VAR1", "type": "string"}  # No business_name
        ]
        output_parameters = []
        
        # Act
        self.agent._save_parameters_to_database(program_id, chunk_name, input_parameters, output_parameters)
        
        # Assert
        call_args = self.mock_knowledge_db.insert_data_definitions.call_args
        saved_parameters = call_args[0][1]
        assert saved_parameters[0]['business_name'] == 'INPUT_VAR1'

    def test_save_parameters_to_database_handles_exception(self):
        """Test that database exceptions are handled gracefully."""
        # Arrange
        program_id = "TEST_PROGRAM"
        chunk_name = "TEST_CHUNK"
        input_parameters = [{"name": "TEST_VAR", "type": "string"}]
        output_parameters = []
        
        # Mock database to raise exception
        self.mock_knowledge_db.insert_data_definitions.side_effect = Exception("Database error")
        
        # Act & Assert - should not raise exception
        self.agent._save_parameters_to_database(program_id, chunk_name, input_parameters, output_parameters)

    @patch('src.platform.agents.knowledge_miner.core.KnowledgeMinerAgent._save_parameters_to_database')
    def test_analyze_chunk_calls_save_parameters(self, mock_save_parameters):
        """Test that _analyze_chunk_with_dependencies calls _save_parameters_to_database."""
        # Arrange
        program_id = "TEST_PROGRAM"
        chunk_name = "TEST_CHUNK"
        code_content = "COBOL CODE HERE"
        used_variables = []
        referenced_chunks_analysis = []
        
        # Mock parameter extraction
        input_params = [{"name": "INPUT1", "type": "string"}]
        output_params = [{"name": "OUTPUT1", "type": "string"}]
        
        self.agent.parameter_extractor.extract_input_parameters.return_value = input_params
        self.agent.parameter_extractor.extract_output_parameters.return_value = output_params
        
        # Mock other components
        self.agent.business_analyzer.extract_business_name.return_value = "Test Business Name"
        self.agent.business_analyzer.extract_business_description.return_value = "Test Description"
        self.agent.business_analyzer.extract_business_logic_with_aggregation.return_value = "Test Logic"
        self.agent.spec_generator.generate_functional_specification.return_value = "Test Spec"
        
        self.agent.utils.initialize_analysis_result.return_value = {}
        self.agent.utils.prepare_variable_context.return_value = "Variable context"
        self.agent.utils.prepare_dependency_context.return_value = "Dependency context"
        self.agent.utils.aggregate_business_rules.return_value = "Aggregated rules"
        
        # Act
        result = self.agent._analyze_chunk_with_dependencies(
            program_id, chunk_name, code_content, used_variables, referenced_chunks_analysis
        )
        
        # Assert
        mock_save_parameters.assert_called_once_with(program_id, chunk_name, input_params, output_params)
        assert result['input_parameters'] == input_params
        assert result['output_parameters'] == output_params

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
