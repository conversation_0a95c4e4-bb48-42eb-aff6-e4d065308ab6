"""
Unit tests for KnowledgeMinerUtils.
"""
import pytest
import os
import tempfile
from unittest.mock import Mock, patch, mock_open
from src.platform.agents.knowledge_miner.utils import KnowledgeMinerUtils
from src.platform.agents.base_agent import AgentInput


class TestKnowledgeMinerUtils:
    """Test cases for KnowledgeMinerUtils."""

    @pytest.fixture
    def mock_knowledge_db(self):
        """Create mock knowledge database."""
        mock_db = Mock()
        mock_db.db_path = "/tmp/test.db"
        return mock_db

    @pytest.fixture
    def utils(self, mock_knowledge_db):
        """Create KnowledgeMinerUtils instance for testing."""
        return KnowledgeMinerUtils(mock_knowledge_db)

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    @pytest.fixture
    def sample_agent_input(self, temp_dir):
        """Sample agent input for testing."""
        return AgentInput(
            working_directory=temp_dir,
            knowledge_base={}
        )

    def test_generate_documentation_index_creates_directory(self, utils, sample_agent_input):
        """Test that generate_documentation_index creates the documentation directory."""
        # Ensure the documentation directory doesn't exist initially
        docs_dir = os.path.join(sample_agent_input.working_directory, "documentation_for_chunks")
        assert not os.path.exists(docs_dir)

        # Call the method
        result = utils.generate_documentation_index(sample_agent_input, [])

        # Verify the directory was created
        assert os.path.exists(docs_dir)
        
        # Verify the index file was created
        index_path = os.path.join(docs_dir, "business_analysis_index.md")
        assert os.path.exists(index_path)
        assert result == index_path

    def test_generate_documentation_index_with_existing_directory(self, utils, sample_agent_input):
        """Test generate_documentation_index when directory already exists."""
        # Pre-create the documentation directory
        docs_dir = os.path.join(sample_agent_input.working_directory, "documentation_for_chunks")
        os.makedirs(docs_dir, exist_ok=True)
        
        # Call the method
        result = utils.generate_documentation_index(sample_agent_input, [])

        # Verify it still works
        assert os.path.exists(docs_dir)
        index_path = os.path.join(docs_dir, "business_analysis_index.md")
        assert os.path.exists(index_path)
        assert result == index_path

    def test_generate_documentation_index_with_generated_docs(self, utils, sample_agent_input):
        """Test generate_documentation_index with actual generated documentation files."""
        # Create some mock documentation files
        docs_dir = os.path.join(sample_agent_input.working_directory, "documentation_for_chunks")
        os.makedirs(docs_dir, exist_ok=True)
        
        # Create mock doc files
        doc1_path = os.path.join(docs_dir, "PROG1_MAIN.md")
        doc2_path = os.path.join(docs_dir, "PROG1_CALC.md")
        doc3_path = os.path.join(docs_dir, "PROG2_INIT.md")
        
        for doc_path in [doc1_path, doc2_path, doc3_path]:
            with open(doc_path, 'w') as f:
                f.write("# Test Documentation\n")
        
        generated_docs = [doc1_path, doc2_path, doc3_path]
        
        # Call the method
        result = utils.generate_documentation_index(sample_agent_input, generated_docs)

        # Verify the index file was created and contains references to the docs
        index_path = os.path.join(sample_agent_input.working_directory, "documentation_for_chunks", "business_analysis_index.md")
        assert result == index_path
        assert os.path.exists(index_path)
        
        # Read the index file and verify it contains the expected content
        with open(index_path, 'r') as f:
            content = f.read()
        
        assert "# Business Analysis Documentation Index" in content
        assert "PROG1" in content
        assert "PROG2" in content
        assert "MAIN" in content
        assert "CALC" in content
        assert "INIT" in content

    def test_save_analysis_to_markdown_creates_directory(self, utils, sample_agent_input):
        """Test that save_analysis_to_markdown creates the documentation_for_chunks directory."""
        analysis_result = {
            "business_name": "Test Function",
            "business_description": "Test description",
            "business_logic": "Test logic",
            "functional_spec": "Test spec",
            "input_parameters": [],
            "output_parameters": []
        }

        # Call the method
        result = utils.save_analysis_to_markdown(
            sample_agent_input, "TEST-PROG", "MAIN-PARA", analysis_result
        )

        # Verify the directory was created
        docs_dir = os.path.join(sample_agent_input.working_directory, "documentation_for_chunks", "TEST-PROG")
        assert os.path.exists(docs_dir)
        
        # Verify the markdown file was created
        expected_path = os.path.join(docs_dir, "TEST-PROG_MAIN-PARA.md")
        assert result == expected_path
        assert os.path.exists(expected_path)

    @patch('os.makedirs')
    def test_generate_documentation_index_handles_makedirs_error(self, mock_makedirs, utils, sample_agent_input):
        """Test that generate_documentation_index handles directory creation errors gracefully."""
        # Mock makedirs to raise an exception
        mock_makedirs.side_effect = PermissionError("Permission denied")

        # Call the method - should handle the error gracefully
        result = utils.generate_documentation_index(sample_agent_input, [])

        # Should return None on error
        assert result is None

    def test_initialize_analysis_result(self, utils):
        """Test analysis result initialization."""
        result = utils.initialize_analysis_result("TEST-PROG", "MAIN-PARA")

        assert result["input_parameters"] == []
        assert result["output_parameters"] == []
        assert result["business_name"] == "Function in MAIN-PARA"
        assert result["business_description"] == "Code chunk from TEST-PROG.MAIN-PARA"
        assert result["business_logic"] == ""
        assert result["functional_spec"] == ""

    def test_is_exit_only_chunk(self, utils):
        """Test exit-only chunk detection."""
        # Test exit-only chunk
        exit_only_code = """
        MAIN-PARA.
            EXIT.
        """
        assert utils.is_exit_only_chunk(exit_only_code) is True

        # Test normal chunk
        normal_code = """
        MAIN-PARA.
            MOVE 1 TO COUNTER.
            EXIT.
        """
        assert utils.is_exit_only_chunk(normal_code) is False

        # Test empty chunk
        empty_code = ""
        assert utils.is_exit_only_chunk(empty_code) is True

        # Test whitespace-only chunk
        whitespace_code = "   \n   \n   "
        assert utils.is_exit_only_chunk(whitespace_code) is True
