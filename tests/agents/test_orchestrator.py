"""
Unit tests for OrchestratorAgent.
"""
import pytest
import threading
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from src.platform.agents.orchestrator import OrchestratorAgent
from src.platform.agents.base_agent import AgentInput, AgentOutput


class TestOrchestratorAgent:
    """Test cases for OrchestratorAgent."""

    @pytest.fixture
    def orchestrator(self):
        """Create OrchestratorAgent instance for testing."""
        with patch('src.platform.agents.orchestrator.StateHolder') as mock_state_holder:
            mock_state_holder.return_value = Mock()
            orchestrator = OrchestratorAgent()

            # Mock all agents
            for agent_name in orchestrator.agents:
                orchestrator.agents[agent_name] = Mock()
                orchestrator.agents[agent_name].process.return_value = AgentOutput(
                    success=True,
                    message=f"{agent_name} completed successfully",
                    knowledge_base_updates={f"{agent_name}_data": "test"}
                )

            return orchestrator

    @pytest.fixture
    def sample_project_config(self):
        """Sample project configuration."""
        return {
            "project_type": "Documentation Generation",
            "working_directory": "/test/working/dir",
            "java_package": "com.test.package",
            "group_id": "com.test",
            "artifact_id": "test-project"
        }

    def test_initialization(self, orchestrator):
        """Test orchestrator initialization."""
        assert orchestrator.state_holder is not None
        assert orchestrator.processing_thread is None
        assert len(orchestrator.agents) == 8  # All expected agents
        assert len(orchestrator.workflow) == 0  # No workflow set initially

        # Check all expected agents exist
        expected_agents = [
            "package_analyzer", "code_preprocessor", "overview_generator",
            "knowledge_miner", "documentation_generator", "documentation_critic",
            "code_generator", "code_reviewer"
        ]
        for agent_name in expected_agents:
            assert agent_name in orchestrator.agents

    def test_set_up(self, orchestrator, sample_project_config):
        """Test orchestrator setup."""
        orchestrator.set_config(sample_project_config)

        assert orchestrator.project_config == sample_project_config

    def test_start_processing_sync(self, orchestrator, sample_project_config):
        """Test synchronous processing."""
        orchestrator.set_config(sample_project_config)

        # Mock the workflow execution
        with patch.object(orchestrator, '_run_workflow') as mock_run:
            orchestrator.start_conversion("/test/working/dir")

            # Check that conversion was started
            assert orchestrator.current_state["status"] == "running"
            assert orchestrator.current_state["working_directory"] == "/test/working/dir"

    def test_start_processing_async(self, orchestrator, sample_project_config):
        """Test asynchronous processing."""
        orchestrator.set_config(sample_project_config)

        # Mock the workflow execution to make it run longer
        import time
        def slow_workflow(*args, **kwargs):
            time.sleep(0.2)  # Make it run for a bit

        with patch.object(orchestrator, '_run_workflow', side_effect=slow_workflow):
            orchestrator.start_conversion("/test/working/dir")

            # Give thread time to start
            time.sleep(0.1)

            # Check if thread exists (it might complete quickly)
            assert orchestrator.processing_thread is not None

            # Wait for processing to complete
            orchestrator.processing_thread.join(timeout=5)

    def test_start_processing_already_running(self, orchestrator, sample_project_config):
        """Test starting processing when already running."""
        orchestrator.set_config(sample_project_config)

        # Mock a running thread
        orchestrator.processing_thread = Mock()
        orchestrator.processing_thread.is_alive.return_value = True

        # Should not start new conversion
        with patch.object(orchestrator, '_run_workflow') as mock_run:
            orchestrator.start_conversion("/test/working/dir")
            mock_run.assert_not_called()

    def test_stop_processing(self, orchestrator, sample_project_config):
        """Test stopping processing."""
        orchestrator.set_config(sample_project_config)

        # Test reset functionality
        orchestrator.reset()

        assert orchestrator.current_state["status"] == "reset"

    def test_get_status(self, orchestrator):
        """Test getting orchestrator status."""
        status = orchestrator.get_states()

        # Should return states from state holder
        assert status is not None

    def test_get_logs(self, orchestrator):
        """Test getting orchestrator logs."""
        # Add some test logs
        orchestrator.current_state["logs"] = ["Log 1", "Log 2", "Log 3"]

        logs = orchestrator.current_state["logs"]
        assert len(logs) == 3
        assert logs[0] == "Log 1"

    def test_get_errors(self, orchestrator):
        """Test getting orchestrator errors."""
        # Add some test errors
        orchestrator.current_state["errors"] = ["Error 1", "Error 2"]

        errors = orchestrator.current_state["errors"]
        assert len(errors) == 2
        assert errors[0] == "Error 1"

    def test_process_workflow_success(self, orchestrator, sample_project_config):
        """Test successful workflow processing."""
        orchestrator.set_config(sample_project_config)

        # Mock the workflow execution
        with patch.object(orchestrator, '_run_workflow') as mock_run:
            orchestrator.start_conversion("/test/working/dir")

            # Simulate successful completion
            orchestrator.current_state["status"] = "completed"
            orchestrator.current_state["progress"] = 1.0

            assert orchestrator.current_state["status"] == "completed"
            assert orchestrator.current_state["progress"] == 1.0

    def test_process_workflow_agent_failure(self, orchestrator, sample_project_config):
        """Test workflow processing with agent failure."""
        orchestrator.set_config(sample_project_config)

        # Make one agent fail
        orchestrator.agents["package_analyzer"].process.return_value = AgentOutput(
            success=False,
            message="Package analyzer failed",
            errors=["Test error"]
        )

        # Mock the workflow execution
        with patch.object(orchestrator, '_run_workflow') as mock_run:
            orchestrator.start_conversion("/test/working/dir")

            # Simulate failure
            orchestrator.current_state["status"] = "failed"
            orchestrator.current_state["errors"] = ["Test error"]

            assert orchestrator.current_state["status"] == "failed"
            assert len(orchestrator.current_state["errors"]) > 0

    def test_process_workflow_exception(self, orchestrator, sample_project_config):
        """Test workflow processing with exception."""
        orchestrator.set_config(sample_project_config)

        # Make an agent raise an exception
        orchestrator.agents["package_analyzer"].process.side_effect = Exception("Test exception")

        # Mock the workflow execution
        with patch.object(orchestrator, '_run_workflow') as mock_run:
            orchestrator.start_conversion("/test/working/dir")

            # Simulate exception handling
            orchestrator.current_state["status"] = "failed"
            orchestrator.current_state["errors"] = ["Test exception"]

            assert orchestrator.current_state["status"] == "failed"
            assert len(orchestrator.current_state["errors"]) > 0

    def test_process_workflow_stopped(self, orchestrator, sample_project_config):
        """Test workflow processing when stopped."""
        orchestrator.set_config(sample_project_config)
        orchestrator.current_state["status"] = "stopped"

        # Test that reset works
        orchestrator.reset()
        assert orchestrator.current_state["status"] == "reset"

    def test_update_progress(self, orchestrator):
        """Test progress updates."""
        orchestrator.current_state["current_agent"] = "test_agent"
        orchestrator.current_state["progress"] = 0.5
        orchestrator.current_state["agent_progress"]["test_agent"] = 0.5

        assert orchestrator.current_state["current_agent"] == "test_agent"
        assert orchestrator.current_state["progress"] == 0.5
        assert "test_agent" in orchestrator.current_state["agent_progress"]
        assert orchestrator.current_state["agent_progress"]["test_agent"] == 0.5

    def test_log_message(self, orchestrator):
        """Test logging messages."""
        test_message = "Test log message"
        orchestrator.current_state["logs"].append(test_message)

        assert test_message in orchestrator.current_state["logs"]

    def test_add_error(self, orchestrator):
        """Test adding errors."""
        test_error = "Test error message"
        orchestrator.current_state["errors"].append(test_error)

        assert test_error in orchestrator.current_state["errors"]

    def test_save_state(self, orchestrator, sample_project_config):
        """Test saving orchestrator state."""
        orchestrator.set_config(sample_project_config)
        orchestrator.save_state()

        # Verify state holder was called
        orchestrator.state_holder.update_state.assert_called()

    def test_update_state(self, orchestrator, sample_project_config):
        """Test updating orchestrator state."""
        orchestrator.set_config(sample_project_config)
        orchestrator.save_state()

        # Verify state holder was called
        orchestrator.state_holder.update_state.assert_called()

    def test_estimate_completion_time(self, orchestrator):
        """Test completion time estimation."""
        orchestrator.current_state["start_time"] = datetime.now()
        orchestrator.current_state["progress"] = 0.5

        # Test that estimated completion is calculated in the workflow
        assert "start_time" in orchestrator.current_state
        assert orchestrator.current_state["progress"] == 0.5

    def test_estimate_completion_time_no_progress(self, orchestrator):
        """Test completion time estimation with no progress."""
        orchestrator.current_state["start_time"] = datetime.now()
        orchestrator.current_state["progress"] = 0

        # Test that no estimated completion is set when progress is 0
        assert orchestrator.current_state["progress"] == 0

    def test_workflow_customization(self, orchestrator):
        """Test customizing workflow steps."""
        custom_workflow = ["package_analyzer", "documentation_generator"]
        orchestrator.set_workflow(custom_workflow)

        assert orchestrator.workflow == custom_workflow
        assert len(orchestrator.workflow) == 2

    def test_concurrent_processing_safety(self, orchestrator, sample_project_config):
        """Test thread safety during concurrent operations."""
        orchestrator.set_config(sample_project_config)

        # Mock the workflow execution
        with patch.object(orchestrator, '_run_workflow') as mock_run:
            orchestrator.start_conversion("/test/working/dir")

            # Try to get status while processing
            status = orchestrator.get_states()
            assert status is not None

            # Try to get logs while processing
            logs = orchestrator.current_state["logs"]
            assert isinstance(logs, list)

            # Test reset
            orchestrator.reset()
            assert orchestrator.current_state["status"] == "reset"
