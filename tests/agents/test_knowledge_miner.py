"""
Unit tests for KnowledgeMinerAgent.
"""
import pytest
import json
from unittest.mock import Mo<PERSON>, patch, MagicMock
from src.platform.agents.knowledge_miner.core import KnowledgeMinerAgent
from src.platform.agents.base_agent import AgentInput, AgentOutput


class TestKnowledgeMinerAgent:
    """Test cases for KnowledgeMinerAgent."""

    @pytest.fixture
    def agent(self, mock_llm, mock_knowledge_db):
        """Create KnowledgeMinerAgent instance for testing."""
        with patch('src.platform.agents.knowledge_miner.core.llm_settings') as mock_llm_settings:
            mock_llm_settings.llm = mock_llm
            agent = KnowledgeMinerAgent()
            agent.knowledge_db = mock_knowledge_db
            return agent

    @pytest.fixture
    def sample_chunk_data(self):
        """Sample chunk data for testing."""
        return {
            "program_id": "TEST-PROG",
            "chunk_name": "MAIN-PARA",
            "code": """
            MOVE 12345 TO CUSTOMER-ID.
            MOVE 'JOHN DOE' TO CUSTOMER-NAME.
            DISPLAY 'Customer: ' CUSTOMER-NAME.
            """,
            "variables": ["CUSTOMER-ID", "CUSTOMER-NAME"],
            "dependencies": []
        }

    def test_agent_initialization(self, agent):
        """Test agent initialization."""
        assert agent.name == "knowledge_miner"
        assert agent.llm is not None
        assert agent.knowledge_db is not None
        assert agent.json_analyzer is not None

    def test_set_up(self, agent):
        """Test set_up method."""
        config = {"test": "config"}
        agent.set_up(config)  # Should not raise any exceptions

    @patch('os.path.exists')
    @patch('os.listdir')
    def test_process_success(self, mock_listdir, mock_exists, agent, sample_agent_input, sample_chunk_data):
        """Test successful processing."""
        # Setup mocks
        mock_exists.return_value = True
        mock_listdir.return_value = ["test_chunks.json"]

        with patch('builtins.open', create=True) as mock_open:
            mock_open.return_value.__enter__.return_value.read.return_value = json.dumps([sample_chunk_data])

            # Mock LLM responses
            agent.llm.invoke.return_value = json.dumps([{
                "name": "CUSTOMER-ID",
                "type": "variable",
                "business_name": "Customer ID",
                "description": "Customer identifier"
            }])

            result = agent.process(sample_agent_input)

        assert isinstance(result, AgentOutput)
        assert result.success is True
        assert "business_logic_summaries" in result.knowledge_base_updates

    def test_process_no_chunks_directory(self, agent, sample_agent_input):
        """Test processing when no programs are found."""
        # Mock the utils to return no programs
        with patch.object(agent.utils, 'get_all_cobol_programs', return_value=[]):
            with patch.object(agent.utils, 'generate_documentation_index', return_value=None):
                result = agent.process(sample_agent_input)

        assert isinstance(result, AgentOutput)
        assert result.success is True  # Should succeed with 0 programs processed
        assert "0 chunks analyzed across 0 programs" in result.message

    def test_should_skip_chunk_exit_only(self, agent):
        """Test skipping chunks with only EXIT statements."""
        exit_only_code = """
        MAIN-PARA.
            EXIT.
        """
        assert agent.utils.is_exit_only_chunk(exit_only_code) is True

        normal_code = """
        MAIN-PARA.
            MOVE 1 TO COUNTER.
            EXIT.
        """
        assert agent.utils.is_exit_only_chunk(normal_code) is False

    def test_should_skip_chunk_empty(self, agent):
        """Test skipping empty chunks."""
        empty_code = ""
        assert agent.utils.is_exit_only_chunk(empty_code) is True

        whitespace_code = "   \n   \n   "
        assert agent.utils.is_exit_only_chunk(whitespace_code) is True

    def test_initialize_analysis_result(self, agent):
        """Test analysis result initialization."""
        result = agent.utils.initialize_analysis_result("TEST-PROG", "MAIN-PARA")

        assert result["input_parameters"] == []
        assert result["output_parameters"] == []
        assert result["business_name"] == "Function in MAIN-PARA"
        assert result["business_description"] == "Code chunk from TEST-PROG.MAIN-PARA"
        assert result["business_logic"] == ""
        assert result["functional_spec"] == ""

    def test_extract_input_parameters_success(self, agent, sample_chunk_data):
        """Test successful input parameter extraction."""
        # Mock LLM response
        mock_response = json.dumps([{
            "name": "CUSTOMER-ID",
            "type": "variable",
            "business_name": "Customer ID",
            "description": "Customer identifier"
        }])
        agent.llm.invoke.return_value = mock_response

        params = agent.parameter_extractor.extract_input_parameters(
            sample_chunk_data["program_id"],
            sample_chunk_data["chunk_name"],
            sample_chunk_data["code"],
            "Test context"
        )

        assert len(params) == 1
        assert params[0]["name"] == "CUSTOMER-ID"
        assert params[0]["type"] == "variable"

    def test_extract_input_parameters_invalid_json(self, agent, sample_chunk_data):
        """Test input parameter extraction with invalid JSON response."""
        # Mock invalid JSON response
        agent.llm.invoke.return_value = "Invalid JSON response"

        params = agent.parameter_extractor.extract_input_parameters(
            sample_chunk_data["program_id"],
            sample_chunk_data["chunk_name"],
            sample_chunk_data["code"],
            "Test context"
        )

        assert params == []

    def test_extract_output_parameters_success(self, agent, sample_chunk_data):
        """Test successful output parameter extraction."""
        # Mock LLM response
        mock_response = json.dumps([{
            "name": "CUSTOMER-NAME",
            "type": "variable",
            "business_name": "Customer Name",
            "description": "Customer full name"
        }])
        agent.llm.invoke.return_value = mock_response

        params = agent.parameter_extractor.extract_output_parameters(
            sample_chunk_data["program_id"],
            sample_chunk_data["chunk_name"],
            sample_chunk_data["code"],
            "Test context"
        )

        assert len(params) == 1
        assert params[0]["name"] == "CUSTOMER-NAME"
        assert params[0]["type"] == "variable"

    def test_extract_business_name(self, agent, sample_chunk_data):
        """Test business name extraction."""
        agent.llm.invoke.return_value = "Customer Data Processing"

        business_name = agent.business_analyzer.extract_business_name(
            sample_chunk_data["program_id"],
            sample_chunk_data["chunk_name"],
            sample_chunk_data["code"],
            "Test context"
        )

        assert business_name == "Customer Data Processing"

    def test_extract_business_name_with_quotes(self, agent, sample_chunk_data):
        """Test business name extraction with quoted response."""
        agent.llm.invoke.return_value = '"Customer Data Processing"'

        business_name = agent.business_analyzer.extract_business_name(
            sample_chunk_data["program_id"],
            sample_chunk_data["chunk_name"],
            sample_chunk_data["code"],
            "Test context"
        )

        assert business_name == "Customer Data Processing"

    def test_extract_business_description(self, agent, sample_chunk_data):
        """Test business description extraction."""
        expected_description = "This function processes customer data and displays information."
        agent.llm.invoke.return_value = expected_description

        description = agent.business_analyzer.extract_business_description(
            sample_chunk_data["program_id"],
            sample_chunk_data["chunk_name"],
            sample_chunk_data["code"],
            "Test context"
        )

        assert description == expected_description

    def test_extract_business_logic(self, agent, sample_chunk_data):
        """Test business logic extraction."""
        expected_logic = "When customer ID is provided, display customer name."
        agent.llm.invoke.return_value = expected_logic

        logic = agent.business_analyzer.extract_business_logic(
            sample_chunk_data["program_id"],
            sample_chunk_data["chunk_name"],
            sample_chunk_data["code"],
            "Test context"
        )

        assert logic == expected_logic

    def test_generate_functional_specification(self, agent, sample_chunk_data):
        """Test functional specification generation with 4 separate LLM calls."""
        # Mock responses for each of the 4 sections
        purpose_response = "## FUNCTION PURPOSE\nThis function processes customer data and displays customer information."
        algorithm_response = "## ALGORITHM\n1. Read customer ID\n2. Display customer name"
        validation_response = "## VALIDATION RULES\n- Customer ID must be numeric\n- Customer ID must be positive"
        error_handling_response = "## ERROR HANDLING\n- Handle invalid customer ID\n- Handle file not found errors"

        # Configure mock to return different responses for each call
        agent.llm.invoke.side_effect = [
            purpose_response,
            algorithm_response,
            validation_response,
            error_handling_response
        ]

        spec = agent.spec_generator.generate_functional_specification(
            sample_chunk_data["program_id"],
            sample_chunk_data["chunk_name"],
            sample_chunk_data["code"],
            "Test context"
        )

        # Verify that 4 LLM calls were made
        assert agent.llm.invoke.call_count == 4

        # Verify the aggregated result contains all sections
        assert "# FUNCTIONAL SPECIFICATION" in spec
        assert purpose_response in spec
        assert algorithm_response in spec
        assert validation_response in spec
        assert error_handling_response in spec

    def test_parse_parameters_from_response_valid_json(self, agent):
        """Test parsing parameters from valid JSON response."""
        json_response = json.dumps([{
            "name": "TEST-VAR",
            "type": "variable",
            "business_name": "Test Variable",
            "description": "Test description"
        }])

        params = agent.parameter_extractor._parse_parameters_from_response(json_response, "input")

        assert len(params) == 1
        assert params[0]["name"] == "TEST-VAR"

    def test_parse_parameters_from_response_invalid_json(self, agent):
        """Test parsing parameters from invalid JSON response."""
        invalid_response = "Not valid JSON"

        params = agent.parameter_extractor._parse_parameters_from_response(invalid_response, "input")

        assert params == []

    def test_parse_parameters_from_response_empty_array(self, agent):
        """Test parsing parameters from empty array response."""
        empty_response = "[]"

        params = agent.parameter_extractor._parse_parameters_from_response(empty_response, "input")

        assert params == []

    def test_generate_function_purpose(self, agent, sample_chunk_data):
        """Test individual function purpose generation."""
        expected_purpose = "## FUNCTION PURPOSE\nThis function processes customer data."
        agent.llm.invoke.return_value = expected_purpose

        # Mock the language plugin setup
        mock_plugin = Mock()
        mock_template_manager = Mock()
        mock_template_manager.render_template.return_value = "Test prompt for function purpose"
        mock_plugin.get_template_manager.return_value = mock_template_manager

        # Mock the system message method
        with patch.object(agent.spec_generator, '_get_language_specific_system_message', return_value="Test system message"):
            purpose = agent.spec_generator._generate_function_purpose(
                sample_chunk_data["program_id"],
                sample_chunk_data["chunk_name"],
                sample_chunk_data["code"],
                "Test context",
                mock_plugin,
                "cobol"
            )

        assert purpose == expected_purpose
        assert agent.llm.invoke.call_count == 1

    def test_generate_algorithm(self, agent, sample_chunk_data):
        """Test individual algorithm generation."""
        expected_algorithm = "## ALGORITHM\n1. Read input\n2. Process data\n3. Return result"
        agent.llm.invoke.return_value = expected_algorithm

        # Mock the language plugin setup
        mock_plugin = Mock()
        mock_template_manager = Mock()
        mock_template_manager.render_template.return_value = "Test prompt for algorithm"
        mock_plugin.get_template_manager.return_value = mock_template_manager

        # Mock the system message method
        with patch.object(agent.spec_generator, '_get_language_specific_system_message', return_value="Test system message"):
            algorithm = agent.spec_generator._generate_algorithm(
                sample_chunk_data["program_id"],
                sample_chunk_data["chunk_name"],
                sample_chunk_data["code"],
                "Test context",
                mock_plugin,
                "cobol"
            )

        assert algorithm == expected_algorithm
        assert agent.llm.invoke.call_count == 1

    def test_generate_validation_rules(self, agent, sample_chunk_data):
        """Test individual validation rules generation."""
        expected_validation = "## VALIDATION RULES\n- Input must be numeric\n- Input must be positive"
        agent.llm.invoke.return_value = expected_validation

        # Mock the language plugin setup
        mock_plugin = Mock()
        mock_template_manager = Mock()
        mock_template_manager.render_template.return_value = "Test prompt for validation rules"
        mock_plugin.get_template_manager.return_value = mock_template_manager

        # Mock the system message method
        with patch.object(agent.spec_generator, '_get_language_specific_system_message', return_value="Test system message"):
            validation = agent.spec_generator._generate_validation_rules(
                sample_chunk_data["program_id"],
                sample_chunk_data["chunk_name"],
                sample_chunk_data["code"],
                "Test context",
                mock_plugin,
                "cobol"
            )

        assert validation == expected_validation
        assert agent.llm.invoke.call_count == 1

    def test_generate_error_handling(self, agent, sample_chunk_data):
        """Test individual error handling generation."""
        expected_error_handling = "## ERROR HANDLING\n- Handle file errors\n- Handle validation errors"
        agent.llm.invoke.return_value = expected_error_handling

        # Mock the language plugin setup
        mock_plugin = Mock()
        mock_template_manager = Mock()
        mock_template_manager.render_template.return_value = "Test prompt for error handling"
        mock_plugin.get_template_manager.return_value = mock_template_manager

        # Mock the system message method
        with patch.object(agent.spec_generator, '_get_language_specific_system_message', return_value="Test system message"):
            error_handling = agent.spec_generator._generate_error_handling(
                sample_chunk_data["program_id"],
                sample_chunk_data["chunk_name"],
                sample_chunk_data["code"],
                "Test context",
                mock_plugin,
                "cobol"
            )

        assert error_handling == expected_error_handling
        assert agent.llm.invoke.call_count == 1

    def test_aggregate_spec_sections(self, agent):
        """Test aggregation of specification sections."""
        purpose = "## FUNCTION PURPOSE\nTest purpose"
        algorithm = "## ALGORITHM\nTest algorithm"
        validation = "## VALIDATION RULES\nTest validation"
        error_handling = "## ERROR HANDLING\nTest error handling"

        result = agent.spec_generator._aggregate_spec_sections(
            purpose, algorithm, validation, error_handling
        )

        assert "# FUNCTIONAL SPECIFICATION" in result
        assert purpose in result
        assert algorithm in result
        assert validation in result
        assert error_handling in result
        assert "This document should be detailed enough" in result
