"""
Unit tests for Code Generator fixes.
Tests the fixes for Java project creation and file generation issues.
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock

from src.platform.agents.code_generator import CodeGeneratorAgent
from src.platform.agents.base_agent import AgentInput, AgentOutput


class TestCodeGeneratorFixes:
    """Test code generator fixes for Java project and file generation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.agent = CodeGeneratorAgent()

    @patch('src.platform.plugins.plugin_loader.get_plugin_loader')
    def test_code_generator_returns_correct_format_for_reviewer(self, mock_plugin_loader):
        """Test that code generator returns format expected by code_reviewer."""
        # Arrange
        mock_plugin = Mock()
        mock_code_generator = Mock()
        
        # Mock plugin result with generated_files
        plugin_result = {
            "success": True,
            "generated_files": {
                "Application.java": "public class Application { }",
                "TestService.java": "public class TestService { }"
            },
            "message": "Code generation completed"
        }
        
        mock_code_generator.generate_code.return_value = plugin_result
        mock_plugin.get_code_generator.return_value = mock_code_generator
        mock_plugin_loader.return_value.get_target_plugin.return_value = mock_plugin
        
        input_data = AgentInput(
            working_directory=self.temp_dir,
            knowledge_base={
                "target_technology": "java_spring",
                "config": {"project_name": "test-project"}
            }
        )
        
        # Act
        result = self.agent.process(input_data)
        
        # Assert
        assert isinstance(result, AgentOutput)
        assert result.success is True
        
        # Check that generated_code has the correct format for code_reviewer
        generated_code = result.knowledge_base_updates["generated_code"]
        assert isinstance(generated_code, dict)
        
        # Should have service_name as key
        service_names = list(generated_code.keys())
        assert len(service_names) == 1
        assert "java_spring_microservice" in service_names
        
        # Service should contain files dict
        service_files = generated_code["java_spring_microservice"]
        assert isinstance(service_files, dict)
        assert "Application.java" in service_files
        assert "TestService.java" in service_files
        assert service_files["Application.java"] == "public class Application { }"

    @patch('src.platform.plugins.plugin_loader.get_plugin_loader')
    def test_code_generator_handles_empty_generated_files(self, mock_plugin_loader):
        """Test that code generator handles empty generated_files gracefully."""
        # Arrange
        mock_plugin = Mock()
        mock_code_generator = Mock()
        
        # Mock plugin result with empty generated_files
        plugin_result = {
            "success": True,
            "generated_files": {},
            "message": "No files generated"
        }
        
        mock_code_generator.generate_code.return_value = plugin_result
        mock_plugin.get_code_generator.return_value = mock_code_generator
        mock_plugin_loader.return_value.get_target_plugin.return_value = mock_plugin
        
        input_data = AgentInput(
            working_directory=self.temp_dir,
            knowledge_base={"target_technology": "java_spring"}
        )
        
        # Act
        result = self.agent.process(input_data)
        
        # Assert
        assert result.success is True
        generated_code = result.knowledge_base_updates["generated_code"]
        assert generated_code == {}

    def test_transform_result_for_reviewer(self):
        """Test the _transform_result_for_reviewer method."""
        # Arrange
        plugin_result = {
            "success": True,
            "generated_files": {
                "Application.java": "public class Application { }",
                "Service.java": "public class Service { }"
            }
        }
        
        # Act
        transformed = self.agent._transform_result_for_reviewer(plugin_result, "java_spring")
        
        # Assert
        assert "java_spring_microservice" in transformed
        service_files = transformed["java_spring_microservice"]
        assert len(service_files) == 2
        assert "Application.java" in service_files
        assert "Service.java" in service_files

    def test_transform_result_handles_missing_generated_files(self):
        """Test transformation handles missing generated_files key."""
        # Arrange
        plugin_result = {"success": True, "message": "No files"}
        
        # Act
        transformed = self.agent._transform_result_for_reviewer(plugin_result, "java_spring")
        
        # Assert
        assert transformed == {}

    @patch('src.platform.plugins.plugin_loader.get_plugin_loader')
    def test_code_generator_passes_working_directory_to_plugin(self, mock_plugin_loader):
        """Test that working directory is passed to plugin in config."""
        # Arrange
        mock_plugin = Mock()
        mock_code_generator = Mock()
        mock_code_generator.generate_code.return_value = {"success": True, "generated_files": {}}
        mock_plugin.get_code_generator.return_value = mock_code_generator
        mock_plugin_loader.return_value.get_target_plugin.return_value = mock_plugin
        
        working_dir = "/test/working/dir"
        input_data = AgentInput(
            working_directory=working_dir,
            knowledge_base={"target_technology": "java_spring", "config": {"project_name": "test"}}
        )
        
        # Act
        self.agent.process(input_data)
        
        # Assert
        call_args = mock_code_generator.generate_code.call_args
        config = call_args[0][1]  # Second argument is config
        assert config["working_directory"] == working_dir

    @patch('src.platform.plugins.plugin_loader.get_plugin_loader')
    def test_code_generator_passes_knowledge_base_to_plugin(self, mock_plugin_loader):
        """Test that entire knowledge base is passed to plugin as source_analysis."""
        # Arrange
        mock_plugin = Mock()
        mock_code_generator = Mock()
        mock_code_generator.generate_code.return_value = {"success": True, "generated_files": {}}
        mock_plugin.get_code_generator.return_value = mock_code_generator
        mock_plugin_loader.return_value.get_target_plugin.return_value = mock_plugin
        
        knowledge_base = {
            "target_technology": "java_spring",
            "business_logic_summaries": {"PROG1": {"CHUNK1": "Business logic here"}},
            "config": {"project_name": "test"}
        }
        
        input_data = AgentInput(
            working_directory=self.temp_dir,
            knowledge_base=knowledge_base
        )
        
        # Act
        self.agent.process(input_data)
        
        # Assert
        call_args = mock_code_generator.generate_code.call_args
        source_analysis = call_args[0][0]  # First argument is source_analysis
        assert source_analysis["knowledge_base"] == knowledge_base

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
