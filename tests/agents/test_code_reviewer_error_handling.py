"""
Unit tests for CodeReviewerAgent error handling scenarios.
"""
import pytest
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock

from src.platform.agents.code_reviewer import CodeReviewerAgent
from src.platform.agents.base_agent import AgentInput, AgentOutput


class TestCodeReviewerErrorHandling:
    """Test cases for CodeReviewerAgent error handling."""

    @pytest.fixture
    def agent(self):
        """Create CodeReviewerAgent instance for testing."""
        with patch('src.platform.agents.code_reviewer.llm') as mock_llm:
            mock_llm.invoke.return_value = "Mock LLM response"
            agent = CodeReviewerAgent()
            return agent

    @pytest.fixture
    def temp_working_dir(self):
        """Create temporary working directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    def test_missing_knowledge_base(self, agent, temp_working_dir):
        """Test handling of missing knowledge base."""
        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={}
        )

        result = agent.process(input_data)

        assert result.success is False
        assert "No knowledge base provided" in result.message
        assert "Missing knowledge_base in input data" in result.errors

    def test_legacy_knowledge_base_structure(self, agent, temp_working_dir):
        """Test handling of legacy knowledge base structure."""
        # Create mock generated code in the format expected by code reviewer
        generated_code = {
            "services": {
                "TestService.java": "public class TestService {}"
            }
        }

        # Test legacy structure where generated_code is nested under knowledge_base key
        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={
                "knowledge_base": {
                    "generated_code": generated_code
                }
            }
        )

        with patch.object(agent, '_review_file') as mock_review:
            mock_review.return_value = (
                {"status": "PASS", "message": "Code looks good", "issues": [], "improvements": []},
                "public class TestService {}"
            )

            result = agent.process(input_data)

            assert result.success is True
            assert "Code review completed successfully" in result.message

    def test_modern_knowledge_base_structure(self, agent, temp_working_dir):
        """Test handling of modern knowledge base structure."""
        # Create mock generated code in the format expected by code reviewer
        generated_code = {
            "services": {
                "TestService.java": "public class TestService {}"
            }
        }

        # Test modern structure where generated_code is at top level
        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={
                "generated_code": generated_code
            }
        )

        with patch.object(agent, '_review_file') as mock_review:
            mock_review.return_value = (
                {"status": "PASS", "message": "Code looks good", "issues": [], "improvements": []},
                "public class TestService {}"
            )

            result = agent.process(input_data)

            assert result.success is True
            assert "Code review completed successfully" in result.message

    def test_missing_generated_code_in_both_structures(self, agent, temp_working_dir):
        """Test handling when generated_code is missing in both legacy and modern structures."""
        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={
                "knowledge_base": {
                    "other_data": "some value"
                },
                "other_top_level_data": "another value"
            }
        )

        result = agent.process(input_data)

        assert result.success is False
        assert "No generated code found to review" in result.message
        assert "Missing generated_code in knowledge base" in result.errors

    def test_template_loading_failure(self, agent, temp_working_dir):
        """Test handling of template loading failures."""
        generated_code = {
            "services": {
                "TestService.java": "public class TestService {}"
            }
        }

        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={"generated_code": generated_code}
        )

        # Mock template manager to raise an exception
        with patch('src.platform.tools.utils.template_manager.get_template_manager') as mock_get_tm:
            mock_tm = Mock()
            mock_tm.render_template.side_effect = Exception("Template not found")
            mock_get_tm.return_value = mock_tm

            result = agent.process(input_data)

            # The agent should handle template errors gracefully
            assert result.success is True  # Should still succeed with fallback handling

    def test_llm_invocation_failure(self, agent, temp_working_dir):
        """Test handling of LLM invocation failures."""
        generated_code = {
            "services": {
                "TestService.java": "public class TestService {}"
            }
        }

        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={"generated_code": generated_code}
        )

        # Mock LLM to raise an exception
        with patch.object(agent, 'llm') as mock_llm:
            mock_llm.invoke.side_effect = Exception("LLM API error")

            result = agent.process(input_data)

            assert result.success is False
            assert "Code review failed" in result.message
            assert any("LLM API error" in error for error in result.errors)

    def test_file_system_errors(self, agent):
        """Test handling of file system errors."""
        generated_code = {
            "services": {
                "TestService.java": "public class TestService {}"
            }
        }

        # Use non-existent directory to trigger file system errors
        input_data = AgentInput(
            working_directory="/non/existent/directory",
            knowledge_base={"generated_code": generated_code}
        )

        result = agent.process(input_data)

        assert result.success is False
        assert "Code review failed" in result.message
        assert len(result.errors) > 0

    @pytest.mark.unit
    def test_error_recovery_mechanisms(self, agent, temp_working_dir):
        """Test that the agent can recover from various error conditions."""
        generated_code = {
            "services": {
                "TestService.java": "public class TestService {}"
            }
        }

        input_data = AgentInput(
            working_directory=temp_working_dir,
            knowledge_base={"generated_code": generated_code}
        )

        # Test that agent can handle partial failures
        with patch.object(agent, '_review_file') as mock_review:
            # First call fails, second succeeds
            mock_review.side_effect = [
                Exception("First file review failed"),
                (
                    {"status": "PASS", "message": "Code looks good", "issues": [], "improvements": []},
                    "public class TestService {}"
                )
            ]

            result = agent.process(input_data)

            # Should handle the error gracefully and continue processing
            assert result.success is False  # Due to the exception
            assert "Code review failed" in result.message
