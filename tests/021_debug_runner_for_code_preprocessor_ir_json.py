import logging
import helpers.test_paths as paths
from src.plugins.legacy.cobol.tools.parsers.cobol_parser_cli.src.parser.parser import CobolParser
from src.plugins.legacy.cobol.tools.parsers.cobol_parser_cli.src.ir.builder import IRBuilder
from src.plugins.legacy.cobol.tools.parsers.cobol_parser_cli.src.ir.serializer import IRSerializer
from src.plugins.legacy.cobol.tools.parsers.cobol_parser_cli.src.parser.ast_builder import AstBuilder
from src.plugins.legacy.cobol.tools.parsers.cobol_parser_cli.src.preprocessor.copybook import CopybookExpander

def main():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("documentation_agent_test")

    try:
        with open(paths.cobol_path, 'r', encoding='utf-8', errors='replace') as file:
            cobol_source = file.read()

        print(paths.code_samples_path)

        copybook_expander = CopybookExpander([paths.code_samples_path], 10)
        expanded_cobol_source = copybook_expander.expand_source(cobol_source, "COBOL.cbl")

        logger.info(f"expanded_cobol_source: {expanded_cobol_source}")

        cobol_parser = CobolParser()
        ir_builder = IRBuilder()
        ir_serializer = IRSerializer()

        ast = cobol_parser.parse(expanded_cobol_source, "COBOL.cbl")
        ast_builder = AstBuilder("CBACT01C.cbl", "COBOL.cbl", cobol_source)
        ast = ast_builder.transform(ast)
        ir = ir_builder.build(ast)
        ir_serializer.to_json(ir, paths.ir_path)
        return 0
    except Exception as e:
        logger.error(f"code preprocessor intermediate results test failed", e)
        return 1


if __name__ == "__main__":
    exit(main())
