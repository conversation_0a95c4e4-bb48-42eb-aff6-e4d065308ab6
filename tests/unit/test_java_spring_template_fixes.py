"""
Unit tests for Java Spring template fixes.
Tests the fixes for the "'list' object has no attribute 'items'" error.
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from src.plugins.targets.java_spring.tools.project_manager.file_generator import ProjectFileGenerator
from src.plugins.targets.java_spring.agents.code_generator import JavaCodeGenerator


class TestTemplateValidationFixes:
    """Test cases for template validation fixes."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield tmpdir

    @pytest.fixture
    def mock_template_manager(self):
        """Create a mock template manager."""
        mock_manager = Mock()
        mock_manager.template_exists.return_value = True
        mock_manager.render_template.return_value = "# Generated content"
        return mock_manager

    @pytest.fixture
    def file_generator(self, mock_template_manager):
        """Create ProjectFileGenerator instance with mocked template manager."""
        generator = ProjectFileGenerator(
            template_manager=mock_template_manager,
            default_package="com.test.app",
            default_group_id="com.test",
            default_artifact_id="test-app",
            default_version="1.0.0",
            java_version="17",
            spring_boot_version="3.2.0"
        )
        return generator

    @pytest.fixture
    def code_generator(self):
        """Create JavaCodeGenerator instance with mocked components."""
        mock_llm = Mock()
        mock_knowledge_db = Mock()
        generator = JavaCodeGenerator(mock_llm, mock_knowledge_db)
        return generator

    def test_validate_template_context_fixes_custom_properties_list(self, file_generator):
        """Test that custom_properties list is converted to dict."""
        context = {
            "app_name": "test-app",
            "custom_properties": ["prop1", "prop2"]  # This should be a dict
        }

        validated = file_generator._validate_template_context(context)

        assert isinstance(validated["custom_properties"], dict)
        assert validated["custom_properties"] == {}
        assert validated["app_name"] == "test-app"  # Other properties unchanged

    def test_validate_template_context_fixes_custom_config_list(self, file_generator):
        """Test that custom_config list is converted to dict."""
        context = {
            "app_name": "test-app",
            "custom_config": ["config1", "config2"]  # This should be a dict
        }

        validated = file_generator._validate_template_context(context)

        assert isinstance(validated["custom_config"], dict)
        assert validated["custom_config"] == {}

    def test_validate_template_context_preserves_valid_dicts(self, file_generator):
        """Test that valid dictionaries are preserved."""
        context = {
            "app_name": "test-app",
            "custom_properties": {"key1": "value1", "key2": "value2"},
            "custom_config": {"setting1": "value1"}
        }

        validated = file_generator._validate_template_context(context)

        assert validated["custom_properties"] == {"key1": "value1", "key2": "value2"}
        assert validated["custom_config"] == {"setting1": "value1"}

    def test_validate_template_context_handles_non_dict_types(self, file_generator):
        """Test that non-dict types are converted to empty dict."""
        context = {
            "custom_properties": "invalid_string",
            "custom_config": 123
        }

        validated = file_generator._validate_template_context(context)

        assert validated["custom_properties"] == {}
        assert validated["custom_config"] == {}

    def test_application_properties_generation_with_list_custom_properties(self, file_generator, temp_dir):
        """Test application.properties generation with list custom_properties doesn't crash."""
        # Create the resources directory
        resources_dir = os.path.join(temp_dir, "src/main/resources")
        os.makedirs(resources_dir, exist_ok=True)

        config = {
            "app_name": "test-app",
            "custom_properties": ["prop1", "prop2"]  # This would cause the original error
        }

        # This should not raise an exception
        result = file_generator.generate_application_properties(temp_dir, config)

        assert result is not None
        assert os.path.exists(result)

    def test_application_yml_generation_with_list_custom_config(self, file_generator, temp_dir):
        """Test application.yml generation with list custom_config doesn't crash."""
        # Create the resources directory
        resources_dir = os.path.join(temp_dir, "src/main/resources")
        os.makedirs(resources_dir, exist_ok=True)

        config = {
            "app_name": "test-app",
            "custom_config": ["config1", "config2"]  # This would cause similar error
        }

        # This should not raise an exception
        result = file_generator.generate_application_yml(temp_dir, config)

        assert result is not None
        assert os.path.exists(result)

    def test_code_generator_validate_template_context_fixes_existing_mappings(self, code_generator):
        """Test that existing_mappings list is converted to dict in code generator."""
        context = {
            "chunk_docs": {"business_name": "Test"},
            "existing_mappings": ["mapping1", "mapping2"],  # Should be dict
            "java_structures": []
        }

        validated = code_generator._validate_template_context(context)

        assert isinstance(validated["existing_mappings"], dict)
        assert validated["existing_mappings"] == {}

    def test_code_generator_validate_template_context_fixes_java_structures(self, code_generator):
        """Test that java_structures non-list is converted to list."""
        context = {
            "chunk_docs": {"business_name": "Test"},
            "existing_mappings": {},
            "java_structures": "not_a_list"  # Should be list
        }

        validated = code_generator._validate_template_context(context)

        assert isinstance(validated["java_structures"], list)
        assert validated["java_structures"] == []

    def test_code_generator_validate_template_context_fixes_chunk_docs(self, code_generator):
        """Test that chunk_docs non-dict is converted to dict."""
        context = {
            "chunk_docs": ["not", "a", "dict"],  # Should be dict
            "existing_mappings": {},
            "java_structures": []
        }

        validated = code_generator._validate_template_context(context)

        assert isinstance(validated["chunk_docs"], dict)
        assert validated["chunk_docs"] == {}

    def test_template_path_fixes_use_correct_paths(self, file_generator, temp_dir):
        """Test that template paths are corrected to not include java_project prefix."""
        # Create the resources directory
        resources_dir = os.path.join(temp_dir, "src/main/resources")
        os.makedirs(resources_dir, exist_ok=True)

        # Mock the render_template method to verify correct path is used
        with patch.object(file_generator.template_manager, 'render_template') as mock_render:
            mock_render.return_value = "# Generated content"

            # This should call render_template with the corrected path
            file_generator.generate_application_properties(temp_dir)

            # Verify the correct template path is used (corrected path without prefix)
            mock_render.assert_called_once()
            call_args = mock_render.call_args
            assert call_args[0][0] == "application.properties.j2"

    @patch('src.plugins.targets.java_spring.tools.project_manager.file_generator.os.makedirs')
    @patch('builtins.open', create=True)
    def test_error_handling_in_template_rendering(self, mock_open, mock_makedirs, file_generator, temp_dir):
        """Test that template rendering errors are handled gracefully."""
        # Make template_exists return True but render_template raise an exception
        file_generator.template_manager.template_exists.return_value = True
        file_generator.template_manager.render_template.side_effect = Exception("Template error")

        # Mock file operations
        mock_file = Mock()
        mock_open.return_value.__enter__.return_value = mock_file

        # This should fall back to embedded template instead of crashing
        with pytest.raises(Exception):  # The method should still raise the exception
            file_generator.generate_application_properties(temp_dir)

    def test_integration_template_validation_prevents_items_error(self, file_generator, temp_dir):
        """Integration test: ensure the fixes prevent the original 'list' object has no attribute 'items' error."""
        # Create the resources directory
        resources_dir = os.path.join(temp_dir, "src/main/resources")
        os.makedirs(resources_dir, exist_ok=True)

        # Create a mock template that would try to call .items() on custom_properties
        def mock_render_template(template_name, context):
            # Simulate what the template does - try to call .items() on custom_properties
            if 'custom_properties' in context and context['custom_properties']:
                # This would fail with the original error if custom_properties is a list
                for key, value in context['custom_properties'].items():
                    pass
            return "# Generated content"

        file_generator.template_manager.render_template.side_effect = mock_render_template

        # This config would have caused the original error
        config = {
            "app_name": "test-app",
            "custom_properties": ["prop1", "prop2"]  # List instead of dict
        }

        # This should not raise "'list' object has no attribute 'items'" error
        result = file_generator.generate_application_properties(temp_dir, config)

        assert result is not None
        assert os.path.exists(result)


if __name__ == "__main__":
    pytest.main([__file__])
