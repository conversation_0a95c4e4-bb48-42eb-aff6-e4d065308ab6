"""
Unit tests for COBOL LLM analyzer functionality.
Tests the LLM analyzer component in isolation.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import json

from src.plugins.legacy.cobol.tools.data_extractors.llm_analyzer import LLMAnalyzer
from src.plugins.legacy.cobol.tools.data_extractors.core import CobolDataDefinitionExtractor


class TestCobolLLMAnalyzer:
    """Unit tests for COBOL LLM analyzer."""

    @pytest.fixture
    def llm_analyzer(self):
        """Create LLMAnalyzer instance with mocked LLM."""
        with patch('src.plugins.legacy.cobol.tools.data_extractors.llm_analyzer.llm') as mock_llm:
            analyzer = LLMAnalyzer()
            analyzer.llm = mock_llm
            return analyzer

    @pytest.fixture
    def sample_cobol_structure(self):
        """Sample COBOL data structure for testing."""
        return """
       01 WS-CUSTOMER-RECORD.
           05 WS-CUSTOMER-ID       PIC 9(6).
           05 WS-CUSTOMER-NAME     PIC X(30).
           05 WS-CUSTOMER-BALANCE  PIC S9(7)V99 COMP-3.
           05 WS-CUSTOMER-STATUS   PIC X.
               88 ACTIVE-CUSTOMER  VALUE 'A'.
               88 INACTIVE-CUSTOMER VALUE 'I'.
        """

    @pytest.fixture
    def mock_llm_response(self):
        """Mock LLM response with valid JSON."""
        return """
        Here are the extracted data items:

        ```json
        [
            {
                "name": "WS-CUSTOMER-ID",
                "level": 5,
                "business_name": "Customer ID",
                "item_type": "variable",
                "data_type": "NUMERIC",
                "length": 6,
                "description": "Customer identification number",
                "parent_name": "WS-CUSTOMER-RECORD"
            },
            {
                "name": "WS-CUSTOMER-NAME",
                "level": 5,
                "business_name": "Customer Name",
                "item_type": "variable",
                "data_type": "ALPHANUMERIC",
                "length": 30,
                "description": "Customer full name",
                "parent_name": "WS-CUSTOMER-RECORD"
            },
            {
                "name": "ACTIVE-CUSTOMER",
                "level": 88,
                "business_name": "Active Customer",
                "item_type": "condition",
                "data_type": "CONDITION",
                "description": "Condition name for active customer status",
                "parent_name": "WS-CUSTOMER-STATUS",
                "possible_values": ["A"]
            }
        ]
        ```
        """

    def test_analyze_structure_with_llm_success(self, llm_analyzer, sample_cobol_structure, mock_llm_response):
        """Test successful LLM analysis of COBOL structure."""
        # Setup
        mock_response = Mock()
        mock_response = mock_llm_response
        llm_analyzer.llm.invoke.return_value = mock_response

        # Execute
        result = llm_analyzer.analyze_structure_with_llm(
            sample_cobol_structure,
            "WS-CUSTOMER-RECORD",
            "TESTPROG",
            "TESTPROG.cbl"
        )

        # Verify
        assert len(result) == 3, "Should extract 3 data items"

        # Check first item (WS-CUSTOMER-ID)
        customer_id = next(item for item in result if item['name'] == 'WS-CUSTOMER-ID')
        assert customer_id['data_type'] == 'NUMERIC'
        assert customer_id['length'] == 6
        assert customer_id['business_name'] == 'Customer ID'
        assert customer_id['program_id'] == 'TESTPROG'
        assert customer_id['source_file_name'] == 'TESTPROG.cbl'

        # Check condition name (88-level item)
        active_customer = next(item for item in result if item['name'] == 'ACTIVE-CUSTOMER')
        assert active_customer['item_type'] == 'condition'
        assert active_customer['level'] == 88
        assert active_customer['possible_values'] == ['A']

    def test_analyze_structure_with_llm_json_parsing_error(self, llm_analyzer, sample_cobol_structure):
        """Test LLM analysis with invalid JSON response."""
        # Setup - return invalid JSON
        mock_response = Mock()
        mock_response = "This is not valid JSON response"
        llm_analyzer.llm.invoke.return_value = mock_response

        # Execute - should raise exception instead of using fallback
        with pytest.raises(Exception) as exc_info:
            llm_analyzer.analyze_structure_with_llm(
                sample_cobol_structure,
                "WS-CUSTOMER-RECORD",
                "TESTPROG",
                "TESTPROG.cbl"
            )

        # Verify exception is raised instead of fallback
        assert "LLM analysis failed" in str(exc_info.value)

    def test_analyze_structure_with_llm_exception_handling(self, llm_analyzer, sample_cobol_structure):
        """Test LLM analysis with exception during LLM call - should raise exception after retries."""
        # Setup - LLM throws exception
        llm_analyzer.llm.invoke.side_effect = Exception("LLM service unavailable")

        # Execute - should raise exception after retries
        with pytest.raises(Exception) as exc_info:
            llm_analyzer.analyze_structure_with_llm(
                sample_cobol_structure,
                "WS-CUSTOMER-RECORD",
                "TESTPROG",
                "TESTPROG.cbl"
            )

        # Verify the exception message indicates retry failure
        assert "LLM analysis failed after 3 attempts" in str(exc_info.value)

    def test_enhanced_json_extraction(self, llm_analyzer):
        """Test enhanced JSON extraction with various formats."""
        # Test markdown JSON
        response1 = """```json
        [{"name": "TEST-FIELD", "level": 5}]
        ```"""
        result1 = llm_analyzer._extract_json_from_response_enhanced(response1)
        assert result1 == '[{"name": "TEST-FIELD", "level": 5}]'

        # Test JSON with common issues (trailing commas)
        response2 = """[{"name": "TEST-FIELD", "level": 5,}]"""
        result2 = llm_analyzer._extract_json_from_response_enhanced(response2)
        assert '"name": "TEST-FIELD"' in result2

        # Test no JSON found
        response3 = "This is just text with no JSON"
        result3 = llm_analyzer._extract_json_from_response_enhanced(response3)
        assert result3 == ""

    def test_parse_llm_response_with_markdown_json(self, llm_analyzer):
        """Test parsing LLM response with JSON in markdown code block."""
        response = """
        Here are the data items:

        ```json
        [
            {
                "name": "TEST-FIELD",
                "data_type": "NUMERIC",
                "length": 5
            }
        ]
        ```
        """

        result = llm_analyzer._parse_llm_response(response, "TEST-STRUCT", "TESTPROG", "test.cbl")

        assert len(result) == 1
        assert result[0]['name'] == 'TEST-FIELD'
        assert result[0]['data_type'] == 'NUMERIC'

    def test_parse_llm_response_with_plain_json(self, llm_analyzer):
        """Test parsing LLM response with plain JSON array."""
        response = """
        [
            {
                "name": "TEST-FIELD",
                "data_type": "ALPHANUMERIC",
                "length": 10
            }
        ]
        """

        result = llm_analyzer._parse_llm_response(response, "TEST-STRUCT", "TESTPROG", "test.cbl")

        assert len(result) == 1
        assert result[0]['name'] == 'TEST-FIELD'
        assert result[0]['data_type'] == 'ALPHANUMERIC'

    def test_standardize_data_item(self, llm_analyzer):
        """Test standardization of LLM-extracted data item."""
        raw_item = {
            "name": "WS-TEST-FIELD",
            "level": 5,
            "business_name": "Test Field",
            "item_type": "variable",
            "data_type": "numeric",  # lowercase
            "length": 10,
            "description": "A test field",
            "parent_name": "WS-PARENT"
        }

        result = llm_analyzer._standardize_data_item(
            raw_item, "WS-PARENT", "TESTPROG", "test.cbl"
        )

        assert result['name'] == 'WS-TEST-FIELD'
        assert result['data_type'] == 'NUMERIC'  # Should be uppercase
        assert result['program_id'] == 'TESTPROG'
        assert result['source_file_name'] == 'test.cbl'
        assert result['top_parent_datastructure_name'] == 'WS-PARENT'

    def test_standardize_data_item_missing_name(self, llm_analyzer):
        """Test standardization with missing required name field."""
        raw_item = {
            "level": 5,
            "data_type": "NUMERIC"
        }

        result = llm_analyzer._standardize_data_item(
            raw_item, "WS-PARENT", "TESTPROG", "test.cbl"
        )

        assert result is None  # Should return None for invalid items

    def test_standardize_data_item_with_none_values(self, llm_analyzer):
        """Test standardization with None values for various fields."""
        raw_item = {
            "name": "WS-TEST-FIELD",
            "level": 5,
            "business_name": None,  # None value
            "item_type": None,      # None value
            "data_type": None,      # None value
            "length": 10,
            "description": None,    # None value
            "parent_name": None,    # None value
            "possible_values": None,
            "default_value": None,
            "occurs_info": None,
            "redefines_info": None
        }

        result = llm_analyzer._standardize_data_item(
            raw_item, "WS-PARENT", "TESTPROG", "test.cbl"
        )

        # Should not return None and should handle None values gracefully
        assert result is not None
        assert result['name'] == 'WS-TEST-FIELD'
        assert result['business_name'] == ''  # None should become empty string
        assert result['item_type'] == 'variable'  # None should become default
        assert result['data_type'] == 'UNKNOWN'  # None should become default
        assert result['description'] == ''  # None should become empty string
        assert result['parent_name'] == 'WS-PARENT'  # None becomes top_parent when level > 1
        assert result['program_id'] == 'TESTPROG'
        assert result['source_file_name'] == 'test.cbl'
        assert result['top_parent_datastructure_name'] == 'WS-PARENT'

    def test_standardize_data_item_with_string_level(self, llm_analyzer):
        """Test standardization with string level values (like '01', '05')."""
        raw_item = {
            "name": "TWO-BYTES-ALPHA",
            "level": "01",  # String level
            "business_name": "Two Bytes Alpha",
            "item_type": "group",
            "data_type": "UNKNOWN",
            "length": None,
            "description": "Group item redefining TWO-BYTES-BINARY",
            "parent_name": None,
            "possible_values": [],
            "default_value": None,
            "occurs_info": None,
            "redefines_info": "Redefines TWO-BYTES-BINARY"
        }

        result = llm_analyzer._standardize_data_item(
            raw_item, "TWO-BYTES-ALPHA", "TESTPROG", "test.cbl"
        )

        # Should not return None and should handle string level correctly
        assert result is not None
        assert result['name'] == 'TWO-BYTES-ALPHA'
        assert result['level'] == 1  # String "01" should become integer 1
        assert result['item_type'] == 'group'
        assert result['data_type'] == 'UNKNOWN'
        assert result['business_name'] == 'Two Bytes Alpha'
        assert result['description'] == 'Group item redefining TWO-BYTES-BINARY'
        assert result['redefines'] == 'Redefines TWO-BYTES-BINARY'  # redefines_info mapped to redefines
        assert result['program_id'] == 'TESTPROG'
        assert result['source_file_name'] == 'test.cbl'
        assert result['top_parent_datastructure_name'] == 'TWO-BYTES-ALPHA'

    def test_extract_json_from_response_markdown(self, llm_analyzer):
        """Test JSON extraction from markdown code block."""
        response = """
        Some text before

        ```json
        [{"name": "TEST"}]
        ```

        Some text after
        """

        result = llm_analyzer._extract_json_from_response(response)
        assert result == '[{"name": "TEST"}]'

    def test_extract_json_from_response_plain(self, llm_analyzer):
        """Test JSON extraction from plain response."""
        response = '[{"name": "TEST", "type": "NUMERIC"}]'

        result = llm_analyzer._extract_json_from_response(response)
        assert result == '[{"name": "TEST", "type": "NUMERIC"}]'

    def test_extract_json_from_response_no_json(self, llm_analyzer):
        """Test JSON extraction when no JSON is present."""
        response = "This response contains no JSON data"

        result = llm_analyzer._extract_json_from_response(response)
        assert result == ""


class TestCobolDataDefinitionExtractorIntegration:
    """Integration tests for CobolDataDefinitionExtractor with LLM analyzer."""

    @pytest.fixture
    def extractor(self):
        """Create CobolDataDefinitionExtractor instance."""
        return CobolDataDefinitionExtractor()

    @pytest.fixture
    def sample_chunk(self):
        """Sample chunk data for testing."""
        return {
            'code': """
       01 WS-CUSTOMER-RECORD.
           05 WS-CUSTOMER-ID       PIC 9(6).
           05 WS-CUSTOMER-NAME     PIC X(30).
            """,
            'chunk_type': 'DATA_DIVISION_WORKING_STORAGE',
            'chunk_name': 'WS-CUSTOMER-RECORD'
        }

    def test_process_chunk_invokes_llm_analyzer(self, extractor, sample_chunk):
        """Test that process_chunk properly invokes LLM analyzer."""
        with patch.object(extractor.llm_analyzer, 'analyze_structure_with_llm') as mock_llm_analyze:
            mock_llm_analyze.return_value = [
                {
                    'name': 'WS-CUSTOMER-ID',
                    'data_type': 'NUMERIC',
                    'program_id': 'TESTPROG'
                }
            ]

            # Execute
            result = extractor.process_chunk(sample_chunk, 'TESTPROG')

            # Verify LLM analyzer was called
            assert mock_llm_analyze.called
            assert len(result) >= 1  # Should include LLM results

    def test_process_chunk_handles_llm_failure(self, extractor, sample_chunk):
        """Test that process_chunk propagates LLM analyzer failure - no more fallback in core."""
        with patch.object(extractor.llm_analyzer, 'analyze_structure_with_llm') as mock_llm_analyze:
            mock_llm_analyze.side_effect = Exception("LLM failed")

            # Execute - should raise exception since we removed fallback from core
            with pytest.raises(Exception) as exc_info:
                extractor.process_chunk(sample_chunk, 'TESTPROG')

            # Verify the exception is propagated
            assert "LLM failed" in str(exc_info.value)
