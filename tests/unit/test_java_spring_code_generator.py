"""
Unit tests for Java Spring code generator.
Tests the fix for the 'list' object has no attribute 'items' error and other functionality.
"""

import pytest
import json
import re
from unittest.mock import Mock, MagicMock
from typing import Dict, Any, Optional


class MockJavaCodeGenerator:
    """Mock Java code generator with the actual methods we want to test."""

    def __init__(self):
        self.logger = Mock()

    def _extract_mappings_from_response(self, response: str) -> Dict[str, Any]:
        """Extract mappings from LLM response."""
        import re
        import json

        self.logger.debug(f"Extracting mappings from response (length: {len(response)})")

        # Try multiple patterns to find JSON mappings
        json_patterns = [
            r'```json\s*(.*?)\s*```',           # Standard JSON code block
            r'```\s*(.*?)\s*```',               # Generic code block
            r'mappings?\s*:\s*(\{.*?\})',       # mappings: {...}
            r'(\{[^{}]*"[^"]*"\s*:\s*"[^"]*"[^{}]*\})',  # Simple key-value object
        ]

        for i, pattern in enumerate(json_patterns):
            match = re.search(pattern, response, re.DOTALL | re.IGNORECASE)
            if match:
                try:
                    json_text = match.group(1).strip()
                    self.logger.debug(f"Found JSON candidate using pattern {i+1}: {json_text[:100]}...")

                    parsed_json = json.loads(json_text)

                    # Ensure we return a dictionary, not a list
                    if isinstance(parsed_json, dict):
                        self.logger.info(f"Successfully extracted {len(parsed_json)} mappings")
                        return parsed_json
                    elif isinstance(parsed_json, list):
                        self.logger.warning("LLM returned a list instead of dictionary for mappings, attempting conversion")
                        # Try to convert list to dict if it contains key-value pairs
                        if parsed_json and isinstance(parsed_json[0], dict):
                            # If it's a list of objects, merge them
                            result = {}
                            for item in parsed_json:
                                if isinstance(item, dict):
                                    result.update(item)
                            return result
                        return {}
                    else:
                        self.logger.warning(f"LLM returned unexpected type for mappings: {type(parsed_json)}")
                        return {}

                except json.JSONDecodeError as e:
                    self.logger.debug(f"Pattern {i+1} failed JSON parsing: {str(e)}")
                    continue

        self.logger.warning("No valid JSON mappings found in LLM response")
        return {}

    def _save_mappings_to_database(self, program_id: str, chunk_name: str,
                                   mappings: Dict[str, Any], tools):
        """Save mappings to database."""
        try:
            # Validate that mappings is a dictionary
            if not isinstance(mappings, dict):
                self.logger.warning(f"Mappings is not a dictionary (type: {type(mappings)}), skipping database save")
                return

            if not mappings:
                self.logger.info("No mappings to save to database")
                return

            self.logger.info(f"Saving {len(mappings)} mappings to database for {program_id}.{chunk_name}")

            for cobol_name, java_name in mappings.items():
                tools.knowledge_db.save_cobol_java_mapping(
                    program_id=program_id,
                    chunk_name=chunk_name,
                    cobol_name=cobol_name,
                    java_name=java_name,
                    mapping_type="variable"
                )
        except Exception as e:
            self.logger.error(f"Error saving mappings to database: {str(e)}")

    def _extract_java_code_from_response(self, response: str) -> str:
        """Extract Java code from LLM response."""
        import re

        # Look for Java code blocks
        java_pattern = r'```java\s*(.*?)\s*```'
        match = re.search(java_pattern, response, re.DOTALL | re.IGNORECASE)

        if match:
            return match.group(1).strip()

        # Fallback: look for any code block
        code_pattern = r'```\s*(.*?)\s*```'
        match = re.search(code_pattern, response, re.DOTALL)

        if match:
            return match.group(1).strip()

        return ""

    def _extract_class_name_from_java_code(self, java_code: str) -> Optional[str]:
        """Extract class name from Java code."""
        import re

        # Look for class declaration
        class_pattern = r'public\s+class\s+(\w+)'
        match = re.search(class_pattern, java_code)

        if match:
            return match.group(1)

        return None


class TestJavaSpringCodeGenerator:
    """Test the Java Spring code generator with comprehensive coverage."""

    def setup_method(self):
        """Set up test fixtures."""
        self.generator = MockJavaCodeGenerator()

    def test_extract_mappings_with_valid_dict_json(self):
        """Test extracting mappings from valid dictionary JSON."""
        response = '''
        Here's the generated code:

        ```java
        public class TestService {
            // code here
        }
        ```

        ```json
        {
            "WS-CUSTOMER-ID": "customerId",
            "WS-CUSTOMER-NAME": "customerName",
            "WS-BALANCE": "balance"
        }
        ```
        '''

        result = self.generator._extract_mappings_from_response(response)

        assert isinstance(result, dict)
        assert len(result) == 3
        assert result["WS-CUSTOMER-ID"] == "customerId"
        assert result["WS-CUSTOMER-NAME"] == "customerName"
        assert result["WS-BALANCE"] == "balance"

    def test_extract_mappings_with_list_json_single_objects(self):
        """Test extracting mappings from list JSON with single key-value objects."""
        response = '''
        ```json
        [
            {"WS-CUSTOMER-ID": "customerId"},
            {"WS-CUSTOMER-NAME": "customerName"},
            {"WS-BALANCE": "balance"}
        ]
        ```
        '''

        result = self.generator._extract_mappings_from_response(response)

        # Should convert list to dictionary
        assert isinstance(result, dict)
        assert len(result) == 3
        assert result["WS-CUSTOMER-ID"] == "customerId"
        assert result["WS-CUSTOMER-NAME"] == "customerName"
        assert result["WS-BALANCE"] == "balance"

    def test_extract_mappings_with_list_json_multi_key_objects(self):
        """Test extracting mappings from list JSON with multi-key objects."""
        response = '''
        ```json
        [
            {"WS-CUSTOMER-ID": "customerId", "WS-CUSTOMER-NAME": "customerName"},
            {"WS-BALANCE": "balance", "WS-STATUS": "status"}
        ]
        ```
        '''

        result = self.generator._extract_mappings_from_response(response)

        # Should merge all objects into single dictionary
        assert isinstance(result, dict)
        assert len(result) == 4
        assert result["WS-CUSTOMER-ID"] == "customerId"
        assert result["WS-CUSTOMER-NAME"] == "customerName"
        assert result["WS-BALANCE"] == "balance"
        assert result["WS-STATUS"] == "status"

    def test_extract_mappings_with_list_json_non_dict_items(self):
        """Test extracting mappings from list JSON with non-dictionary items."""
        response = '''
        ```json
        ["string1", "string2", "string3"]
        ```
        '''

        result = self.generator._extract_mappings_from_response(response)

        # Should return empty dict for non-dict list items
        assert isinstance(result, dict)
        assert len(result) == 0

    def test_extract_mappings_with_invalid_json(self):
        """Test extracting mappings from invalid JSON."""
        response = '''
        ```json
        {invalid json here, missing quotes: value}
        ```
        '''

        result = self.generator._extract_mappings_from_response(response)

        # Should return empty dict, not raise an error
        assert isinstance(result, dict)
        assert len(result) == 0

    def test_extract_mappings_with_no_json(self):
        """Test extracting mappings when no JSON is present."""
        response = '''
        Here's some code without any JSON mappings.
        Just plain text response.
        '''

        result = self.generator._extract_mappings_from_response(response)

        # Should return empty dict
        assert isinstance(result, dict)
        assert len(result) == 0

    def test_extract_mappings_with_generic_code_block(self):
        """Test extracting mappings from generic code block (not json-specific)."""
        response = '''
        ```
        {
            "WS-FIELD1": "field1",
            "WS-FIELD2": "field2"
        }
        ```
        '''

        result = self.generator._extract_mappings_from_response(response)

        assert isinstance(result, dict)
        assert len(result) == 2
        assert result["WS-FIELD1"] == "field1"
        assert result["WS-FIELD2"] == "field2"

    def test_extract_mappings_with_mappings_prefix(self):
        """Test extracting mappings with 'mappings:' prefix pattern."""
        response = '''
        The mappings are:
        mappings: {"WS-ID": "id", "WS-NAME": "name"}
        '''

        result = self.generator._extract_mappings_from_response(response)

        assert isinstance(result, dict)
        assert len(result) == 2
        assert result["WS-ID"] == "id"
        assert result["WS-NAME"] == "name"

    def test_save_mappings_with_valid_dict(self):
        """Test saving valid dictionary mappings to database."""
        mappings = {
            "WS-CUSTOMER-ID": "customerId",
            "WS-CUSTOMER-NAME": "customerName",
            "WS-BALANCE": "balance"
        }

        mock_tools = Mock()
        mock_tools.knowledge_db.save_cobol_java_mapping = Mock()

        self.generator._save_mappings_to_database("TEST_PROG", "TEST_CHUNK", mappings, mock_tools)

        # Should call save method for each mapping
        assert mock_tools.knowledge_db.save_cobol_java_mapping.call_count == 3

        # Verify the calls
        calls = mock_tools.knowledge_db.save_cobol_java_mapping.call_args_list
        assert any("WS-CUSTOMER-ID" in str(call) for call in calls)
        assert any("customerId" in str(call) for call in calls)

    def test_save_mappings_with_list_input(self):
        """Test saving list input to database (should be handled gracefully)."""
        mappings = ["item1", "item2", "item3"]  # This would cause the original error

        mock_tools = Mock()
        mock_tools.knowledge_db.save_cobol_java_mapping = Mock()

        # Should not raise an error
        self.generator._save_mappings_to_database("TEST_PROG", "TEST_CHUNK", mappings, mock_tools)

        # Should not call save method since input is invalid
        assert mock_tools.knowledge_db.save_cobol_java_mapping.call_count == 0

        # Should log warning
        self.generator.logger.warning.assert_called()

    def test_save_mappings_with_empty_dict(self):
        """Test saving empty dictionary to database."""
        mappings = {}

        mock_tools = Mock()
        mock_tools.knowledge_db.save_cobol_java_mapping = Mock()

        self.generator._save_mappings_to_database("TEST_PROG", "TEST_CHUNK", mappings, mock_tools)

        # Should not call save method since dict is empty
        assert mock_tools.knowledge_db.save_cobol_java_mapping.call_count == 0

        # Should log info about no mappings
        self.generator.logger.info.assert_called()

    def test_save_mappings_with_none_input(self):
        """Test saving None input to database."""
        mappings = None

        mock_tools = Mock()
        mock_tools.knowledge_db.save_cobol_java_mapping = Mock()

        # Should not raise an error
        self.generator._save_mappings_to_database("TEST_PROG", "TEST_CHUNK", mappings, mock_tools)

        # Should not call save method since input is invalid
        assert mock_tools.knowledge_db.save_cobol_java_mapping.call_count == 0

        # Should log warning
        self.generator.logger.warning.assert_called()

    def test_save_mappings_with_string_input(self):
        """Test saving string input to database."""
        mappings = "not a dictionary"

        mock_tools = Mock()
        mock_tools.knowledge_db.save_cobol_java_mapping = Mock()

        # Should not raise an error
        self.generator._save_mappings_to_database("TEST_PROG", "TEST_CHUNK", mappings, mock_tools)

        # Should not call save method since input is invalid
        assert mock_tools.knowledge_db.save_cobol_java_mapping.call_count == 0

        # Should log warning with type information
        self.generator.logger.warning.assert_called()
        warning_call = self.generator.logger.warning.call_args[0][0]
        assert "str" in warning_call

    def test_save_mappings_database_error(self):
        """Test handling database errors during mapping save."""
        mappings = {"WS-FIELD": "field"}

        mock_tools = Mock()
        mock_tools.knowledge_db.save_cobol_java_mapping.side_effect = Exception("Database error")

        # Should not raise an error, should handle gracefully
        self.generator._save_mappings_to_database("TEST_PROG", "TEST_CHUNK", mappings, mock_tools)

        # Should log error
        self.generator.logger.error.assert_called()

    def test_extract_java_code_from_response_java_block(self):
        """Test extracting Java code from response with java code block."""
        response = '''
        Here's the generated code:

        ```java
        public class TestService {
            public String process() {
                return "test";
            }
        }
        ```
        '''

        result = self.generator._extract_java_code_from_response(response)

        assert "public class TestService" in result
        assert "public String process()" in result
        assert "return \"test\";" in result

    def test_extract_java_code_from_response_generic_block(self):
        """Test extracting Java code from generic code block."""
        response = '''
        ```
        public class GenericService {
            // code here
        }
        ```
        '''

        result = self.generator._extract_java_code_from_response(response)

        assert "public class GenericService" in result

    def test_extract_java_code_from_response_no_code(self):
        """Test extracting Java code when no code blocks present."""
        response = '''
        Just some text without any code blocks.
        '''

        result = self.generator._extract_java_code_from_response(response)

        assert result == ""

    def test_extract_class_name_from_java_code(self):
        """Test extracting class name from Java code."""
        java_code = '''
        package com.example;

        public class CustomerService {
            // methods here
        }
        '''

        result = self.generator._extract_class_name_from_java_code(java_code)

        assert result == "CustomerService"

    def test_extract_class_name_from_java_code_no_class(self):
        """Test extracting class name when no class declaration present."""
        java_code = '''
        // Just some comments
        // No class declaration
        '''

        result = self.generator._extract_class_name_from_java_code(java_code)

        assert result is None


if __name__ == "__main__":
    # Run a quick verification test
    print("Running Java Spring Code Generator tests...")

    # Test the specific error case that was causing issues
    test_response = '''
    ```json
    [
        {"WS-CUSTOMER-ID": "customerId"},
        {"WS-CUSTOMER-NAME": "customerName"}
    ]
    ```
    '''

    # Simulate the fix
    import json
    import re

    json_pattern = r'```json\s*(.*?)\s*```'
    match = re.search(json_pattern, test_response, re.DOTALL | re.IGNORECASE)

    if match:
        parsed_json = json.loads(match.group(1).strip())

        print(f"Parsed JSON type: {type(parsed_json)}")
        print(f"Parsed JSON content: {parsed_json}")

        # Apply our fix
        if isinstance(parsed_json, dict):
            result = parsed_json
        elif isinstance(parsed_json, list):
            print("✓ Detected list response (would cause original error)")
            if parsed_json and isinstance(parsed_json[0], dict):
                result = {}
                for item in parsed_json:
                    if isinstance(item, dict):
                        result.update(item)
                print(f"✓ Converted to dict: {result}")
            else:
                result = {}
        else:
            result = {}

        # Test that we can now safely call .items() on the result
        try:
            for k, v in result.items():
                print(f"✓ Mapping: {k} -> {v}")
            print("✓ No 'list' object has no attribute 'items' error!")
        except AttributeError as e:
            print(f"❌ Error: {e}")

    print("\nTest completed successfully!")
