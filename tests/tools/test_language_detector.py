"""
Unit tests for language detection functionality - Plugin Architecture.
"""
import pytest
from unittest.mock import Mock, patch
from src.platform.tools.language_detector import detect_language


class TestLanguageDetectorPlugin:
    """Test cases for language detection using plugin architecture."""

    @pytest.fixture
    def mock_plugin_loader(self):
        """Create mock plugin loader with language plugins."""
        loader = Mock()

        # Mock COBOL plugin
        cobol_plugin = Mock()
        cobol_plugin.get_name.return_value = "cobol"
        cobol_detector = Mock()
        cobol_detector.get_confidence_score.return_value = 0.9
        cobol_plugin.get_detector.return_value = cobol_detector

        # Mock Java plugin
        java_plugin = Mock()
        java_plugin.get_name.return_value = "java"
        java_detector = Mock()
        java_detector.get_confidence_score.return_value = 0.1
        java_plugin.get_detector.return_value = java_detector

        loader.get_language_plugins.return_value = {
            "cobol": cobol_plugin,
            "java": java_plugin
        }
        loader.detect_language.return_value = "cobol"

        return loader

    @patch('src.platform.plugins.plugin_loader.get_plugin_loader')
    def test_detect_language_plugin_based(self, mock_get_loader, mock_plugin_loader):
        """Test language detection using plugin system."""
        mock_get_loader.return_value = mock_plugin_loader

        cobol_code = """
        IDENTIFICATION DIVISION.
        PROGRAM-ID. TEST-PROGRAM.
        DATA DIVISION.
        WORKING-STORAGE SECTION.
        01 WS-COUNTER PIC 9(3) VALUE 0.
        PROCEDURE DIVISION.
        DISPLAY "Hello World".
        STOP RUN.
        """

        # Use a filename without extension to force plugin detection
        result = detect_language(cobol_code, "test")

        assert result == "cobol"
        mock_plugin_loader.detect_language.assert_called_once_with(cobol_code)

    @patch('src.platform.plugins.plugin_loader.get_plugin_loader')
    def test_detect_language_no_plugins(self, mock_get_loader):
        """Test language detection when no plugins are available."""
        mock_loader = Mock()
        mock_loader.detect_language.return_value = None
        mock_get_loader.return_value = mock_loader

        # Use a filename without extension to avoid extension-based detection
        result = detect_language("some code", "test")

        assert result == "other"  # Should fall back to 'other'

    @patch('src.platform.plugins.plugin_loader.get_plugin_loader')
    def test_detect_language_plugin_error(self, mock_get_loader):
        """Test language detection when plugin system fails."""
        mock_loader = Mock()
        mock_loader.detect_language.side_effect = Exception("Plugin error")
        mock_loader.get_language_plugins.return_value = {}
        mock_loader.get_available_languages.return_value = []
        mock_get_loader.return_value = mock_loader

        # With plugin architecture, should fall back to extension-based detection
        cobol_code = """
        IDENTIFICATION DIVISION.
        PROGRAM-ID. TEST-PROGRAM.
        """

        result = detect_language(cobol_code, "test.cob")

        # Should detect COBOL by extension since plugins failed
        assert result == "other"  # No plugins available, no extension mapping

    def test_detect_language_by_extension(self, mock_plugin_loader):
        """Test language detection by file extension."""
        with patch('src.platform.plugins.plugin_loader.get_plugin_loader', return_value=mock_plugin_loader):
            # Test COBOL extensions - should use plugin detection
            result = detect_language("DISPLAY 'TEST'.", "test.cob")
            assert result == "cobol"

    def test_detect_language_confidence_threshold(self, mock_plugin_loader):
        """Test language detection confidence threshold."""
        # Mock low confidence detection
        cobol_plugin = mock_plugin_loader.get_language_plugins.return_value["cobol"]
        cobol_detector = cobol_plugin.get_detector.return_value
        cobol_detector.get_confidence_score.return_value = 0.3  # Below threshold

        mock_plugin_loader.detect_language.return_value = None  # Low confidence

        with patch('src.platform.plugins.plugin_loader.get_plugin_loader', return_value=mock_plugin_loader):
            # Use a filename without extension to avoid extension-based detection
            result = detect_language("ambiguous code", "test")

            assert result == "other"  # Should fall back to 'other'

    def test_plugin_integration(self, mock_plugin_loader):
        """Test integration with plugin system."""
        with patch('src.platform.plugins.plugin_loader.get_plugin_loader', return_value=mock_plugin_loader):
            # Test that the function properly uses plugin loader
            plugins = mock_plugin_loader.get_language_plugins.return_value

            assert "cobol" in plugins
            assert "java" in plugins

            # Test that detectors are properly accessed
            cobol_plugin = plugins["cobol"]
            detector = cobol_plugin.get_detector.return_value
            assert detector is not None

    def test_plugin_required_architecture(self):
        """Test that plugin architecture is enforced when plugins fail."""
        with patch('src.platform.plugins.plugin_loader.get_plugin_loader', side_effect=Exception("No plugins")):
            # With plugin architecture, no fallback patterns should be used
            cobol_code = """
            IDENTIFICATION DIVISION.
            PROGRAM-ID. TEST-PROGRAM.
            DATA DIVISION.
            WORKING-STORAGE SECTION.
            01 WS-COUNTER PIC 9(3) VALUE 0.
            PROCEDURE DIVISION.
            DISPLAY "Hello World".
            STOP RUN.
            """

            result = detect_language(cobol_code, "test.cob")

            # Should return 'other' since no plugins are available (plugin architecture enforced)
            assert result == "other"

    def test_multiple_language_detection(self, mock_plugin_loader):
        """Test detection when multiple languages have similar confidence."""
        # Mock similar confidence scores
        cobol_plugin = mock_plugin_loader.get_language_plugins.return_value["cobol"]
        java_plugin = mock_plugin_loader.get_language_plugins.return_value["java"]

        cobol_detector = cobol_plugin.get_detector.return_value
        java_detector = java_plugin.get_detector.return_value

        cobol_detector.get_confidence_score.return_value = 0.7
        java_detector.get_confidence_score.return_value = 0.6

        # Plugin loader should return the highest confidence language
        mock_plugin_loader.detect_language.return_value = "cobol"

        with patch('src.platform.plugins.plugin_loader.get_plugin_loader', return_value=mock_plugin_loader):
            # Use a filename without extension to avoid extension-based detection
            result = detect_language("mixed code", "test")

            assert result == "cobol"
