#!/usr/bin/env python

import os
import sys
import logging
import argparse
import pytest
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)


@pytest.mark.skip(reason="COBOL preprocessor depends on missing tools.standalone module")
def test_cobol_preprocessor(input_file=None, project_name=None):
    """
    Test the COBOL preprocessor with flow diagram generation.

    Args:
        input_file: Path to the COBOL file to process (relative to project root)
        project_name: Name of the project folder to use in output path
    """
    try:
        from src.plugins.legacy.cobol.tools.preprocessor import CobolPreprocessor

        # Initialize the preprocessor
        preprocessor = CobolPreprocessor()

        # Default values
        if input_file is None:
            input_file = "code_samples/CBACT01C.cbl"

        if project_name is None:
            project_name = "test_project"

        # Convert input path to absolute if needed
        if not os.path.isabs(input_file):
            input_file = os.path.join(PROJECT_ROOT, input_file)

        # Get file name for output
        file_name = os.path.basename(input_file)

        # Output path
        output_dir = os.path.join(PROJECT_ROOT, "out", project_name, "preprocessed", "cobol")
        output_file = os.path.join(output_dir, file_name)

        # Make sure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Process the file
        logger.info(f"Processing {input_file} -> {output_file}")
        result = preprocessor.preprocess_file(input_file, output_file)

        if result:
            logger.info("Preprocessing completed successfully")

            # Check if flow diagram was created
            flow_dir = os.path.join(output_dir, "rekt")
            if os.path.exists(flow_dir):
                logger.info(f"REKT analysis output directory found: {flow_dir}")

                # List generated files
                for root, dirs, files in os.walk(flow_dir):
                    rel_path = os.path.relpath(root, flow_dir)
                    if rel_path != ".":
                        logger.info(f"Files in {rel_path}:")
                    else:
                        logger.info("Files in root:")
                    for file in files:
                        logger.info(f"  - {file}")
            else:
                logger.warning(f"REKT analysis output directory not found: {flow_dir}")
        else:
            logger.error("Preprocessing failed")

        # Use assertions for pytest compatibility
        assert result is not False, "Preprocessing should not fail"
        return result

    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)
        assert False, f"Test failed with exception: {str(e)}"


def main():
    """Parse command line arguments and run the test."""
    parser = argparse.ArgumentParser(description="Test COBOL preprocessor with flow diagram generation")
    parser.add_argument("-f", "--file", help="Path to the COBOL file to process")
    parser.add_argument("-p", "--project", help="Project name for output directory")
    args = parser.parse_args()

    result = test_cobol_preprocessor(args.file, args.project)
    return 0 if result else 1


if __name__ == "__main__":
    sys.exit(main())