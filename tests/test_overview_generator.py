#!/usr/bin/env python

import os
import sys
import logging
import argparse
import pytest
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

from src.platform.agents.base_agent import AgentInput
from src.platform.agents.overview_generator import OverviewGeneratorAgent


def test_overview_generator(project_name=None):
    """
    Test the CobolOverviewGeneratorAgent.

    Args:
        project_name: Name of the project folder to use in working directory
    """
    try:
        # Default project name if not provided
        if project_name is None:
            project_name = "test_project"

        # Set up working directory
        working_directory = os.path.join(PROJECT_ROOT, "out", project_name)

        # Check if the required directories exist
        dotfiles_dir = os.path.join(working_directory, "preprocessed/cobol/rekt/dotfiles")
        flowcharts_dir = os.path.join(working_directory, "preprocessed/cobol/rekt/flowcharts")

        if not os.path.exists(dotfiles_dir):
            logger.error(f"Dot files directory not found: {dotfiles_dir}")
            logger.info("Please run test_cobol_flow.py first to generate the necessary files")
            pytest.skip("Dot files directory not found - run test_cobol_flow.py first")

        if not os.path.exists(flowcharts_dir):
            logger.warning(f"Flowcharts directory not found: {flowcharts_dir}")

        # Count dot files to process
        dot_files = [f for f in os.listdir(dotfiles_dir) if f.endswith('.dot')]
        if not dot_files:
            logger.error("No dot files found to process")
            pytest.skip("No dot files found to process")

        logger.info(f"Found {len(dot_files)} dot files to process")

        # Initialize the agent
        agent = OverviewGeneratorAgent()

        # Create agent input
        input_data = AgentInput(
            working_directory=working_directory,
            knowledge_base={}  # Empty knowledge base for this test
        )

        # Process the input
        logger.info("Running OverviewGeneratorAgent")
        result = agent.process(input_data)

        # Display the result
        if result.success:
            logger.info(f"Success: {result.message}")

            # List generated files
            overview_dir = os.path.join(working_directory, "programs_overview")
            if os.path.exists(overview_dir):
                logger.info(f"Generated overview files in: {overview_dir}")

                # List overview files
                for file in os.listdir(overview_dir):
                    if file == "img":
                        continue  # Skip the img directory

                    file_path = os.path.join(overview_dir, file)
                    if os.path.isfile(file_path):
                        logger.info(f"  - {file} ({os.path.getsize(file_path)} bytes)")

                # Check image directory
                img_dir = os.path.join(overview_dir, "img")
                if os.path.exists(img_dir):
                    logger.info(f"Generated flowchart images in: {img_dir}")
                    for img in os.listdir(img_dir):
                        logger.info(f"  - {img}")

                # Use assertions for pytest compatibility
                assert True, "Overview generation completed successfully"
            else:
                logger.error(f"Overview directory not found: {overview_dir}")
                assert False, f"Overview directory should exist: {overview_dir}"
        else:
            logger.error(f"Error: {result.message}")
            for error in result.errors:
                logger.error(f"  - {error}")
            assert False, f"Overview generation should not fail: {result.message}"

    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=True)
        assert False, f"Test failed with exception: {str(e)}"


def main():
    """Parse command line arguments and run the test."""
    parser = argparse.ArgumentParser(description="Test COBOL Overview Generator Agent")
    parser.add_argument("-p", "--project", help="Project name for working directory")
    args = parser.parse_args()

    try:
        test_overview_generator(args.project)
        return 0
    except Exception:
        return 1


if __name__ == "__main__":
    sys.exit(main())