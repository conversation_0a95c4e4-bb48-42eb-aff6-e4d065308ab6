import tests.helpers.test_paths as paths

neo4j_config = {
    # "enabled": False  # Disable Neo4j for now
    "url": "bolt://localhost:7687",
    "user": "neo4j",
    "password": "12345678",
    "batch_size": 100,
}

temp_cobol_parser_config = {
    "directories": {
        "copybooks": paths.code_samples_path,
        "output": paths.cobol_parser_temp_output_path
    },
    "parser": {
        "preserve_comments": True,
        "max_recursion_depth": 10
    },
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    },
    "neo4j": neo4j_config
}

knowledge_base = {
    "organized_directory": paths.organized_path,
    "out_directory": paths.working_dir_path,
    "file_stats": {
            "total_files": 2,
            "languages": {"cobol": 2},
            "file_types": {"text/x-Algol68": 1, "text/plain": 1},
            "file_sizes": { "medium": 1, "small": 1 },
            "directories": []
    },
    "file_info": [
        {
            "filename": "CBACT01C.cbl",
            "relative_path": "CBACT01C.cbl",
            "language": "cobol",
            "size": 15515,
            "mime_type": "text/x-Algol68",
            "organized_path": "C:\\dev\\CAM\\genai_ram\\tests\\out\\full_conversion\\organized\\cobol\\CBACT01C.cbl",
            "is_copybook": False
        },
        {
            "filename": "CVACT01Y.cpy",
            "relative_path": "CVACT01Y.cpy",
            "language": "cobol",
            "size": 1125,
            "mime_type": "text/plain",
            "organized_path": "C:\\dev\\CAM\\genai_ram\\tests\\out\\full_conversion\\organized\\cobol\\copybooks\\CVACT01Y.cpy",
            "is_copybook": True
        }
    ],
    "package_summary": """
    ### Summary of the Package Content
    - **Total Files**: 2
    - **Languages Detected**: COBOL (2 files)
    - **File Types**: 
      - `text/x-Algol68`: 1 file
      - `text/plain`: 1 file
      
     ### Main Programming Languages
     - The primary programming language for the package appears to be COBOL, as both files are identified as COBOL files. The file types provided (`text/x-Algol68` and `text/plain`) may be indicative of how the files are stored or their content format, but they do not necessarily represent additional programming languages used.
     ### High-Level Approach for Migrating to Java Microservices
     1. **Assessment and Planning**:
        - Conduct a detailed assessment of the COBOL codebase to understand its functionality, dependencies, and data flow.
        - Identify the core business logic that needs to be preserved during migration.
        - Determine the scope and priority of the migration based on business needs and technical feasibility.
     2. **Design**:
        - Define the architecture for the new Java microservices, focusing on modularity, scalability, and maintainability.
        - Break down the COBOL monolith into smaller, cohesive microservices that align with business functionalities.
     3. **Development**:
        - Choose appropriate frameworks and tools for developing Java microservices (e.g., Spring Boot, Docker, Kubernetes).
        - Gradually translate COBOL logic into Java, ensuring that each microservice is independently deployable and testable.
        - Use automated testing to verify the functionality of each microservice.
     4. **Integration and Testing**:
        - Implement integration points between microservices and any existing systems or databases if necessary.
        - Conduct thorough testing, including unit tests, integration tests, and user acceptance tests, to ensure the new system meets requirements.
     5. **Deployment and Monitoring**:
        - Deploy the Java microservices to a cloud environment or on-premises infrastructure.
        - Set up monitoring and logging to track system performance and identify potential issues early.
     6. **Training and Support**:
        - Provide training for development and operations teams on the new system.
        - Offer ongoing support and maintenance to address any post-migration issues.
     ### Potential Challenges in the Migration Process
     - **Complexity of Business Logic**: COBOL applications often contain intricate business logic that can be challenging to translate into modern programming paradigms.
     - **Data Migration**: Ensuring data consistency and integrity while migrating from legacy databases or file systems to modern databases can be complex.
     - **Skill Gap**: The migration requires expertise in both COBOL and Java, which might necessitate additional training or hiring.
     - **Integration with Legacy Systems**: If the COBOL system interfaces with other legacy systems, maintaining compatibility during and after the migration can be difficult.
     - **Performance Optimization**: Ensuring that the new Java microservices perform at least as well as the legacy COBOL system requires careful design and optimization.
     - **Regulatory and Compliance Issues**: Legacy systems often have regulatory requirements that must be preserved during migration, necessitating careful validation and testing. 
     This structured approach aims to ensure a smooth transition from COBOL to a modern Java microservices architecture while mitigating potential risks and challenges.,
      """,
}

knowledge_base_updates_from_code_preprocessor = {
    "preprocessed_directory": "C:\\dev\\CAM\\genai_ram\\tests\\out\\full_conversion\\preprocessed",
    "chunked_directory": "C:\\dev\\CAM\\genai_ram\\tests\\out\\full_conversion\\chunked",
    "processed_files": [
        {
            "original_path": "C:\\dev\\CAM\\genai_ram\\tests\\out\\full_conversion\\organized\\cobol\\CBACT01C.cbl",
            "preprocessed_path": "C:\\dev\\CAM\\genai_ram\\tests\\out\\full_conversion\\preprocessed\\cobol\\CBACT01C.cbl",
            "chunks_dir": "C:\\dev\\CAM\\genai_ram\\tests\\out\\full_conversion\\chunked\\cobol\\CBACT01C",
            "language": "cobol",
            "chunk_count": 14,
            "extracted_data_items": 36,
            "relative_path": "CBACT01C.cbl"
        }
    ],
    "preprocessing_stats": {
        "cobol": 1
    },
    "chunk_stats": {
        "cobol": 14
    },
    "data_extraction_stats": {
        "cobol": 36
    }
}

knowledge_base_updates_from_cobol_overview = {
  "cobol_overviews": {
    "directory": "C:\\dev\\CAM\\genai_ram\\tests\\out\\full_conversion\\programs_overview",
    "processed_files": [
      {
        "program_name": "CBACT01C.cbl",
        "overview_path": "C:\\dev\\CAM\\genai_ram\\tests\\out\\full_conversion\\programs_overview\\CBACT01C.cbl.md",
        "flowchart_path": "C:\\dev\\CAM\\genai_ram\\tests\\out\\full_conversion\\programs_overview\\img\\CBACT01C.cbl.png"
      }
    ]
  }
}

current_state = {
    "status": "idle",
    "current_agent": None,
    "progress": 0,
    "working_directory": None,
    "knowledge_base": knowledge_base,
    "documentation": {},
    "generated_code": {},
    "errors": [],
    "logs": [],
    "start_time": None,
    "agent_progress": {}
}


