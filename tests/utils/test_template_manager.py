"""
Unit tests for TemplateManager.
"""
from pathlib import Path

import pytest
import os
import tempfile
import shutil
from unittest.mock import patch
from src.platform.tools.utils.template_manager import TemplateManager, get_template_manager, render_template


class TestTemplateManager:
    """Test cases for TemplateManager."""

    @pytest.fixture
    def temp_templates_dir(self):
        """Create temporary templates directory."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)

    @pytest.fixture
    def template_manager(self, temp_templates_dir):
        """Create TemplateManager instance with temporary directory."""
        return TemplateManager(Path(temp_templates_dir))

    @pytest.fixture
    def sample_template_content(self):
        """Sample template content for testing."""
        return """Hello {{ name }}!
Your age is {{ age }}.
{% if premium %}
You are a premium user.
{% endif %}"""

    def test_initialization(self, template_manager, temp_templates_dir):
        """Test TemplateManager initialization."""
        assert template_manager.template_dirs[0] == temp_templates_dir
        assert template_manager.env is not None
        assert os.path.exists(temp_templates_dir)

    def test_initialization_without_jinja2(self):
        """Test initialization when Jinja2 is not available."""
        with patch('src.platform.tools.utils.template_manager.JINJA2_AVAILABLE', False):
            with pytest.raises(ImportError, match="Jinja2 is required"):
                TemplateManager()

    def test_create_template_file(self, template_manager, sample_template_content):
        """Test creating a template file."""
        template_name = "test_template.j2"

        result = template_manager.create_template_file(template_name, sample_template_content)

        assert result is True
        assert template_manager.template_exists(template_name)

        # Verify file was created
        template_path = template_manager.get_template_path(template_name)
        assert os.path.exists(template_path)

        with open(template_path, 'r') as f:
            content = f.read()
            assert content == sample_template_content

    def test_render_template(self, template_manager, sample_template_content):
        """Test rendering a template."""
        template_name = "test_template.j2"
        template_manager.create_template_file(template_name, sample_template_content)

        context = {
            "name": "John",
            "age": 30,
            "premium": True
        }

        result = template_manager.render_template(template_name, context)

        expected = """Hello John!
Your age is 30.
You are a premium user."""

        assert result.strip() == expected.strip()

    def test_render_template_not_found(self, template_manager):
        """Test rendering non-existent template."""
        with pytest.raises(FileNotFoundError, match="Template not found"):
            template_manager.render_template("nonexistent.j2")

    def test_render_string_template(self, template_manager):
        """Test rendering template from string."""
        template_string = "Hello {{ name }}! You have {{ count }} messages."
        context = {"name": "Alice", "count": 5}

        result = template_manager.render_string_template(template_string, context)

        assert result == "Hello Alice! You have 5 messages."

    def test_custom_filters(self, template_manager):
        """Test custom Jinja2 filters."""
        # Test camelCase filter
        template_content = "{{ 'CUSTOMER-ID' | camelCase }}"
        result = template_manager.render_string_template(template_content)
        assert result == "customerId"

        # Test pascalCase filter
        template_content = "{{ 'customer-service' | pascalCase }}"
        result = template_manager.render_string_template(template_content)
        assert result == "CustomerService"

        # Test snakeCase filter
        template_content = "{{ 'CustomerID' | snakeCase }}"
        result = template_manager.render_string_template(template_content)
        assert result == "customer_id"

        # Test javaPackage filter
        template_content = "{{ 'My-App' | javaPackage }}"
        result = template_manager.render_string_template(template_content)
        assert result == "myapp"

    def test_template_exists(self, template_manager, sample_template_content):
        """Test checking if template exists."""
        template_name = "test_template.j2"

        # Template doesn't exist initially
        assert template_manager.template_exists(template_name) is False

        # Create template
        template_manager.create_template_file(template_name, sample_template_content)

        # Template exists now
        assert template_manager.template_exists(template_name) is True

    def test_list_templates(self, template_manager, sample_template_content):
        """Test listing templates."""
        # Create multiple templates
        template_manager.create_template_file("template1.j2", sample_template_content)
        template_manager.create_template_file("agents/template2.j2", sample_template_content)
        template_manager.create_template_file("java/template3.j2", sample_template_content)

        # List all templates
        all_templates = template_manager.list_templates()
        assert "template1.j2" in all_templates
        assert "agents/template2.j2" in all_templates
        assert "java/template3.j2" in all_templates

        # List templates with pattern
        agent_templates = template_manager.list_templates("agents/*")
        assert "agents/template2.j2" in agent_templates
        assert "template1.j2" not in agent_templates

    def test_get_template_path(self, template_manager):
        """Test getting template path."""
        template_name = "test/template.j2"
        expected_path = template_manager.template_dirs[0] / "test" / "template.j2"

        result = template_manager.get_template_path(template_name)
        assert result == expected_path

    def test_clear_cache(self, template_manager, sample_template_content):
        """Test clearing template cache."""
        template_name = "test_template.j2"
        template_manager.create_template_file(template_name, sample_template_content)

        # Render template to populate cache
        template_manager.render_template(template_name, {"name": "Test"})
        assert template_name in template_manager._template_cache

        # Clear cache
        template_manager.clear_cache()
        assert len(template_manager._template_cache) == 0

    def test_reload_templates(self, template_manager, sample_template_content):
        """Test reloading templates."""
        template_name = "test_template.j2"
        template_manager.create_template_file(template_name, sample_template_content)

        # Render template to populate cache
        template_manager.render_template(template_name, {"name": "Test"})
        assert len(template_manager._template_cache) > 0

        # Reload templates
        template_manager.reload_templates()
        assert len(template_manager._template_cache) == 0

    def test_render_template_with_nested_directories(self, template_manager):
        """Test rendering templates in nested directories."""
        template_content = "Hello {{ name }}!"
        template_name = "agents/knowledge_miner/test.j2"

        template_manager.create_template_file(template_name, template_content)

        result = template_manager.render_template(template_name, {"name": "World"})
        assert result == "Hello World!"

    def test_error_handling_in_template_creation(self, template_manager):
        """Test error handling when creating template files."""
        # Try to create template with invalid path
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            result = template_manager.create_template_file("test.j2", "content")
            assert result is False

    def test_error_handling_in_template_rendering(self, template_manager):
        """Test error handling when rendering templates."""
        # Create template with syntax error
        invalid_template = "Hello {{ name"  # Missing closing brace
        template_name = "invalid.j2"
        template_manager.create_template_file(template_name, invalid_template)

        with pytest.raises(Exception, match="Error rendering template"):
            template_manager.render_template(template_name, {"name": "Test"})


class TestGlobalTemplateFunctions:
    """Test cases for global template functions."""

    def test_get_template_manager_singleton(self):
        """Test that get_template_manager returns singleton instance."""
        manager1 = get_template_manager()
        manager2 = get_template_manager()

        assert manager1 is manager2

    @patch('src.platform.tools.utils.template_manager._template_manager', None)
    def test_render_template_convenience_function(self):
        """Test the convenience render_template function."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a template file
            template_path = os.path.join(temp_dir, "test.j2")
            with open(template_path, 'w') as f:
                f.write("Hello {{ name }}!")

            # Mock the global template manager
            with patch('src.platform.tools.utils.template_manager.TEMPLATES_DIR', temp_dir):
                result = render_template("test.j2", {"name": "World"})
                assert result == "Hello World!"
