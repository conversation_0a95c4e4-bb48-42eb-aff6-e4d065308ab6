# Legacy Code Modernization Platform
Transform your legacy applications into modern, maintainable systems using a powerful plugin-based architecture.

## Features

### Plugin-Based Architecture
Extensible system supporting multiple source languages (COBOL, RPG, Assembly, PL/1, REXX) and target technologies (Java Spring Boot, .NET, Python) through a modular plugin system.

### Documentation Generation
Generate comprehensive documentation from legacy code including architecture diagrams, code logic, and data models using language-specific plugins.

### Full Code Conversion
Convert legacy code to modern target technologies with automated project structure generation, including documentation and automated tests.

### Data Discovery Project
Analyze and document all data fields, structures, and relationships in legacy applications using intelligent language detection.

### Dependency Analysis
Generate detailed call graphs, data flow diagrams, and dependency maps for legacy applications with plugin-specific analyzers.

### Knowledge Base Builder
Build a queryable knowledge base of your legacy code to understand business rules and logic with centralized data management.

## Setup Instructions

1. Install GraphViz Dot utility:

```bash
aim download entry quad:/3rd/graphviz/2.38/ia32-nt-4.0 --path I:\ds\data\tools\graphviz\2.38 --truncate

cd I:\ds\data\tools\graphviz\2.38
mv ia32-nt-4.0/* .
set PATH=I:\ds\data\tools\graphviz\2.38;%PATH%
```

2. Install dependencies:

```bash
# On macOS:
brew install libmagic
```

```bash
# On Linux (Ubuntu/Debian):
sudo apt-get install libmagic1
```

```bash
# On Windows:
pip uninstall python-magic python-magic-bin # to avoid conflicts
pip install python-magic-bin
```

```bash
# Install Python dependencies
# On windows: first check that it does not include python-magic
pip install -r requirements.txt
```

3. Environment Variables

Set up the `.env` file with the following content:
```python
# Database configuration
DB_HOST=localhost
DB_PORT=...
DB_NAME=...
DB_USER=...
DB_PASSWORD=...

# API keys
AZURE_API_BASE='https://..../'

AZURE_API_VERSION="2024-02-01"

# Application settings
DEBUG=True
PORT=8000

OUT_DIR="./out"
```

4. Create ./cert folder and load certificate there (.pem)

5. Run the application:

```bash
streamlit run app.py
```


## System Architecture

The application follows a plugin-based pipeline architecture where each step is handled by specialized agents using language and target-specific plugins:

### Core Platform
- **Plugin Loader**: Dynamically loads and manages language and target plugins
- **Agent Orchestrator**: Coordinates the processing pipeline
- **Knowledge Database**: Centralized storage for analysis results and mappings

### Language Plugins
- **COBOL Plugin**: Comprehensive COBOL language support with preprocessor, chunker, and analyzer
- **RPG Plugin**: IBM RPG language processing capabilities
- **Assembly Plugin**: Mainframe assembly language support
- **PL/1 Plugin**: PL/1 language analysis and processing
- **REXX Plugin**: REXX scripting language support

### Target Plugins
- **Java Spring Plugin**: Generates modern Java Spring Boot applications
- **Documentation Plugin**: Creates comprehensive documentation in multiple formats

### Processing Pipeline
1. **Package Analyzer**: Analyzes uploaded archives and organizes files by detected language
2. **Language Detection**: Uses plugin-based detection for accurate language identification
3. **Code Preprocessing**: Processes files using language-specific plugin preprocessors
4. **Knowledge Mining**: Extracts variables, procedures, and business logic using plugin analyzers
5. **Code Generation**: Generates target code using target-specific plugin generators
6. **Documentation Generation**: Creates documentation using plugin-specific generators

## File Storage

All files are stored in the output directory with the following structure based on the processing pipeline:

```
/out_example_[date]/
└── [project_name]/
    ├── uploads/                    # Original uploaded files
    ├── organized/                  # Files organized by language
    │   ├── cobol/                 # COBOL programs and copybooks
    │   ├── jcl/                   # JCL job control language files
    │   ├── data/                  # Data files and definitions
    │   ├── config/                # Configuration files
    │   └── other/                 # Other file types
    ├── preprocessed/              # Preprocessed source files
    │   └── cobol/
    │       ├── json/              # JSON analysis results
    │       └── rekt/              # Processed COBOL files
    │           ├── cfg/           # Control flow graphs
    │           ├── data_structures/ # Data structure analysis
    │           ├── flowcharts/    # Generated flowcharts
    │           └── dotfiles/      # Graphviz dot files
    ├── chunked/                   # Code broken into logical chunks
    │   └── cobol/
    │       └── [program_name]/    # Individual program chunks
    ├── documentation/             # Generated documentation
    │   ├── [timestamp]/           # Timestamped documentation
    │   │   ├── overview/          # Program overviews
    │   │   ├── api/               # API documentation
    │   │   ├── architecture/      # Architecture documentation
    │   │   └── data_model/        # Data model documentation
    │   └── [category]/            # Documentation by category
    ├── documentation_for_chunks/  # Chunk-specific documentation
    │   └── [program_name]/        # Documentation for each program
    ├── programs_overview/         # Program overview files
    │   ├── img/                   # Generated diagrams and images
    │   └── [program].md           # Program documentation
    ├── java_project/              # Generated Java Spring Boot project
    │   ├── src/main/java/         # Java source code
    │   │   └── com/generated/cobol/
    │   │       ├── config/        # Configuration classes
    │   │       ├── model/         # Data model classes
    │   │       └── service/       # Service classes
    │   ├── src/main/resources/    # Application resources
    │   ├── src/test/              # Test files
    │   ├── docs/                  # Project documentation
    │   ├── logs/                  # Application logs
    │   └── target/                # Build artifacts
    └── extracted/                 # Extracted archive contents
```

## Environment Variables

Set up the `.env` file with the following content:

```
# Database configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mydb
DB_USER=myuser
DB_PASSWORD=mypassword

# API keys
AZURE_OPENAI_ENDPOINT=your_endpoint
AZURE_OPENAI_API_KEY=your_api_key
OPENAI_API_KEY=your_api_key
OPENAI_API_VERSION="2024-05-01-preview"

# Application settings
DEBUG=True
PORT=8000

OUT_DIR="./out"
```

## Plugin Architecture

The platform uses a modular plugin architecture that allows for easy extension and customization:

### Plugin Types

#### Language Plugins
Language plugins handle source code analysis and processing:
- **Detector**: Identifies language from code content and file extensions
- **Preprocessor**: Handles language-specific preprocessing (copybook expansion, includes, etc.)
- **Chunker**: Breaks code into logical chunks (paragraphs, procedures, functions)
- **Analyzer**: Extracts data structures, variables, and business logic

#### Target Plugins
Target plugins handle code generation for specific technologies:
- **Code Generator**: Generates code in the target language/framework
- **Project Manager**: Creates project structure and configuration files
- **Documentation Generator**: Creates target-specific documentation

### Plugin Registration

Plugins are registered in `config/plugin_registry.py`:

```python
PLUGIN_REGISTRY = {
    "cobol": PluginConfig(
        name="cobol",
        display_name="COBOL Language Plugin",
        plugin_type=PluginType.LANGUAGE,
        module_path="src.plugins.legacy.cobol.plugin",
        class_name="CobolLanguagePlugin",
        # ... configuration
    ),
    "java_spring": PluginConfig(
        name="java_spring",
        display_name="Java Spring Boot Plugin",
        plugin_type=PluginType.TARGET,
        module_path="src.plugins.targets.java_spring.plugin",
        class_name="JavaSpringPlugin",
        # ... configuration
    )
}
```

### Adding New Plugins

To add a new language plugin:
1. Create plugin directory: `src/plugins/legacy/[language]/`
2. Implement plugin class extending `LanguagePlugin`
3. Implement required components (detector, preprocessor, chunker, analyzer)
4. Register plugin in `config/plugin_registry.py`

To add a new target plugin:
1. Create plugin directory: `src/plugins/targets/[target]/`
2. Implement plugin class extending `TargetPlugin`
3. Implement required components (code generator, project manager, documentation generator)
4. Register plugin in `config/plugin_registry.py`