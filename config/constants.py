"""
Constants and configuration values for the RAM2 application.
Centralized location for all hardcoded values, file paths, and configuration settings.
"""
import os
from pathlib import Path
from dotenv import load_dotenv


def find_project_root(requirements_file='requirements.txt') -> Path:
    current_dir = Path(__file__).parent
    for parent in [current_dir, *current_dir.parents]:
        if (parent / requirements_file).exists():
            return parent

    raise FileNotFoundError(
        f"Project root not found. Project '{requirements_file}' file not found in or above '{current_dir}'.")


# =============================================================================
# DIRECTORY AND FILE PATHS
# =============================================================================

# Base directories

load_dotenv()
OUT_DIR = Path(os.environ.get('OUT_DIR', 'out'))

PROJECT_ROOT = find_project_root()
DEFAULT_OUT_DIR = PROJECT_ROOT / OUT_DIR
TEMPLATE_DIRS = {
    'platform_templates': PROJECT_ROOT / 'src/platform/templates',
    'cobol_plugin_templates': PROJECT_ROOT / 'src/plugins/legacy/cobol/templates',
    'java_spring_templates': PROJECT_ROOT / 'src/plugins/targets/java_spring/templates'
}

# =============================================================================
# JAVA PROJECT DEFAULTS
# =============================================================================

DEFAULT_JAVA_PACKAGE = "com.generated.cobol"
DEFAULT_GROUP_ID = "com.generated"
DEFAULT_ARTIFACT_ID = "cobol-conversion"
DEFAULT_VERSION = "1.0.0"
DEFAULT_JAVA_VERSION = "11"

# Maven configuration
MAVEN_COMPILER_SOURCE = "11"
MAVEN_COMPILER_TARGET = "11"
SPRING_BOOT_VERSION = "3.1.0"

# =============================================================================
# LLM AND AI SETTINGS
# =============================================================================

MAX_CONTEXT_TOKENS = 100000
CONTEXT_THRESHOLD = 0.8
MAX_ITERATIONS = 50

# Token estimation (rough approximation: 1 token ≈ 4 characters)
CHARS_PER_TOKEN = 4

# =============================================================================
# FILE EXTENSIONS
# =============================================================================

COBOL_EXTENSIONS = ['.cbl', '.cobol', '.COBOL', '.cob', '.CBL', '.COB']
COPYBOOK_EXTENSIONS = ['.cpy', '.copy', '.CPY', '.COPY']
RPG_EXTENSIONS = ['.rpg', '.RPG', '.rpgle', '.RPGLE']
JCL_EXTENSIONS = ['.jcl', '.JCL']
ASSEMBLY_EXTENSIONS = ['.asm', '.ASM', '.s', '.S']
PL1_EXTENSIONS = ['.pl1', '.PL1', '.pli', '.PLI']
REXX_EXTENSIONS = ['.rexx', '.REXX', '.rex', '.REX']
JAVA_EXTENSIONS = ['.java', '.JAVA']

CONFIG_EXTENSIONS = ['.xml', '.json', '.properties', '.yml', '.yaml', '.ini', '.cfg', '.conf']
DATA_EXTENSIONS = ['.txt', '.md', '.csv', '.dat', '.log']

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

# SQLite database names
KNOWLEDGE_DB_NAME = "knowledge_base.sqlite3"
STATE_DB_NAME = "orchestrator_state.sqlite3"

# Neo4j default settings
DEFAULT_NEO4J_URL = "bolt://localhost:7687"
DEFAULT_NEO4J_USER = "neo4j"
DEFAULT_NEO4J_PASSWORD = "password"
DEFAULT_NEO4J_BATCH_SIZE = 100

# =============================================================================
# FILE SIZE CATEGORIES
# =============================================================================

FILE_SIZE_SMALL = 1024  # 1KB
FILE_SIZE_MEDIUM = 10240  # 10KB
FILE_SIZE_LARGE = 102400  # 100KB
FILE_SIZE_VERY_LARGE = 1048576  # 1MB

# =============================================================================
# AGENT WORKFLOW
# =============================================================================

DEFAULT_WORKFLOW_STEPS = [
    "package_analyzer",
    "code_preprocessor",
    "overview_generator",
    "knowledge_miner",
    "documentation_generator",
    "documentation_critic",
    "code_generator",
    "code_reviewer"
]

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)'
LOG_LEVEL = 'INFO'
LOG_FILE_NAME = 'ram20.log'

# =============================================================================
# SYSTEM MESSAGES FOR LLM
# =============================================================================

# System messages moved to template files - see src/platform/templates/system_messages/
# These constants are kept for backward compatibility during transition
COBOL_ANALYST_SYSTEM_MSG = "You are an expert COBOL code analyst specializing in extracting business meaning from legacy code."
JAVA_GENERATOR_SYSTEM_MSG = "You are an expert Java Spring Boot developer converting COBOL business logic to modern microservices."
DOCUMENTATION_SYSTEM_MSG = "You are an expert technical writer creating clear, comprehensive documentation for legacy code modernization."
CODE_REVIEWER_SYSTEM_MSG = "You are an expert code reviewer ensuring quality, best practices, and maintainability in generated Java code."

# =============================================================================
# BUSINESS LOGIC EXTRACTION
# =============================================================================

# Parameter types for business logic analysis
PARAMETER_TYPES = {
    'VARIABLE': 'variable',
    'FLAT_FILE': 'flat_file',
    'VSAM': 'VSAM',
    'DB2_DATABASE': 'IBM DB2 database',
    'IMS_DATABASE': 'IMS DB',
    'MESSAGE_QUEUE': 'queue',
    'API': 'api',
    'ENVIRONMENT': 'environment'
}

# Business name examples for guidance
BUSINESS_NAME_EXAMPLES = [
    "Customer Account Validation",
    "Invoice Total Calculation",
    "Payment Status Update",
    "Order Fulfillment Check"
]

# =============================================================================
# IMS SEGMENT MAPPINGS
# =============================================================================

# COBOL IMS Database Segment to Business Name Mapping
# Used for enhancing Java code generation with meaningful business context
IMS_SEGMENT_BUSINESS_MAPPINGS = {
    'AMSAM00': 'Account Base Information',
    'AMSAM0E': 'Customer Information',
    'AMSAM01': 'Account to Customer Information',
    'AMSAM31': 'Small Business Plus Information',
    'AMSAM0J': 'June Release Part 1',
    'AMSAM1J': 'June Release Part 2',
    'AMSAM2J': 'June Release Part 3',
    'AMSAM3J': 'June Release Part 4',
    'AMSAM4J': 'June Release Part 5',
    'AMSAM02': 'Active Account Information',
    'AMSAM32': 'Account Dispute Information',
    'AMSAM33': 'Account Balance Information',
    'AMSAM35': 'Transaction Level Processing Information',
    'AMSAM0K': 'January 2010 Case Action',
    'AMSAM1K': 'January 2010 Case Action Sequence',
    'AMSAM2K': 'January 2010 Case Action Sequence Variable',
    'AMSAMRK': 'October 2010 Six-Month Review',
    'AMSAM04': 'Population Information',
    'AMSAM0A': 'Card Number Information',
    'AMSAM3A': 'Warning Bulletin',
    'AMSAM3B': 'Security Fraud',
    'AMSAM08': 'Charge Information',
    'AMSAM38': 'Transaction Balance Information',
    'AMSAM09': 'Security Base Information',
    'AMSAM0B': 'Account Miscellaneous Data',
    'AMSAM0C': 'Account History Data',
    'AMSAM0D': 'Account Processing Information',
    'AMSAM0F': 'Account Base Information Continuation',
    'AMSAM0G': 'Extra Information #1',
    'AMSAM0H': 'Extra Information #2',
    'ELSEL00': 'Event Logging Database',
    'ELSEL01': 'Event Logging Database',
    'ELSEL02': 'Event Logging Database',
    'CCSCC00': 'Account Base Information',
    'CCSCC10': 'Customer Information and Static Data',
    'CCSCC20': 'Chip Card Application Data',
    'RWSRW00': 'Rewards Information',
    'RWSRWS3': 'Rewards SDEP History',
    'RWSRW01': 'Earning Method',
    'RWSRW21': 'Calculation Option',
    'RWSRW51': 'Rewards Category',
    'RWSRW02': 'Fulfillment Option',
    'RWSRW03': 'Earned, Adjusted, Transferred History',
    'RWSRW04': 'Track Transfer History of Rewards Product Codes',
    'CUSCM00': 'Customer Base Information',
    'CUSCM09': 'Customer Code Update Needed (SDEP)',
    'CUSCM01': 'Customer Address Information',
    'CUSCM02': 'Account Relationship Information',
    'CUSCM03': 'Customer Relationship Information',
    'CUSCM04': 'Customer Notes Information',
    'CUSCM34': 'Notes Text',
    'CUSCM05': 'Personal Information',
    'CUSCM06': 'Employment Information',
    'CUSCM07': 'Custom Data Information',
    'CUSCM08': 'Customer Skip Trace',
    'XASXA00': 'Extension Account Master Root',
    'XASXA01': 'Extension Product Change Information',
    'XASXA02': 'Extension Warning Bulletin Information',
    'XASXA03': 'Incentive Program History',
    'XASXA04': 'Miscellaneous Data',
    'XASXA05': 'Security Fraud Information',
    'XASXA06': 'Future Use',
    'XASXA07': 'Minimum Payment Information',
    'XASXA0K': 'Extension APTS Case Level Information',
    'XASXA1K': 'Extension APTS Action Variable Parent',
    'XASXA2K': 'Extension APTS Action Variable',
    'XASXA08': 'Card Reissue',
    'XASXA09': 'Rewards History',
    'XASXA29': 'Rewards History',
    'CCSH00': 'Account Base Information',
    'CCSH10': 'Sequence Number and Script Function Data',
    'CCSH20': 'Tandem Data'
}

# =============================================================================
# CODE GENERATION SETTINGS
# =============================================================================

# Java naming conventions
JAVA_PACKAGE_SEPARATOR = "."
JAVA_CLASS_SUFFIX = "Service"
JAVA_CONTROLLER_SUFFIX = "Controller"
JAVA_MODEL_SUFFIX = ""

# Spring Boot annotations
SPRING_ANNOTATIONS = {
    'SERVICE': '@Service',
    'CONTROLLER': '@RestController',
    'COMPONENT': '@Component',
    'REPOSITORY': '@Repository',
    'AUTOWIRED': '@Autowired',
    'REQUEST_MAPPING': '@RequestMapping',
    'GET_MAPPING': '@GetMapping',
    'POST_MAPPING': '@PostMapping'
}

# =============================================================================
# ERROR MESSAGES
# =============================================================================

ERROR_MESSAGES = {
    'NO_DOCUMENTATION_FOUND': 'No documentation found in working directory',
    'NO_CHUNKS_DIRECTORY': 'No chunks directory found',
    'INVALID_PROJECT_CONFIG': 'Invalid project configuration provided',
    'DATABASE_CONNECTION_FAILED': 'Failed to connect to database',
    'FILE_NOT_FOUND': 'Required file not found',
    'PERMISSION_DENIED': 'Permission denied accessing file or directory',
    'INVALID_JSON': 'Invalid JSON format in response',
    'LLM_API_ERROR': 'Error communicating with LLM API',
    'TEMPLATE_NOT_FOUND': 'Template file not found',
    'GENERATION_INCOMPLETE': 'Code generation incomplete after maximum iterations'
}

# =============================================================================
# SUCCESS MESSAGES
# =============================================================================

SUCCESS_MESSAGES = {
    'AGENT_COMPLETED': 'Agent completed successfully',
    'FILE_PROCESSED': 'File processed successfully',
    'DATABASE_UPDATED': 'Database updated successfully',
    'TEMPLATE_RENDERED': 'Template rendered successfully',
    'PROJECT_CREATED': 'Java project created successfully',
    'CODE_GENERATED': 'Java code generated successfully'
}

# =============================================================================
# VALIDATION PATTERNS
# =============================================================================

# Regex patterns for validation
JAVA_PACKAGE_PATTERN = r'^[a-z][a-z0-9_]*(\.[a-z][a-z0-9_]*)*$'
JAVA_CLASS_NAME_PATTERN = r'^[A-Z][a-zA-Z0-9_]*$'
COBOL_PROGRAM_ID_PATTERN = r'^[A-Z0-9\-]{1,8}$'

# =============================================================================
# ENVIRONMENT VARIABLES
# =============================================================================

ENV_VARS = {
    'OUT_DIR': 'OUT_DIR',
    'OPENAI_API_KEY': 'OPENAI_API_KEY',
    'NEO4J_URL': 'NEO4J_URL',
    'NEO4J_USER': 'NEO4J_USER',
    'NEO4J_PASSWORD': 'NEO4J_PASSWORD',
    'LOG_LEVEL': 'LOG_LEVEL'
}


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def get_out_dir() -> str:
    """Get the output directory from environment or use default."""
    return os.environ.get(ENV_VARS['OUT_DIR'], DEFAULT_OUT_DIR)


def get_log_level() -> str:
    """Get the log level from environment or use default."""
    return os.environ.get(ENV_VARS['LOG_LEVEL'], LOG_LEVEL)


def get_templates_dir(templates_set_name: str = None) -> str | None:
    """Get a templates directory path."""
    if not templates_set_name:
        return None
    return str(TEMPLATE_DIRS.get(templates_set_name))


def get_java_project_defaults() -> dict:
    """Get default Java project configuration."""
    return {
        'package': DEFAULT_JAVA_PACKAGE,
        'group_id': DEFAULT_GROUP_ID,
        'artifact_id': DEFAULT_ARTIFACT_ID,
        'version': DEFAULT_VERSION,
        'java_version': DEFAULT_JAVA_VERSION
    }
