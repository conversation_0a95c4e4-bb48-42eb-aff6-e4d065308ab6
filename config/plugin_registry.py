"""
Plugin registry for managing language and target technology plugins.
"""
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Any


class PluginType(Enum):
    """Types of plugins supported by the system."""
    LANGUAGE = "language"
    TARGET = "target"


@dataclass
class PluginConfig:
    """Configuration for a plugin."""
    name: str
    display_name: str
    description: str
    plugin_type: PluginType
    module_path: str
    class_name: str
    version: str
    author: str
    dependencies: List[str]
    supported_versions: List[str]
    enabled: bool = True
    config: Dict[str, Any] = None


# Registry of all available plugins
PLUGIN_REGISTRY: Dict[str, PluginConfig] = {

    # Language Plugins
    "cobol": PluginConfig(
        name="cobol",
        display_name="COBOL Language Plugin",
        description="Comprehensive COBOL language support with preprocessor, chunker, and analyzer",
        plugin_type=PluginType.LANGUAGE,
        module_path="src.plugins.legacy.cobol.plugin",
        class_name="CobolLanguagePlugin",
        version="1.0.0",
        author="RAM2 Team",
        dependencies=[],
        supported_versions=["COBOL-85", "IBM Enterprise COBOL"],
        config={
            "copybook_extensions": [".cpy", ".copy", ".inc"],
            "source_extensions": [".cob", ".cbl", ".cobol"],
            "dialect": "ibm"
        }
    ),

    "rpg": PluginConfig(
        name="rpg",
        display_name="RPG Language Plugin",
        description="RPG language support for both fixed and free format",
        plugin_type=PluginType.LANGUAGE,
        module_path="src.plugins.legacy.rpg.plugin",
        class_name="RpgLanguagePlugin",
        version="1.0.0",
        author="RAM2 Team",
        dependencies=[],
        supported_versions=["RPG III", "RPG IV", "ILE RPG"],
        enabled=False,  # Disabled until plugin is implemented
        config={
            "source_extensions": [".rpg", ".rpgle", ".sqlrpgle"],
            "copy_extensions": [".rpginc", ".inc"]
        }
    ),

    "assembly": PluginConfig(
        name="assembly",
        display_name="Assembly Language Plugin",
        description="Assembly language support for mainframe systems",
        plugin_type=PluginType.LANGUAGE,
        module_path="src.plugins.legacy.assembly.plugin",
        class_name="AssemblyLanguagePlugin",
        version="1.0.0",
        author="RAM2 Team",
        dependencies=[],
        supported_versions=["IBM System/370", "IBM System/390", "z/Architecture"],
        enabled=False,  # Disabled until plugin is implemented
        config={
            "source_extensions": [".asm", ".s", ".hlasm"],
            "macro_extensions": [".mac", ".mcr"]
        }
    ),

    "jcl": PluginConfig(
        name="jcl",
        display_name="JCL Language Plugin",
        description="Job Control Language support for mainframe batch processing",
        plugin_type=PluginType.LANGUAGE,
        module_path="src.plugins.legacy.jcl.plugin",
        class_name="JclLanguagePlugin",
        version="1.0.0",
        author="RAM2 Team",
        dependencies=[],
        supported_versions=["MVS JCL", "z/OS JCL"],
        enabled=False,  # Disabled until plugin is implemented
        config={
            "source_extensions": [".jcl", ".job", ".proc"],
            "proc_extensions": [".proc", ".prc"]
        }
    ),

    # Target Technology Plugins
    "java_spring": PluginConfig(
        name="java_spring",
        display_name="Java Spring Boot Plugin",
        description="Java Spring Boot code generation and project management",
        plugin_type=PluginType.TARGET,
        module_path="src.plugins.targets.java_spring.plugin",
        class_name="JavaSpringPlugin",
        version="1.0.0",
        author="RAM2 Team",
        dependencies=[],
        supported_versions=["Spring Boot 2.x", "Spring Boot 3.x"],
        config={
            "java_version": "17",
            "spring_boot_version": "3.2.0",
            "build_tool": "maven",
            "package_structure": "com.generated.cobol"
        }
    ),

    "python_django": PluginConfig(
        name="python_django",
        display_name="Python Django Plugin",
        description="Python Django web application generation",
        plugin_type=PluginType.TARGET,
        module_path="src.plugins.targets.python_django.plugin",
        class_name="PythonDjangoPlugin",
        version="1.0.0",
        author="RAM2 Team",
        dependencies=[],
        supported_versions=["Django 4.x", "Django 5.x"],
        enabled=False,  # Disabled until plugin is implemented
        config={
            "python_version": "3.11",
            "django_version": "4.2",
            "database": "postgresql"
        }
    ),

    "nodejs_express": PluginConfig(
        name="nodejs_express",
        display_name="Node.js Express Plugin",
        description="Node.js Express API generation",
        plugin_type=PluginType.TARGET,
        module_path="src.plugins.targets.nodejs_express.plugin",
        class_name="NodejsExpressPlugin",
        version="1.0.0",
        author="RAM2 Team",
        dependencies=[],
        supported_versions=["Node.js 18.x", "Node.js 20.x"],
        enabled=False,  # Disabled until plugin is implemented
        config={
            "node_version": "20",
            "express_version": "4.18",
            "typescript": True
        }
    ),

    "documentation": PluginConfig(
        name="documentation",
        display_name="Documentation Plugin",
        description="Multi-format documentation generation",
        plugin_type=PluginType.TARGET,
        module_path="src.plugins.targets.documentation.plugin",
        class_name="DocumentationPlugin",
        version="1.0.0",
        author="RAM2 Team",
        dependencies=[],
        supported_versions=["Markdown", "HTML", "PDF"],
        enabled=False,  # Disabled until plugin is implemented
        config={
            "default_format": "markdown",
            "include_diagrams": True,
            "template_engine": "jinja2"
        }
    )
}


def get_plugin_config(plugin_name: str) -> Optional[PluginConfig]:
    """
    Get plugin configuration by name.

    Args:
        plugin_name: Name of the plugin

    Returns:
        Optional[PluginConfig]: Plugin configuration or None if not found
    """
    return PLUGIN_REGISTRY.get(plugin_name)


def get_language_plugins() -> Dict[str, PluginConfig]:
    """
    Get all language plugins.

    Returns:
        Dict[str, PluginConfig]: Dictionary of language plugins
    """
    return {name: config for name, config in PLUGIN_REGISTRY.items()
            if config.plugin_type == PluginType.LANGUAGE}


def get_target_plugins() -> Dict[str, PluginConfig]:
    """
    Get all target plugins.

    Returns:
        Dict[str, PluginConfig]: Dictionary of target plugins
    """
    return {name: config for name, config in PLUGIN_REGISTRY.items()
            if config.plugin_type == PluginType.TARGET}


def get_enabled_plugins() -> Dict[str, PluginConfig]:
    """
    Get all enabled plugins.

    Returns:
        Dict[str, PluginConfig]: Dictionary of enabled plugins
    """
    return {name: config for name, config in PLUGIN_REGISTRY.items()
            if config.enabled}


def register_plugin(plugin_config: PluginConfig) -> None:
    """
    Register a new plugin.

    Args:
        plugin_config: Plugin configuration to register
    """
    PLUGIN_REGISTRY[plugin_config.name] = plugin_config


def unregister_plugin(plugin_name: str) -> bool:
    """
    Unregister a plugin.

    Args:
        plugin_name: Name of plugin to unregister

    Returns:
        bool: True if plugin was unregistered, False if not found
    """
    if plugin_name in PLUGIN_REGISTRY:
        del PLUGIN_REGISTRY[plugin_name]
        return True
    return False


def enable_plugin(plugin_name: str) -> bool:
    """
    Enable a plugin.

    Args:
        plugin_name: Name of plugin to enable

    Returns:
        bool: True if plugin was enabled, False if not found
    """
    if plugin_name in PLUGIN_REGISTRY:
        PLUGIN_REGISTRY[plugin_name].enabled = True
        return True
    return False


def disable_plugin(plugin_name: str) -> bool:
    """
    Disable a plugin.

    Args:
        plugin_name: Name of plugin to disable

    Returns:
        bool: True if plugin was disabled, False if not found
    """
    if plugin_name in PLUGIN_REGISTRY:
        PLUGIN_REGISTRY[plugin_name].enabled = False
        return True
    return False
