# Centralized Configuration System

This directory contains the centralized configuration system for managing project types, agents, and workflows. This system provides a single place to add new functionality and configure the entire application.

## 📁 Files Overview

### `project_types.py`
**Single place to add new project types**
- Defines all supported project types (Code Conversion, Knowledge Builder, etc.)
- Contains complete configuration for each project type
- Specifies required inputs, outputs, and UI renderers

### `agent_registry.py`
**Single place to register new agents**
- Central registry of all available agents
- Contains agent metadata, dependencies, and capabilities
- Handles dynamic agent instantiation

### `workflow_assembly.py`
**Single place to define and assemble workflows**
- Defines workflows for each project type
- Specifies the sequence of agents to execute
- Configures parallel execution and dependencies

## 🚀 How to Add New Functionality

### Adding a New Project Type

1. **Edit `project_types.py`:**
   ```python
   # Add to ProjectType enum
   MODERNIZATION = "modernization"
   
   # Add to PROJECT_TYPE_REGISTRY
   ProjectType.MODERNIZATION: ProjectTypeConfig(
       name="modernization",
       display_name="Legacy Modernization",
       description="Modernize legacy applications",
       icon="⚡",
       workflow_name="modernization_workflow",
       required_inputs=["source_files", "target_architecture"],
       optional_inputs=["modernization_strategy"],
       output_artifacts=["modernized_code", "architecture_docs"],
       ui_renderer="modernization_project_renderer"
   )
   ```

2. **The new project type is automatically available everywhere!**

### Adding a New Agent

1. **Create the agent class** (e.g., `agents/test_generator.py`):
   ```python
   from .base_agent import BaseAgent, AgentInput, AgentOutput
   
   class TestGeneratorAgent(BaseAgent):
       def __init__(self):
           super().__init__("test_generator")
       
       def process(self, agent_input: AgentInput) -> AgentOutput:
           # Implementation here
           pass
   ```

2. **Edit `agent_registry.py`:**
   ```python
   # Add to AgentType enum
   TEST_GENERATOR = "test_generator"
   
   # Add to AGENT_REGISTRY
   AgentType.TEST_GENERATOR: AgentConfig(
       name="test_generator",
       display_name="Test Generator",
       description="Generates comprehensive test suites",
       module_path="agents.test_generator",
       class_name="TestGeneratorAgent",
       category="generation",
       dependencies=["knowledge_miner"],
       outputs=["unit_tests", "integration_tests"],
       parallel_capable=True,
       estimated_duration="5-10 minutes",
       icon="🧪"
   )
   ```

3. **The new agent is automatically available in the orchestrator!**

### Adding a New Workflow

1. **Edit `workflow_assembly.py`:**
   ```python
   "modernization_workflow": WorkflowDefinition(
       name="modernization_workflow",
       display_name="Legacy Modernization Workflow",
       description="Complete modernization process",
       project_type=ProjectType.MODERNIZATION,
       estimated_duration="60-120 minutes",
       steps=[
           WorkflowStep(
               agent_type=AgentType.PACKAGE_ANALYZER,
               name="analyze_legacy_structure",
               description="Analyze legacy application"
           ),
           WorkflowStep(
               agent_type=AgentType.TEST_GENERATOR,
               name="generate_tests",
               description="Generate test suites"
           ),
           # ... more steps
       ]
   )
   ```

2. **The workflow is automatically available for the project type!**

## 🔧 Usage in Code

### Using the Orchestrator

```python
from agents.orchestrator import OrchestratorAgent

# Create orchestrator
orchestrator = OrchestratorAgent()

# Get available project types
project_types = orchestrator.get_available_project_types()

# Set project type (automatically configures workflow)
orchestrator.set_project_type("code_conversion")

# Get available agents
agents = orchestrator.get_available_agents()

# Customize workflow if needed
custom_workflow = ["package_analyzer", "knowledge_miner", "code_generator"]
orchestrator.set_workflow(custom_workflow)
```

### Direct Registry Access

```python
from config.project_types import get_all_project_types, get_project_type_by_name
from config.agent_registry import create_agent_instance, AgentType
from config.workflow_assembly import get_workflow_for_project_type

# Get project type info
project_config = get_project_type_by_name("code_conversion")

# Create agent instance
agent = create_agent_instance(AgentType.KNOWLEDGE_MINER)

# Get workflow for project type
workflow = get_workflow_for_project_type(ProjectType.CODE_CONVERSION)
```

## 🎯 Benefits

1. **Single Source of Truth**: All configuration in one place
2. **Automatic Integration**: New additions are immediately available
3. **Type Safety**: Enums prevent typos and invalid references
4. **Validation**: Built-in validation for configurations
5. **Extensibility**: Easy to add new functionality
6. **Maintainability**: Clear separation of concerns

## 📋 Configuration Validation

The system includes built-in validation:

```python
from config.workflow_assembly import validate_workflow

# Validate a workflow
errors = validate_workflow(workflow_definition)
if errors:
    print(f"Validation errors: {errors}")
```

## 🔍 Examples

See `examples/centralized_system_usage.py` for complete examples of:
- Adding new project types
- Registering new agents
- Creating custom workflows
- Using the orchestrator

## 🚨 Important Notes

1. **Agent Dependencies**: Ensure agent dependencies are correctly specified
2. **Module Paths**: Agent module paths must be valid Python import paths
3. **Workflow Validation**: Workflows are validated for cycles and missing agents
4. **Backward Compatibility**: Existing code continues to work unchanged

This centralized system makes it easy to extend the application while maintaining clean, organized code!
