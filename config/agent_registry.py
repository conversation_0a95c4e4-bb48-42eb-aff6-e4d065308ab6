"""
Centralized agent registration system.
This is the single place to register all agents and their configurations.
"""
from enum import Enum
from typing import Dict, List, Any, Type, Optional
from dataclasses import dataclass


class AgentType(Enum):
    """
    Enumeration of all available agent types.
    Add new agent types here.
    """
    PACKAGE_ANALYZER = "package_analyzer"
    CODE_PREPROCESSOR = "code_preprocessor"
    OVERVIEW_GENERATOR = "overview_generator"
    KNOWLEDGE_MINER = "knowledge_miner"
    DOCUMENTATION_GENERATOR = "documentation_generator"
    DOCUMENTATION_CRITIC = "documentation_critic"
    CODE_GENERATOR = "code_generator"
    CODE_REVIEWER = "code_reviewer"



    # Add new agent types here:
    # DATA_ANALYZER = "data_analyzer"
    # PERFORMANCE_OPTIMIZER = "performance_optimizer"
    # SECURITY_SCANNER = "security_scanner"
    # TEST_GENERATOR = "test_generator"


@dataclass
class AgentConfig:
    """Configuration for an agent."""
    name: str
    display_name: str
    description: str
    module_path: str
    class_name: str
    category: str
    dependencies: List[str]
    outputs: List[str]
    parallel_capable: bool
    estimated_duration: str
    icon: str


# Central registry of all agent configurations
# Add new agents here with their complete configuration
AGENT_REGISTRY: Dict[AgentType, AgentConfig] = {

    AgentType.PACKAGE_ANALYZER: AgentConfig(
        name="package_analyzer",
        display_name="Package Analyzer",
        description="Analyzes project structure and identifies source files using language plugins",
        module_path="src.platform.agents.package_analyzer",
        class_name="PackageAnalyzerAgent",
        category="analysis",
        dependencies=[],
        outputs=["program_inventory", "file_structure", "dependencies"],
        parallel_capable=True,
        estimated_duration="1-2 minutes",
        icon="📦"
    ),

    AgentType.CODE_PREPROCESSOR: AgentConfig(
        name="code_preprocessor",
        display_name="Code Preprocessor",
        description="Preprocesses COBOL code by expanding copybooks and normalizing syntax",
        module_path="src.platform.agents.code_preprocessor",
        class_name="CodePreprocessorAgent",
        category="preprocessing",
        dependencies=["package_analyzer"],
        outputs=["preprocessed_code", "copybook_expansions", "syntax_tree"],
        parallel_capable=True,
        estimated_duration="2-5 minutes",
        icon="⚙️"
    ),

    AgentType.OVERVIEW_GENERATOR: AgentConfig(
        name="overview_generator",
        display_name="Overview Generator",
        description="Generates high-level overview using language plugins",
        module_path="src.platform.agents.overview_generator",
        class_name="OverviewGeneratorAgent",
        category="analysis",
        dependencies=["code_preprocessor"],
        outputs=["program_overview", "code_chunks", "structure_analysis"],
        parallel_capable=True,
        estimated_duration="3-7 minutes",
        icon="🔍"
    ),

    AgentType.KNOWLEDGE_MINER: AgentConfig(
        name="knowledge_miner",
        display_name="Knowledge Miner",
        description="Extracts business logic and functional specifications from code chunks",
        module_path="src.platform.agents.knowledge_miner.core",
        class_name="KnowledgeMinerAgent",
        category="extraction",
        dependencies=["overview_generator"],
        outputs=["business_logic", "functional_specs", "data_flows"],
        parallel_capable=True,
        estimated_duration="5-15 minutes",
        icon="🧠"
    ),

    AgentType.DOCUMENTATION_GENERATOR: AgentConfig(
        name="documentation_generator",
        display_name="Documentation Generator",
        description="Generates comprehensive technical and business documentation",
        module_path="src.platform.agents.documentation_gen",
        class_name="DocumentationGeneratorAgent",
        category="documentation",
        dependencies=["knowledge_miner"],
        outputs=["technical_docs", "business_docs", "api_specs"],
        parallel_capable=True,
        estimated_duration="3-8 minutes",
        icon="📚"
    ),

    AgentType.DOCUMENTATION_CRITIC: AgentConfig(
        name="documentation_critic",
        display_name="Documentation Critic",
        description="Reviews and improves generated documentation for quality and completeness",
        module_path="src.platform.agents.documentation_critic",
        class_name="DocumentationCriticAgent",
        category="quality_assurance",
        dependencies=["documentation_generator"],
        outputs=["reviewed_docs", "quality_metrics", "improvement_suggestions"],
        parallel_capable=False,
        estimated_duration="2-5 minutes",
        icon="🔍"
    ),

    AgentType.CODE_GENERATOR: AgentConfig(
        name="code_generator",
        display_name="Code Generator",
        description="Generates target technology code using target plugins",
        module_path="src.platform.agents.code_generator",
        class_name="CodeGeneratorAgent",
        category="generation",
        dependencies=["documentation_critic"],
        outputs=["generated_code", "target_config", "data_models"],
        parallel_capable=True,
        estimated_duration="10-20 minutes",
        icon="⚡"
    ),

    AgentType.CODE_REVIEWER: AgentConfig(
        name="code_reviewer",
        display_name="Code Reviewer",
        description="Reviews generated code for quality, best practices, and correctness using target plugins",
        module_path="src.platform.agents.code_reviewer",
        class_name="CodeReviewerAgent",
        category="quality_assurance",
        dependencies=["code_generator"],
        outputs=["code_review", "quality_metrics", "refactoring_suggestions"],
        parallel_capable=False,
        estimated_duration="5-10 minutes",
        icon="👁️"
    ),



    # Add new agents here following the same pattern:
    # AgentType.DATA_ANALYZER: AgentConfig(
    #     name="data_analyzer",
    #     display_name="Data Structure Analyzer",
    #     description="Analyzes data structures and relationships in COBOL programs",
    #     module_path="agents.data_analyzer",
    #     class_name="DataAnalyzer",
    #     category="analysis",
    #     dependencies=["code_preprocessor"],
    #     outputs=["data_models", "relationships", "data_lineage"],
    #     parallel_capable=True,
    #     estimated_duration="3-6 minutes",
    #     icon="🗃️"
    # ),
}


def get_agent_config(agent_type: AgentType) -> AgentConfig:
    """
    Get configuration for an agent type.

    Args:
        agent_type: The agent type enum

    Returns:
        AgentConfig: Configuration for the agent type

    Raises:
        KeyError: If agent type is not registered
    """
    if agent_type not in AGENT_REGISTRY:
        raise KeyError(f"Agent type {agent_type} is not registered")

    return AGENT_REGISTRY[agent_type]


def get_all_agent_configs() -> List[AgentConfig]:
    """
    Get all registered agent configurations.

    Returns:
        List[AgentConfig]: List of all agent configurations
    """
    return list(AGENT_REGISTRY.values())


def get_agent_config_by_name(name: str) -> AgentConfig:
    """
    Get agent configuration by name.

    Args:
        name: The agent name

    Returns:
        AgentConfig: Configuration for the agent

    Raises:
        ValueError: If agent name is not found
    """
    for agent_type, config in AGENT_REGISTRY.items():
        if config.name == name:
            return config

    raise ValueError(f"Agent with name '{name}' not found")


def get_agents_by_category(category: str) -> List[AgentConfig]:
    """
    Get all agents in a specific category.

    Args:
        category: The category name

    Returns:
        List[AgentConfig]: List of agent configurations in the category
    """
    return [config for config in AGENT_REGISTRY.values() if config.category == category]


def get_agent_categories() -> List[str]:
    """
    Get all unique agent categories.

    Returns:
        List[str]: List of all agent categories
    """
    return list(set(config.category for config in AGENT_REGISTRY.values()))


def is_valid_agent_name(name: str) -> bool:
    """
    Check if an agent name is valid.

    Args:
        name: The agent name to check

    Returns:
        bool: True if valid, False otherwise
    """
    try:
        get_agent_config_by_name(name)
        return True
    except ValueError:
        return False


def get_agent_names() -> List[str]:
    """
    Get all agent names.

    Returns:
        List[str]: List of all agent names
    """
    return [config.name for config in AGENT_REGISTRY.values()]


def create_agent_instance(agent_type: AgentType):
    """
    Create an instance of an agent.

    Args:
        agent_type: The agent type to instantiate

    Returns:
        Agent instance

    Raises:
        ImportError: If agent module cannot be imported
        AttributeError: If agent class cannot be found
    """
    config = get_agent_config(agent_type)

    # Dynamic import of the agent module
    import importlib
    module = importlib.import_module(config.module_path)
    agent_class = getattr(module, config.class_name)

    return agent_class()
