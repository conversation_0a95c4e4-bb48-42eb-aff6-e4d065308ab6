"""
Centralized project type definitions.
This is the single place to add new project types and their configurations.
"""
from enum import Enum
from typing import Dict, List, Any
from dataclasses import dataclass


class ProjectType(Enum):
    """
    Enumeration of all supported project types.
    Add new project types here.
    """
    CODE_CONVERSION = "code_conversion"
    KNOWLEDGE_BUILDER = "knowledge_builder"
    DOCUMENTATION = "documentation"
    DATA_DISCOVERY = "data_discovery"
    DEPENDENCY_ANALYSIS = "dependency_analysis"
    
    # Add new project types here:
    # MODERNIZATION = "modernization"
    # TESTING = "testing"
    # MIGRATION = "migration"


@dataclass
class ProjectTypeConfig:
    """Configuration for a project type."""
    name: str
    display_name: str
    description: str
    icon: str
    workflow_name: str
    required_inputs: List[str]
    optional_inputs: List[str]
    output_artifacts: List[str]
    ui_renderer: str


# Central registry of all project type configurations
# Add new project types here with their complete configuration
PROJECT_TYPE_REGISTRY: Dict[ProjectType, ProjectTypeConfig] = {
    
    ProjectType.CODE_CONVERSION: ProjectTypeConfig(
        name="code_conversion",
        display_name="COBOL to Java Conversion",
        description="Convert COBOL programs to Java Spring Boot applications with full business logic preservation",
        icon="🔄",
        workflow_name="code_conversion_workflow",
        required_inputs=["source_files", "target_language"],
        optional_inputs=["conversion_rules", "naming_conventions"],
        output_artifacts=["java_code", "documentation", "mappings", "test_files"],
        ui_renderer="code_conversion_project_renderer"
    ),
    
    ProjectType.KNOWLEDGE_BUILDER: ProjectTypeConfig(
        name="knowledge_builder",
        display_name="Business Knowledge Extraction",
        description="Extract and document business logic, rules, and processes from legacy code",
        icon="🧠",
        workflow_name="knowledge_extraction_workflow",
        required_inputs=["source_files"],
        optional_inputs=["domain_context", "business_glossary"],
        output_artifacts=["business_documentation", "process_flows", "data_dictionary"],
        ui_renderer="knowledge_builder_project_renderer"
    ),
    
    ProjectType.DOCUMENTATION: ProjectTypeConfig(
        name="documentation",
        display_name="Code Documentation Generation",
        description="Generate comprehensive technical and business documentation from source code",
        icon="📚",
        workflow_name="documentation_generation_workflow",
        required_inputs=["source_files"],
        optional_inputs=["documentation_templates", "style_guide"],
        output_artifacts=["technical_docs", "api_docs", "user_guides"],
        ui_renderer="documentation_project_renderer"
    ),
    
    ProjectType.DATA_DISCOVERY: ProjectTypeConfig(
        name="data_discovery",
        display_name="Data Structure Analysis",
        description="Analyze and map data structures, relationships, and data flows",
        icon="🔍",
        workflow_name="data_discovery_workflow",
        required_inputs=["source_files"],
        optional_inputs=["data_samples", "schema_definitions"],
        output_artifacts=["data_models", "relationship_diagrams", "data_lineage"],
        ui_renderer="data_discovery_project_renderer"
    ),
    
    ProjectType.DEPENDENCY_ANALYSIS: ProjectTypeConfig(
        name="dependency_analysis",
        display_name="Dependency & Call Tree Analysis",
        description="Analyze code dependencies, call trees, and module relationships",
        icon="🌳",
        workflow_name="dependency_analysis_workflow",
        required_inputs=["source_files"],
        optional_inputs=["analysis_scope", "exclusion_patterns"],
        output_artifacts=["dependency_graphs", "call_trees", "impact_analysis"],
        ui_renderer="dependency_analysis_project_renderer"
    ),
    
    # Add new project types here following the same pattern:
    # ProjectType.MODERNIZATION: ProjectTypeConfig(
    #     name="modernization",
    #     display_name="Legacy Modernization",
    #     description="Modernize legacy applications with current technologies and patterns",
    #     icon="⚡",
    #     workflow_name="modernization_workflow",
    #     required_inputs=["source_files", "target_architecture"],
    #     optional_inputs=["modernization_strategy", "technology_stack"],
    #     output_artifacts=["modernized_code", "architecture_docs", "migration_plan"],
    #     ui_renderer="modernization_project_renderer"
    # ),
}


def get_project_type_config(project_type: ProjectType) -> ProjectTypeConfig:
    """
    Get configuration for a project type.
    
    Args:
        project_type: The project type enum
        
    Returns:
        ProjectTypeConfig: Configuration for the project type
        
    Raises:
        KeyError: If project type is not registered
    """
    if project_type not in PROJECT_TYPE_REGISTRY:
        raise KeyError(f"Project type {project_type} is not registered")
    
    return PROJECT_TYPE_REGISTRY[project_type]


def get_all_project_types() -> List[ProjectTypeConfig]:
    """
    Get all registered project type configurations.
    
    Returns:
        List[ProjectTypeConfig]: List of all project type configurations
    """
    return list(PROJECT_TYPE_REGISTRY.values())


def get_project_type_by_name(name: str) -> ProjectTypeConfig:
    """
    Get project type configuration by name.
    
    Args:
        name: The project type name
        
    Returns:
        ProjectTypeConfig: Configuration for the project type
        
    Raises:
        ValueError: If project type name is not found
    """
    for project_type, config in PROJECT_TYPE_REGISTRY.items():
        if config.name == name:
            return config
    
    raise ValueError(f"Project type with name '{name}' not found")


def is_valid_project_type(name: str) -> bool:
    """
    Check if a project type name is valid.
    
    Args:
        name: The project type name to check
        
    Returns:
        bool: True if valid, False otherwise
    """
    try:
        get_project_type_by_name(name)
        return True
    except ValueError:
        return False


def get_project_type_names() -> List[str]:
    """
    Get all project type names.
    
    Returns:
        List[str]: List of all project type names
    """
    return [config.name for config in PROJECT_TYPE_REGISTRY.values()]


def get_project_type_display_names() -> List[str]:
    """
    Get all project type display names.
    
    Returns:
        List[str]: List of all project type display names
    """
    return [config.display_name for config in PROJECT_TYPE_REGISTRY.values()]
