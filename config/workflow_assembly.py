"""
Centralized workflow assembly system.
This is the single place to define and assemble workflows for different project types.
"""
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .project_types import ProjectType
from .agent_registry import AgentType


@dataclass
class WorkflowStep:
    """Definition of a single workflow step."""
    agent_type: AgentType
    name: str
    description: str
    parallel_group: Optional[str] = None
    conditional: bool = False
    condition: Optional[str] = None
    retry_count: int = 3
    timeout_minutes: int = 30


@dataclass
class WorkflowDefinition:
    """Complete workflow definition."""
    name: str
    display_name: str
    description: str
    project_type: ProjectType
    steps: List[WorkflowStep]
    estimated_duration: str
    parallel_execution: bool = False


# Central registry of all workflow definitions
# Add new workflows here and modify existing ones
WORKFLOW_REGISTRY: Dict[str, WorkflowDefinition] = {

    "code_conversion_workflow": WorkflowDefinition(
        name="code_conversion_workflow",
        display_name="COBOL to Java Conversion Workflow",
        description="Complete workflow for converting COBOL programs to Java Spring Boot applications",
        project_type=ProjectType.CODE_CONVERSION,
        estimated_duration="30-60 minutes",
        parallel_execution=True,
        steps=[
            WorkflowStep(
                agent_type=AgentType.PACKAGE_ANALYZER,
                name="package_analyzer",
                description="Analyze project structure and identify COBOL programs"
            ),
            WorkflowStep(
                agent_type=AgentType.CODE_PREPROCESSOR,
                name="code_preprocessor",
                description="Preprocess COBOL code and expand copybooks"
            ),
            WorkflowStep(
                agent_type=AgentType.OVERVIEW_GENERATOR,
                name="overview_generator",
                description="Generate program overview and create code chunks"
            ),
            WorkflowStep(
                agent_type=AgentType.KNOWLEDGE_MINER,
                name="knowledge_miner",
                description="Extract business logic and functional specifications"
            ),
            WorkflowStep(
                agent_type=AgentType.DOCUMENTATION_GENERATOR,
                name="documentation_generator",
                description="Generate comprehensive documentation"
            ),
            WorkflowStep(
                agent_type=AgentType.DOCUMENTATION_CRITIC,
                name="documentation_critic",
                description="Review and improve documentation quality"
            ),
            WorkflowStep(
                agent_type=AgentType.CODE_GENERATOR,
                name="code_generator",
                description="Generate Java Spring Boot code"
            ),
            WorkflowStep(
                agent_type=AgentType.CODE_REVIEWER,
                name="code_reviewer",
                description="Review generated Java code for quality"
            ),
        ]
    ),

    "knowledge_extraction_workflow": WorkflowDefinition(
        name="knowledge_extraction_workflow",
        display_name="Business Knowledge Extraction Workflow",
        description="Workflow for extracting and documenting business knowledge from legacy code",
        project_type=ProjectType.KNOWLEDGE_BUILDER,
        estimated_duration="20-40 minutes",
        parallel_execution=True,
        steps=[
            WorkflowStep(
                agent_type=AgentType.PACKAGE_ANALYZER,
                name="package_analyzer",
                description="Analyze project structure and identify programs"
            ),
            WorkflowStep(
                agent_type=AgentType.CODE_PREPROCESSOR,
                name="code_preprocessor",
                description="Preprocess code for analysis"
            ),
            WorkflowStep(
                agent_type=AgentType.OVERVIEW_GENERATOR,
                name="overview_generator",
                description="Generate program overview and structure analysis"
            ),
            WorkflowStep(
                agent_type=AgentType.KNOWLEDGE_MINER,
                name="knowledge_miner",
                description="Extract business logic and processes"
            ),
            WorkflowStep(
                agent_type=AgentType.DOCUMENTATION_GENERATOR,
                name="documentation_generator",
                description="Generate business documentation"
            ),
            WorkflowStep(
                agent_type=AgentType.DOCUMENTATION_CRITIC,
                name="documentation_critic",
                description="Review and enhance documentation"
            ),
        ]
    ),

    "documentation_generation_workflow": WorkflowDefinition(
        name="documentation_generation_workflow",
        display_name="Documentation Generation Workflow",
        description="Workflow for generating comprehensive technical and business documentation",
        project_type=ProjectType.DOCUMENTATION,
        estimated_duration="15-30 minutes",
        parallel_execution=True,
        steps=[
            WorkflowStep(
                agent_type=AgentType.PACKAGE_ANALYZER,
                name="package_analyzer",
                description="Analyze project structure"
            ),
            WorkflowStep(
                agent_type=AgentType.CODE_PREPROCESSOR,
                name="code_preprocessor",
                description="Preprocess code for documentation"
            ),
            WorkflowStep(
                agent_type=AgentType.OVERVIEW_GENERATOR,
                name="overview_generator",
                description="Generate program overview"
            ),
            WorkflowStep(
                agent_type=AgentType.KNOWLEDGE_MINER,
                name="knowledge_miner",
                description="Extract business knowledge for documentation"
            ),
            WorkflowStep(
                agent_type=AgentType.DOCUMENTATION_GENERATOR,
                name="documentation_generator",
                description="Generate comprehensive documentation"
            ),
            WorkflowStep(
                agent_type=AgentType.DOCUMENTATION_CRITIC,
                name="documentation_critic",
                description="Review and improve documentation"
            ),
        ]
    ),

    "data_discovery_workflow": WorkflowDefinition(
        name="data_discovery_workflow",
        display_name="Data Discovery Workflow",
        description="Workflow for analyzing data structures and relationships",
        project_type=ProjectType.DATA_DISCOVERY,
        estimated_duration="10-25 minutes",
        parallel_execution=True,
        steps=[
            WorkflowStep(
                agent_type=AgentType.PACKAGE_ANALYZER,
                name="package_analyzer",
                description="Analyze project structure and data files"
            ),
            WorkflowStep(
                agent_type=AgentType.CODE_PREPROCESSOR,
                name="code_preprocessor",
                description="Preprocess code for data analysis"
            ),
            WorkflowStep(
                agent_type=AgentType.OVERVIEW_GENERATOR,
                name="overview_generator",
                description="Generate data structure overview"
            ),
            WorkflowStep(
                agent_type=AgentType.KNOWLEDGE_MINER,
                name="knowledge_miner",
                description="Extract data definitions and relationships"
            ),
            WorkflowStep(
                agent_type=AgentType.DOCUMENTATION_GENERATOR,
                name="documentation_generator",
                description="Generate data documentation"
            ),
        ]
    ),

    "dependency_analysis_workflow": WorkflowDefinition(
        name="dependency_analysis_workflow",
        display_name="Dependency Analysis Workflow",
        description="Workflow for analyzing code dependencies and call trees",
        project_type=ProjectType.DEPENDENCY_ANALYSIS,
        estimated_duration="10-20 minutes",
        parallel_execution=True,
        steps=[
            WorkflowStep(
                agent_type=AgentType.PACKAGE_ANALYZER,
                name="package_analyzer",
                description="Analyze project structure and dependencies"
            ),
            WorkflowStep(
                agent_type=AgentType.CODE_PREPROCESSOR,
                name="code_preprocessor",
                description="Preprocess code for dependency analysis"
            ),
            WorkflowStep(
                agent_type=AgentType.OVERVIEW_GENERATOR,
                name="overview_generator",
                description="Generate program structure and call analysis"
            ),
            WorkflowStep(
                agent_type=AgentType.DOCUMENTATION_GENERATOR,
                name="documentation_generator",
                description="Generate dependency documentation"
            ),
        ]
    ),

    "plugin_based_conversion_workflow": WorkflowDefinition(
        name="plugin_based_conversion_workflow",
        display_name="Plugin-Based Language-Agnostic Conversion Workflow",
        description="Complete workflow using plugin system for any source language to any target technology",
        project_type=ProjectType.CODE_CONVERSION,
        estimated_duration="25-50 minutes",
        parallel_execution=True,
        steps=[
            WorkflowStep(
                agent_type=AgentType.PACKAGE_ANALYZER,
                name="package_analyzer",
                description="Analyze project structure and identify source files"
            ),
            WorkflowStep(
                agent_type=AgentType.CODE_PREPROCESSOR,
                name="code_preprocessor",
                description="Preprocess source code and extract chunks"
            ),
            WorkflowStep(
                agent_type=AgentType.KNOWLEDGE_MINER,
                name="knowledge_miner",
                description="Extract business knowledge from preprocessed code"
            ),
            WorkflowStep(
                agent_type=AgentType.DOCUMENTATION_GENERATOR,
                name="documentation_generator",
                description="Generate comprehensive documentation"
            ),
            WorkflowStep(
                agent_type=AgentType.DOCUMENTATION_CRITIC,
                name="documentation_critic",
                description="Review and enhance generated documentation"
            ),
            WorkflowStep(
                agent_type=AgentType.CODE_GENERATOR,
                name="code_generator",
                description="Generate target code from business logic"
            ),
            WorkflowStep(
                agent_type=AgentType.CODE_REVIEWER,
                name="code_reviewer",
                description="Review generated code for quality"
            ),
        ]
    ),

    # Add new workflows here following the same pattern:
    # "modernization_workflow": WorkflowDefinition(
    #     name="modernization_workflow",
    #     display_name="Legacy Modernization Workflow",
    #     description="Workflow for modernizing legacy applications",
    #     project_type=ProjectType.MODERNIZATION,
    #     estimated_duration="45-90 minutes",
    #     parallel_execution=True,
    #     steps=[
    #         WorkflowStep(
    #             agent_type=AgentType.PACKAGE_ANALYZER,
    #             name="analyze_legacy_structure",
    #             description="Analyze legacy application structure"
    #         ),
    #         WorkflowStep(
    #             agent_type=AgentType.DATA_ANALYZER,
    #             name="analyze_data_structures",
    #             description="Analyze data structures for modernization"
    #         ),
    #         # ... more steps
    #     ]
    # ),
}


def get_workflow_definition(workflow_name: str) -> WorkflowDefinition:
    """
    Get workflow definition by name.

    Args:
        workflow_name: Name of the workflow

    Returns:
        WorkflowDefinition: The workflow definition

    Raises:
        KeyError: If workflow is not found
    """
    if workflow_name not in WORKFLOW_REGISTRY:
        raise KeyError(f"Workflow '{workflow_name}' is not registered")

    return WORKFLOW_REGISTRY[workflow_name]


def get_workflow_for_project_type(project_type: ProjectType) -> WorkflowDefinition:
    """
    Get workflow definition for a project type.

    Args:
        project_type: The project type

    Returns:
        WorkflowDefinition: The workflow definition

    Raises:
        ValueError: If no workflow is found for the project type
    """
    for workflow in WORKFLOW_REGISTRY.values():
        if workflow.project_type == project_type:
            return workflow

    raise ValueError(f"No workflow found for project type {project_type}")


def get_all_workflows() -> List[WorkflowDefinition]:
    """
    Get all registered workflow definitions.

    Returns:
        List[WorkflowDefinition]: List of all workflow definitions
    """
    return list(WORKFLOW_REGISTRY.values())


def get_workflow_names() -> List[str]:
    """
    Get all workflow names.

    Returns:
        List[str]: List of all workflow names
    """
    return list(WORKFLOW_REGISTRY.keys())


def validate_workflow(workflow: WorkflowDefinition) -> List[str]:
    """
    Validate a workflow definition.

    Args:
        workflow: The workflow to validate

    Returns:
        List[str]: List of validation errors (empty if valid)
    """
    errors = []

    if not workflow.steps:
        errors.append("Workflow must have at least one step")

    # Check for dependency cycles
    step_names = [step.name for step in workflow.steps]
    if len(step_names) != len(set(step_names)):
        errors.append("Workflow steps must have unique names")

    # Validate agent types exist
    from .agent_registry import AGENT_REGISTRY
    for step in workflow.steps:
        if step.agent_type not in AGENT_REGISTRY:
            errors.append(f"Agent type {step.agent_type} is not registered")

    return errors


def create_custom_workflow(name: str, display_name: str, description: str,
                         project_type: ProjectType, steps: List[WorkflowStep]) -> WorkflowDefinition:
    """
    Create a custom workflow definition.

    Args:
        name: Workflow name
        display_name: Display name
        description: Description
        project_type: Project type
        steps: List of workflow steps

    Returns:
        WorkflowDefinition: The created workflow definition

    Raises:
        ValueError: If workflow validation fails
    """
    workflow = WorkflowDefinition(
        name=name,
        display_name=display_name,
        description=description,
        project_type=project_type,
        steps=steps,
        estimated_duration="Variable"
    )

    errors = validate_workflow(workflow)
    if errors:
        raise ValueError(f"Workflow validation failed: {', '.join(errors)}")

    return workflow
