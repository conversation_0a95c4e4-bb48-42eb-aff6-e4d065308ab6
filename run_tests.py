#!/usr/bin/env python3
"""
Test runner script for RAM2 project.
Provides different test execution modes and coverage reporting.
"""
import sys
import subprocess
import argparse
import os
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"❌ Command not found: {cmd[0]}")
        print("Make sure pytest is installed: pip install -r requirements.txt")
        return False


def install_dependencies():
    """Install test dependencies."""
    print("Installing test dependencies...")
    cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
    return run_command(cmd, "Installing dependencies")


def run_unit_tests():
    """Run unit tests only."""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "-m", "unit or not (integration or slow or requires_neo4j or requires_llm)",
        "--cov=agents",
        "--cov=tools", 
        "--cov=ui",
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov"
    ]
    return run_command(cmd, "Unit tests")


def run_integration_tests():
    """Run integration tests."""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "-m", "integration",
        "--cov=agents",
        "--cov=tools",
        "--cov=ui",
        "--cov-report=term-missing"
    ]
    return run_command(cmd, "Integration tests")


def run_all_tests():
    """Run all tests including slow ones."""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--cov=agents",
        "--cov=tools",
        "--cov=ui", 
        "--cov-report=term-missing",
        "--cov-report=html:htmlcov"
    ]
    return run_command(cmd, "All tests")


def run_tests_with_external_services():
    """Run tests that require external services (Neo4j, LLM APIs)."""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--run-neo4j",
        "--run-llm",
        "--cov=agents",
        "--cov=tools",
        "--cov=ui",
        "--cov-report=term-missing"
    ]
    return run_command(cmd, "Tests with external services")


def run_specific_test(test_path):
    """Run a specific test file or test function."""
    cmd = [
        sys.executable, "-m", "pytest",
        test_path,
        "-v",
        "--tb=short"
    ]
    return run_command(cmd, f"Specific test: {test_path}")


def check_coverage():
    """Check test coverage and generate reports."""
    print("\n" + "="*60)
    print("Coverage Summary")
    print("="*60)
    
    # Generate coverage report
    cmd = [sys.executable, "-m", "coverage", "report", "--show-missing"]
    run_command(cmd, "Coverage report")
    
    # Check if HTML coverage report exists
    html_report = Path("htmlcov/index.html")
    if html_report.exists():
        print(f"\n📊 HTML coverage report available at: {html_report.absolute()}")
    
    return True


def lint_code():
    """Run code linting."""
    print("Running code linting...")
    
    # Check if flake8 is available
    try:
        cmd = [sys.executable, "-m", "flake8", "agents/", "tools/", "ui/", "--max-line-length=120"]
        return run_command(cmd, "Code linting (flake8)")
    except FileNotFoundError:
        print("flake8 not found. Install with: pip install flake8")
        return True  # Don't fail if linting tool is not available


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="RAM2 Test Runner")
    parser.add_argument(
        "mode",
        choices=["unit", "integration", "all", "external", "install", "coverage", "lint"],
        nargs="?",
        default="unit",
        help="Test mode to run (default: unit)"
    )
    parser.add_argument(
        "--test",
        help="Run specific test file or test function (e.g., tests/agents/test_base_agent.py::TestBaseAgent::test_initialization)"
    )
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install dependencies before running tests"
    )
    
    args = parser.parse_args()
    
    success = True
    
    # Install dependencies if requested
    if args.install_deps or args.mode == "install":
        success = install_dependencies()
        if not success:
            return 1
    
    # Run specific test if provided
    if args.test:
        success = run_specific_test(args.test)
    elif args.mode == "unit":
        success = run_unit_tests()
    elif args.mode == "integration":
        success = run_integration_tests()
    elif args.mode == "all":
        success = run_all_tests()
    elif args.mode == "external":
        success = run_tests_with_external_services()
    elif args.mode == "coverage":
        success = check_coverage()
    elif args.mode == "lint":
        success = lint_code()
    elif args.mode == "install":
        # Already handled above
        pass
    
    # Generate coverage report for test modes
    if args.mode in ["unit", "integration", "all", "external"] and success:
        check_coverage()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
