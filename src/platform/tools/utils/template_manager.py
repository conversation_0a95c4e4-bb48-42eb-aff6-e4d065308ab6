"""
Template management system using Jinja2 for rendering templates.
Provides centralized template loading, rendering, and caching functionality.
"""
import os
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

try:
    from jinja2 import Environment, FileSystemLoader, Template, TemplateNotFound

    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False
    Environment = None
    FileSystemLoader = None
    Template = None
    TemplateNotFound = Exception

from config.constants import TEMPLATE_DIRS


class TemplateManager:
    """
    Manages Jinja2 templates for the RAM2 application.
    Provides template loading, rendering, and caching functionality.
    """

    def __init__(self, template_dir_path: Path = None):
        """
        Initialize the template manager.

        Args:
            templates_dir: Directory containing templates (defaults to TEMPLATES_DIR)
        """
        if not JINJA2_AVAILABLE:
            raise ImportError("Jinja2 is required for template management. Install with: pip install Jinja2")

        self.template_dirs = [template_dir_path] if template_dir_path else TEMPLATE_DIRS.values()
        self.logger = logging.getLogger(__name__)

        # Ensure templates directory exists
        for template_dir in self.template_dirs:
            if not os.path.exists(template_dir):
                os.makedirs(template_dir, exist_ok=True)

        # Initialize the Jinja2 environment with multiple search paths
        self.env = Environment(
            loader=FileSystemLoader(self.template_dirs),
            trim_blocks=True,
            lstrip_blocks=True,
            keep_trailing_newline=True
        )

        # Add custom filters
        self._add_custom_filters()

        # Template cache
        self._template_cache: Dict[str, Template] = {}

        self.logger.info(f"TemplateManager initialized with templates directories: {self.template_dirs}")

    def _add_custom_filters(self):
        """Add custom Jinja2 filters for code generation."""

        def to_camel_case(text: str) -> str:
            """Convert COBOL-STYLE-NAME to camelCase."""
            if not text:
                return text

            # Split by hyphens and underscores
            parts = text.replace('_', '-').split('-')
            if not parts:
                return text

            # First part lowercase, rest title case
            result = parts[0].lower()
            for part in parts[1:]:
                if part:
                    result += part.capitalize()

            return result

        def to_pascal_case(text: str) -> str:
            """Convert kebab-case to PascalCase."""
            if not text:
                return text

            # Split by hyphens and underscores
            parts = text.replace('_', '-').split('-')
            result = ""
            for part in parts:
                if part:
                    result += part.capitalize()

            return result

        def to_snake_case(text: str) -> str:
            """Convert CamelCase or kebab-case to snake_case."""
            if not text:
                return text

            # Replace hyphens with underscores
            text = text.replace('-', '_')

            # Insert underscores before uppercase letters
            import re
            result = re.sub(r'([a-z0-9])([A-Z])', r'\1_\2', text)

            return result.lower()

        def to_java_package(text: str) -> str:
            """Convert text to valid Java package name."""
            if not text:
                return "com.generated"

            # Convert to lowercase and replace invalid characters
            import re
            result = re.sub(r'[^a-zA-Z0-9.]', '', text.lower())

            # Ensure it starts with a letter
            if result and not result[0].isalpha():
                result = "pkg" + result

            return result or "com.generated"

        # Register filters
        self.env.filters['camelCase'] = to_camel_case
        self.env.filters['pascalCase'] = to_pascal_case
        self.env.filters['snakeCase'] = to_snake_case
        self.env.filters['javaPackage'] = to_java_package

    def render_template(self, template_name: str, context: Dict[str, Any] = None) -> str:
        """
        Render a template with the given context.

        Args:
            template_name: Name of the template file (relative to templates_dir)
            context: Dictionary of variables to pass to the template

        Returns:
            str: Rendered template content

        Raises:
            TemplateNotFound: If the template file doesn't exist
            Exception: If template rendering fails
        """
        if context is None:
            context = {}

        try:
            # Get template (with caching)
            template = self._get_template(template_name)

            # Render template
            rendered = template.render(context)

            self.logger.debug(f"Successfully rendered template: {template_name}")
            return rendered

        except TemplateNotFound:
            error_msg = f"Template not found: {template_name}"
            self.logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        except Exception as e:
            error_msg = f"Error rendering template {template_name}: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg) from e

    # noinspection PyTypeChecker
    def _get_template(self, template_name: str) -> Template:
        """
        Get a template with caching.

        Args:
            template_name: Name of the template file

        Returns:
            Template: Jinja2 template object
        """
        if template_name not in self._template_cache:
            self._template_cache[template_name] = self.env.get_template(template_name)

        return self._template_cache[template_name]

    def template_exists(self, template_name: str) -> bool:
        """
        Check if a template exists.

        Args:
            template_name: Name of the template file

        Returns:
            bool: True if template exists, False otherwise
        """
        try:
            self.env.get_template(template_name)
            return True
        except TemplateNotFound:
            return False

    def list_templates(self, pattern: str = None) -> List[str]:
        """
        List available templates.

        Args:
            pattern: Optional pattern to filter templates (e.g., "agents/*")

        Returns:
            List[str]: List of template names
        """
        try:
            templates = self.env.list_templates()

            if pattern:
                import fnmatch
                templates = [t for t in templates if fnmatch.fnmatch(t, pattern)]

            return sorted(templates)

        except Exception as e:
            self.logger.error(f"Error listing templates: {str(e)}")
            return []

    def render_string_template(self, template_string: str, context: Dict[str, Any] = None) -> str:
        """
        Render a template from a string.

        Args:
            template_string: Template content as string
            context: Dictionary of variables to pass to the template

        Returns:
            str: Rendered template content
        """
        if context is None:
            context = {}

        try:
            template = self.env.from_string(template_string)
            return template.render(context)

        except Exception as e:
            error_msg = f"Error rendering string template: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg) from e

    def create_template_file(self, template_name: str, content: str) -> bool:
        """
        Create a new template file.

        Args:
            template_name: Name of the template file to create
            content: Template content

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            template_path = Path(self.template_dirs[0]) / template_name

            # Create directory if it doesn't exist
            template_path.parent.mkdir(parents=True, exist_ok=True)

            # Write template content
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Clear cache for this template
            if template_name in self._template_cache:
                del self._template_cache[template_name]

            self.logger.info(f"Created template file: {template_name}")
            return True

        except Exception as e:
            self.logger.error(f"Error creating template file {template_name}: {str(e)}")
            return False

    def get_template_path(self, template_name: str) -> Path:
        """
        Get the full path to a template file.

        Args:
            template_name: Name of the template file

        Returns:
            str: Full path to the template file
        """
        for templates_dir in self.template_dirs:
            for template_file in templates_dir.rglob('*'):
                if str(template_file.name) == template_name:
                    rel_path = template_file.relative_to(templates_dir)
                    print(str(rel_path).replace('\\', '/'))

        return Path(self.template_dirs) / template_name

    def clear_cache(self):
        """Clear the template cache."""
        self._template_cache.clear()
        self.logger.debug("Template cache cleared")

    def reload_templates(self):
        """Reload all templates (useful for development)."""
        self.clear_cache()

        # Recreate the environment to pick up new templates
        self.env = Environment(
            loader=FileSystemLoader(self.template_dirs),
            trim_blocks=True,
            lstrip_blocks=True,
            keep_trailing_newline=True
        )
        self._add_custom_filters()
        self.logger.info("Templates reloaded")


# Global template manager instance
_template_manager: Optional[TemplateManager] = None


def get_template_manager() -> TemplateManager:
    """
    Get the global template manager instance.

    Returns:
        TemplateManager: Global template manager instance
    """
    global _template_manager
    if _template_manager is None:
        _template_manager = TemplateManager()
    return _template_manager


def render_template(template_name: str, context: Dict[str, Any] = None) -> str:
    """
    Convenience function to render a template using the global template manager.

    Args:
        template_name: Name of the template file
        context: Dictionary of variables to pass to the template

    Returns:
        str: Rendered template content
    """
    return get_template_manager().render_template(template_name, context)
