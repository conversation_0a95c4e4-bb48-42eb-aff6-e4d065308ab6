"""
IMS Segment Mapping Utility for COBOL to Java conversion.
Provides functions to identify IMS segments in COBOL code and map them to business names.
"""
import re
import logging
from typing import Dict, List, Set, Optional, Tuple
from config.constants import IMS_SEGMENT_BUSINESS_MAPPINGS


class IMSSegmentMapper:
    """
    Utility class for identifying and mapping IMS database segments to business names.
    Used to enhance Java code generation with meaningful business context.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.segment_mappings = IMS_SEGMENT_BUSINESS_MAPPINGS
        
        # Patterns for identifying IMS segment references in COBOL code
        self.segment_patterns = [
            # DLI operation patterns
            r'EXEC\s+DLI\s+(?:GU|GN|GHU|GHN|ISRT|DLET)\s+SEGMENT\s*\(\s*([A-Z0-9]+)\s*\)',
            # Copybook include patterns
            r'COPY\s+([A-Z0-9]+)',
            r'INCLUDE\s+([A-Z0-9]+)',
            # Working storage segment references
            r'01\s+([A-Z0-9]+)(?:-SEGMENT|-SEG|-REC)?',
            # Direct segment name references
            r'\b([A-Z]{2,}[A-Z0-9]{2,})\b',
        ]
    
    def identify_segments_in_code(self, cobol_code: str) -> Set[str]:
        """
        Identify all IMS segment names referenced in COBOL code.
        
        Args:
            cobol_code: COBOL source code to analyze
            
        Returns:
            Set[str]: Set of identified segment names that exist in the mapping
        """
        identified_segments = set()
        
        for pattern in self.segment_patterns:
            matches = re.finditer(pattern, cobol_code, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                segment_name = match.group(1).upper()
                # Only include segments that exist in our mapping
                if segment_name in self.segment_mappings:
                    identified_segments.add(segment_name)
                    self.logger.debug(f"Identified IMS segment: {segment_name}")
        
        self.logger.info(f"Identified {len(identified_segments)} IMS segments in code")
        return identified_segments
    
    def get_business_name(self, segment_name: str) -> Optional[str]:
        """
        Get business name for a specific IMS segment.
        
        Args:
            segment_name: IMS segment name (e.g., 'AMSAM00')
            
        Returns:
            Optional[str]: Business name or None if not found
        """
        return self.segment_mappings.get(segment_name.upper())
    
    def get_business_context_for_segments(self, segment_names: Set[str]) -> Dict[str, str]:
        """
        Get business context mapping for multiple segments.
        
        Args:
            segment_names: Set of IMS segment names
            
        Returns:
            Dict[str, str]: Mapping of segment name to business name
        """
        context = {}
        for segment_name in segment_names:
            business_name = self.get_business_name(segment_name)
            if business_name:
                context[segment_name] = business_name
        
        return context
    
    def generate_javadoc_comment(self, segment_name: str) -> str:
        """
        Generate JavaDoc comment with business context for an IMS segment.
        
        Args:
            segment_name: IMS segment name
            
        Returns:
            str: Formatted JavaDoc comment
        """
        business_name = self.get_business_name(segment_name)
        if business_name:
            return f"Handles {business_name} (COBOL segment: {segment_name})"
        else:
            return f"Handles COBOL segment: {segment_name}"
    
    def generate_traceability_comment(self, segment_name: str) -> str:
        """
        Generate inline traceability comment for an IMS segment.
        
        Args:
            segment_name: IMS segment name
            
        Returns:
            str: Formatted traceability comment
        """
        business_name = self.get_business_name(segment_name)
        if business_name:
            return f"// Corresponds to COBOL segment {segment_name}: {business_name}"
        else:
            return f"// Corresponds to COBOL segment {segment_name}"
    
    def enhance_class_name_with_business_context(self, base_name: str, segment_name: str) -> str:
        """
        Enhance Java class name with business context from IMS segment.
        
        Args:
            base_name: Base class name
            segment_name: IMS segment name
            
        Returns:
            str: Enhanced class name incorporating business context
        """
        business_name = self.get_business_name(segment_name)
        if business_name:
            # Convert business name to PascalCase
            business_parts = business_name.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
            # Remove common words that don't add value to class names
            business_parts = business_parts.replace('Information', '').replace('Data', '')
            if business_parts and business_parts != base_name:
                return f"{business_parts}{base_name}"
        
        return base_name
    
    def get_segments_for_template_context(self, cobol_code: str) -> Dict[str, any]:
        """
        Get segment information formatted for template context.
        
        Args:
            cobol_code: COBOL source code to analyze
            
        Returns:
            Dict[str, any]: Template context with segment information
        """
        identified_segments = self.identify_segments_in_code(cobol_code)
        business_context = self.get_business_context_for_segments(identified_segments)
        
        return {
            'ims_segments': list(identified_segments),
            'segment_business_mappings': business_context,
            'has_ims_segments': len(identified_segments) > 0
        }
    
    def validate_segment_name(self, segment_name: str) -> bool:
        """
        Validate if a segment name exists in the mapping.
        
        Args:
            segment_name: IMS segment name to validate
            
        Returns:
            bool: True if segment exists in mapping
        """
        return segment_name.upper() in self.segment_mappings
    
    def get_all_segment_names(self) -> List[str]:
        """
        Get all available IMS segment names.
        
        Returns:
            List[str]: List of all segment names in the mapping
        """
        return list(self.segment_mappings.keys())
    
    def get_segments_by_business_category(self, category_keyword: str) -> Dict[str, str]:
        """
        Get segments that match a business category keyword.
        
        Args:
            category_keyword: Keyword to search for in business names
            
        Returns:
            Dict[str, str]: Mapping of segment names to business names that match the category
        """
        matching_segments = {}
        keyword_lower = category_keyword.lower()
        
        for segment_name, business_name in self.segment_mappings.items():
            if keyword_lower in business_name.lower():
                matching_segments[segment_name] = business_name
        
        return matching_segments


# Global instance for easy access
ims_segment_mapper = IMSSegmentMapper()


def get_ims_segment_mapper() -> IMSSegmentMapper:
    """
    Get the global IMS segment mapper instance.
    
    Returns:
        IMSSegmentMapper: Global mapper instance
    """
    return ims_segment_mapper
