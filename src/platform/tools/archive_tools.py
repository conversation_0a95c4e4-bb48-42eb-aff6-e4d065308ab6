import os
import zipfile
import tarfile
import gzip
import shutil
import logging
from typing import List, Optional

logger = logging.getLogger("tools.archive")

def extract_archive(file_path: str, target_dir: str) -> bool:
    """
    Extract an archive file to a target directory

    Args:
        file_path: Path to the archive file
        target_dir: Directory to extract to

    Returns:
        bool: True if extraction was successful, False otherwise
    """
    try:
        filename = os.path.basename(file_path)

        # Handle ZIP archives
        if filename.lower().endswith('.zip'):
            logger.info(f"Extracting ZIP archive: {filename}")
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                zip_ref.extractall(target_dir)
            return True

        # Handle TAR archives
        elif any(filename.lower().endswith(ext) for ext in ['.tar', '.tar.gz', '.tgz']):
            logger.info(f"Extracting TAR archive: {filename}")
            with tarfile.open(file_path, 'r:*') as tar_ref:
                tar_ref.extractall(target_dir)
            return True

        # Handle GZIP files (not tar.gz, which is handled above)
        elif filename.lower().endswith('.gz') and not filename.lower().endswith('.tar.gz'):
            logger.info(f"Extracting GZIP file: {filename}")
            output_path = os.path.join(target_dir, filename[:-3])  # Remove .gz extension
            with gzip.open(file_path, 'rb') as f_in:
                with open(output_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            return True

        else:
            logger.warning(f"Unsupported archive format: {filename}")
            return False

    except Exception as e:
        logger.error(f"Error extracting archive {file_path}: {str(e)}")
        return False

def list_archive_contents(file_path: str) -> List[str]:
    """
    List the contents of an archive file

    Args:
        file_path: Path to the archive file

    Returns:
        List[str]: List of file names in the archive
    """
    try:
        filename = os.path.basename(file_path)

        # Handle ZIP archives
        if filename.lower().endswith('.zip'):
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                return zip_ref.namelist()

        # Handle TAR archives
        elif any(filename.lower().endswith(ext) for ext in ['.tar', '.tar.gz', '.tgz']):
            with tarfile.open(file_path, 'r:*') as tar_ref:
                return [member.name for member in tar_ref.getmembers()]

        # Handle GZIP files (not tar.gz)
        elif filename.lower().endswith('.gz') and not filename.lower().endswith('.tar.gz'):
            return [filename[:-3]]  # Remove .gz extension

        else:
            logger.warning(f"Unsupported archive format: {filename}")
            return []

    except Exception as e:
        logger.error(f"Error listing archive contents {file_path}: {str(e)}")
        return []

def create_archive(source_dir: str, output_path: str, archive_type: str = 'zip') -> bool:
    """
    Create an archive from files in a directory

    Args:
        source_dir: Directory containing files to archive
        output_path: Path to save the archive
        archive_type: Type of archive ('zip' or 'tar')

    Returns:
        bool: True if archive creation was successful, False otherwise
    """
    try:
        if archive_type.lower() == 'zip':
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, _, files in os.walk(source_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, source_dir)
                        zipf.write(file_path, arcname)
            return True

        elif archive_type.lower() == 'tar':
            with tarfile.open(output_path, 'w:gz') as tar:
                tar.add(source_dir, arcname=os.path.basename(source_dir))
            return True

        else:
            logger.error(f"Unsupported archive type: {archive_type}")
            return False

    except Exception as e:
        logger.error(f"Error creating archive: {str(e)}")
        return False