import logging
from src.platform.tools.data.kuzu import KuzuConnector
from typing import Any, List, Dict

class GraphNode(Any):
    """Abstract interface for graph database nodes"""
    pass

class GraphDatabase:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("documentation_gen_agent")

    def __init__(self):
        self.connector = KuzuConnector()

    def connect(self) -> bool:
        return self.connector.connect()

    def disconnect(self) -> None:
        self.connector.disconnect()

    def get_node_performs_and_calls_by_uuid(self, el_uuid) -> List[Dict[str, Any]] | None:
        if not self.connect():
            self.logger.error(
                "(022_test_code_preprocessor_neo4j_retrieve_ops) ERROR: Can't connect to the Neo4j database")
            return None
        result = self.connector.get_nodes_perform_call_relations(el_uuid)
        self.disconnect()

        return result

    def get_nodes_calls_by_uuid(self, el_uuid) -> List[Dict[str, Any]] | None:
        if not self.connect():
            self.logger.error(
                "(022_test_code_preprocessor_neo4j_retrieve_ops) ERROR: Can't connect to the Neo4j database")
            return None
        result = self.connector.get_nodes_call_relations(el_uuid)
        self.disconnect()

        return result

    def get_nodes_performs_by_uuid(self, el_uuid) -> List[Dict[str, Any]] | None:
        if not self.connect():
            self.logger.error(
                "(022_test_code_preprocessor_neo4j_retrieve_ops) ERROR: Can't connect to the Neo4j database")
            return None
        result = self.connector.get_nodes_perform_relations(el_uuid)
        self.disconnect()

        return result

    # def get_callable_leaves(self) -> list[dict[str, Any]] | None:
    def get_callable_leaves(self) -> list[dict[str, Any]] | None:
        if not self.connect():
            self.logger.error(
                "(022_test_code_preprocessor_neo4j_retrieve_ops) ERROR: Can't connect to the Neo4j database")
            return None
        result = self.connector.get_callable_leaves()
        self.disconnect()

        return result

    def get_direct_callers(self, callee_uuids: list[str]) -> list[dict[str, Any]] | None:
        if not self.connect():
            self.logger.error(
                "(022_test_code_preprocessor_neo4j_retrieve_ops) ERROR: Can't connect to the Neo4j database")
            return None
        result = self.connector.get_direct_callers(callee_uuids)
        self.disconnect()

        return result
