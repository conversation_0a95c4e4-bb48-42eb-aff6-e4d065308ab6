import json
import os
import sqlite3
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import re

from src.platform.tools.knowledge_database import KnowledgeDatabase


class CodeGeneratorTools:
    """
    Language-agnostic tools for the code generator agent to interact with the knowledge database
    and file system. Target-specific functionality is delegated to target plugins.
    """

    def __init__(self, knowledge_db: KnowledgeDatabase, working_directory: str):
        self.knowledge_db = knowledge_db
        self.working_directory = working_directory
        self.logger = None

    def set_logger(self, logger):
        """Set the logger for the tools"""
        self.logger = logger

    def get_java_data_structures(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Get information about generated Java data structures

        Args:
            program_id: ID of the program

        Returns:
            List[Dict[str, Any]]: Java data structures information
        """
        try:
            return self.knowledge_db.get_java_data_structures(program_id)

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting Java data structures: {str(e)}")
            return []

    def get_language_mappings(self, program_id: str, source_language: str = "cobol",
                             target_language: str = "java", chunk_name: Optional[str] = None,
                             mapping_type: Optional[str] = None) -> Dict[str, str]:
        """
        Get source to target language name mappings

        Args:
            program_id: ID of the program
            source_language: Source language (default: "cobol")
            target_language: Target language (default: "java")
            chunk_name: Name of the chunk (optional)
            mapping_type: Type of mapping (method, variable, class, field)

        Returns:
            Dict[str, str]: Source name -> Target name mappings
        """
        try:
            # For backward compatibility, if it's cobol->java, use the existing method
            if source_language == "cobol" and target_language == "java":
                return self.knowledge_db.get_cobol_java_mappings(program_id, chunk_name, mapping_type)
            else:
                # TODO: Implement generic language mapping support
                if self.logger:
                    self.logger.warning(f"Generic {source_language}->{target_language} mappings not yet implemented")
                return {}

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting {source_language}-{target_language} mappings: {str(e)}")
            return {}

    def save_cobol_java_mappings(self, program_id: str, chunk_name: str, mappings: Dict[str, Any]) -> Dict[str, Any]:
        """
        Save COBOL to Java name mappings

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            mappings: Dictionary with mappings by type

        Returns:
            Dict[str, Any]: Result of saving mappings
        """
        try:
            self.knowledge_db.save_cobol_java_mapping(program_id, chunk_name, mappings)

            if self.logger:
                self.logger.info(f"Saved COBOL-Java mappings for {program_id}.{chunk_name}")

            return {"success": True}

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error saving COBOL-Java mappings: {str(e)}")
            return {"error": str(e), "success": False}

    def save_comprehensive_cobol_java_mapping(self, program_id: str, chunk_name: str, cobol_name: str,
                                             java_name: str, mapping_type: str, mapping_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Save a single comprehensive COBOL to Java mapping with detailed information.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            cobol_name: COBOL identifier name
            java_name: Java identifier name
            mapping_type: Type of mapping ('method', 'class', 'field', 'parameter', 'variable')
            mapping_info: Dictionary containing detailed mapping information

        Returns:
            Dict[str, Any]: Result of saving mapping
        """
        try:
            self.knowledge_db.save_comprehensive_cobol_java_mapping(
                program_id, chunk_name, cobol_name, java_name, mapping_type, mapping_info
            )

            if self.logger:
                self.logger.info(f"Saved comprehensive mapping: {cobol_name} -> {java_name} ({mapping_type})")

            return {"success": True}

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error saving comprehensive mappings: {str(e)}")
            return {"error": str(e), "success": False}

    def get_upstream_chunk_info(self, program_id: str, chunk_name: str) -> Dict[str, Any]:
        """
        Get information about chunks that this chunk depends on (upstream).
        Now uses actual Java class and method names from mappings.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk

        Returns:
            Dict[str, Any]: Upstream chunk information with real Java names
        """
        try:
            # Get the chunk's code to analyze dependencies
            chunk = self.knowledge_db.get_chunk_by_name(program_id, chunk_name)
            if not chunk:
                return {"error": f"Chunk {program_id}.{chunk_name} not found"}

            # Analyze PERFORM statements in the code to find dependencies
            code = chunk.get("code", "")
            upstream_chunks = self._extract_perform_statements(code, program_id)

            # Get detailed information for each upstream chunk
            upstream_info = []
            for upstream_chunk_name in upstream_chunks:
                upstream_chunk = self.knowledge_db.get_chunk_by_name(program_id, upstream_chunk_name)
                if upstream_chunk and upstream_chunk.get("analysis_status") == "complete":
                    # Get Java mappings for this upstream chunk
                    mappings = self.get_language_mappings(program_id, "cobol", "java", upstream_chunk_name)

                    # Extract actual Java class and method names
                    java_class_name = None
                    java_method_name = None

                    # Look for class mapping (usually stored as main business name)
                    business_name = upstream_chunk.get("business_name", "")
                    if business_name in mappings:
                        java_class_name = mappings[business_name]

                    # Look for method mapping
                    for cobol_name, java_name in mappings.items():
                        # Method mappings might be stored with different patterns
                        if cobol_name.endswith("_METHOD") or cobol_name == upstream_chunk_name:
                            java_method_name = java_name
                            break

                    # Fallback to business name conversion if no mapping found
                    if not java_class_name:
                        java_class_name = self._convert_business_name_to_java_class(business_name)
                    if not java_method_name:
                        java_method_name = self._convert_business_name_to_java_method(business_name)

                    upstream_info.append({
                        "chunk_name": upstream_chunk_name,
                        "business_name": business_name,
                        "business_description": upstream_chunk.get("business_description", ""),
                        "input_parameters": upstream_chunk.get("input_parameters", []),
                        "output_parameters": upstream_chunk.get("output_parameters", []),
                        "java_class_name": java_class_name,
                        "java_method_name": java_method_name,
                        "has_mappings": bool(mappings)
                    })

            return {
                "chunk_id": f"{program_id}.{chunk_name}",
                "upstream_chunks": upstream_info
            }

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting upstream chunk info: {str(e)}")
            return {"error": str(e)}

    def _convert_business_name_to_java_class(self, business_name: str) -> str:
        """Convert business name to Java class name as fallback"""
        import re
        words = re.findall(r'\b\w+\b', business_name)
        return ''.join(word.capitalize() for word in words) + "Service"

    def _convert_business_name_to_java_method(self, business_name: str) -> str:
        """Convert business name to Java method name as fallback"""
        import re
        words = re.findall(r'\b\w+\b', business_name)
        if not words:
            return "process"
        return words[0].lower() + ''.join(word.capitalize() for word in words[1:])

    def get_cobol_structure_info(self, program_id: str, variable_name: str) -> Dict[str, Any]:
        """
        Get COBOL structure hierarchy information for a variable

        Args:
            program_id: ID of the program
            variable_name: Name of the variable

        Returns:
            Dict[str, Any]: Structure hierarchy information
        """
        try:
            return self.knowledge_db.get_cobol_structure_hierarchy(program_id, variable_name)

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting COBOL structure info: {str(e)}")
            return {}

    def get_related_java_structures(self, program_id: str, cobol_structure: str) -> List[Dict[str, Any]]:
        """
        Get related Java data structures for a COBOL structure

        Args:
            program_id: ID of the program
            cobol_structure: Name of the COBOL structure

        Returns:
            List[Dict[str, Any]]: Related Java structures
        """
        try:
            structures = self.get_java_data_structures(program_id)

            # Filter structures related to the COBOL structure
            related = []
            for structure in structures:
                if (structure.get('cobol_structure_name') == cobol_structure or
                        structure.get('parent_structure') == cobol_structure):
                    related.append(structure)

            return related

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting related Java structures: {str(e)}")
            return []

    def get_downstream_chunk_info(self, program_id: str, chunk_name: str) -> Dict[str, Any]:
        """
        Get information about chunks that depend on this chunk (downstream).

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk

        Returns:
            Dict[str, Any]: Downstream chunk information
        """
        try:
            conn = sqlite3.connect(self.knowledge_db.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Find all chunks that PERFORM this chunk
            cursor.execute("""
                SELECT chunk_name, code, business_name, business_description
                FROM chunks
                WHERE program_id = ? AND analysis_status = 'complete'
            """, (program_id,))

            chunks = cursor.fetchall()
            conn.close()

            downstream_chunks = []
            for chunk in chunks:
                chunk_code = chunk["code"]
                # Check if this chunk performs the target chunk
                if self._chunk_performs_target(chunk_code, chunk_name):
                    downstream_chunks.append({
                        "chunk_name": chunk["chunk_name"],
                        "business_name": chunk.get("business_name", ""),
                        "business_description": chunk.get("business_description", "")
                    })

            return {
                "chunk_id": f"{program_id}.{chunk_name}",
                "downstream_chunks": downstream_chunks
            }

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting downstream chunk info: {str(e)}")
            return {"error": str(e)}

    def get_variables_info(self, program_id: str, variable_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Get information about variables used in a program.

        Args:
            program_id: ID of the program
            variable_names: Optional list of specific variable names to get info for

        Returns:
            Dict[str, Any]: Variables information
        """
        try:
            conn = sqlite3.connect(self.knowledge_db.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            if variable_names:
                # Get specific variables
                placeholders = ",".join(["?" for _ in variable_names])
                cursor.execute(f"""
                    SELECT * FROM variables
                    WHERE program_id = ? AND name IN ({placeholders})
                """, [program_id] + variable_names)
            else:
                # Get all variables for the program
                cursor.execute("""
                    SELECT * FROM variables
                    WHERE program_id = ?
                """, (program_id,))

            variables = cursor.fetchall()
            conn.close()

            variables_info = []
            for var in variables:
                var_dict = dict(var)
                # Parse JSON fields if they exist
                for field in ['possible_values', 'occurs']:
                    if var_dict.get(field) and isinstance(var_dict[field], str):
                        try:
                            var_dict[field] = json.loads(var_dict[field])
                        except json.JSONDecodeError:
                            pass
                variables_info.append(var_dict)

            return {
                "program_id": program_id,
                "variables": variables_info
            }

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting variables info: {str(e)}")
            return {"error": str(e)}

    def write_file_to_disk(self, file_path: str, content: str, relative_to_working_dir: bool = True) -> Dict[str, Any]:
        """
        Write content to a file on disk.

        Args:
            file_path: Path to the file
            content: Content to write
            relative_to_working_dir: Whether file_path is relative to working directory

        Returns:
            Dict[str, Any]: Result of the write operation
        """
        try:
            if relative_to_working_dir:
                full_path = os.path.join(self.working_directory, file_path)
            else:
                full_path = file_path

            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(full_path), exist_ok=True)

            # Write the file
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)

            if self.logger:
                self.logger.info(f"Successfully wrote file: {full_path}")

            return {
                "success": True,
                "file_path": full_path,
                "size": len(content)
            }

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error writing file {file_path}: {str(e)}")
            return {"error": str(e), "success": False}

    def get_java_project_structure(self) -> Dict[str, Any]:
        """
        Get the current Java project structure from disk.

        Returns:
            Dict[str, Any]: Java project structure information
        """
        try:
            java_project_dir = os.path.join(self.working_directory, "java_project")

            if not os.path.exists(java_project_dir):
                return {
                    "exists": False,
                    "message": "Java project directory does not exist"
                }

            structure = self._scan_directory_structure(java_project_dir)

            return {
                "exists": True,
                "project_dir": java_project_dir,
                "structure": structure,
                "java_files": self._find_java_files(java_project_dir),
                "pom_exists": os.path.exists(os.path.join(java_project_dir, "pom.xml"))
            }

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting Java project structure: {str(e)}")
            return {"error": str(e)}

    def save_generation_progress(self, program_id: str, chunk_name: str, status: str,
                                 java_class_info: Optional[Dict[str, Any]] = None,
                                 error_message: Optional[str] = None) -> Dict[str, Any]:
        """
        Save generation progress to the database.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            status: Generation status (in_progress, completed, failed)
            java_class_info: Information about generated Java class
            error_message: Error message if generation failed

        Returns:
            Dict[str, Any]: Result of saving progress
        """
        try:
            conn = sqlite3.connect(self.knowledge_db.db_path)
            cursor = conn.cursor()

            # Create table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS code_generation_progress (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    program_id TEXT,
                    chunk_name TEXT,
                    status TEXT,
                    java_class_info TEXT,
                    error_message TEXT,
                    generation_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(program_id, chunk_name)
                )
            """)

            # Insert or update progress
            cursor.execute("""
                INSERT OR REPLACE INTO code_generation_progress
                (program_id, chunk_name, status, java_class_info, error_message)
                VALUES (?, ?, ?, ?, ?)
            """, (
                program_id,
                chunk_name,
                status,
                json.dumps(java_class_info) if java_class_info else None,
                error_message
            ))

            conn.commit()
            conn.close()

            if self.logger:
                self.logger.info(f"Saved generation progress for {program_id}.{chunk_name}: {status}")

            return {"success": True}

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error saving generation progress: {str(e)}")
            return {"error": str(e), "success": False}

    def get_generation_progress(self, program_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get code generation progress from the database.

        Args:
            program_id: Optional program ID to filter by

        Returns:
            Dict[str, Any]: Generation progress information
        """
        try:
            conn = sqlite3.connect(self.knowledge_db.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            if program_id:
                cursor.execute("""
                    SELECT * FROM code_generation_progress
                    WHERE program_id = ?
                    ORDER BY generation_timestamp DESC
                """, (program_id,))
            else:
                cursor.execute("""
                    SELECT * FROM code_generation_progress
                    ORDER BY generation_timestamp DESC
                """)

            progress_records = cursor.fetchall()
            conn.close()

            progress_list = []
            for record in progress_records:
                record_dict = dict(record)
                if record_dict.get("java_class_info"):
                    try:
                        record_dict["java_class_info"] = json.loads(record_dict["java_class_info"])
                    except json.JSONDecodeError:
                        record_dict["java_class_info"] = None
                progress_list.append(record_dict)

            return {
                "progress_records": progress_list,
                "total_records": len(progress_list)
            }

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting generation progress: {str(e)}")
            return {"error": str(e)}

    def get_chunk_cobol_code(self, program_id: str, chunk_name: str) -> Dict[str, Any]:
        """
        Get the original COBOL code for a chunk for validation purposes.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk

        Returns:
            Dict[str, Any]: COBOL code information
        """
        try:
            chunk = self.knowledge_db.get_chunk_by_name(program_id, chunk_name)
            if not chunk:
                return {"error": f"Chunk {program_id}.{chunk_name} not found"}

            return {
                "chunk_id": f"{program_id}.{chunk_name}",
                "cobol_code": chunk.get("code", ""),
                "chunk_type": chunk.get("chunk_type", "")
            }

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting COBOL code: {str(e)}")
            return {"error": str(e)}

    def validate_target_against_source(self, program_id: str, chunk_name: str, target_code: str,
                                     source_language: str = "cobol", target_language: str = "java") -> Dict[str, Any]:
        """
        Validate generated target code against original source code using plugin system.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            target_code: Generated target code
            source_language: Source language (default: cobol)
            target_language: Target language (default: java)

        Returns:
            Dict[str, Any]: Validation results
        """
        try:
            # Get source code
            source_info = self.get_chunk_cobol_code(program_id, chunk_name)  # TODO: Make this language-agnostic
            if "error" in source_info:
                return source_info

            source_code = source_info["cobol_code"]  # TODO: Make this language-agnostic

            # Use plugin system for validation
            try:
                from src.platform.plugins.plugin_loader import get_plugin_loader
                plugin_loader = get_plugin_loader()

                # Get target plugin for validation
                target_plugin = plugin_loader.get_target_plugin(target_language)
                if target_plugin and hasattr(target_plugin, 'validate_against_source'):
                    return target_plugin.validate_against_source(
                        source_code, target_code, source_language, program_id, chunk_name
                    )

            except Exception as e:
                if self.logger:
                    self.logger.warning(f"Plugin-based validation failed: {str(e)}")

            # Basic validation if no plugin available
            return {
                "chunk_id": f"{program_id}.{chunk_name}",
                "validation_passed": True,
                "warnings": ["Plugin-based validation not available"],
                "suggestions": []
            }

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error validating target against source: {str(e)}")
            return {"error": str(e)}

    # Language-specific methods moved to plugins
    # _extract_perform_statements, _chunk_performs_target, _cobol_to_java_method_name
    # are now handled by the respective language and target plugins

    def _scan_directory_structure(self, directory: str) -> Dict[str, Any]:
        """
        Scan directory structure recursively.

        Args:
            directory: Directory to scan

        Returns:
            Dict[str, Any]: Directory structure
        """
        structure = {
            "type": "directory",
            "name": os.path.basename(directory),
            "children": []
        }

        try:
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)

                if os.path.isdir(item_path):
                    structure["children"].append(self._scan_directory_structure(item_path))
                else:
                    structure["children"].append({
                        "type": "file",
                        "name": item,
                        "size": os.path.getsize(item_path)
                    })
        except PermissionError:
            structure["error"] = "Permission denied"

        return structure

    def _find_java_files(self, directory: str) -> List[Dict[str, Any]]:
        """
        Find all Java files in a directory recursively.

        Args:
            directory: Directory to search

        Returns:
            List[Dict[str, Any]]: List of Java files with information
        """
        java_files = []

        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith('.java'):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, directory)

                    java_files.append({
                        "name": file,
                        "relative_path": rel_path,
                        "full_path": file_path,
                        "size": os.path.getsize(file_path)
                    })

        return java_files