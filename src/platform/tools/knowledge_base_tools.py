import json
from typing import List, Dict, Any, Optional

from langchain.tools.base import BaseTool
from langchain.tools import Tool

class KnowledgeBaseTools:
    """
    Tools for the knowledge miner agent to work with the knowledge database
    """

    def __init__(self, knowledge_db):
        self.knowledge_db = knowledge_db
        self.logger = None

    def set_logger(self, logger):
        """Set the logger for the tools"""
        self.logger = logger

    def create_tools(self) -> List[BaseTool]:
        """
        Create tools for the knowledge mining agent

        Returns:
            List[BaseTool]: List of tools for the agent
        """
        tools = [
            Tool(
                name="get_program_info",
                func=self.get_program_info,
                description="Get information about a program by its ID. Returns program details including language, size, etc."
            ),
            Tool(
                name="get_chunk_info",
                func=self.get_chunk_info,
                description="Get information about a code chunk by its program ID and chunk name. Returns chunk details including code."
            ),
            <PERSON>l(
                name="get_variable_info",
                func=self.get_variable_info,
                description="Get information about a variable by its name and program ID. Returns variable details."
            ),
            <PERSON><PERSON>(
                name="search_variables",
                func=self.search_variables,
                description="Search for variables in a program that match certain criteria."
            ),
            <PERSON>l(
                name="update_chunk_business_info",
                func=self.update_chunk_business_info,
                description="Update a chunk's business information including inputs, outputs, business name, and description."
            ),
            Tool(
                name="get_all_chunks_for_program",
                func=self.get_all_chunks_for_program,
                description="Get all chunks for a specific program ID."
            ),
            Tool(
                name="get_all_programs_by_language",
                func=self.get_all_programs_by_language,
                description="Get a list of all programs in the database for a specific language (e.g., 'cobol', 'java')."
            )
        ]

        return tools

    def get_program_info(self, program_id: str) -> str:
        """
        Get program information from the database

        Args:
            program_id: ID of the program

        Returns:
            str: JSON string with program information
        """
        try:
            program_info = self.knowledge_db.get_program_details(program_id)
            if program_info:
                # Return only the program info, not chunks and variables
                return json.dumps(program_info["program"])
            return f"Program with ID {program_id} not found"
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting program info: {str(e)}")
            return f"Error getting program info: {str(e)}"

    def get_chunk_info(self, program_id: str, chunk_name: str) -> str:
        """
        Get chunk information from the database

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk

        Returns:
            str: JSON string with chunk information
        """
        try:
            chunk = self.knowledge_db.get_chunk_by_name(program_id, chunk_name)
            if chunk:
                return json.dumps(chunk)
            return f"Chunk with name {chunk_name} not found in program {program_id}"
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting chunk info: {str(e)}")
            return f"Error getting chunk info: {str(e)}"

    def get_variable_info(self, program_id: str, variable_name: str) -> str:
        """
        Get variable information from the database

        Args:
            program_id: ID of the program
            variable_name: Name of the variable

        Returns:
            str: JSON string with variable information
        """
        try:
            variable = self.knowledge_db.get_variable_by_name(program_id, variable_name)
            if variable:
                return json.dumps(variable)
            return f"Variable with name {variable_name} not found in program {program_id}"
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting variable info: {str(e)}")
            return f"Error getting variable info: {str(e)}"

    def search_variables(self, program_id: str, name_pattern: Optional[str] = None) -> str:
        """
        Search for variables in a program that match the given pattern

        Args:
            program_id: ID of the program
            name_pattern: Pattern to match variable names (optional)

        Returns:
            str: JSON string with matching variables
        """
        try:
            variables = self.knowledge_db.search_variables(program_id, name_pattern)
            return json.dumps(variables)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error searching variables: {str(e)}")
            return f"Error searching variables: {str(e)}"

    def update_chunk_business_info(self, program_id: str, chunk_name: str,
                                   input_parameters: List[str], output_parameters: List[str],
                                   business_name: str, business_description: str) -> str:
        """
        Update business information for a chunk

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            input_parameters: List of input parameters
            output_parameters: List of output parameters
            business_name: Business-oriented name for the chunk
            business_description: Business description of what the chunk does

        Returns:
            str: Success or error message
        """
        try:
            # Prepare analysis data for the update
            analysis_data = {
                'input_parameters': input_parameters,
                'output_parameters': output_parameters,
                'business_name': business_name,
                'business_description': business_description
            }

            # Use the correct method name
            self.knowledge_db.update_chunk_analysis(
                program_id=program_id,
                chunk_name=chunk_name,
                analysis_data=analysis_data
            )
            return f"Successfully updated business info for chunk {chunk_name} in program {program_id}"
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error updating chunk business info: {str(e)}")
            return f"Error updating chunk business info: {str(e)}"

    def get_all_chunks_for_program(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Get all chunks for a specific program ID

        Args:
            program_id: ID of the program

        Returns:
            List[Dict[str, Any]]: List of chunks
        """
        try:
            return self.knowledge_db.get_chunks_by_program(program_id)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting chunks for program {program_id}: {str(e)}")
            return []

    def get_all_programs_by_language(self, language: str = "cobol") -> List[Dict[str, Any]]:
        """
        Get a list of all programs in the database for a specific language

        Args:
            language: Programming language to filter by (default: "cobol")

        Returns:
            List[Dict[str, Any]]: List of programs for the specified language
        """
        try:
            # Get all programs and filter by language
            all_programs = self.knowledge_db.get_all_programs()
            return [program for program in all_programs if program.get('language', '').lower() == language.lower()]
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting {language} programs: {str(e)}")
            return []