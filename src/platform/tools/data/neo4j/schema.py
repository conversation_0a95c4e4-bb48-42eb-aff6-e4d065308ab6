import logging
from typing import Dict, Any, List

from py2neo import Graph

class Neo4jSchema:
    """Neo4j schema definitions for COBOL parser"""
    
    def __init__(self):
        """Initialize the Neo4j schema"""
        self.logger = logging.getLogger(__name__)
    
    def create_constraints(self, graph: Graph) -> None:
        """Create constraints for Neo4j
        
        Args:
            graph: The Neo4j graph
        """
        # Create constraints for each node type
        # Using the newer Neo4j constraint syntax (Neo4j 4.x+)
        constraints = [
            "CREATE CONSTRAINT cobol_module_uuid IF NOT EXISTS FOR (n:CobolModule) REQUIRE n.uuid IS UNIQUE",
            "CREATE CONSTRAINT cobol_identification_division_uuid IF NOT EXISTS FOR (n:CobolIdentificationDivision) REQUIRE n.uuid IS UNIQUE",
            "CREATE CONSTRAINT cobol_environment_division_uuid IF NOT EXISTS FOR (n:CobolEnvironmentDivision) REQUIRE n.uuid IS UNIQUE",
            "CREATE CONSTRAINT cobol_data_division_uuid IF NOT EXISTS FOR (n:CobolDataDivision) REQUIRE n.uuid IS UNIQUE",
            "CREATE CONSTRAINT cobol_procedure_division_uuid IF NOT EXISTS FOR (n:CobolProcedureDivision) REQUIRE n.uuid IS UNIQUE",
            "CREATE CONSTRAINT cobol_section_uuid IF NOT EXISTS FOR (n:CobolSection) REQUIRE n.uuid IS UNIQUE",
            "CREATE CONSTRAINT cobol_paragraph_uuid IF NOT EXISTS FOR (n:CobolParagraph) REQUIRE n.uuid IS UNIQUE",
            "CREATE CONSTRAINT cobol_entry_paragraph_uuid IF NOT EXISTS FOR (n:CobolEntryParagraph) REQUIRE n.uuid IS UNIQUE"
        ]
        
        for constraint in constraints:
            try:
                graph.run(constraint)
            except Exception as e:
                self.logger.warning(f"Failed to create constraint: {str(e)}")
    
    def create_indexes(self, graph: Graph) -> None:
        """Create indexes for Neo4j
        
        Args:
            graph: The Neo4j graph
        """
        # Create indexes for common query fields
        indexes = [
            "CREATE INDEX cobol_module_module_id IF NOT EXISTS FOR (n:CobolModule) ON (n.module_id)",
            "CREATE INDEX cobol_identification_division_program_id IF NOT EXISTS FOR (n:CobolIdentificationDivision) ON (n.program_id)",
            "CREATE INDEX cobol_section_section_name IF NOT EXISTS FOR (n:CobolSection) ON (n.section_name)",
            "CREATE INDEX cobol_paragraph_paragraph_name IF NOT EXISTS FOR (n:CobolParagraph) ON (n.paragraph_name)",
            "CREATE INDEX cobol_entry_paragraph_entry_name IF NOT EXISTS FOR (n:CobolEntryParagraph) ON (n.entry_name)"
        ]
        
        for index in indexes:
            try:
                graph.run(index)
            except Exception as e:
                self.logger.warning(f"Failed to create index: {str(e)}")
    
    def map_node_properties(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Map node properties according to Neo4j schema
        
        Args:
            node: The node to map
            
        Returns:
            The mapped properties
        """
        # Filter out 'type' from properties, as it's used as the label
        properties = {k: v for k, v in node.items() if k != "type"}
        
        # Special handling for different node types
        node_type = node.get("type")
        
        # Convert boolean values to actual booleans
        if "is_unnamed" in properties:
            properties["is_unnamed"] = bool(properties["is_unnamed"])
        
        if "is_main_entry" in properties:
            properties["is_main_entry"] = bool(properties["is_main_entry"])
        
        # Any additional property transformations can be added here
        
        return properties
