import os
import logging
from typing import Dict, List, Any

from py2neo import Graph, Node

from .schema import Neo4jSchema

class Neo4jConnector:
    """Connector for Neo4j database"""

    def __init__(self):
        """Initialize the Neo4j connector"""
        self.url = os.getenv("NEO4J_URL", "bolt://localhost:7687")
        self.user = os.getenv("NEO4J_USER", "neo4j")
        self.password = os.getenv("NEO4J_PASSWORD", "12345678")
        self.batch_size = os.getenv("NEO4J_BATCH_SIZE", 100)
        self.logger = logging.getLogger(__name__)
        self.schema = Neo4jSchema()
        self.graph = None
        self.cached_nodes = []  # Cache of nodes for relationship lookups

    def connect(self) -> bool:
        """Connect to the Neo4j database
        
        Returns:
            True if connected successfully, False otherwise
        """
        try:
            self.graph = Graph(self.url, auth=(self.user, self.password))
            self.logger.debug(f"Connected to Neo4j database: {self.url}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to Neo4j database: {str(e)}")
            return False

    def create_schema(self) -> bool:
        """Create the Neo4j schema (constraints and indexes)

        Returns:
            True if schema created successfully, False otherwise
        """
        if not self.graph:
            self.logger.error("Not connected to Neo4j database")
            return False

        try:
            # Create constraints and indexes
            self.schema.create_constraints(self.graph)
            self.schema.create_indexes(self.graph)
            return True
        except Exception as e:
            self.logger.error(f"Failed to create Neo4j schema: {str(e)}")
            return False

    def store_ir(self, ir: Dict[str, Any]) -> bool:
        """Store the intermediate representation in Neo4j

        Args:
            ir: The intermediate representation

        Returns:
            True if stored successfully, False otherwise
        """
        if not self.graph:
            self.logger.error("Not connected to Neo4j database")
            return False

        nodes = ir.get("nodes", [])
        relationships = ir.get("relationships", [])

        # Make sure we have valid data
        if not isinstance(nodes, list):
            self.logger.error(f"Expected nodes to be a list, got {type(nodes)}")
            return False

        if not isinstance(relationships, list):
            self.logger.error(f"Expected relationships to be a list, got {type(relationships)}")
            return False

        # Log the contents of the IR for debugging
        self.logger.debug(f"IR nodes: {len(nodes)}")
        self.logger.debug(f"IR relationships: {len(relationships)}")

        # Cache the nodes for relationship processing
        self.cached_nodes = nodes.copy()

        try:
            # Start a transaction (makes no difference for nodes as we do our own tx)
            tx = self.graph.begin()

            # Store nodes and relationships
            self._store_nodes(nodes)
            self._store_relationships(relationships)

            # Commit the transaction
            tx.commit()

            # Clear the node cache after processing
            self.cached_nodes = []

            self.logger.info(f"Stored {len(nodes)} nodes and {len(relationships)} relationships in Neo4j")
            return True
        except Exception as e:
            self.logger.error(f"Failed to store IR in Neo4j: {str(e)}")
            return False

    def _store_nodes(self, nodes: List[Dict[str, Any]]) -> None:
        """Store nodes in Neo4j using batch operations

        Args:
            nodes: List of nodes from the intermediate representation
        """
        try:
            # Ensure nodes is a list of dictionaries
            if not isinstance(nodes, list):
                self.logger.error(f"Expected nodes to be a list, got {type(nodes)}")
                return

            # Group nodes by type for batch processing
            node_groups = {}
            for node in nodes:
                if not isinstance(node, dict):
                    self.logger.warning(f"Skipping non-dictionary node: {type(node)}")
                    continue

                node_type = node.get("type")
                if not node_type:
                    self.logger.warning(f"Skipping node with no type: {node}")
                    continue

                if node_type not in node_groups:
                    node_groups[node_type] = []
                node_groups[node_type].append(node)

            # Process each node type
            for node_type, node_list in node_groups.items():
                # Filter out 'type' from properties, as it's used as the label
                batch_data = []
                for node in node_list:
                    try:
                        # Ensure node has UUID
                        if not node.get("uuid"):
                            self.logger.warning(f"Node missing UUID, skipping: {node}")
                            continue

                        # Map properties
                        processed_props = self.schema.map_node_properties(node)
                        # The expected format is a single dict of properties, with a matcher
                        batch_data.append((processed_props, {"uuid": node.get("uuid")}))
                    except Exception as e:
                        self.logger.warning(f"Error processing node {node.get('uuid', 'unknown')}: {str(e)}")

                # Process nodes individually
                if batch_data:
                    try:
                        tx = self.graph.begin()
                        for props, matcher in batch_data:
                            # Create or merge node using a natural key instead of UUID
                            # Determine the appropriate merge key based on node type
                            if node_type == 'CobolModule':
                                query = f"""
                                MERGE (n:{node_type} {{module_id: $module_id}})
                                SET n += $props
                                RETURN n
                                """
                                self.graph.run(query, module_id=props.get('module_id', ''), props=props)
                            elif node_type == 'CobolIdentificationDivision':
                                query = f"""
                                MERGE (n:{node_type} {{module_id: $module_id}})
                                SET n += $props
                                RETURN n
                                """
                                self.graph.run(query, module_id=props.get('module_id', ''), props=props)
                            elif node_type in ['CobolEnvironmentDivision', 'CobolDataDivision',
                                               'CobolProcedureDivision']:
                                query = f"""
                                MERGE (n:{node_type} {{module_id: $module_id}})
                                SET n += $props
                                RETURN n
                                """
                                self.graph.run(query, module_id=props.get('module_id', ''), props=props)
                            elif node_type == 'CobolSection':
                                query = f"""
                                MERGE (n:{node_type} {{module_id: $module_id, section_name: $section_name}})
                                SET n += $props
                                RETURN n
                                """
                                self.graph.run(query, module_id=props.get('module_id', ''),
                                               section_name=props.get('section_name', ''), props=props)
                            elif node_type in ['CobolParagraph', 'CobolEntryParagraph']:
                                query = f"""
                                MERGE (n:{node_type} {{module_id: $module_id, paragraph_name: $paragraph_name}})
                                SET n += $props
                                RETURN n
                                """
                                self.graph.run(query, module_id=props.get('module_id', ''),
                                               paragraph_name=props.get('paragraph_name', ''), props=props)
                            else:
                                # Fallback to creating a node with UUID for unknown types
                                node = Node(node_type, **props)
                                tx.create(node)
                        tx.commit()
                        self.logger.info(f"Successfully stored {len(batch_data)} nodes of type {node_type}")
                    except Exception as e:
                        self.logger.error(f"Error creating nodes of type {node_type}: {str(e)}")
                else:
                    self.logger.warning(f"No valid nodes found for type {node_type}")
        except Exception as e:
            self.logger.error(f"Error in _store_nodes: {str(e)}")

    def _store_relationships(self, relationships: List[Dict[str, Any]]) -> None:
        """Store relationships in Neo4j using batch operations

        Args:
            relationships: List of relationships from the intermediate representation
        """
        try:
            # Ensure relationships is a list of dictionaries
            if not isinstance(relationships, list):
                self.logger.error(f"Expected relationships to be a list, got {type(relationships)}")
                return

            # Group relationships by type for batch processing
            relationship_groups = {}
            for rel in relationships:
                if not isinstance(rel, dict):
                    self.logger.warning(f"Skipping non-dictionary relationship: {type(rel)}")
                    continue

                rel_type = rel.get("type")
                if not rel_type:
                    self.logger.warning(f"Skipping relationship with no type: {rel}")
                    continue

                if rel_type not in relationship_groups:
                    relationship_groups[rel_type] = []
                relationship_groups[rel_type].append(rel)

            # Process each relationship type
            for rel_type, rel_list in relationship_groups.items():
                # Process relationships
                for rel in rel_list:
                    try:
                        from_uuid = rel.get("from_uuid")
                        to_uuid = rel.get("to_uuid")

                        # Validate source and target UUIDs
                        if not from_uuid or not to_uuid:
                            self.logger.warning(f"Relationship missing source or target UUID, skipping: {rel}")
                            continue

                        # Get properties (excluding type and the UUIDs)
                        # TODO Unused variable - should we delete it?
                        properties = {k: v for k, v in rel.items()
                                      if k not in ["type", "from_uuid", "to_uuid"]}

                        # Look up source and target nodes in the cached nodes
                        source_node = None
                        target_node = None

                        for node in self.cached_nodes:
                            if node.get("uuid") == from_uuid:
                                source_node = node
                            if node.get("uuid") == to_uuid:
                                target_node = node

                            # Break early if we found both nodes
                            if source_node and target_node:
                                break

                        # Only proceed if we found both nodes
                        if not source_node or not target_node:
                            self.logger.warning(f"Could not find source or target node for relationship")
                            continue

                        # Get node types and key properties
                        source_type = source_node.get("type")
                        target_type = target_node.get("type")

                        # We need to create Cypher queries based on node types
                        if source_type == "CobolModule":
                            source_match = f"MATCH (source:{source_type} {{module_id: $source_module_id}})"
                            source_params = {
                                "source_module_id": source_node.get("module_id")
                            }
                        elif source_type in ["CobolIdentificationDivision", "CobolEnvironmentDivision",
                                             "CobolDataDivision", "CobolProcedureDivision"]:
                            source_match = f"MATCH (source:{source_type} {{module_id: $source_module_id}})"
                            source_params = {
                                "source_module_id": source_node.get("module_id")
                            }
                        elif source_type == "CobolSection":
                            source_match = f"MATCH (source:{source_type} {{module_id: $source_module_id, section_name: $source_section_name}})"
                            source_params = {
                                "source_module_id": source_node.get("module_id"),
                                "source_section_name": source_node.get("section_name")
                            }
                        elif source_type in ["CobolParagraph", "CobolEntryParagraph"]:
                            source_match = f"MATCH (source:{source_type} {{module_id: $source_module_id, paragraph_name: $source_paragraph_name}})"
                            source_params = {
                                "source_module_id": source_node.get("module_id"),
                                "source_paragraph_name": source_node.get("paragraph_name")
                            }
                        else:
                            # Default to UUID match for unknown types
                            source_match = "MATCH (source) WHERE source.uuid = $source_uuid"
                            source_params = {
                                "source_uuid": source_node.get("uuid")
                            }

                        if target_type == "CobolModule":
                            target_match = f"MATCH (target:{target_type} {{module_id: $target_module_id}})"
                            target_params = {
                                "target_module_id": target_node.get("module_id")
                            }
                        elif target_type in ["CobolIdentificationDivision", "CobolEnvironmentDivision",
                                             "CobolDataDivision", "CobolProcedureDivision"]:
                            target_match = f"MATCH (target:{target_type} {{module_id: $target_module_id}})"
                            target_params = {
                                "target_module_id": target_node.get("module_id")
                            }
                        elif target_type == "CobolSection":
                            target_match = f"MATCH (target:{target_type} {{module_id: $target_module_id, section_name: $target_section_name}})"
                            target_params = {
                                "target_module_id": target_node.get("module_id"),
                                "target_section_name": target_node.get("section_name")
                            }
                        elif target_type in ["CobolParagraph", "CobolEntryParagraph"]:
                            target_match = f"MATCH (target:{target_type} {{module_id: $target_module_id, paragraph_name: $target_paragraph_name}})"
                            target_params = {
                                "target_module_id": target_node.get("module_id"),
                                "target_paragraph_name": target_node.get("paragraph_name")
                            }
                        else:
                            # Default to UUID match for unknown types
                            target_match = "MATCH (target) WHERE target.uuid = $target_uuid"
                            target_params = {
                                "target_uuid": target_node.get("uuid")
                            }

                        # Build the merge query
                        # Neo4j doesn't allow using $props directly in MERGE patterns
                        # Just create the relationship without properties since they're mostly empty anyway
                        query = f"""
                        {source_match}
                        {target_match}
                        MERGE (source)-[r:{rel_type}]->(target)
                        RETURN count(r)
                        """

                        # Combine parameters
                        all_params = {**source_params, **target_params}

                        # Execute the query
                        try:
                            self.graph.run(query, **all_params)
                        except Exception as e:
                            self.logger.warning(f"Error creating relationship: {str(e)}")
                            try:
                                # Fallback to UUID-based match
                                fallback_query = f"""
                                MATCH (source) WHERE source.uuid = $source_uuid
                                MATCH (target) WHERE target.uuid = $target_uuid
                                MERGE (source)-[r:{rel_type}]->(target)
                                RETURN count(r)
                                """
                                fallback_params = {
                                    "source_uuid": source_node.get("uuid"),
                                    "target_uuid": target_node.get("uuid")
                                }
                                self.graph.run(fallback_query, **fallback_params)
                            except Exception as e2:
                                self.logger.error(f"Fallback relationship creation failed: {str(e2)}")
                    except Exception as e:
                        self.logger.warning(f"Error processing relationship: {str(e)}")

                self.logger.info(f"Successfully stored relationships of type {rel_type}")
        except Exception as e:
            self.logger.error(f"Error in _store_relationships: {str(e)}")

    def clear_database(self) -> bool:
        """Clear all data from the Neo4j database

        Returns:
            True if cleared successfully, False otherwise
        """
        if not self.graph:
            self.logger.error("Not connected to Neo4j database")
            return False

        try:
            self.graph.run("MATCH (n) DETACH DELETE n")
            self.logger.info("Cleared all data from Neo4j database")
            return True
        except Exception as e:
            self.logger.error(f"Failed to clear Neo4j database: {str(e)}")
            return False

    def disconnect(self) -> None:
        """Disconnect from the Neo4j database"""
        self.graph = None
        self.logger.debug("Disconnected from Neo4j database")

    def get_nodes_by_cobol_element_type(self, cobol_el_type: str) -> list[Node]:
        try:
            query = f"MATCH (n) WHERE (n:{cobol_el_type}) RETURN n"

            result = self.graph.run(query).data()
            cobol_nodes = [record["n"] for record in result]
            self.logger.info(f"Retrieved {len(cobol_nodes)} cobol nodes of {cobol_el_type} type")

            return cobol_nodes
        except Exception as e:
            self.logger.error(f"Error while retrieving callable nodes of {cobol_el_type} type: {str(e)}")
            return []



    def get_callable_leaves(self) -> list[Node] | None:
        try:
            query = f"""
            MATCH (n) 
            WHERE (n:CobolParagraph OR n:CobolEntryParagraph OR n:MissingCobolModule OR n:CobolModule) AND NOT (n)-->() 
            RETURN n
            """

            result = self.graph.run(query).data()
            callable_leaves = [record["n"] for record in result]
            self.logger.info(f"Retrieved {len(callable_leaves)} callable leaf nodes")

            return callable_leaves
        except Exception as e:
            self.logger.error(f"Error while retrieving callable leaf nodes: {str(e)}")
            return None

    def get_direct_callers(self, callees: list[Node]) -> list[Node] | None:
        try:

            callee_ids = [node.identity for node in callees]

            query = f"""
            MATCH (parent)-[r:CALLS|PERFORMS]->(child) 
            WHERE id(child) IN $callee_ids 
            RETURN DISTINCT parent            
            """

            result = self.graph.run(query, callee_ids=callee_ids).data()
            callers = [record["parent"] for record in result]

            return callers

        except Exception as e:
            self.logger.error(f"Error while retrieving callers for nodes list: {str(e)}")
            return None

    def get_nodes_perform_call_relations(self, node_uuid: str) -> List[Dict[str, Any]]:
        try:
            query = f"""
            MATCH (n)-[r:CALLS|PERFORMS]->(m) 
            WHERE n.uuid = $node_uuid 
            RETURN DISTINCT n, type(r) as rel_type, m            
            """

            result = self.graph.run(query, node_uuid=node_uuid).data()
            relations = [{"from_node": record["n"], "rel_type": record["rel_type"], "to_node": record["m"]} for record in result]

            return relations

        except Exception as e:
            self.logger.error(f"Error while retrieving callers for the node {node_uuid} list: {str(e)}" )
            return []


    def get_nodes_call_relations(self, node_uuid: str) -> List[Dict[str, Any]]:
        try:
            query = f"""
            MATCH (n)-[r:CALLS]->(m) 
            WHERE n.uuid = $node_uuid 
            RETURN DISTINCT n, type(r) as rel_type, m            
            """

            result = self.graph.run(query, node_uuid=node_uuid).data()
            relations = [{"from_node": record["n"], "rel_type": record["rel_type"], "to_node": record["m"]} for record in result]

            return relations

        except Exception as e:
            self.logger.error(f"Error while retrieving callers for the node {node_uuid} list: {str(e)}" )
            return []


    def get_nodes_perform_relations(self, node_uuid: str) -> List[Dict[str, Any]]:
        try:
            query = f"""
            MATCH (n)-[r:PERFORMS]->(m) 
            WHERE n.uuid = $node_uuid 
            RETURN DISTINCT n, type(r) as rel_type, m            
            """

            result = self.graph.run(query, node_uuid=node_uuid).data()
            relations = [{"from_node": record["n"], "rel_type": record["rel_type"], "to_node": record["m"]} for record in result]

            return relations

        except Exception as e:
            self.logger.error(f"Error while retrieving callers for the node {node_uuid} list: {str(e)}" )
            return []

