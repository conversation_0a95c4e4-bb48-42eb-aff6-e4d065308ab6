import logging
import kuzu
from pathlib import Path
from typing import Dict, List, Any

from kuzu import Connection

from config.constants import OUT_DIR
from .schema import KuzuSchema


# noinspection SqlNoDataSourceInspection,PyMethodMayBeStatic,SqlDialectInspection
class KuzuConnector:
    """Connector for Kuzu database"""

    def __init__(self):
        """Initialize the Neo4j connector """

        self.connection: Connection | None = None
        self.in_transaction = False
        self.db_path: Path | None = OUT_DIR / "kuzu.db"
        self.logger = logging.getLogger(__name__)
        self.schema = KuzuSchema()
        self.cached_nodes = []  # Cache of nodes for relationship lookups

    # def connect(self, db_path: Path = OUT_DIR / "kuzu.db") -> bool:
    def connect(self) -> bool:
        """Connect to the Neo4j database
        
        Returns:
            True if connected successfully, False otherwise
        """

        try:
            if not self.connection:
                db = kuzu.Database(self.db_path)
                self.connection = kuzu.Connection(db)
                self.logger.info(f"Connected to the Kuzu database: {self.db_path}")

            # elif self.db_path != db_path:
            #     self.db_path = db_path
            #     db = kuzu.Database(self.db_path)
            #     self.connection = kuzu.Connection(db)
            #     self.logger.info(f"Connected to the Kuzu database: {self.db_path}")

            return True

        except Exception as e:
            self.logger.error(f"Failed to connect to the Kuze database: {str(e)}")
            return False

    def create_schema(self) -> bool:
        """Create the Neo4j schema (constraints and indexes)

        Returns:
            True if schema created successfully, False otherwise
        """
        if not self.connection:
            self.logger.error("Not connected to Neo4j database")
            return False

        try:
            # Create constraints and indexes
            self.schema.create_schema(self.connection)
            return True
        except Exception as e:
            self.logger.error(f"Failed to create Neo4j schema: {str(e)}")
            return False

    def store_ir(self, ir: Dict[str, Any]) -> bool:
        """Store the intermediate representation in Neo4j

        Args:
            ir: The intermediate representation

        Returns:
            True if stored successfully, False otherwise
        """
        if not self.connection:
            self.logger.error("Not connected to the graph database")
            return False

        nodes = ir.get("nodes", [])
        relationships = ir.get("relationships", [])

        # Make sure we have valid data
        if not isinstance(nodes, list):
            self.logger.error(f"Expected nodes to be a list, got {type(nodes)}")
            return False

        if not isinstance(relationships, list):
            self.logger.error(f"Expected relationships to be a list, got {type(relationships)}")
            return False

        # Log the contents of the IR for debugging
        self.logger.debug(f"IR nodes: {len(nodes)}")
        self.logger.debug(f"IR relationships: {len(relationships)}")

        # Cache the nodes for relationship processing
        self.cached_nodes = nodes.copy()

        try:
            # Start a transaction (makes no difference for nodes as we do our own tx)
            self._begin_transaction()

            # Store nodes and relationships
            self._store_nodes(nodes)
            self._store_relationships(relationships)  ## TODO remate to _store_includes_relationships()

            # Commit the transaction
            self._commit_transaction()

            # Clear the node cache after processing
            self.cached_nodes = []

            self.logger.info(f"Stored {len(nodes)} nodes and {len(relationships)} relationships in Kuzu graph db")
            return True
        except Exception as e:
            self._rollback_transaction()
            self.logger.error(f"Failed to store IR in Neo4j: {str(e)}")
            return False

    def _store_nodes(self, nodes: List[Dict[str, Any]]) -> None:
        """Store nodes in Neo4j using batch operations

        Args:
            nodes: List of nodes from the intermediate representation
        """
        try:
            # Ensure nodes is a list of dictionaries
            if not isinstance(nodes, list):
                self.logger.error(f"Expected nodes to be a list, got {type(nodes)}")
                return

            # Group nodes by type for batch processing
            node_groups = {}
            for node in nodes:
                if not isinstance(node, dict):
                    self.logger.warning(f"Skipping non-dictionary node: {type(node)}")
                    continue

                node_type = node.get("type")
                if not node_type:
                    self.logger.warning(f"Skipping node with no type: {node}")
                    continue

                if node_type not in node_groups:
                    node_groups[node_type] = []
                node_groups[node_type].append(node)

            # Process each node type
            for node_type, node_list in node_groups.items():
                # Filter out 'type' from properties, as it's used as the label
                batch_data = []
                for node in node_list:
                    try:
                        # Ensure node has UUID
                        if not node.get("uuid"):
                            self.logger.warning(f"Node missing UUID, skipping: {node}")
                            continue

                        # Map properties
                        processed_props = self.schema.map_node_properties(node)
                        # The expected format is a single dict of properties, with a matcher
                        batch_data.append((processed_props, {"uuid": node.get("uuid")}))
                    except Exception as e:
                        self.logger.warning(f"Error processing node {node.get('uuid', 'unknown')}: {str(e)}")

                # Process nodes individually
                if batch_data:
                    try:
                        for props, matcher in batch_data:
                            # Create or merge node using a natural key instead of UUID
                            # Determine the appropriate merge key based on node type
                            self._create_or_merge_if_exists(node_type, props)

                        self.logger.info(f"Successfully stored {len(batch_data)} nodes of type {node_type}")
                    except Exception as e:
                        self.logger.error(f"Error creating nodes of type {node_type}: {str(e)}")
                else:
                    self.logger.warning(f"No valid nodes found for type {node_type}")
        except Exception as e:
            self.logger.error(f"Error in _store_nodes: {str(e)}")

    def _store_relationships(self, relationships: List[Dict[str, Any]]) -> None:
        """Store relationships in Neo4j using batch operations

        Args:
            relationships: List of relationships from the intermediate representation
        """
        try:
            # Ensure relationships is a list of dictionaries
            if not isinstance(relationships, list):  ## TODO >> Duplicated code => ?? extract to a method ??
                self.logger.error(f"Expected relationships to be a list, got {type(relationships)}")
                return

            # Group relationships by type for batch processing
            relationship_groups = {}
            for rel in relationships:
                if not isinstance(rel, dict):
                    self.logger.warning(f"Skipping non-dictionary relationship: {type(rel)}")
                    continue

                rel_type = rel.get("type")
                if not rel_type:
                    self.logger.warning(f"Skipping relationship with no type: {rel}")
                    continue

                if rel_type not in relationship_groups:
                    relationship_groups[rel_type] = []
                relationship_groups[rel_type].append(rel)

            # Process each relationship type
            for rel_type, rel_list in relationship_groups.items():
                # Process relationships
                for rel in rel_list:
                    try:
                        from_uuid = rel.get("from_uuid")
                        to_uuid = rel.get("to_uuid")

                        # Validate source and target UUIDs
                        if not from_uuid or not to_uuid:
                            self.logger.warning(f"Relationship missing source or target UUID, skipping: {rel}")
                            continue

                        # Get properties (excluding type and the UUIDs)
                        # TODO Unused variable - should we delete it?
                        properties = {k: v for k, v in rel.items()
                                      if k not in ["type", "from_uuid", "to_uuid"]}

                        # Look up source and target nodes in the cached nodes
                        source_node = None
                        target_node = None

                        for node in self.cached_nodes:
                            if node.get("uuid") == from_uuid:
                                source_node = node
                            if node.get("uuid") == to_uuid:
                                target_node = node

                            # Break early if we found both nodes
                            if source_node and target_node:
                                break

                        # Only proceed if we found both nodes
                        if not source_node or not target_node:
                            self.logger.warning(f"Could not find source or target node for relationship")
                            continue

                        # Get node types and key properties
                        source_type = source_node.get("type")
                        target_type = target_node.get("type")

                        # We need to create Cypher queries based on node types
                        if source_type == "CobolModule":
                            source_match = f"MATCH (source:{source_type} {{module_id: $source_module_id}})"
                            source_params = {
                                "source_module_id": source_node.get("module_id")
                            }
                        elif source_type in ["CobolIdentificationDivision", "CobolEnvironmentDivision",
                                             "CobolDataDivision", "CobolProcedureDivision"]:
                            source_match = f"MATCH (source:{source_type} {{module_id: $source_module_id}})"
                            source_params = {
                                "source_module_id": source_node.get("module_id")
                            }
                        elif source_type == "CobolSection":
                            source_match = f"MATCH (source:{source_type} {{module_id: $source_module_id, section_name: $source_section_name}})"
                            source_params = {
                                "source_module_id": source_node.get("module_id"),
                                "source_section_name": source_node.get("section_name")
                            }
                        elif source_type in ["CobolParagraph", "CobolEntryParagraph"]:
                            source_match = f"MATCH (source:{source_type} {{module_id: $source_module_id, paragraph_name: $source_paragraph_name}})"
                            source_params = {
                                "source_module_id": source_node.get("module_id"),
                                "source_paragraph_name": source_node.get("paragraph_name")
                            }
                        else:
                            # Default to UUID match for unknown types
                            source_match = "MATCH (source) WHERE source.uuid = $source_uuid"
                            source_params = {
                                "source_uuid": source_node.get("uuid")
                            }

                        if target_type == "CobolModule":
                            target_match = f"MATCH (target:{target_type} {{module_id: $target_module_id}})"
                            target_params = {
                                "target_module_id": target_node.get("module_id")
                            }
                        elif target_type in ["CobolIdentificationDivision", "CobolEnvironmentDivision",
                                             "CobolDataDivision", "CobolProcedureDivision"]:
                            target_match = f"MATCH (target:{target_type} {{module_id: $target_module_id}})"
                            target_params = {
                                "target_module_id": target_node.get("module_id")
                            }
                        elif target_type == "CobolSection":
                            target_match = f"MATCH (target:{target_type} {{module_id: $target_module_id, section_name: $target_section_name}})"
                            target_params = {
                                "target_module_id": target_node.get("module_id"),
                                "target_section_name": target_node.get("section_name")
                            }
                        elif target_type in ["CobolParagraph", "CobolEntryParagraph"]:
                            target_match = f"MATCH (target:{target_type} {{module_id: $target_module_id, paragraph_name: $target_paragraph_name}})"
                            target_params = {
                                "target_module_id": target_node.get("module_id"),
                                "target_paragraph_name": target_node.get("paragraph_name")
                            }
                        else:
                            # Default to UUID match for unknown types
                            target_match = "MATCH (target) WHERE target.uuid = $target_uuid"
                            target_params = {
                                "target_uuid": target_node.get("uuid")
                            }

                        # Build the create query ## TODO check if merging is potentially needed
                        query = f"""
                        {source_match}
                        {target_match}
                        CREATE (source)-[:{rel_type} {{uuid: $rel_uuid, from_uuid: $source_uuid, to_uuid: $target_uuid}}]->(target)
                        """

                        # Combine parameters
                        all_params = {**source_params, **target_params}

                        # Execute the query
                        try:
                            self.connection.execute(query, all_params)
                        except Exception as e:
                            self.logger.warning(f"Error creating relationship: {str(e)}")
                            try:
                                # Fallback to UUID-based match
                                fallback_query = f"""
                                MATCH (source) WHERE source.uuid = $source_uuid
                                MATCH (target) WHERE target.uuid = $target_uuid
                                MERGE (source)-[r:{rel_type}]->(target)
                                RETURN count(r)
                                """
                                fallback_query = f"""
                                MATCH (source) WHERE source.uuid = $source_uuid
                                MATCH (target) WHERE target.uuid = $target_uuid
                                CREATE (source)-[:{rel_type} {{uuid: $rel_uuid, from_uuid: $source_uuid, to_uuid: $target_uuid}}]->(target)
                                """
                                fallback_params = {
                                    "source_uuid": source_node.get("uuid"),
                                    "target_uuid": target_node.get("uuid")
                                }
                                self.connection.execute(fallback_query, **fallback_params)
                            except Exception as e2:
                                self.logger.error(f"Fallback relationship creation failed: {str(e2)}")
                    except Exception as e:
                        self.logger.warning(f"Error processing relationship: {str(e)}")

                self.logger.info(f"Successfully stored relationships of type {rel_type}")
        except Exception as e:
            self.logger.error(f"Error in _store_relationships: {str(e)}")

    def clear_database(self) -> bool:
        """Clear all data from the Neo4j database

        Returns:
            True if cleared successfully, False otherwise
        """
        if not self.connection:
            self.logger.error("Not connected to Neo4j database")
            return False

        try:
            self.connection.execute("MATCH (n) DETACH DELETE n")
            self.logger.info("Cleared all data from Neo4j database")
            return True
        except Exception as e:
            self.logger.error(f"Failed to clear Neo4j database: {str(e)}")
            return False

    def disconnect(self) -> None:
        """Disconnect from the Neo4j database"""
        self.connection = None
        self.logger.info("Disconnected from Kuzu database")

    def get_nodes_by_cobol_element_type(self, cobol_el_type: str) -> list[dict[str, Any]]:
        try:
            query = f"MATCH (n) WHERE (n:{cobol_el_type}) RETURN n"

            result = self.connection.execute(query)
            cobol_nodes = [record["n"] for record in result]
            self.logger.info(f"Retrieved {len(cobol_nodes)} cobol nodes of {cobol_el_type} type")

            return cobol_nodes
        except Exception as e:
            self.logger.error(f"Error while retrieving callable nodes of {cobol_el_type} type: {str(e)}")
            return []

    def get_callable_leaves(self) -> dict[str, Any] | None:
        try:
            query = """
            MATCH (n) 
            WHERE (label(n) = 'CobolParagraph' OR label(n) = 'CobolEntryParagraph' OR label(n) = 'MissingCobolModule' OR label(n) = 'CobolModule') 
            AND NOT (n)-->() 
            RETURN n.uuid AS uuid, n.module_id AS module_id, label(n) AS type_label, n.name AS name, n.full_text AS full_text, n.parent_name AS parent_name
            """
            response = self.connection.execute(query)
            nodes = []
            while response.has_next():
                uuid, module_id, type_label, name, full_text, parent_name = response.get_next()
                nodes.append({
                    'uuid': uuid,
                    'module_id': module_id,
                    'type_label': type_label,
                    'name': name,
                    'full_text': full_text,
                    'parent_name': parent_name
                })

            return nodes if nodes else None

        except Exception as e:
            self.logger.error(f"Error while retrieving callable leaf nodes: {str(e)}")
            return None

    def get_direct_callers(self, callee_uuids: list[str]) -> list[dict[str, Any]]:
        try:

            nodes = []

            # Get callers via CALLS relationships
            calls_query = """
            MATCH (p)-[r:CALLS]->(child) 
            WHERE child.uuid IN $callee_uuids 
            RETURN DISTINCT p.uuid AS uuid, p.module_id AS module_id, label(p) AS type_label, p.name AS name, p.full_text AS full_text, p.parent_name AS parent_name
            """
            calls_result = self.connection.execute(calls_query, {"callee_uuids": callee_uuids})
            while calls_result.has_next():
                uuid, module_id, type_label, name, full_text, parent_name = calls_result.get_next()
                nodes.append({
                    'uuid': uuid,
                    'module_id': module_id,
                    'type_label': type_label,
                    'name': name,
                    'full_text': full_text,
                    'parent_name': parent_name
                })

            # Get callers via PERFORMS relationships
            performs_query = """
            MATCH (p)-[r:PERFORMS]->(child) 
            WHERE child.uuid IN $callee_uuids 
            RETURN DISTINCT p.uuid AS uuid, p.module_id AS module_id, label(p) AS type_label, p.name AS name, p.full_text AS full_text, p.parent_name AS parent_name
            """
            performs_result = self.connection.execute(performs_query, {"callee_uuids": callee_uuids})

            while performs_result.has_next():
                uuid, module_id, type_label, name, full_text, parent_name = performs_result.get_next()
                nodes.append({
                    'uuid': uuid,
                    'module_id': module_id,
                    'type_label': type_label,
                    'name': name,
                    'full_text': full_text,
                    'parent_name': parent_name
                })

            return nodes if nodes else []

        except Exception as e:
            self.logger.error(f"Error while retrieving callable leaf nodes: {str(e)}")
            return []

    def get_nodes_perform_call_relations(self, node_uuid: str) -> List[Dict[str, Any]]:
        try:
            query = f"""
            MATCH (n)-[r:CALLS|PERFORMS]->(m) 
            WHERE n.uuid = $node_uuid 
            RETURN DISTINCT n, type(r) as rel_type, m            
            """

            result = self.connection.execute(query, {'node_uuid': node_uuid})
            relations = [{"from_node": record["n"], "rel_type": record["rel_type"], "to_node": record["m"]} for record
                         in result]

            return relations

        except Exception as e:
            self.logger.error(f"Error while retrieving callers for the node {node_uuid} list: {str(e)}")
            return []

    def get_nodes_call_relations(self, node_uuid: str) -> List[Dict[str, Any]]:
        try:
            query = f"""
            MATCH (n)-[r:PERFORMS]->(m) 
            WHERE n.uuid = $node_uuid 
            RETURN DISTINCT n.uuid as from_uuid, 'CALLS' as rel_type, m.uuid as to_uuid
            """

            result = self.connection.execute(query, {'node_uuid': node_uuid})
            # relations = [{"from_node": record["n"], "rel_type": record["rel_type"], "to_node": record["m"]} for record
            #              in result]
            relations = []
            while result.has_next():
                from_uuid, rel_type, to_uuid = result.get_next()
                relations.append({
                    'from_uuid': from_uuid,
                    'rel_type': rel_type,
                    'to_uuid': to_uuid
                })

            return relations if relations else None
        except Exception as e:
            self.logger.error(f"Error while retrieving callers for the node {node_uuid} list: {str(e)}")
            return []

    def get_nodes_perform_relations(self, node_uuid: str) -> List[Dict[str, Any]] | None:
        try:
            query = f"""
            MATCH (n)-[r:PERFORMS]->(m) 
            WHERE n.uuid = $node_uuid 
            RETURN DISTINCT n.uuid as from_uuid, 'PERFORMS' as rel_type, m.uuid as to_uuid
            """

            result = self.connection.execute(query, {'node_uuid': node_uuid})
            # relations = [{"from_node": record["n"], "rel_type": record["rel_type"], "to_node": record["m"]} for record
            #              in result]
            relations = []
            while result.has_next():
                from_uuid, rel_type, to_uuid = result.get_next()
                relations.append({
                    'from_uuid': from_uuid,
                    'rel_type': rel_type,
                    'to_uuid': to_uuid
                })

            return relations if relations else None

        except Exception as e:
            self.logger.error(f"Error while retrieving callers for the node {node_uuid} list: {str(e)}")
            return []

    def _begin_transaction(self) -> None:
        if self.in_transaction:
            return
        query = "BEGIN TRANSACTION;"
        self.connection.execute(query)
        self.in_transaction = True

    def _commit_transaction(self) -> None:
        query = "COMMIT;"
        self.connection.execute(query)
        self.in_transaction = False

    def _rollback_transaction(self) -> None:
        query = "ROLLBACK;"
        self.connection.execute(query)
        self.in_transaction = False

    def _create_or_merge_if_exists(self, node_type: str, props: Dict[str, Any]):
        """

        """
        matching_fields = ['module_id', 'section_name', 'paragraph_name']
        # noinspection PyBroadException
        lookup_expression = None
        update_set_expression = None
        create_set_expression = None
        try:
            return_expression = "RETURN n;"
            matching_fields = [matching_field for matching_field in matching_fields if matching_field in props.keys()]
            matching_expressions = [f"n.{matching_field}=${matching_field}" for matching_field in matching_fields]
            where_clause = " AND ".join(matching_expressions).strip()
            match_expression = f"MATCH (n:{node_type}) WHERE {where_clause}"

            """
            Lookup expression, e.g.:
            MATCH (n:CobolModule) WHERE n.module_id = $module_id 
            RETURN n
            """
            lookup_expression = match_expression + '\n' + return_expression
            lookup_parameters = {key: value for key, value in props.items() if key in matching_fields}

            """
            Set expression, e.g.:
            CREATE (n:CobolModule {uuid: $uuid, module_id: $module_id, file_name: $file_name, full_text: $full_text}) 
            RETURN n
            """
            all_fields = [key for key, value in props.items()]
            create_set_expressions = [f"{field}:${field}" for field in all_fields]
            create_set_expression = f"CREATE (n:{node_type} {{{','.join(create_set_expressions).strip()}}})"
            create_set_expression = create_set_expression + '\n' + return_expression
            create_parameters = {**props}

            """ 
            MATCH (n:CobolModule) WHERE n.module_id = $module_id SET n.file_name=$file_name, n.full_text=$full_text 
            RETURN n
            """
            update_fields = [field for field in all_fields if field != "uuid"
                             if field not in matching_fields and field != "uuid"]
            update_set_expressions = [f"n.{field}=${field}" for field in update_fields]
            update_set_expression = f"SET " + ",".join(update_set_expressions).strip()
            update_set_expression = match_expression + '\n' + update_set_expression + '\n' + return_expression
            update_parameters = {key: value for key, value in props.items() if key in update_fields}
            update_parameters = {**lookup_parameters, **update_parameters}

            # Step 1: Try to find an existing node
            result = self.connection.execute(lookup_expression, lookup_parameters)
            if result.has_next():
                # Step 2a: Update an existing node
                self.connection.execute(update_set_expression, update_parameters)
            else:
                # Step 2b: Create a new node
                self.connection.execute(create_set_expression, create_parameters)

        except Exception as e:
            expression = lookup_expression if lookup_expression else (
                update_set_expression if update_set_expression else create_set_expression)
            self.logger.error(
                f"Create or update of n:{node_type} failed while generating CYPHER query {expression}."
            )
