import logging

from kuzu import Connection

class CallTreeSchema:
    """Neo4j schema definitions for COBOL call tree analysis"""
    
    def __init__(self):
        """Initialize the Call Tree schema"""
        self.logger = logging.getLogger(__name__)
    
    def create_schema(self, connection: Connection) -> None:
        """Create constraints for Neo4j
        
        Args:
            connection: The Neo4j graph
        """
        # Create tables for each node type
        tables = [
            """
            CREATE NODE TABLE MissingCobolModule (
                uuid STRING PRIMARY KEY,
                module_id STRING
            );
            """,
            """
            CREATE NODE TABLE MissingCobolSection (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                section_name STRING,
                parent_name STRING,
                is_unnamed BOOLEAN
            );
            """,
            """
            CREATE NODE TABLE MissingCobolParagraph (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                paragraph_name STRING,
                parent_name STRING,
                is_unnamed BOOLEAN
            );
            """,
            """
            CREATE NODE TABLE CobolVariableTarget (
                uuid STRING PRIMARY KEY,
                module_id STRING,
                name STRING
            );
            """
        ]

        for table in tables:
            try:
                connection.execute(table)
            except Exception as e:
                if "already exists" in str(e).lower():
                    self.logger.warning(f"Table already exists {table} skipping creation. {str(e)}")
                else:
                    self.logger.warning(f"Failed to create table {table} {str(e)}")

    def register_relationship_types(self, connection: Connection) -> None:
        """Register relationship types in Neo4j
        
        Args:
            connection: The Neo4j graph
        """
        # No specific action needed for relationship types in Neo4j,
        # they are created when relationships are created.
        # This method exists for future extensions if needed.
        pass