import logging
from typing import Dict, Any

from kuzu import Connection


# noinspection PyMethodMayBeStatic
class KuzuSchema:
    """Neo4j schema definitions for COBOL parser"""

    def __init__(self):
        """Initialize the Neo4j schema"""
        self.logger = logging.getLogger(__name__)

    def create_schema(self, connection: Connection) -> None:
        """Create constraints for Neo4j
        
            "CREATE NODE TABLE CobolModule (uuid STRING, PRIMARY KEY (uuid));"
        Args:
            connection: The Neo4j graph
        """
        # Create constraints for each node type
        tables = [
            """
            CREATE NODE TABLE MissingCobolModule (
                uuid STRING PRIMARY KEY,
                module_id STRING,
                call_text STRING,
                is_dynamic BOOLEAN
            );
            """,
            """
            CREATE NODE TABLE MissingCobolSection (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                section_name STRING,
                parent_name STRING,
                is_unnamed BOOLEAN
            );
            """,
            """
            CREATE NODE TABLE MissingCobolParagraph (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                paragraph_name STRING,
                parent_name STRING,
                is_unnamed BOOLEAN
            );
            """,
            """
            CREATE NODE TABLE CobolVariableTarget (
                uuid STRING PRIMARY KEY,
                module_id STRING,
                name STRING
            );
            """,
            """
            CREATE NODE TABLE CobolModule (
                uuid STRING PRIMARY KEY,
                module_id STRING,
                file_name STRING,
                full_text STRING
            );
            """,
            """
            CREATE NODE TABLE CobolIdentificationDivision (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                full_text STRING,
                program_id STRING
            );
            """,
            """
            CREATE NODE TABLE CobolEnvironmentDivision (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                full_text STRING
            );
            """,
            """
            CREATE NODE TABLE CobolDataDivision (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                full_text STRING
            );
            """,
            """
            CREATE NODE TABLE CobolProcedureDivision (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                full_text STRING
            );
            """,
            """
            CREATE NODE TABLE CobolSection (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                section_name STRING,
                full_text STRING,
                parent_name STRING,
                is_unnamed BOOLEAN
            );
            """,
            """
            CREATE NODE TABLE CobolParagraph (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                paragraph_name STRING,
                full_text STRING,
                parent_name STRING,
                is_unnamed BOOLEAN
            );
            """,
            """
            CREATE NODE TABLE CobolEntryParagraph (
                uuid STRING PRIMARY KEY,
                name STRING,
                module_id STRING,
                paragraph_name STRING,
                entry_name STRING,
                full_text STRING,
                parent_name STRING,
                is_main_entry BOOLEAN,
                is_unnamed BOOLEAN
            );
            """,
            """
            CREATE REL TABLE INCLUDES (
                FROM CobolModule TO CobolIdentificationDivision,
                FROM CobolModule TO CobolEnvironmentDivision,
                FROM CobolModule TO CobolDataDivision,
                FROM CobolModule TO CobolProcedureDivision,
                FROM CobolDataDivision TO CobolSection,
                FROM CobolProcedureDivision TO CobolSection,
                FROM CobolSection TO CobolParagraph,
                FROM CobolSection TO CobolEntryParagraph,
                uuid STRING PRIMARY KEY,
                from_uuid STRING,
                to_uuid STRING
            );
            """,
            """
            CREATE REL TABLE PERFORMS (
                FROM CobolEntryParagraph TO CobolParagraph,
                FROM CobolEntryParagraph TO CobolSection,
                FROM CobolParagraph TO CobolSection,
                FROM CobolParagraph TO CobolParagraph,
                uuid STRING PRIMARY KEY,
                from_uuid STRING,
                to_uuid STRING,
                perform_text STRING,
                is_conditional BOOLEAN
            );
            """,
            """
            CREATE REL TABLE CALLS (
                FROM CobolEntryParagraph TO CobolModule,
                FROM CobolEntryParagraph TO MissingCobolModule,
                FROM CobolParagraph TO CobolModule,
                FROM CobolParagraph TO MissingCobolModule,
                uuid STRING PRIMARY KEY,
                from_uuid STRING,
                to_uuid STRING,
                call_text STRING,
                is_dynamic BOOLEAN
            );
            """,
            """
            CREATE REL TABLE MODULE_CALLS (
                FROM CobolModule TO CobolModule,
                FROM CobolModule TO MissingCobolModule,
                uuid STRING PRIMARY KEY,
                from_uuid STRING,
                to_uuid STRING
            );
            """,
            """
            CREATE REL TABLE GOTO (
                FROM CobolEntryParagraph TO CobolParagraph,
                FROM CobolEntryParagraph TO CobolSection,
                FROM CobolParagraph TO CobolSection,
                FROM CobolParagraph TO CobolParagraph,
                uuid STRING PRIMARY KEY,
                from_uuid STRING,
                to_uuid STRING,
                goto_text STRING
            );
            """
        ]
        for table in tables:
            try:
                connection.execute(table)
            except Exception as e:
                if "already exists" in str(e).lower():
                    self.logger.warning(f"Table already exists {table} skipping creation. {str(e)}")
                else:
                    self.logger.warning(f"Failed to create table {table} {str(e)}")

    def map_node_properties(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Map node properties according to Neo4j schema
        
        Args:
            node: The node to map
            
        Returns:
            The mapped properties
        """
        # Filter out 'type' from properties, as it's used as the label
        properties = {k: v for k, v in node.items() if k != "type"}

        # Special handling for different node types
        node_type = node.get("type")

        # Convert boolean values to actual booleans
        if "is_unnamed" in properties:
            properties["is_unnamed"] = bool(properties["is_unnamed"])

        if "is_main_entry" in properties:
            properties["is_main_entry"] = bool(properties["is_main_entry"])

        # Any additional property transformations can be added here

        return properties
