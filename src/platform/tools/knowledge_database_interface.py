"""
Knowledge Database interface with proper method exposure.
This module ensures that the mapping manager methods are properly accessible.
"""

import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


class KnowledgeDatabaseInterface:
    """
    Interface wrapper for KnowledgeDatabase that ensures proper method exposure.
    This class acts as a bridge between the code generator and the underlying database components.
    """

    def __init__(self, knowledge_db):
        """
        Initialize the interface wrapper.

        Args:
            knowledge_db: The underlying KnowledgeDatabase instance
        """
        self.knowledge_db = knowledge_db
        self.logger = logging.getLogger(__name__)

    def __getattr__(self, name):
        """
        Delegate attribute access to the underlying knowledge database.
        This ensures backward compatibility for existing method calls.
        """
        return getattr(self.knowledge_db, name)

    # COBOL-Java Mapping Methods
    def get_cobol_java_mappings(self, program_id: str, chunk_name: str = None, mapping_type: str = None) -> Dict[str, str]:
        """
        Get COBOL to Java mappings.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk (optional)
            mapping_type: Type of mapping (optional)

        Returns:
            Dict[str, str]: COBOL name -> Java name mappings
        """
        try:
            if hasattr(self.knowledge_db, 'mapping_manager') and self.knowledge_db.mapping_manager:
                return self.knowledge_db.mapping_manager.get_cobol_java_mappings(program_id, chunk_name, mapping_type)
            else:
                self.logger.warning("No mapping_manager available in knowledge_db")
                return {}
        except Exception as e:
            self.logger.error(f"Error getting COBOL-Java mappings: {str(e)}")
            return {}

    def save_cobol_java_mappings(self, program_id: str, chunk_name: str, mappings: Dict[str, Any]):
        """
        Save COBOL to Java mappings.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            mappings: Mappings to save
        """
        try:
            if hasattr(self.knowledge_db, 'mapping_manager') and self.knowledge_db.mapping_manager:
                self.knowledge_db.mapping_manager.save_cobol_java_mapping(program_id, chunk_name, mappings)
            else:
                self.logger.warning("No mapping_manager available in knowledge_db")
        except Exception as e:
            self.logger.error(f"Error saving COBOL-Java mappings: {str(e)}")

    # Java Data Structure Methods
    def get_java_data_structures(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Get Java data structures for a program.

        Args:
            program_id: ID of the program

        Returns:
            List[Dict[str, Any]]: Java data structures
        """
        try:
            if hasattr(self.knowledge_db, 'mapping_manager') and self.knowledge_db.mapping_manager:
                return self.knowledge_db.mapping_manager.get_java_data_structures(program_id)
            else:
                self.logger.warning("No mapping_manager available in knowledge_db")
                return []
        except Exception as e:
            self.logger.error(f"Error getting Java data structures: {str(e)}")
            return []

    def save_java_data_structure(self, program_id: str, structure_info: Dict[str, Any]):
        """
        Save Java data structure information.

        Args:
            program_id: ID of the program
            structure_info: Structure information to save
        """
        try:
            if hasattr(self.knowledge_db, 'mapping_manager') and self.knowledge_db.mapping_manager:
                self.knowledge_db.mapping_manager.save_java_data_structure(program_id, structure_info)
            else:
                self.logger.warning("No mapping_manager available in knowledge_db")
        except Exception as e:
            self.logger.error(f"Error saving Java data structure: {str(e)}")

    # Business Name Mapping Methods
    def get_business_name_mappings(self, program_id: str) -> Dict[str, Dict[str, str]]:
        """
        Get business name mappings for data class generation.

        Args:
            program_id: ID of the program

        Returns:
            Dict[str, Dict[str, str]]: COBOL name -> business info mappings
        """
        try:
            if hasattr(self.knowledge_db, 'mapping_manager') and self.knowledge_db.mapping_manager:
                return self.knowledge_db.mapping_manager.get_business_name_mappings(program_id)
            else:
                # Fallback to direct database call
                variables = self.knowledge_db.get_data_definitions_by_program(program_id)
                result = {}
                for var in variables:
                    name = var.get('name', '')
                    business_name = var.get('business_name', '')
                    description = var.get('description', '')
                    if name and business_name:
                        result[name] = {
                            'business_name': business_name,
                            'description': description
                        }
                return result
        except Exception as e:
            self.logger.error(f"Error getting business name mappings: {str(e)}")
            return {}

    def get_data_definitions_by_program(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Get data definitions (variables) for a program.

        Args:
            program_id: ID of the program

        Returns:
            List[Dict[str, Any]]: List of variable definitions
        """
        try:
            # Delegate to the underlying knowledge database
            return self.knowledge_db.get_data_definitions_by_program(program_id)
        except Exception as e:
            self.logger.error(f"Error getting data definitions: {str(e)}")
            return []

    # Java Field Mapping Methods
    def save_java_field_mappings(self, program_id: str, field_mappings: List[Dict[str, Any]]):
        """
        Save Java field mappings.

        Args:
            program_id: ID of the program
            field_mappings: List of field mappings
        """
        try:
            if hasattr(self.knowledge_db, 'mapping_manager') and self.knowledge_db.mapping_manager:
                self.knowledge_db.mapping_manager.save_java_field_mappings(program_id, field_mappings)
            else:
                self.logger.warning("No mapping_manager available in knowledge_db")
        except Exception as e:
            self.logger.error(f"Error saving Java field mappings: {str(e)}")

    def get_java_field_mappings(self, program_id: str, java_class: str = None) -> List[Dict[str, Any]]:
        """
        Get Java field mappings.

        Args:
            program_id: ID of the program
            java_class: Java class name to filter by (optional)

        Returns:
            List[Dict[str, Any]]: List of field mappings
        """
        try:
            if hasattr(self.knowledge_db, 'mapping_manager') and self.knowledge_db.mapping_manager:
                return self.knowledge_db.mapping_manager.get_java_field_mappings(program_id, java_class)
            else:
                self.logger.warning("No mapping_manager available in knowledge_db")
                return []
        except Exception as e:
            self.logger.error(f"Error getting Java field mappings: {str(e)}")
            return []

    # Copybook and Chunk Methods
    def get_chunks_by_type(self, chunk_type: str) -> List[Dict[str, Any]]:
        """
        Get chunks by type.

        Args:
            chunk_type: Type of chunks to retrieve

        Returns:
            List[Dict[str, Any]]: List of chunks
        """
        try:
            if hasattr(self.knowledge_db, 'query_manager') and self.knowledge_db.query_manager:
                return self.knowledge_db.query_manager.get_chunks_by_type(chunk_type)
            else:
                # Fallback to direct database call
                return self.knowledge_db.get_chunks_by_type(chunk_type)
        except Exception as e:
            self.logger.error(f"Error getting chunks by type: {str(e)}")
            return []

    def get_chunks_by_program(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Get all chunks for a program.

        Args:
            program_id: ID of the program

        Returns:
            List[Dict[str, Any]]: List of chunks
        """
        try:
            return self.knowledge_db.get_chunks_by_program(program_id)
        except Exception as e:
            self.logger.error(f"Error getting chunks by program: {str(e)}")
            return []

    # Program Management Methods
    def get_program_details(self, program_id: str) -> Optional[Dict[str, Any]]:
        """
        Get program details.

        Args:
            program_id: ID of the program

        Returns:
            Optional[Dict[str, Any]]: Program details or None
        """
        try:
            if hasattr(self.knowledge_db, 'program_manager') and self.knowledge_db.program_manager:
                return self.knowledge_db.program_manager.get_program_details(program_id)
            else:
                return self.knowledge_db.get_program_details(program_id)
        except Exception as e:
            self.logger.error(f"Error getting program details: {str(e)}")
            return None

    def get_all_programs(self) -> List[Dict[str, Any]]:
        """
        Get all programs.

        Returns:
            List[Dict[str, Any]]: List of programs
        """
        try:
            if hasattr(self.knowledge_db, 'program_manager') and self.knowledge_db.program_manager:
                return self.knowledge_db.program_manager.get_all_programs()
            else:
                return self.knowledge_db.get_all_programs()
        except Exception as e:
            self.logger.error(f"Error getting all programs: {str(e)}")
            return []

    # Database Statistics
    def get_mapping_statistics(self) -> Dict[str, Any]:
        """
        Get mapping statistics.

        Returns:
            Dict[str, Any]: Mapping statistics
        """
        try:
            if hasattr(self.knowledge_db, 'mapping_manager') and self.knowledge_db.mapping_manager:
                return self.knowledge_db.mapping_manager.get_mapping_statistics()
            else:
                return {}
        except Exception as e:
            self.logger.error(f"Error getting mapping statistics: {str(e)}")
            return {}

    def get_database_statistics(self) -> Dict[str, Any]:
        """
        Get database statistics.

        Returns:
            Dict[str, Any]: Database statistics
        """
        try:
            if hasattr(self.knowledge_db, 'query_manager') and self.knowledge_db.query_manager:
                return self.knowledge_db.query_manager.get_database_statistics()
            else:
                return {}
        except Exception as e:
            self.logger.error(f"Error getting database statistics: {str(e)}")
            return {}

    # Utility Methods
    def ensure_mapping_manager_available(self) -> bool:
        """
        Ensure mapping manager is available and properly initialized.

        Returns:
            bool: True if mapping manager is available, False otherwise
        """
        if not hasattr(self.knowledge_db, 'mapping_manager'):
            self.logger.error("knowledge_db does not have mapping_manager attribute")
            return False

        if not self.knowledge_db.mapping_manager:
            self.logger.error("knowledge_db.mapping_manager is None")
            return False

        return True

    def debug_knowledge_db_structure(self):
        """
        Debug method to print the structure of the knowledge database.
        Useful for troubleshooting integration issues.
        """
        self.logger.info("=== DEBUGGING KNOWLEDGE DATABASE STRUCTURE ===")
        self.logger.info(f"Knowledge DB type: {type(self.knowledge_db)}")
        self.logger.info(f"Knowledge DB attributes: {dir(self.knowledge_db)}")

        if hasattr(self.knowledge_db, 'mapping_manager'):
            self.logger.info(f"mapping_manager type: {type(self.knowledge_db.mapping_manager)}")
            if self.knowledge_db.mapping_manager:
                self.logger.info(f"mapping_manager attributes: {dir(self.knowledge_db.mapping_manager)}")
            else:
                self.logger.error("mapping_manager is None")
        else:
            self.logger.error("No mapping_manager attribute found")

        if hasattr(self.knowledge_db, 'query_manager'):
            self.logger.info(f"query_manager type: {type(self.knowledge_db.query_manager)}")
        else:
            self.logger.warning("No query_manager attribute found")

        if hasattr(self.knowledge_db, 'program_manager'):
            self.logger.info(f"program_manager type: {type(self.knowledge_db.program_manager)}")
        else:
            self.logger.warning("No program_manager attribute found")

        self.logger.info("=== END DEBUGGING ===")


def create_knowledge_database_interface(knowledge_db):
    """
    Factory function to create a properly configured knowledge database interface.

    Args:
        knowledge_db: The underlying KnowledgeDatabase instance

    Returns:
        KnowledgeDatabaseInterface: Configured interface
    """
    interface = KnowledgeDatabaseInterface(knowledge_db)

    # Debug the structure if needed
    if logger.isEnabledFor(logging.DEBUG):
        interface.debug_knowledge_db_structure()

    # Verify that essential components are available
    if not interface.ensure_mapping_manager_available():
        logger.warning("Mapping manager is not available - some functionality may be limited")

    return interface