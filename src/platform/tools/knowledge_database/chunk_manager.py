"""
Chunk management functionality for KnowledgeDatabase.
Handles CRUD operations for code chunk records.
"""
import json
import sqlite3
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger("tools.knowledge_database.chunk_manager")


class ChunkManager:
    """
    Manages code chunk records in the knowledge database.
    """

    def __init__(self, db_path: str):
        """
        Initialize the chunk manager.

        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path

    def insert_chunks(self, program_id: str, chunks: List[Dict[str, Any]]):
        """
        Insert chunks for a specific program.

        Args:
            program_id: Identifier of the program
            chunks: List of chunk dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            for chunk in chunks:
                cursor.execute('''
                    INSERT INTO chunks 
                    (program_id, chunk_type, chunk_name, chunk_size, code, metadata) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    program_id,
                    chunk.get('chunk_type', 'UNKNOWN'),
                    chunk.get('chunk_name', 'UNKNOWN'),
                    len(chunk.get('code', '').splitlines()),
                    chunk.get('code', ''),
                    json.dumps(chunk.get('metadata', {}))
                ))
            conn.commit()
            logger.info(f"Inserted {len(chunks)} chunks for program {program_id}")

    def get_chunk_by_name(self, program_id: str, chunk_name: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific chunk by program ID and chunk name.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk

        Returns:
            Chunk dictionary or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM chunks 
                WHERE program_id = ? AND chunk_name = ?
            ''', (program_id, chunk_name))

            chunk = cursor.fetchone()
            if chunk:
                chunk_dict = dict(chunk)
                # Parse JSON fields
                for field in ['metadata', 'input_parameters', 'output_parameters']:
                    if chunk_dict.get(field):
                        try:
                            chunk_dict[field] = json.loads(chunk_dict[field])
                        except json.JSONDecodeError:
                            chunk_dict[field] = {} if field == 'metadata' else []
                return chunk_dict
            return None

    def get_chunks_by_program(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Get all chunks for a specific program.

        Args:
            program_id: ID of the program

        Returns:
            List of chunk dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM chunks 
                WHERE program_id = ? 
                ORDER BY chunk_name
            ''', (program_id,))

            chunks = cursor.fetchall()
            result = []
            
            for chunk in chunks:
                chunk_dict = dict(chunk)
                # Parse JSON fields
                for field in ['metadata', 'input_parameters', 'output_parameters']:
                    if chunk_dict.get(field):
                        try:
                            chunk_dict[field] = json.loads(chunk_dict[field])
                        except json.JSONDecodeError:
                            chunk_dict[field] = {} if field == 'metadata' else []
                result.append(chunk_dict)
            
            return result

    def get_chunks_by_type(self, chunk_type: str) -> List[Dict[str, Any]]:
        """
        Get chunks by type.

        Args:
            chunk_type: Type of chunks to retrieve

        Returns:
            List of chunk dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM chunks 
                WHERE chunk_type = ? 
                ORDER BY program_id, chunk_name
            ''', (chunk_type,))

            chunks = cursor.fetchall()
            result = []
            
            for chunk in chunks:
                chunk_dict = dict(chunk)
                # Parse JSON fields
                for field in ['metadata', 'input_parameters', 'output_parameters']:
                    if chunk_dict.get(field):
                        try:
                            chunk_dict[field] = json.loads(chunk_dict[field])
                        except json.JSONDecodeError:
                            chunk_dict[field] = {} if field == 'metadata' else []
                result.append(chunk_dict)
            
            return result

    def update_chunk_analysis(self, program_id: str, chunk_name: str, analysis_data: Dict[str, Any]):
        """
        Update chunk with analysis results.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            analysis_data: Analysis results to store
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Prepare update fields
            update_fields = []
            update_values = []

            if 'business_name' in analysis_data:
                update_fields.append('business_name = ?')
                update_values.append(analysis_data['business_name'])

            if 'business_description' in analysis_data:
                update_fields.append('business_description = ?')
                update_values.append(analysis_data['business_description'])

            if 'input_parameters' in analysis_data:
                update_fields.append('input_parameters = ?')
                update_values.append(json.dumps(analysis_data['input_parameters']))

            if 'output_parameters' in analysis_data:
                update_fields.append('output_parameters = ?')
                update_values.append(json.dumps(analysis_data['output_parameters']))

            if 'business_logic' in analysis_data:
                update_fields.append('business_logic = ?')
                update_values.append(analysis_data['business_logic'])

            if 'functional_spec' in analysis_data:
                update_fields.append('functional_spec = ?')
                update_values.append(analysis_data['functional_spec'])

            if 'analysis_status' in analysis_data:
                update_fields.append('analysis_status = ?')
                update_values.append(analysis_data['analysis_status'])

            # Always update analysis timestamp
            update_fields.append('analysis_timestamp = CURRENT_TIMESTAMP')

            if update_fields:
                update_values.extend([program_id, chunk_name])
                query = f'''
                    UPDATE chunks 
                    SET {', '.join(update_fields)}
                    WHERE program_id = ? AND chunk_name = ?
                '''
                cursor.execute(query, update_values)
                conn.commit()
                logger.info(f"Updated analysis for chunk {chunk_name} in program {program_id}")

    def delete_chunk(self, program_id: str, chunk_name: str) -> bool:
        """
        Delete a specific chunk.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk

        Returns:
            True if chunk was deleted, False if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                DELETE FROM chunks 
                WHERE program_id = ? AND chunk_name = ?
            ''', (program_id, chunk_name))

            deleted_count = cursor.rowcount
            conn.commit()

            if deleted_count > 0:
                logger.info(f"Deleted chunk {chunk_name} from program {program_id}")
                return True
            return False

    def get_chunks_by_analysis_status(self, status: str) -> List[Dict[str, Any]]:
        """
        Get chunks by analysis status.

        Args:
            status: Analysis status to filter by

        Returns:
            List of chunk dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM chunks 
                WHERE analysis_status = ? 
                ORDER BY program_id, chunk_name
            ''', (status,))

            chunks = cursor.fetchall()
            result = []
            
            for chunk in chunks:
                chunk_dict = dict(chunk)
                # Parse JSON fields
                for field in ['metadata', 'input_parameters', 'output_parameters']:
                    if chunk_dict.get(field):
                        try:
                            chunk_dict[field] = json.loads(chunk_dict[field])
                        except json.JSONDecodeError:
                            chunk_dict[field] = {} if field == 'metadata' else []
                result.append(chunk_dict)
            
            return result

    def search_chunks(self, search_term: str) -> List[Dict[str, Any]]:
        """
        Search chunks by name, business name, or description.

        Args:
            search_term: Term to search for

        Returns:
            List of matching chunk dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            search_pattern = f"%{search_term}%"
            cursor.execute('''
                SELECT * FROM chunks 
                WHERE chunk_name LIKE ? 
                   OR business_name LIKE ? 
                   OR business_description LIKE ?
                ORDER BY program_id, chunk_name
            ''', (search_pattern, search_pattern, search_pattern))

            chunks = cursor.fetchall()
            result = []
            
            for chunk in chunks:
                chunk_dict = dict(chunk)
                # Parse JSON fields
                for field in ['metadata', 'input_parameters', 'output_parameters']:
                    if chunk_dict.get(field):
                        try:
                            chunk_dict[field] = json.loads(chunk_dict[field])
                        except json.JSONDecodeError:
                            chunk_dict[field] = {} if field == 'metadata' else []
                result.append(chunk_dict)
            
            return result

    def get_chunk_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about chunks in the database.

        Returns:
            Dictionary containing chunk statistics
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Total chunks
            cursor.execute("SELECT COUNT(*) FROM chunks")
            total_chunks = cursor.fetchone()[0]

            # Chunks by type
            cursor.execute("""
                SELECT chunk_type, COUNT(*) as count 
                FROM chunks 
                GROUP BY chunk_type 
                ORDER BY count DESC
            """)
            chunks_by_type = dict(cursor.fetchall())

            # Chunks by analysis status
            cursor.execute("""
                SELECT analysis_status, COUNT(*) as count 
                FROM chunks 
                GROUP BY analysis_status 
                ORDER BY count DESC
            """)
            chunks_by_status = dict(cursor.fetchall())

            # Average chunk size
            cursor.execute("SELECT AVG(chunk_size) FROM chunks WHERE chunk_size > 0")
            avg_chunk_size = cursor.fetchone()[0] or 0

            return {
                'total_chunks': total_chunks,
                'chunks_by_type': chunks_by_type,
                'chunks_by_status': chunks_by_status,
                'average_chunk_size': round(avg_chunk_size, 2)
            }
