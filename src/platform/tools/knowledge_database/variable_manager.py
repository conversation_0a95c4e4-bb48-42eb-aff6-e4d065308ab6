"""
Variable/Data definition management functionality for KnowledgeDatabase.
Handles CRUD operations for variable and data definition records with Java mapping support.
"""
import json
import sqlite3
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger("tools.knowledge_database.variable_manager")


class VariableManager:
    """
    Manages variable and data definition records in the knowledge database.
    """

    def __init__(self, db_path: str):
        """
        Initialize the variable manager.

        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path

    def insert_data_definitions(self, program_id: str, data_items: List[Dict[str, Any]]):
        """
        Insert data definitions extracted from source code.

        Args:
            program_id: Identifier of the program
            data_items: List of data item dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for item in data_items:
                # Make a copy of the item to avoid modifying the original
                item_copy = item.copy()

                # Rename some fields to match database schema
                if 'cobol_name' in item_copy:
                    item_copy['name'] = item_copy.pop('cobol_name')

                # Map 'type' field to 'data_type' if present
                if 'type' in item_copy:
                    item_copy['data_type'] = item_copy.pop('type')

                if 'parameter_type' in item_copy:
                    item_copy['data_type'] = item_copy.pop('parameter_type')

                item_copy['source_file_name'] = str(item_copy.get('source_file_name', 'unknown'))

                # Convert lists to strings for database storage
                if 'possible_values' in item_copy and isinstance(item_copy['possible_values'], list):
                    item_copy['possible_values'] = json.dumps(item_copy['possible_values'])

                # Convert boolean fields to integers
                for bool_field in ['is_signed', 'is_filler', 'is_equ']:
                    if bool_field in item_copy:
                        item_copy[bool_field] = 1 if item_copy[bool_field] else 0

                # Convert complex structures to JSON strings
                for complex_field in ['occurs']:
                    if complex_field in item_copy and isinstance(item_copy[complex_field], (dict, list)):
                        item_copy[complex_field] = json.dumps(item_copy[complex_field])

                # Handle Java mapping fields if present
                java_fields = ['java_annotations']
                for java_field in java_fields:
                    if java_field in item_copy and isinstance(item_copy[java_field], list):
                        item_copy[java_field] = json.dumps(item_copy[java_field])

                # Always use the program_id parameter, not the one from the item
                item_copy['program_id'] = program_id

                # Filter out fields that don't belong in the variables table
                allowed_fields = {
                    'program_id', 'name', 'data_type', 'description', 'scope', 'business_name', 'level', 'length',
                    'default_value', 'possible_values', 'item_type', 'is_signed', 'is_filler', 'is_equ', 'occurs',
                    'redefines', 'storage_type', 'decimals', 'source_file_name', 'metadata', 'top_parent_datastructure_name',
                    'parent_name', 'java_name', 'java_class', 'parent_java_class', 'full_qualified_name',
                    'java_data_type', 'java_annotations', 'is_java_entity_field', 'java_mapping_notes', 'java_generation_timestamp'
                }

                filtered_item = {k: v for k, v in item_copy.items() if k in allowed_fields}

                # Build the query dynamically
                columns = []
                values = []
                placeholders = []

                for key, value in filtered_item.items():
                    columns.append(key)
                    values.append(value)
                    placeholders.append('?')

                query = f'''
                    INSERT INTO variables
                    ({", ".join(columns)})
                    VALUES ({", ".join(placeholders)})
                '''

                try:
                    cursor.execute(query, values)
                except sqlite3.Error as e:
                    logger.error(f"Error inserting data definition {filtered_item.get('name')}: {e}")
                    # Continue with next item

            conn.commit()
            logger.info(f"Inserted {len(data_items)} data definitions for program {program_id}")

    def insert_cobol_data_definitions(self, program_id: str, data_items: List[Dict[str, Any]]):
        """
        Legacy method for backward compatibility.
        Use insert_data_definitions instead.
        """
        return self.insert_data_definitions(program_id, data_items)

    def save_data_definition(self, data_item: Dict[str, Any]):
        """
        Save a single data definition.

        Args:
            data_item: Data definition dictionary
        """
        self.insert_data_definitions(data_item.get('program_id', 'UNKNOWN'), [data_item])

    def get_data_definitions_by_program(self, program_id: str, include_java_mappings: bool = True) -> List[Dict[str, Any]]:
        """
        Get all data definitions for a program, optionally including Java mapping information.

        Args:
            program_id: ID of the program
            include_java_mappings: Whether to include Java mapping fields

        Returns:
            List of data definition dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                           SELECT * FROM variables
                           WHERE program_id = ?
                           ORDER BY name
                           ''', (program_id,))

            variables = cursor.fetchall()
            result = []

            for var in variables:
                var_dict = dict(var)

                # Parse JSON fields
                json_fields = ['possible_values', 'occurs', 'metadata']
                if include_java_mappings:
                    json_fields.append('java_annotations')

                for field in json_fields:
                    if var_dict.get(field):
                        try:
                            var_dict[field] = json.loads(var_dict[field])
                        except json.JSONDecodeError:
                            pass

                # Convert integer boolean fields back to boolean
                bool_fields = ['is_signed', 'is_filler', 'is_equ']
                if include_java_mappings:
                    bool_fields.append('is_java_entity_field')

                for bool_field in bool_fields:
                    if bool_field in var_dict and var_dict[bool_field] is not None:
                        var_dict[bool_field] = bool(var_dict[bool_field])

                result.append(var_dict)

            return result

    def get_variables_with_java_mappings(self, program_id: str, java_class: str = None) -> List[Dict[str, Any]]:
        """
        Get variables that have Java mappings.

        Args:
            program_id: ID of the program
            java_class: Optional Java class filter

        Returns:
            List of variables with Java mapping information
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = '''
                    SELECT * FROM variables
                    WHERE program_id = ? AND java_name IS NOT NULL \
                    '''
            params = [program_id]

            if java_class:
                query += ' AND java_class = ?'
                params.append(java_class)

            query += ' ORDER BY java_class, name'

            cursor.execute(query, params)
            variables = cursor.fetchall()
            result = []

            for var in variables:
                var_dict = dict(var)

                # Parse JSON fields
                for field in ['possible_values', 'occurs', 'metadata', 'java_annotations']:
                    if var_dict.get(field):
                        try:
                            var_dict[field] = json.loads(var_dict[field])
                        except json.JSONDecodeError:
                            pass

                # Convert integer boolean fields back to boolean
                for bool_field in ['is_signed', 'is_filler', 'is_equ', 'is_java_entity_field']:
                    if bool_field in var_dict and var_dict[bool_field] is not None:
                        var_dict[bool_field] = bool(var_dict[bool_field])

                result.append(var_dict)

            return result

    def search_data_definitions(self, search_term: str, include_java: bool = True) -> List[Dict[str, Any]]:
        """
        Search for data definitions by name, business name, or Java name.

        Args:
            search_term: Term to search for
            include_java: Whether to include Java field searches

        Returns:
            List of matching data definition dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            search_pattern = f"%{search_term}%"

            if include_java:
                cursor.execute('''
                               SELECT * FROM variables
                               WHERE name LIKE ?
                                  OR business_name LIKE ?
                                  OR description LIKE ?
                                  OR java_name LIKE ?
                                  OR java_class LIKE ?
                               ORDER BY program_id, name
                               ''', (search_pattern, search_pattern, search_pattern, search_pattern, search_pattern))
            else:
                cursor.execute('''
                               SELECT * FROM variables
                               WHERE name LIKE ?
                                  OR business_name LIKE ?
                                  OR description LIKE ?
                               ORDER BY program_id, name
                               ''', (search_pattern, search_pattern, search_pattern))

            variables = cursor.fetchall()
            result = []

            for var in variables:
                var_dict = dict(var)

                # Parse JSON fields
                for field in ['possible_values', 'occurs', 'metadata', 'java_annotations']:
                    if var_dict.get(field):
                        try:
                            var_dict[field] = json.loads(var_dict[field])
                        except json.JSONDecodeError:
                            pass

                # Convert integer boolean fields back to boolean
                for bool_field in ['is_signed', 'is_filler', 'is_equ', 'is_java_entity_field']:
                    if bool_field in var_dict and var_dict[bool_field] is not None:
                        var_dict[bool_field] = bool(var_dict[bool_field])

                result.append(var_dict)

            return result

    def get_structure_hierarchy(self, top_parent: str, program_id: str, include_java: bool = True) -> Dict[str, Any]:
        """
        Get the complete hierarchy for a data structure, optionally including Java mappings.

        Args:
            top_parent: Name of the top-level structure
            program_id: ID of the program
            include_java: Whether to include Java mapping information

        Returns:
            Hierarchical structure information
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get all variables in the structure
            cursor.execute('''
                           SELECT * FROM variables
                           WHERE program_id = ?
                             AND (name = ? OR top_parent_datastructure_name = ?)
                           ORDER BY level, name
                           ''', (program_id, top_parent, top_parent))

            variables = cursor.fetchall()

            if not variables:
                return {}

            # Build hierarchy
            hierarchy = {
                'top_parent': top_parent,
                'program_id': program_id,
                'children': [],
                'total_variables': len(variables)
            }

            # Convert to dictionaries and organize by level
            var_dicts = []
            for var in variables:
                var_dict = dict(var)

                # Parse JSON fields
                json_fields = ['possible_values', 'occurs', 'metadata']
                if include_java:
                    json_fields.append('java_annotations')

                for field in json_fields:
                    if var_dict.get(field):
                        try:
                            var_dict[field] = json.loads(var_dict[field])
                        except json.JSONDecodeError:
                            pass

                # Convert boolean fields
                bool_fields = ['is_signed', 'is_filler', 'is_equ']
                if include_java:
                    bool_fields.append('is_java_entity_field')

                for bool_field in bool_fields:
                    if bool_field in var_dict and var_dict[bool_field] is not None:
                        var_dict[bool_field] = bool(var_dict[bool_field])

                var_dicts.append(var_dict)

            # Build the hierarchical structure
            hierarchy['children'] = self._build_hierarchy_tree(var_dicts, top_parent)

            return hierarchy

    def _build_hierarchy_tree(self, variables: List[Dict[str, Any]], parent_name: str) -> List[Dict[str, Any]]:
        """
        Build a hierarchical tree structure from flat variable list.

        Args:
            variables: List of variable dictionaries
            parent_name: Name of the parent to build children for

        Returns:
            List of child variable dictionaries with their own children
        """
        children = []

        for var in variables:
            if var.get('parent_name') == parent_name:
                # This variable is a direct child of the parent
                var_copy = var.copy()
                # Recursively build children for this variable
                var_copy['children'] = self._build_hierarchy_tree(variables, var['name'])
                children.append(var_copy)

        return children

    def get_variables_by_type(self, data_type: str) -> List[Dict[str, Any]]:
        """
        Get variables by data type.

        Args:
            data_type: Data type to filter by

        Returns:
            List of variable dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                           SELECT * FROM variables
                           WHERE data_type = ?
                           ORDER BY program_id, name
                           ''', (data_type,))

            variables = cursor.fetchall()
            result = []

            for var in variables:
                var_dict = dict(var)

                # Parse JSON fields
                for field in ['possible_values', 'occurs', 'metadata', 'java_annotations']:
                    if var_dict.get(field):
                        try:
                            var_dict[field] = json.loads(var_dict[field])
                        except json.JSONDecodeError:
                            pass

                # Convert integer boolean fields back to boolean
                for bool_field in ['is_signed', 'is_filler', 'is_equ', 'is_java_entity_field']:
                    if bool_field in var_dict and var_dict[bool_field] is not None:
                        var_dict[bool_field] = bool(var_dict[bool_field])

                result.append(var_dict)

            return result

    def delete_variable(self, program_id: str, variable_name: str) -> bool:
        """
        Delete a specific variable.

        Args:
            program_id: ID of the program
            variable_name: Name of the variable

        Returns:
            True if variable was deleted, False if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                           DELETE FROM variables
                           WHERE program_id = ? AND name = ?
                           ''', (program_id, variable_name))

            deleted_count = cursor.rowcount
            conn.commit()

            if deleted_count > 0:
                logger.info(f"Deleted variable {variable_name} from program {program_id}")
                return True
            return False

    def clear_java_mappings(self, program_id: str) -> int:
        """
        Clear Java mapping fields for all variables in a program.

        Args:
            program_id: ID of the program

        Returns:
            Number of variables updated
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                           UPDATE variables
                           SET java_name = NULL,
                               java_class = NULL,
                               parent_java_class = NULL,
                               full_qualified_name = NULL,
                               java_data_type = NULL,
                               java_annotations = NULL,
                               is_java_entity_field = 0,
                               java_mapping_notes = NULL,
                               java_generation_timestamp = NULL
                           WHERE program_id = ?
                           ''', (program_id,))

            updated_count = cursor.rowcount
            conn.commit()

            logger.info(f"Cleared Java mappings for {updated_count} variables in program {program_id}")
            return updated_count

    def get_variable_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about variables in the database, including Java mapping stats.

        Returns:
            Dictionary containing variable statistics
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Total variables
            cursor.execute("SELECT COUNT(*) FROM variables")
            total_variables = cursor.fetchone()[0]

            # Variables by type
            cursor.execute("""
                           SELECT data_type, COUNT(*) as count
                           FROM variables
                           GROUP BY data_type
                           ORDER BY count DESC
                           """)
            variables_by_type = dict(cursor.fetchall())

            # Variables by item type
            cursor.execute("""
                           SELECT item_type, COUNT(*) as count
                           FROM variables
                           GROUP BY item_type
                           ORDER BY count DESC
                           """)
            variables_by_item_type = dict(cursor.fetchall())

            # Java mapping statistics
            cursor.execute("SELECT COUNT(*) FROM variables WHERE java_name IS NOT NULL")
            variables_with_java_mappings = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT java_class) FROM variables WHERE java_class IS NOT NULL")
            distinct_java_classes = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM variables WHERE is_java_entity_field = 1")
            entity_fields = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM variables WHERE java_name IS NOT NULL AND is_java_entity_field = 0")
            data_fields = cursor.fetchone()[0]

            return {
                'total_variables': total_variables,
                'variables_by_type': variables_by_type,
                'variables_by_item_type': variables_by_item_type,
                'variables_with_java_mappings': variables_with_java_mappings,
                'distinct_java_classes': distinct_java_classes,
                'entity_fields': entity_fields,
                'data_fields': data_fields,
                'mapping_percentage': round((variables_with_java_mappings / total_variables * 100), 2) if total_variables > 0 else 0
            }