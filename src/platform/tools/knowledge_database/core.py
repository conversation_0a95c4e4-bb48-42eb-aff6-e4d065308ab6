"""
Core KnowledgeDatabase implementation.
Main orchestration for SQLite database operations and knowledge management.
"""
import os
import json
import sqlite3
import datetime
from typing import Dict, List, Optional, Any, Union

from .schema_manager import SchemaManager
from .program_manager import ProgramManager
from .chunk_manager import ChunkManager
from .variable_manager import VariableManager
from .mapping_manager import MappingManager
from .query_manager import QueryManager


class KnowledgeDatabase:
    """
    Manages SQLite database for storing program, chunk, and analysis information
    with advanced querying and management capabilities.
    """

    def __init__(self, db_path: str = None):
        """
        Initialize the SQLite database.

        Args:
            db_path: Optional path to the SQLite database file.
                     If None, creates a timestamped database in the 'out' directory
        """
        # Create 'out' directory if it doesn't exist
        out_dir = os.path.join(os.getcwd(), "out")
        os.makedirs(out_dir, exist_ok=True)

        # Generate database path if not provided
        if db_path is None:
            db_path = os.path.join(out_dir, f"knowledge_base.sqlite3")

        self.db_path = db_path

        # Initialize specialized managers
        self.schema_manager = SchemaManager(self.db_path)
        self.program_manager = ProgramManager(self.db_path)
        self.chunk_manager = ChunkManager(self.db_path)
        self.variable_manager = VariableManager(self.db_path)
        self.mapping_manager = MappingManager(self.db_path)
        self.query_manager = QueryManager(self.db_path)

        # Create tables and run migrations
        self.schema_manager.migrate_schema()

    # Program management methods
    def insert_program(self, program_info: Dict[str, Any]):
        """Insert or update program information."""
        return self.program_manager.insert_program(program_info)

    def get_program_details(self, program_id: str) -> Dict[str, Any]:
        """Retrieve full details of a program."""
        return self.program_manager.get_program_details(program_id)

    def get_all_programs(self) -> List[Dict[str, Any]]:
        """Get all programs in the database."""
        return self.program_manager.get_all_programs()

    def delete_program(self, program_id: str) -> bool:
        """Delete a program and all its associated data."""
        return self.program_manager.delete_program(program_id)

    # Chunk management methods
    def insert_chunks(self, program_id: str, chunks: List[Dict[str, Any]]):
        """Insert chunks for a specific program."""
        return self.chunk_manager.insert_chunks(program_id, chunks)

    def get_chunk_by_name(self, program_id: str, chunk_name: str) -> Optional[Dict[str, Any]]:
        """Get a specific chunk by program ID and chunk name."""
        return self.chunk_manager.get_chunk_by_name(program_id, chunk_name)

    def get_chunks_by_program(self, program_id: str) -> List[Dict[str, Any]]:
        """Get all chunks for a specific program."""
        return self.chunk_manager.get_chunks_by_program(program_id)

    def update_chunk_analysis(self, program_id: str, chunk_name: str, analysis_data: Dict[str, Any]):
        """Update chunk with analysis results."""
        return self.chunk_manager.update_chunk_analysis(program_id, chunk_name, analysis_data)

    def get_analyzed_chunks(self) -> List[Dict[str, Any]]:
        """Get all analyzed chunks across all programs."""
        # Get chunks that have been analyzed (have business_name or business_description)
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM chunks
                WHERE (business_name IS NOT NULL AND business_name != '')
                   OR (business_description IS NOT NULL AND business_description != '')
                ORDER BY program_id, chunk_name
            ''')

            chunks = cursor.fetchall()
            result = []

            for chunk in chunks:
                chunk_dict = dict(chunk)
                # Parse JSON fields
                for field in ['metadata', 'input_parameters', 'output_parameters']:
                    if chunk_dict.get(field):
                        try:
                            chunk_dict[field] = json.loads(chunk_dict[field])
                        except json.JSONDecodeError:
                            chunk_dict[field] = {} if field == 'metadata' else []
                result.append(chunk_dict)

            return result

    def get_chunk_documentation(self, program_id: str, chunk_name: str) -> Optional[Dict[str, Any]]:
        """Get chunk documentation by program ID and chunk name."""
        return self.get_chunk_by_name(program_id, chunk_name)

    def get_all_chunks(self) -> List[Dict[str, Any]]:
        """Get all chunks across all programs."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM chunks
                ORDER BY program_id, chunk_name
            ''')

            chunks = cursor.fetchall()
            result = []

            for chunk in chunks:
                chunk_dict = dict(chunk)
                # Parse JSON fields
                for field in ['metadata', 'input_parameters', 'output_parameters']:
                    if chunk_dict.get(field):
                        try:
                            chunk_dict[field] = json.loads(chunk_dict[field])
                        except json.JSONDecodeError:
                            chunk_dict[field] = {} if field == 'metadata' else []
                result.append(chunk_dict)

            return result

    # Variable/Data definition management methods
    def insert_data_definitions(self, program_id: str, data_items: List[Dict[str, Any]]):
        """Insert data definitions extracted from code."""
        return self.variable_manager.insert_data_definitions(program_id, data_items)

    def insert_cobol_data_definitions(self, program_id: str, data_items: List[Dict[str, Any]]):
        """Insert COBOL data definitions extracted from code."""
        return self.variable_manager.insert_cobol_data_definitions(program_id, data_items)

    def save_data_definition(self, data_item: Dict[str, Any]):
        """Save a single data definition."""
        return self.variable_manager.save_data_definition(data_item)

    def get_data_definitions_by_program(self, program_id: str) -> List[Dict[str, Any]]:
        """Get all data definitions for a program."""
        return self.variable_manager.get_data_definitions_by_program(program_id)

    def search_data_definitions(self, search_term: str) -> List[Dict[str, Any]]:
        """Search for data definitions by name or business name."""
        return self.variable_manager.search_data_definitions(search_term)

    def get_structure_hierarchy(self, top_parent: str, program_id: str) -> Dict[str, Any]:
        """Get the complete hierarchy for a data structure."""
        return self.variable_manager.get_structure_hierarchy(top_parent, program_id)

    # Mapping management methods
    def save_cobol_java_mapping(self, program_id: str, chunk_name: str, mappings: Dict[str, Any]):
        """Save COBOL to Java name mappings."""
        return self.mapping_manager.save_cobol_java_mapping(program_id, chunk_name, mappings)

    def save_comprehensive_cobol_java_mapping(self, program_id: str, chunk_name: str, cobol_name: str,
                                             java_name: str, mapping_type: str, mapping_info: Dict[str, Any]):
        """Save a single comprehensive COBOL to Java mapping with detailed information."""
        return self.mapping_manager.save_comprehensive_cobol_java_mapping(
            program_id, chunk_name, cobol_name, java_name, mapping_type, mapping_info)

    def get_cobol_java_mappings(self, program_id: str, chunk_name: str = None, mapping_type: str = None) -> Dict[str, str]:
        """Get COBOL to Java name mappings."""
        return self.mapping_manager.get_cobol_java_mappings(program_id, chunk_name, mapping_type)

    def get_comprehensive_cobol_java_mappings(self, program_id: str, chunk_name: str = None,
                                            mapping_type: str = None) -> List[Dict[str, Any]]:
        """Get comprehensive COBOL to Java mappings with all detailed information."""
        return self.mapping_manager.get_comprehensive_cobol_java_mappings(program_id, chunk_name, mapping_type)

    def get_business_name_mappings(self, program_id: str) -> Dict[str, Dict[str, str]]:
        """Get business name mappings from variables table for data class generation."""
        return self.mapping_manager.get_business_name_mappings(program_id)

    def get_data_class_mappings(self, program_id: str, structure_name: str = None) -> Dict[str, Any]:
        """Get data class mappings for template generation."""
        return self.mapping_manager.get_data_class_mappings(program_id, structure_name)

    def save_java_data_structure(self, program_id: str, structure_info: Dict[str, Any]):
        """Save Java data structure information."""
        return self.mapping_manager.save_java_data_structure(program_id, structure_info)

    def get_java_data_structures(self, program_id: str) -> List[Dict[str, Any]]:
        """Get Java data structures for a program."""
        return self.mapping_manager.get_java_data_structures(program_id)

    def get_cobol_structure_hierarchy(self, program_id: str, variable_name: str) -> Dict[str, Any]:
        """Get COBOL structure hierarchy for a variable."""
        return self.mapping_manager.get_cobol_structure_hierarchy(program_id, variable_name)

    # Query and analysis methods
    def get_programs_by_type(self, program_type: str) -> List[Dict[str, Any]]:
        """Get programs by type."""
        return self.query_manager.get_programs_by_type(program_type)

    def get_chunks_by_type(self, chunk_type: str) -> List[Dict[str, Any]]:
        """Get chunks by type."""
        return self.query_manager.get_chunks_by_type(chunk_type)

    def get_variables_by_type(self, data_type: str) -> List[Dict[str, Any]]:
        """Get variables by data type."""
        return self.query_manager.get_variables_by_type(data_type)

    def get_database_statistics(self) -> Dict[str, Any]:
        """Get comprehensive database statistics."""
        return self.query_manager.get_database_statistics()

    def search_across_all_tables(self, search_term: str) -> Dict[str, List[Dict[str, Any]]]:
        """Search across all tables for a term."""
        return self.query_manager.search_across_all_tables(search_term)

    # Utility methods
    def backup_database(self, backup_path: str = None) -> str:
        """Create a backup of the database."""
        if backup_path is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.db_path}.backup_{timestamp}"

        import shutil
        shutil.copy2(self.db_path, backup_path)
        return backup_path

    def optimize_database(self):
        """Optimize the database by running VACUUM and ANALYZE."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("VACUUM")
            conn.execute("ANALYZE")
            conn.commit()

    def get_database_info(self) -> Dict[str, Any]:
        """Get information about the database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Get database size
            cursor.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
            size = cursor.fetchone()[0]

            # Get table information
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            # Get row counts for each table
            table_counts = {}
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                table_counts[table] = cursor.fetchone()[0]

            return {
                'database_path': self.db_path,
                'size_bytes': size,
                'size_mb': round(size / (1024 * 1024), 2),
                'tables': tables,
                'table_counts': table_counts,
                'total_records': sum(table_counts.values())
            }

    def close(self):
        """Close database connections (if needed for cleanup)."""
        # SQLite connections are automatically closed when using context managers
        # This method is here for interface compatibility
        pass
