"""
Database schema management for KnowledgeDatabase.
Handles table creation, migrations, and schema updates.
"""
import sqlite3
import logging
from typing import Dict, List, Any

logger = logging.getLogger("tools.knowledge_database.schema_manager")


class SchemaManager:
    """
    Manages database schema creation and migrations.
    """

    def __init__(self, db_path: str):
        """
        Initialize the schema manager.

        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path

    def create_tables(self):
        """
        Create necessary tables in the database.
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Programs table - added project_name column
            cursor.execute('''
                           CREATE TABLE IF NOT EXISTS programs (
                                                                   program_id TEXT PRIMARY KEY,
                                                                   project_name TEXT,
                                                                   program_type TEXT,
                                                                   file_path TEXT,
                                                                   language TEXT,
                                                                   size INTEGER,
                                                                   analysis_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                                                                   metadata TEXT
                           )
                           ''')

            # Chunks table with business-related columns included directly
            cursor.execute('''
                           CREATE TABLE IF NOT EXISTS chunks (
                                                                 id INTEGER PRIMARY KEY AUTOINCREMENT,
                                                                 program_id TEXT,
                                                                 chunk_type TEXT,
                                                                 chunk_name TEXT,
                                                                 chunk_size INTEGER,
                                                                 code TEXT,
                                                                 metadata TEXT,
                                                                 business_name TEXT,
                                                                 business_description TEXT,
                                                                 input_parameters TEXT,  -- JSON array of input parameters
                                                                 output_parameters TEXT,  -- JSON array of output parameters
                                                                 business_logic TEXT,  -- Additional business logic notes
                                                                 functional_spec TEXT,  -- Functional specification
                                                                 analysis_status TEXT,  -- Status of analysis (pending, complete, error, skipped)
                                                                 analysis_timestamp DATETIME,  -- When the analysis was performed
                                                                 skip_reason TEXT,  -- Reason for skipping chunk analysis
                                                                 FOREIGN KEY(program_id) REFERENCES programs(program_id)
                           )
                           ''')

            # Extended Variables table with Java mapping fields
            cursor.execute('''
                           CREATE TABLE IF NOT EXISTS variables (
                                                                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                                                                    program_id TEXT,
                                                                    name TEXT,
                                                                    data_type TEXT,
                                                                    description TEXT,
                                                                    scope TEXT,
                                                                    business_name TEXT,
                                                                    level INTEGER,
                                                                    length INTEGER,
                                                                    default_value TEXT,
                                                                    possible_values TEXT,
                                                                    item_type TEXT,
                                                                    is_signed INTEGER,
                                                                    is_filler INTEGER,
                                                                    is_equ INTEGER,           -- Added to support constants (EQU items)
                                                                    occurs TEXT,
                                                                    redefines TEXT,
                                                                    storage_type TEXT,
                                                                    decimals INTEGER,
                                                                    source_file_name TEXT,
                                                                    metadata TEXT,
                                                                    top_parent_datastructure_name TEXT,
                                                                    parent_name TEXT,

                               -- Java mapping fields
                                                                    java_name TEXT,                    -- Java field/variable name (camelCase)
                                                                    java_class TEXT,                   -- Java class this field belongs to
                                                                    parent_java_class TEXT,            -- Parent Java class if this is a nested structure
                                                                    full_qualified_name TEXT,          -- Full qualified Java name (package.class.field)
                                                                    java_data_type TEXT,               -- Java data type (String, Integer, BigDecimal, etc.)
                                                                    java_annotations TEXT,             -- JSON array of Java annotations
                                                                    is_java_entity_field INTEGER DEFAULT 0,  -- 1 if this field is in a JPA entity
                                                                    java_mapping_notes TEXT,           -- Additional mapping notes
                                                                    java_generation_timestamp DATETIME,  -- When Java mapping was generated

                                                                    FOREIGN KEY(program_id) REFERENCES programs(program_id)
                           )
                           ''')

            # COBOL to Java name mappings table (comprehensive mapping information)
            cursor.execute('''
                           CREATE TABLE IF NOT EXISTS cobol_java_mappings (
                                                                              id INTEGER PRIMARY KEY AUTOINCREMENT,
                                                                              program_id TEXT,
                                                                              chunk_name TEXT,
                                                                              cobol_name TEXT,
                                                                              java_name TEXT,
                                                                              mapping_type TEXT,  -- 'method', 'class', 'package', 'field', 'parameter', 'variable'
                                                                              context TEXT,       -- additional context

                                                                              -- Extended mapping information
                                                                              java_type TEXT,     -- Java data type
                                                                              package_name TEXT,  -- Java package
                                                                              business_purpose TEXT, -- Business description
                                                                              cobol_data_type TEXT,  -- Original COBOL data type
                                                                              java_annotations TEXT, -- JSON array of annotations
                                                                              field_position TEXT,   -- For file parsing (start-end)
                                                                              is_entity_field INTEGER DEFAULT 0, -- JPA entity field flag
                                                                              has_string_constructor INTEGER DEFAULT 0, -- String constructor flag
                                                                              mapping_notes TEXT,    -- Additional notes
                                                                              generation_metadata TEXT, -- JSON metadata from LLM

                                                                              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                                                                              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                                                                              FOREIGN KEY(program_id) REFERENCES programs(program_id),
                                                                              UNIQUE(program_id, chunk_name, cobol_name, mapping_type)
                           )
                           ''')

            conn.commit()
            logger.info("Database tables created successfully")

    def create_indexes(self):
        """
        Create indexes for better query performance.
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Indexes for programs table
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_programs_type ON programs(program_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_programs_project ON programs(project_name)')

            # Indexes for chunks table
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_chunks_program ON chunks(program_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_chunks_type ON chunks(chunk_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_chunks_name ON chunks(chunk_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_chunks_status ON chunks(analysis_status)')

            # Indexes for variables table
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_variables_program ON variables(program_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_variables_name ON variables(name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_variables_type ON variables(data_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_variables_business_name ON variables(business_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_variables_parent ON variables(top_parent_datastructure_name)')

            # New indexes for Java mapping fields
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_variables_java_name ON variables(java_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_variables_java_class ON variables(java_class)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_variables_parent_java_class ON variables(parent_java_class)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_variables_full_qualified_name ON variables(full_qualified_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_variables_java_entity ON variables(is_java_entity_field)')

            # Indexes for mappings table
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_mappings_program ON cobol_java_mappings(program_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_mappings_chunk ON cobol_java_mappings(chunk_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_mappings_cobol_name ON cobol_java_mappings(cobol_name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_mappings_type ON cobol_java_mappings(mapping_type)')

            conn.commit()
            logger.info("Database indexes created successfully")

    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """
        Get the schema information for a specific table.

        Args:
            table_name: Name of the table

        Returns:
            List of column information dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()

            schema = []
            for col in columns:
                schema.append({
                    'column_id': col[0],
                    'name': col[1],
                    'type': col[2],
                    'not_null': bool(col[3]),
                    'default_value': col[4],
                    'primary_key': bool(col[5])
                })

            return schema

    def get_all_tables(self) -> List[str]:
        """
        Get list of all tables in the database.

        Returns:
            List of table names
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            return [row[0] for row in cursor.fetchall()]

    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the database.

        Args:
            table_name: Name of the table to check

        Returns:
            True if table exists, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                           SELECT name FROM sqlite_master
                           WHERE type='table' AND name=?
                           """, (table_name,))
            return cursor.fetchone() is not None

    def add_column_if_not_exists(self, table_name: str, column_name: str, column_type: str):
        """
        Add a column to a table if it doesn't already exist.

        Args:
            table_name: Name of the table
            column_name: Name of the column to add
            column_type: SQL type of the column
        """
        schema = self.get_table_schema(table_name)
        column_names = [col['name'] for col in schema]

        if column_name not in column_names:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")
                conn.commit()
                logger.info(f"Added column {column_name} to table {table_name}")

    def migrate_schema(self, target_version: int = None):
        """
        Perform schema migrations to bring database up to date.

        Args:
            target_version: Target schema version (optional)
        """
        # Ensure all tables and indexes exist
        self.create_tables()
        self.create_indexes()

        # Add missing columns to existing tables
        self._migrate_chunks_table()

        logger.info("Schema migration completed")

    def _migrate_chunks_table(self):
        """
        Migrate the chunks table to add missing columns.
        """
        try:
            # Add skip_reason column if it doesn't exist
            self.add_column_if_not_exists("chunks", "skip_reason", "TEXT")
        except Exception as e:
            logger.error(f"Error migrating chunks table: {str(e)}")

    def get_database_version(self) -> int:
        """
        Get the current database schema version.

        Returns:
            Current schema version
        """
        # For now, return a default version
        # In the future, this could be stored in a separate version table
        return 1