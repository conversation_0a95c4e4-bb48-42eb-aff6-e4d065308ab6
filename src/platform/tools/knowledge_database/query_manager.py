"""
Query management functionality for KnowledgeDatabase.
Handles complex queries, analytics, and cross-table searches.
"""
import json
import sqlite3
import logging
from typing import Dict, List, Any

logger = logging.getLogger("tools.knowledge_database.query_manager")


class QueryManager:
    """
    Manages complex queries and analytics for the knowledge database.
    """

    def __init__(self, db_path: str):
        """
        Initialize the query manager.

        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path

    def get_programs_by_type(self, program_type: str) -> List[Dict[str, Any]]:
        """
        Get programs by type.

        Args:
            program_type: Type of programs to retrieve

        Returns:
            List of program dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM programs 
                WHERE program_type = ? 
                ORDER BY program_id
            ''', (program_type,))

            programs = cursor.fetchall()
            result = [dict(program) for program in programs]
            
            # Parse JSON fields
            for program in result:
                if program.get('metadata'):
                    try:
                        program['metadata'] = json.loads(program['metadata'])
                    except json.JSONDecodeError:
                        program['metadata'] = {}

            return result

    def get_chunks_by_type(self, chunk_type: str) -> List[Dict[str, Any]]:
        """
        Get chunks by type.

        Args:
            chunk_type: Type of chunks to retrieve

        Returns:
            List of chunk dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM chunks 
                WHERE chunk_type = ? 
                ORDER BY program_id, chunk_name
            ''', (chunk_type,))

            chunks = cursor.fetchall()
            result = []
            
            for chunk in chunks:
                chunk_dict = dict(chunk)
                # Parse JSON fields
                for field in ['metadata', 'input_parameters', 'output_parameters']:
                    if chunk_dict.get(field):
                        try:
                            chunk_dict[field] = json.loads(chunk_dict[field])
                        except json.JSONDecodeError:
                            chunk_dict[field] = {} if field == 'metadata' else []
                result.append(chunk_dict)
            
            return result

    def get_variables_by_type(self, data_type: str) -> List[Dict[str, Any]]:
        """
        Get variables by data type.

        Args:
            data_type: Data type to filter by

        Returns:
            List of variable dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM variables 
                WHERE data_type = ? 
                ORDER BY program_id, name
            ''', (data_type,))

            variables = cursor.fetchall()
            result = []

            for var in variables:
                var_dict = dict(var)
                # Parse JSON fields
                for field in ['possible_values', 'occurs', 'metadata']:
                    if var_dict.get(field):
                        try:
                            var_dict[field] = json.loads(var_dict[field])
                        except json.JSONDecodeError:
                            pass
                
                # Convert integer boolean fields back to boolean
                for bool_field in ['is_signed', 'is_filler', 'is_equ']:
                    if bool_field in var_dict and var_dict[bool_field] is not None:
                        var_dict[bool_field] = bool(var_dict[bool_field])

                result.append(var_dict)

            return result

    def get_database_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive database statistics.

        Returns:
            Dictionary containing database statistics
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            stats = {}

            # Program statistics
            cursor.execute("SELECT COUNT(*) FROM programs")
            stats['total_programs'] = cursor.fetchone()[0]

            cursor.execute("""
                SELECT program_type, COUNT(*) as count 
                FROM programs 
                GROUP BY program_type 
                ORDER BY count DESC
            """)
            stats['programs_by_type'] = dict(cursor.fetchall())

            # Chunk statistics
            cursor.execute("SELECT COUNT(*) FROM chunks")
            stats['total_chunks'] = cursor.fetchone()[0]

            cursor.execute("""
                SELECT chunk_type, COUNT(*) as count 
                FROM chunks 
                GROUP BY chunk_type 
                ORDER BY count DESC
            """)
            stats['chunks_by_type'] = dict(cursor.fetchall())

            cursor.execute("""
                SELECT analysis_status, COUNT(*) as count 
                FROM chunks 
                GROUP BY analysis_status 
                ORDER BY count DESC
            """)
            stats['chunks_by_status'] = dict(cursor.fetchall())

            # Variable statistics
            cursor.execute("SELECT COUNT(*) FROM variables")
            stats['total_variables'] = cursor.fetchone()[0]

            cursor.execute("""
                SELECT data_type, COUNT(*) as count 
                FROM variables 
                GROUP BY data_type 
                ORDER BY count DESC
            """)
            stats['variables_by_type'] = dict(cursor.fetchall())

            cursor.execute("""
                SELECT item_type, COUNT(*) as count 
                FROM variables 
                GROUP BY item_type 
                ORDER BY count DESC
            """)
            stats['variables_by_item_type'] = dict(cursor.fetchall())

            # Mapping statistics
            cursor.execute("SELECT COUNT(*) FROM cobol_java_mappings")
            stats['total_mappings'] = cursor.fetchone()[0]

            cursor.execute("""
                SELECT mapping_type, COUNT(*) as count 
                FROM cobol_java_mappings 
                GROUP BY mapping_type 
                ORDER BY count DESC
            """)
            stats['mappings_by_type'] = dict(cursor.fetchall())

            return stats

    def search_across_all_tables(self, search_term: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        Search across all tables for a term.

        Args:
            search_term: Term to search for

        Returns:
            Dictionary with table names as keys and matching records as values
        """
        results = {}
        search_pattern = f"%{search_term}%"

        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Search programs
            cursor.execute('''
                SELECT * FROM programs 
                WHERE program_id LIKE ? 
                   OR project_name LIKE ? 
                   OR program_type LIKE ?
                ORDER BY program_id
            ''', (search_pattern, search_pattern, search_pattern))
            results['programs'] = [dict(row) for row in cursor.fetchall()]

            # Search chunks
            cursor.execute('''
                SELECT * FROM chunks 
                WHERE chunk_name LIKE ? 
                   OR business_name LIKE ? 
                   OR business_description LIKE ?
                ORDER BY program_id, chunk_name
            ''', (search_pattern, search_pattern, search_pattern))
            results['chunks'] = [dict(row) for row in cursor.fetchall()]

            # Search variables
            cursor.execute('''
                SELECT * FROM variables 
                WHERE name LIKE ? 
                   OR business_name LIKE ? 
                   OR description LIKE ?
                ORDER BY program_id, name
            ''', (search_pattern, search_pattern, search_pattern))
            results['variables'] = [dict(row) for row in cursor.fetchall()]

            # Search mappings
            cursor.execute('''
                SELECT * FROM cobol_java_mappings 
                WHERE cobol_name LIKE ? 
                   OR java_name LIKE ?
                ORDER BY program_id, chunk_name
            ''', (search_pattern, search_pattern))
            results['mappings'] = [dict(row) for row in cursor.fetchall()]

        return results

    def get_program_analysis_summary(self, program_id: str) -> Dict[str, Any]:
        """
        Get a comprehensive analysis summary for a program.

        Args:
            program_id: ID of the program

        Returns:
            Dictionary containing analysis summary
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            summary = {'program_id': program_id}

            # Get program info
            cursor.execute("SELECT * FROM programs WHERE program_id = ?", (program_id,))
            program = cursor.fetchone()
            if program:
                summary['program_info'] = dict(program)

            # Chunk analysis
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_chunks,
                    COUNT(CASE WHEN analysis_status = 'complete' THEN 1 END) as analyzed_chunks,
                    COUNT(CASE WHEN analysis_status = 'error' THEN 1 END) as failed_chunks
                FROM chunks 
                WHERE program_id = ?
            """, (program_id,))
            chunk_stats = cursor.fetchone()
            if chunk_stats:
                summary['chunk_analysis'] = dict(chunk_stats)

            # Variable analysis
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_variables,
                    COUNT(DISTINCT data_type) as unique_data_types,
                    COUNT(CASE WHEN is_equ = 1 THEN 1 END) as constants
                FROM variables 
                WHERE program_id = ?
            """, (program_id,))
            var_stats = cursor.fetchone()
            if var_stats:
                summary['variable_analysis'] = dict(var_stats)

            # Mapping analysis
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_mappings,
                    COUNT(DISTINCT mapping_type) as mapping_types
                FROM cobol_java_mappings 
                WHERE program_id = ?
            """, (program_id,))
            mapping_stats = cursor.fetchone()
            if mapping_stats:
                summary['mapping_analysis'] = dict(mapping_stats)

            return summary

    def get_cross_reference_analysis(self, program_id: str) -> Dict[str, Any]:
        """
        Get cross-reference analysis showing relationships between entities.

        Args:
            program_id: ID of the program

        Returns:
            Dictionary containing cross-reference information
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            analysis = {'program_id': program_id}

            # Get variables referenced in chunks
            cursor.execute("""
                SELECT DISTINCT v.name, v.business_name, v.data_type, c.chunk_name
                FROM variables v
                JOIN chunks c ON v.program_id = c.program_id
                WHERE v.program_id = ? AND c.code LIKE '%' || v.name || '%'
                ORDER BY v.name
            """, (program_id,))
            
            variable_references = cursor.fetchall()
            analysis['variable_references'] = [dict(ref) for ref in variable_references]

            # Get structure hierarchies
            cursor.execute("""
                SELECT DISTINCT top_parent_datastructure_name, COUNT(*) as child_count
                FROM variables 
                WHERE program_id = ? AND top_parent_datastructure_name IS NOT NULL
                GROUP BY top_parent_datastructure_name
                ORDER BY child_count DESC
            """, (program_id,))
            
            structure_hierarchies = cursor.fetchall()
            analysis['structure_hierarchies'] = [dict(struct) for struct in structure_hierarchies]

            return analysis
