"""
Program management functionality for KnowledgeDatabase.
Handles CRUD operations for program records.
"""
import json
import sqlite3
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger("tools.knowledge_database.program_manager")


class ProgramManager:
    """
    Manages program records in the knowledge database.
    """

    def __init__(self, db_path: str):
        """
        Initialize the program manager.

        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path

    def insert_program(self, program_info: Dict[str, Any]):
        """
        Insert or update program information.

        Args:
            program_info: Dictionary containing program details
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO programs 
                (program_id, project_name, program_type, file_path, language, size, metadata) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                program_info.get('program_id', 'UNKNOWN'),
                program_info.get('project_name', 'Unnamed Project'),
                program_info.get('program_type', 'UNKNOWN'),
                program_info.get('file_path', ''),
                program_info.get('language', 'UNKNOWN'),
                program_info.get('size', 0),
                json.dumps(program_info.get('metadata', {}))
            ))
            conn.commit()
            logger.info(f"Inserted/updated program: {program_info.get('program_id')}")

    def get_program_details(self, program_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve full details of a program.

        Args:
            program_id: Identifier of the program

        Returns:
            Dict containing program details or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get program details
            cursor.execute("SELECT * FROM programs WHERE program_id = ?", (program_id,))
            program = cursor.fetchone()

            if not program:
                return None

            # Get chunks
            cursor.execute("SELECT * FROM chunks WHERE program_id = ?", (program_id,))
            chunks = cursor.fetchall()

            # Get variables
            cursor.execute("SELECT * FROM variables WHERE program_id = ?", (program_id,))
            variables = cursor.fetchall()

            # Convert row objects to dictionaries
            result = {
                "program": dict(program),
                "chunks": [dict(chunk) for chunk in chunks],
                "variables": [dict(var) for var in variables]
            }

            # Parse JSON fields
            result = self._parse_json_fields(result)

            return result

    def get_all_programs(self) -> List[Dict[str, Any]]:
        """
        Get all programs in the database.

        Returns:
            List of program dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM programs ORDER BY program_id")
            programs = cursor.fetchall()

            result = [dict(program) for program in programs]
            
            # Parse JSON fields
            for program in result:
                if program.get('metadata'):
                    try:
                        program['metadata'] = json.loads(program['metadata'])
                    except json.JSONDecodeError:
                        program['metadata'] = {}

            return result

    def get_programs_by_project(self, project_name: str) -> List[Dict[str, Any]]:
        """
        Get all programs for a specific project.

        Args:
            project_name: Name of the project

        Returns:
            List of program dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute(
                "SELECT * FROM programs WHERE project_name = ? ORDER BY program_id", 
                (project_name,)
            )
            programs = cursor.fetchall()

            result = [dict(program) for program in programs]
            
            # Parse JSON fields
            for program in result:
                if program.get('metadata'):
                    try:
                        program['metadata'] = json.loads(program['metadata'])
                    except json.JSONDecodeError:
                        program['metadata'] = {}

            return result

    def get_programs_by_type(self, program_type: str) -> List[Dict[str, Any]]:
        """
        Get programs by type.

        Args:
            program_type: Type of programs to retrieve

        Returns:
            List of program dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute(
                "SELECT * FROM programs WHERE program_type = ? ORDER BY program_id", 
                (program_type,)
            )
            programs = cursor.fetchall()

            result = [dict(program) for program in programs]
            
            # Parse JSON fields
            for program in result:
                if program.get('metadata'):
                    try:
                        program['metadata'] = json.loads(program['metadata'])
                    except json.JSONDecodeError:
                        program['metadata'] = {}

            return result

    def update_program_metadata(self, program_id: str, metadata: Dict[str, Any]):
        """
        Update program metadata.

        Args:
            program_id: ID of the program
            metadata: New metadata dictionary
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE programs SET metadata = ? WHERE program_id = ?",
                (json.dumps(metadata), program_id)
            )
            conn.commit()
            logger.info(f"Updated metadata for program: {program_id}")

    def delete_program(self, program_id: str) -> bool:
        """
        Delete a program and all its associated data.

        Args:
            program_id: ID of the program to delete

        Returns:
            True if program was deleted, False if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Check if program exists
            cursor.execute("SELECT COUNT(*) FROM programs WHERE program_id = ?", (program_id,))
            if cursor.fetchone()[0] == 0:
                return False

            # Delete associated data first (due to foreign key constraints)
            cursor.execute("DELETE FROM variables WHERE program_id = ?", (program_id,))
            cursor.execute("DELETE FROM chunks WHERE program_id = ?", (program_id,))
            cursor.execute("DELETE FROM cobol_java_mappings WHERE program_id = ?", (program_id,))

            # Delete the program
            cursor.execute("DELETE FROM programs WHERE program_id = ?", (program_id,))
            
            conn.commit()
            logger.info(f"Deleted program and all associated data: {program_id}")
            return True

    def program_exists(self, program_id: str) -> bool:
        """
        Check if a program exists in the database.

        Args:
            program_id: ID of the program to check

        Returns:
            True if program exists, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM programs WHERE program_id = ?", (program_id,))
            return cursor.fetchone()[0] > 0

    def get_program_statistics(self, program_id: str) -> Dict[str, Any]:
        """
        Get statistics for a specific program.

        Args:
            program_id: ID of the program

        Returns:
            Dictionary containing program statistics
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Get chunk count and types
            cursor.execute("""
                SELECT chunk_type, COUNT(*) as count 
                FROM chunks 
                WHERE program_id = ? 
                GROUP BY chunk_type
            """, (program_id,))
            chunk_stats = dict(cursor.fetchall())

            # Get variable count and types
            cursor.execute("""
                SELECT data_type, COUNT(*) as count 
                FROM variables 
                WHERE program_id = ? 
                GROUP BY data_type
            """, (program_id,))
            variable_stats = dict(cursor.fetchall())

            # Get total counts
            cursor.execute("SELECT COUNT(*) FROM chunks WHERE program_id = ?", (program_id,))
            total_chunks = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM variables WHERE program_id = ?", (program_id,))
            total_variables = cursor.fetchone()[0]

            return {
                'program_id': program_id,
                'total_chunks': total_chunks,
                'total_variables': total_variables,
                'chunk_types': chunk_stats,
                'variable_types': variable_stats
            }

    def _parse_json_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse JSON fields in the data structure.

        Args:
            data: Data structure to parse

        Returns:
            Data structure with parsed JSON fields
        """
        for key, items in data.items():
            if isinstance(items, list):
                for item in items:
                    for field, value in item.items():
                        if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                            try:
                                item[field] = json.loads(value)
                            except json.JSONDecodeError:
                                pass
            elif isinstance(items, dict):
                for field, value in items.items():
                    if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                        try:
                            items[field] = json.loads(value)
                        except json.JSONDecodeError:
                            pass

        return data
