"""
Mapping management functionality for KnowledgeDatabase.
Handles COBOL to Java mappings and data structure relationships.
"""
import json
import sqlite3
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger("tools.knowledge_database.mapping_manager")


class MappingManager:
    """
    Manages COBOL to Java mappings and data structure relationships.
    """

    def __init__(self, db_path: str):
        """
        Initialize the mapping manager.

        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path

    def save_cobol_java_mapping(self, program_id: str, chunk_name: str, mappings: Dict[str, Any]):
        """
        Save COBOL to Java name mappings with comprehensive information.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            mappings: Dictionary with mapping_type as key and name mappings as value
                     e.g., {'method_names': {'COBOL-METHOD': 'javaMethod'}, 'class_names': {...}}
                     OR comprehensive mapping with detailed information
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for mapping_type, name_mappings in mappings.items():
                # Remove '_names' suffix for storage
                db_mapping_type = mapping_type.replace('_names', '')

                # Handle both simple and comprehensive mapping formats
                if isinstance(name_mappings, dict):
                    for cobol_name, mapping_info in name_mappings.items():
                        if isinstance(mapping_info, str):
                            # Simple mapping: cobol_name -> java_name
                            self._save_simple_mapping(cursor, program_id, chunk_name, cobol_name,
                                                    mapping_info, db_mapping_type)
                        elif isinstance(mapping_info, dict):
                            # Comprehensive mapping with detailed information
                            self._save_comprehensive_mapping(cursor, program_id, chunk_name, cobol_name,
                                                           mapping_info, db_mapping_type)

            conn.commit()
            logger.info(f"Saved COBOL-Java mappings for chunk {chunk_name} in program {program_id}")

    def _save_simple_mapping(self, cursor, program_id: str, chunk_name: str, cobol_name: str,
                           java_name: str, mapping_type: str):
        """Save simple COBOL to Java name mapping."""
        cursor.execute('''
            INSERT OR REPLACE INTO cobol_java_mappings
            (program_id, chunk_name, cobol_name, java_name, mapping_type, updated_at)
            VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (program_id, chunk_name, cobol_name, java_name, mapping_type))

    def _save_comprehensive_mapping(self, cursor, program_id: str, chunk_name: str, cobol_name: str,
                                  mapping_info: Dict[str, Any], mapping_type: str):
        """Save comprehensive COBOL to Java mapping with detailed information."""
        java_name = mapping_info.get('java_name') or mapping_info.get('java_method_name') or mapping_info.get('java_field_name') or mapping_info.get('java_variable_name') or mapping_info.get('java_class_name', '')
        java_type = mapping_info.get('java_type', '')
        package_name = mapping_info.get('package', '')
        business_purpose = mapping_info.get('business_purpose') or mapping_info.get('business_name', '')
        cobol_data_type = mapping_info.get('cobol_data_type', '')
        java_annotations = json.dumps(mapping_info.get('java_annotations', [])) if mapping_info.get('java_annotations') else None
        field_position = mapping_info.get('field_position', '')
        is_entity_field = 1 if mapping_info.get('is_entity_field', False) else 0
        has_string_constructor = 1 if mapping_info.get('has_string_constructor', False) else 0
        mapping_notes = mapping_info.get('mapping_notes', '')
        generation_metadata = json.dumps(mapping_info) if mapping_info else None

        cursor.execute('''
            INSERT OR REPLACE INTO cobol_java_mappings
            (program_id, chunk_name, cobol_name, java_name, mapping_type, context,
             java_type, package_name, business_purpose, cobol_data_type, java_annotations,
             field_position, is_entity_field, has_string_constructor, mapping_notes,
             generation_metadata, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (program_id, chunk_name, cobol_name, java_name, mapping_type,
              mapping_info.get('context', ''), java_type, package_name, business_purpose,
              cobol_data_type, java_annotations, field_position, is_entity_field,
              has_string_constructor, mapping_notes, generation_metadata))

    def save_comprehensive_cobol_java_mapping(self, program_id: str, chunk_name: str, cobol_name: str,
                                             java_name: str, mapping_type: str, mapping_info: Dict[str, Any]):
        """
        Save a single comprehensive COBOL to Java mapping with detailed information.

        This method is used by code generators to save individual mappings with full context.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            cobol_name: COBOL identifier name
            java_name: Java identifier name
            mapping_type: Type of mapping ('method', 'class', 'field', 'parameter', 'variable')
            mapping_info: Dictionary containing detailed mapping information
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            self._save_comprehensive_mapping(cursor, program_id, chunk_name, cobol_name,
                                           {**mapping_info, 'java_name': java_name}, mapping_type)
            conn.commit()
            logger.info(f"Saved comprehensive mapping: {cobol_name} -> {java_name} ({mapping_type})")

    def save_java_field_mappings(self, program_id: str, field_mappings: List[Dict[str, Any]]):
        """
        Save Java field mappings to the variables table.

        Args:
            program_id: ID of the program
            field_mappings: List of field mapping dictionaries
        """


        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            for mapping in field_mappings:
                cobol_name = mapping.get('cobol_name', '')
                java_name = mapping.get('java_name', '')
                java_class = mapping.get('java_class', '')
                parent_java_class = mapping.get('parent_java_class', '')
                package_name = mapping.get('package_name', '')
                java_data_type = mapping.get('java_data_type', '')
                java_annotations = mapping.get('java_annotations', [])
                is_entity_field = 1 if mapping.get('is_entity_field', False) else 0
                mapping_notes = mapping.get('mapping_notes', '')



                # Build full qualified name
                if package_name and java_class and java_name:
                    full_qualified_name = f"{package_name}.{java_class}.{java_name}"
                elif java_class and java_name:
                    full_qualified_name = f"{java_class}.{java_name}"
                else:
                    full_qualified_name = java_name

                # Convert annotations to JSON
                annotations_json = json.dumps(java_annotations) if java_annotations else None

                cursor.execute('''
                               UPDATE variables
                               SET java_name = ?,
                                   java_class = ?,
                                   parent_java_class = ?,
                                   full_qualified_name = ?,
                                   java_data_type = ?,
                                   java_annotations = ?,
                                   is_java_entity_field = ?,
                                   java_mapping_notes = ?,
                                   java_generation_timestamp = ?
                               WHERE program_id = ? AND name = ?
                               ''', (
                                   java_name,
                                   java_class,
                                   parent_java_class,
                                   full_qualified_name,
                                   java_data_type,
                                   annotations_json,
                                   is_entity_field,
                                   mapping_notes,
                                   datetime.now().isoformat(),
                                   program_id,
                                   cobol_name
                               ))

                if cursor.rowcount == 0:
                    logger.warning(f"No variable found for COBOL name '{cobol_name}' in program {program_id}")

            conn.commit()
            logger.info(f"Saved {len(field_mappings)} Java field mappings for program {program_id}")

    def save_java_data_structure(self, program_id: str, structure_info: Dict[str, Any]):
        """
        Save Java data structure information by updating variables with Java mapping data.

        Args:
            program_id: ID of the program
            structure_info: Dictionary containing structure information
        """
        try:
            cobol_structure_name = structure_info.get('cobol_structure_name', '')
            java_class_name = structure_info.get('java_class_name', '')
            package_name = structure_info.get('package_name', '')
            is_entity = structure_info.get('is_entity', False)
            parent_structure = structure_info.get('parent_structure', '')
            fields = structure_info.get('fields', [])

            logger.info(f"Saving Java data structure mapping: {cobol_structure_name} -> {java_class_name}")

            # Build field mappings from structure info
            field_mappings = []
            for field in fields:
                field_mapping = {
                    'cobol_name': field.get('cobol_name', ''),
                    'java_name': field.get('java_name', ''),
                    'java_class': java_class_name,
                    'parent_java_class': parent_structure if parent_structure else None,
                    'package_name': package_name,
                    'java_data_type': self._infer_java_type_from_field(field),
                    'java_annotations': self._get_java_annotations(field, is_entity),
                    'is_entity_field': is_entity,
                    'mapping_notes': f"Generated from COBOL structure {cobol_structure_name}"
                }
                field_mappings.append(field_mapping)

            # Save field mappings
            if field_mappings:
                self.save_java_field_mappings(program_id, field_mappings)

            # Save class-level mapping
            self.save_cobol_java_mapping(program_id, cobol_structure_name, {
                'class_names': {cobol_structure_name: java_class_name}
            })

            logger.info(f"Successfully saved Java data structure {java_class_name} for program {program_id}")

        except Exception as e:
            logger.error(f"Error saving Java data structure: {str(e)}")
            raise

    def _infer_java_type_from_field(self, field: Dict[str, Any]) -> str:
        """
        Infer Java data type from COBOL field information.

        Args:
            field: Field information dictionary

        Returns:
            str: Java data type
        """
        cobol_name = field.get('cobol_name', '')
        raw_definition = field.get('raw_definition', '')

        # Basic type inference logic
        if 'PIC X' in raw_definition:
            return 'String'
        elif 'PIC 9' in raw_definition:
            if 'V9' in raw_definition:  # Has decimals
                return 'BigDecimal'
            elif '(11)' in raw_definition or '(10)' in raw_definition:
                return 'Long'
            else:
                return 'Integer'
        elif 'BINARY' in raw_definition:
            return 'Integer'
        elif 'COMP-3' in raw_definition:
            return 'BigDecimal'
        else:
            return 'String'  # Default

    def _get_java_annotations(self, field: Dict[str, Any], is_entity: bool) -> List[str]:
        """
        Get appropriate Java annotations for a field.

        Args:
            field: Field information dictionary
            is_entity: Whether this is an entity field

        Returns:
            List[str]: List of annotation strings
        """
        annotations = []

        if is_entity:
            # Add JPA annotations for entity fields
            annotations.append('@Column')

            # Check if this might be an ID field
            cobol_name = field.get('cobol_name', '').upper()
            if 'ID' in cobol_name or cobol_name.endswith('-ID'):
                annotations.append('@Id')

        return annotations

    def get_cobol_java_mappings(self, program_id: str, chunk_name: str = None, mapping_type: str = None) -> Dict[str, str]:
        """
        Get COBOL to Java name mappings.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk (optional, gets all if None)
            mapping_type: Type of mapping to get (optional, gets all if None)

        Returns:
            Dict[str, str]: Dictionary of COBOL name -> Java name mappings
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = "SELECT * FROM cobol_java_mappings WHERE program_id = ?"
            params = [program_id]

            if chunk_name:
                query += " AND chunk_name = ?"
                params.append(chunk_name)

            if mapping_type:
                # Remove '_names' suffix if present
                db_mapping_type = mapping_type.replace('_names', '')
                query += " AND mapping_type = ?"
                params.append(db_mapping_type)

            cursor.execute(query, params)
            mappings = cursor.fetchall()

            result = {}
            for mapping in mappings:
                result[mapping['cobol_name']] = mapping['java_name']

            return result

    def get_comprehensive_cobol_java_mappings(self, program_id: str, chunk_name: str = None,
                                            mapping_type: str = None) -> List[Dict[str, Any]]:
        """
        Get comprehensive COBOL to Java mappings with all detailed information.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk (optional, gets all if None)
            mapping_type: Type of mapping to get (optional, gets all if None)

        Returns:
            List[Dict[str, Any]]: List of comprehensive mapping dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = "SELECT * FROM cobol_java_mappings WHERE program_id = ?"
            params = [program_id]

            if chunk_name:
                query += " AND chunk_name = ?"
                params.append(chunk_name)

            if mapping_type:
                # Remove '_names' suffix if present
                db_mapping_type = mapping_type.replace('_names', '')
                query += " AND mapping_type = ?"
                params.append(db_mapping_type)

            query += " ORDER BY mapping_type, cobol_name"
            cursor.execute(query, params)
            mappings = cursor.fetchall()

            result = []
            for mapping in mappings:
                mapping_dict = dict(mapping)

                # Parse JSON fields
                if mapping_dict.get('java_annotations'):
                    try:
                        mapping_dict['java_annotations'] = json.loads(mapping_dict['java_annotations'])
                    except json.JSONDecodeError:
                        mapping_dict['java_annotations'] = []

                if mapping_dict.get('generation_metadata'):
                    try:
                        mapping_dict['generation_metadata'] = json.loads(mapping_dict['generation_metadata'])
                    except json.JSONDecodeError:
                        mapping_dict['generation_metadata'] = {}

                # Convert boolean fields
                mapping_dict['is_entity_field'] = bool(mapping_dict.get('is_entity_field', 0))
                mapping_dict['has_string_constructor'] = bool(mapping_dict.get('has_string_constructor', 0))

                result.append(mapping_dict)

            return result

    def get_data_class_mappings(self, program_id: str, structure_name: str = None) -> Dict[str, Any]:
        """
        Get data class mappings for template generation.

        Args:
            program_id: ID of the program
            structure_name: COBOL structure name (optional)

        Returns:
            Dict[str, Any]: Organized mapping data for template use
        """
        mappings = self.get_comprehensive_cobol_java_mappings(program_id)

        # Filter by structure if specified
        if structure_name:
            mappings = [m for m in mappings if m.get('chunk_name') == structure_name or m.get('cobol_name') == structure_name]

        # Organize by mapping type
        organized = {
            'service_info': {},
            'method_mappings': {},
            'parameter_mappings': {},
            'variable_mappings': {},
            'data_class_usage': {},
            'service_dependencies': {}
        }

        for mapping in mappings:
            mapping_type = mapping.get('mapping_type', '')
            cobol_name = mapping.get('cobol_name', '')

            if mapping_type == 'class':
                if not organized['service_info']:
                    organized['service_info'] = {
                        'service_name': mapping.get('java_name', ''),
                        'package': mapping.get('package_name', ''),
                        'business_purpose': mapping.get('business_purpose', '')
                    }
                organized['data_class_usage'][cobol_name] = {
                    'java_class_used': mapping.get('java_name', ''),
                    'usage_pattern': 'parseFromString for file reading' if mapping.get('has_string_constructor') else 'standard data class'
                }
            elif mapping_type == 'method':
                organized['method_mappings'][cobol_name] = {
                    'java_method_name': mapping.get('java_name', ''),
                    'method_signature': mapping.get('java_type', ''),
                    'business_purpose': mapping.get('business_purpose', '')
                }
            elif mapping_type == 'parameter':
                organized['parameter_mappings'][cobol_name] = {
                    'java_name': mapping.get('java_name', ''),
                    'java_type': mapping.get('java_type', ''),
                    'parameter_type': 'input/output',  # Could be enhanced
                    'business_name': mapping.get('business_purpose', '')
                }
            elif mapping_type in ['variable', 'field']:
                organized['variable_mappings'][cobol_name] = {
                    'java_variable_name': mapping.get('java_name', ''),
                    'business_name': mapping.get('business_purpose', ''),
                    'scope': 'method/class'  # Could be enhanced
                }
            elif mapping_type == 'service_dependency':
                # Service dependencies for bean injection
                organized['service_dependencies'][cobol_name] = {
                    'java_service_name': mapping.get('java_name', ''),
                    'field_name': self._convert_to_field_name(mapping.get('java_name', '')),
                    'java_method_name': mapping.get('generation_metadata', {}).get('method_name', 'process'),
                    'method_signature': mapping.get('java_type', ''),
                    'business_purpose': mapping.get('business_purpose', '')
                }

        return organized

    def _convert_to_field_name(self, class_name: str) -> str:
        """Convert Java class name to field name for dependency injection."""
        if not class_name:
            return ''

        # Convert from PascalCase to camelCase
        if class_name.endswith('Service'):
            field_name = class_name[0].lower() + class_name[1:]
        else:
            field_name = class_name[0].lower() + class_name[1:] + 'Service'

        return field_name

    def get_java_data_structures(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Get Java data structures for a program.

        This method retrieves information about Java classes that have been generated
        from COBOL data structures, including field mappings and metadata.

        Args:
            program_id: ID of the program

        Returns:
            List[Dict[str, Any]]: List of Java data structure information
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # Get all Java classes for this program from variables table
                cursor.execute('''
                               SELECT DISTINCT
                                   java_class,
                                   parent_java_class,
                                   MAX(is_java_entity_field) as is_entity,
                                   COUNT(*) as field_count,
                                   MAX(java_generation_timestamp) as last_updated
                               FROM variables
                               WHERE program_id = ? AND java_class IS NOT NULL
                               GROUP BY java_class, parent_java_class
                               ORDER BY java_class
                               ''', (program_id,))

                classes = cursor.fetchall()
                result = []

                for cls in classes:
                    class_info = dict(cls)
                    java_class_name = class_info['java_class']

                    # Get the COBOL structure name for this Java class
                    cursor.execute('''
                                   SELECT cobol_name FROM cobol_java_mappings
                                   WHERE program_id = ? AND java_name = ? AND mapping_type = 'class'
                                   LIMIT 1
                                   ''', (program_id, java_class_name))

                    cobol_mapping = cursor.fetchone()
                    cobol_structure_name = cobol_mapping['cobol_name'] if cobol_mapping else java_class_name

                    # Get fields for this class
                    cursor.execute('''
                                   SELECT
                                       name as cobol_name,
                                       java_name,
                                       java_data_type,
                                       java_annotations,
                                       business_name,
                                       description,
                                       data_type as cobol_data_type,
                                       level,
                                       java_mapping_notes
                                   FROM variables
                                   WHERE program_id = ? AND java_class = ?
                                   ORDER BY name
                                   ''', (program_id, java_class_name))

                    fields = cursor.fetchall()
                    field_list = []

                    for field in fields:
                        field_dict = dict(field)
                        # Parse JSON annotations
                        if field_dict.get('java_annotations'):
                            try:
                                field_dict['java_annotations'] = json.loads(field_dict['java_annotations'])
                            except json.JSONDecodeError:
                                field_dict['java_annotations'] = []
                        else:
                            field_dict['java_annotations'] = []

                        field_list.append(field_dict)

                    # Build complete structure info
                    structure_info = {
                        'cobol_structure_name': cobol_structure_name,
                        'java_class_name': java_class_name,
                        'package_name': 'com.generated.cobol',  # Default package
                        'is_entity': bool(class_info.get('is_entity', 0)),
                        'parent_structure': class_info.get('parent_java_class', ''),
                        'field_count': class_info['field_count'],
                        'last_updated': class_info['last_updated'],
                        'fields': field_list
                    }

                    result.append(structure_info)

                logger.info(f"Retrieved {len(result)} Java data structures for program {program_id}")
                return result

        except Exception as e:
            logger.error(f"Error retrieving Java data structures for program {program_id}: {str(e)}")
            return []

    def get_business_name_mappings(self, program_id: str) -> Dict[str, Dict[str, str]]:
        """
        Get business name mappings from variables table for data class generation.

        This provides the mapping from COBOL identifiers to business names
        that should be used when generating Java data classes.

        Args:
            program_id: ID of the program

        Returns:
            Dict[str, Dict[str, str]]: Mapping of COBOL name to business info
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                               SELECT name, business_name, description
                               FROM variables
                               WHERE program_id = ? AND business_name IS NOT NULL AND business_name != ''
                               ORDER BY name
                               ''', (program_id,))

                variables = cursor.fetchall()
                result = {}

                for var in variables:
                    result[var['name']] = {
                        'business_name': var['business_name'],
                        'description': var['description'] or ''
                    }

                logger.info(f"Retrieved {len(result)} business name mappings for program {program_id}")
                return result

        except Exception as e:
            logger.error(f"Error retrieving business name mappings for program {program_id}: {str(e)}")
            return {}

    def get_java_field_mappings(self, program_id: str, java_class: str = None) -> List[Dict[str, Any]]:
        """
        Get Java field mappings from the variables table.

        Args:
            program_id: ID of the program
            java_class: Java class name to filter by (optional)

        Returns:
            List[Dict[str, Any]]: List of field mappings with Java information
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            query = '''
                    SELECT * FROM variables
                    WHERE program_id = ? AND java_name IS NOT NULL \
                    '''
            params = [program_id]

            if java_class:
                query += " AND java_class = ?"
                params.append(java_class)

            query += " ORDER BY java_class, name"

            cursor.execute(query, params)
            variables = cursor.fetchall()

            result = []
            for var in variables:
                var_dict = dict(var)

                # Parse JSON fields
                if var_dict.get('java_annotations'):
                    try:
                        var_dict['java_annotations'] = json.loads(var_dict['java_annotations'])
                    except json.JSONDecodeError:
                        var_dict['java_annotations'] = []

                # Convert boolean fields
                var_dict['is_java_entity_field'] = bool(var_dict.get('is_java_entity_field', 0))

                result.append(var_dict)

            return result

    def get_java_classes_for_program(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Get all Java classes generated for a program.

        Args:
            program_id: ID of the program

        Returns:
            List[Dict[str, Any]]: List of Java class information
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get distinct Java classes from variables table
            cursor.execute('''
                           SELECT DISTINCT
                               java_class,
                               parent_java_class,
                               MAX(is_java_entity_field) as is_entity,
                               COUNT(*) as field_count,
                               MAX(java_generation_timestamp) as last_updated
                           FROM variables
                           WHERE program_id = ? AND java_class IS NOT NULL
                           GROUP BY java_class, parent_java_class
                           ORDER BY java_class
                           ''', (program_id,))

            classes = cursor.fetchall()
            result = []

            for cls in classes:
                class_dict = dict(cls)
                class_dict['is_entity'] = bool(class_dict.get('is_entity', 0))
                result.append(class_dict)

            return result

    def get_cobol_structure_hierarchy(self, program_id: str, variable_name: str) -> Dict[str, Any]:
        """
        Get COBOL structure hierarchy for a variable with Java mapping information.

        Args:
            program_id: ID of the program
            variable_name: Name of the variable

        Returns:
            Dict[str, Any]: Structure hierarchy information with Java mappings
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get the variable and its hierarchy
            cursor.execute('''
                           SELECT * FROM variables
                           WHERE program_id = ? AND name = ?
                           ''', (program_id, variable_name))

            variable = cursor.fetchone()

            if not variable:
                return {}

            variable_dict = dict(variable)

            # Parse JSON fields
            if variable_dict.get('java_annotations'):
                try:
                    variable_dict['java_annotations'] = json.loads(variable_dict['java_annotations'])
                except json.JSONDecodeError:
                    variable_dict['java_annotations'] = []

            # Get parent structure if exists
            if variable_dict.get('parent_name'):
                cursor.execute('''
                               SELECT * FROM variables
                               WHERE program_id = ? AND name = ?
                               ''', (program_id, variable_dict['parent_name']))

                parent = cursor.fetchone()
                if parent:
                    variable_dict['parent_info'] = dict(parent)

            # Get top structure if exists
            if variable_dict.get('top_parent_datastructure_name'):
                cursor.execute('''
                               SELECT * FROM variables
                               WHERE program_id = ? AND name = ?
                               ''', (program_id, variable_dict['top_parent_datastructure_name']))

                top_parent = cursor.fetchone()
                if top_parent:
                    variable_dict['top_parent_info'] = dict(top_parent)

            return variable_dict

    def get_mappings_by_type(self, mapping_type: str) -> List[Dict[str, Any]]:
        """
        Get all mappings of a specific type.

        Args:
            mapping_type: Type of mappings to retrieve

        Returns:
            List of mapping dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Remove '_names' suffix if present
            db_mapping_type = mapping_type.replace('_names', '')

            cursor.execute('''
                           SELECT * FROM cobol_java_mappings
                           WHERE mapping_type = ?
                           ORDER BY program_id, chunk_name, cobol_name
                           ''', (db_mapping_type,))

            mappings = cursor.fetchall()
            return [dict(mapping) for mapping in mappings]

    def delete_mappings(self, program_id: str, chunk_name: str = None) -> bool:
        """
        Delete mappings for a program or specific chunk.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk (optional, deletes all if None)

        Returns:
            True if mappings were deleted, False if none found
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Delete from cobol_java_mappings table
            if chunk_name:
                cursor.execute('''
                               DELETE FROM cobol_java_mappings
                               WHERE program_id = ? AND chunk_name = ?
                               ''', (program_id, chunk_name))
            else:
                cursor.execute('''
                               DELETE FROM cobol_java_mappings
                               WHERE program_id = ?
                               ''', (program_id,))

            deleted_count = cursor.rowcount

            # Clear Java mapping fields from variables table
            cursor.execute('''
                           UPDATE variables
                           SET java_name = NULL,
                               java_class = NULL,
                               parent_java_class = NULL,
                               full_qualified_name = NULL,
                               java_data_type = NULL,
                               java_annotations = NULL,
                               is_java_entity_field = 0,
                               java_mapping_notes = NULL,
                               java_generation_timestamp = NULL
                           WHERE program_id = ?
                           ''', (program_id,))

            conn.commit()

            if deleted_count > 0:
                logger.info(f"Deleted {deleted_count} mappings for program {program_id}")
                return True
            return False

    def get_mapping_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about mappings in the database.

        Returns:
            Dictionary containing mapping statistics
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Total class/method mappings
            cursor.execute("SELECT COUNT(*) FROM cobol_java_mappings")
            total_mappings = cursor.fetchone()[0]

            # Mappings by type
            cursor.execute("""
                           SELECT mapping_type, COUNT(*) as count
                           FROM cobol_java_mappings
                           GROUP BY mapping_type
                           ORDER BY count DESC
                           """)
            mappings_by_type = dict(cursor.fetchall())

            # Total variables with Java mappings
            cursor.execute("SELECT COUNT(*) FROM variables WHERE java_name IS NOT NULL")
            total_java_field_mappings = cursor.fetchone()[0]

            # Java classes generated
            cursor.execute("SELECT COUNT(DISTINCT java_class) FROM variables WHERE java_class IS NOT NULL")
            total_java_classes = cursor.fetchone()[0]

            # Entity vs Data classes
            cursor.execute("""
                           SELECT
                               SUM(CASE WHEN is_java_entity_field = 1 THEN 1 ELSE 0 END) as entity_fields,
                               SUM(CASE WHEN is_java_entity_field = 0 THEN 1 ELSE 0 END) as data_fields
                           FROM variables
                           WHERE java_name IS NOT NULL
                           """)
            entity_data_stats = cursor.fetchone()

            return {
                'total_class_method_mappings': total_mappings,
                'mappings_by_type': mappings_by_type,
                'total_java_field_mappings': total_java_field_mappings,
                'total_java_classes': total_java_classes,
                'entity_fields': entity_data_stats[0] if entity_data_stats else 0,
                'data_fields': entity_data_stats[1] if entity_data_stats else 0
            }