import os
import re
import magic
import logging
import chardet
from typing import Optional, Dict, List, Tuple
from pygments import lexers
from langchain.schema import HumanMessage, SystemMessage

import llm_settings

logger = logging.getLogger("tools.language_detector")

def _get_extension_mapping():
    """Get file extension mappings from plugins with fallback for non-language files."""
    try:
        from src.platform.plugins.plugin_loader import get_plugin_loader
        plugin_loader = get_plugin_loader()

        extension_mapping = {}

        # Get extensions from language plugins
        for plugin_name, plugin in plugin_loader.get_language_plugins().items():
            try:
                extensions = plugin.get_supported_extensions()
                for ext in extensions:
                    extension_mapping[ext.lower()] = plugin_name
            except Exception as e:
                logger.warning(f"Failed to get extensions from plugin {plugin_name}: {str(e)}")

        # Add non-language-specific extensions
        non_language_extensions = {
            # Config files
            '.cfg': 'config', '.conf': 'config', '.config': 'config', '.ini': 'config',
            '.json': 'config', '.xml': 'config', '.properties': 'config',
            # Data files
            '.dat': 'data', '.csv': 'data', '.txt': 'data', '.md': 'data', '.markdown': 'data',
            # Additional mainframe file types
            '.bms': 'bms',           # Basic Mapping Support for CICS
            '.catlg': 'data',        # Catalog files
            '.cpy-bms': 'bms',       # BMS copybooks
            '.csd': 'cics',          # CICS System Definition
            '.ctl': 'control',       # Control files
            '.data': 'data',         # Data files
            '.icl': 'control'        # Control language
        }
        extension_mapping.update(non_language_extensions)

        return extension_mapping

    except Exception as e:
        logger.warning(f"Failed to load extensions from plugins: {str(e)}")
        # Fallback to basic non-language extensions only
        return {
            '.cfg': 'config', '.conf': 'config', '.config': 'config', '.ini': 'config',
            '.json': 'config', '.xml': 'config', '.properties': 'config',
            '.dat': 'data', '.csv': 'data', '.txt': 'data', '.md': 'data', '.markdown': 'data'
        }

def _get_language_patterns():
    """Get language patterns from plugins only - no fallback patterns."""
    try:
        from src.platform.plugins.plugin_loader import get_plugin_loader
        plugin_loader = get_plugin_loader()

        patterns = {}
        for plugin_name, plugin in plugin_loader.get_language_plugins().items():
            detector = plugin.get_detector()
            if detector and hasattr(detector, 'get_patterns'):
                patterns[plugin_name] = detector.get_patterns()

        return patterns
    except Exception as e:
        logger.warning(f"Failed to load language patterns from plugins: {str(e)}")
        return {}

def detect_language(content_or_path: str, filename: Optional[str] = None, use_llm: bool = True) -> str:
    """
    Detect the programming language of content or a file

    Args:
        content_or_path: Either file content (string) or file path
        filename: Optional filename for extension-based detection
        use_llm: Whether to use LLM for advanced detection

    Returns:
        str: Detected language or 'other' if unknown
    """
    try:
        # Determine if this is a file path or content
        is_file_path = filename is None and (
            os.path.exists(content_or_path) or
            (('/' in content_or_path or '\\' in content_or_path) and
             len(content_or_path) < 500 and '\n' not in content_or_path)
        )

        if is_file_path:
            # Original file-based detection
            return _detect_language_from_file(content_or_path, use_llm)
        else:
            # Content-based detection
            return _detect_language_from_content(content_or_path, filename, use_llm)

    except Exception as e:
        logger.error(f"Error detecting language: {str(e)}")
        return 'other'


def _detect_language_from_file(file_path: str, use_llm: bool = True) -> str:
    """
    Detect the programming language of a file

    Args:
        file_path: Path to the file
        use_llm: Whether to use LLM for advanced detection

    Returns:
        str: Detected language or 'other' if unknown
    """
    try:
        # Check file extension first using plugin-based mapping
        extension_mapping = _get_extension_mapping()
        _, ext = os.path.splitext(file_path.lower())
        if ext.lower() in extension_mapping:
            logger.debug(f"Language detected by extension for {file_path}: {extension_mapping[ext]}")
            return extension_mapping[ext]

        # Try to detect language using Pygments with plugin-based mapping
        try:
            lexer = lexers.get_lexer_for_filename(file_path)
            pygments_lang = lexer.name.lower()

            # Map Pygments language to our plugin languages
            try:
                from src.platform.plugins.plugin_loader import get_plugin_loader
                plugin_loader = get_plugin_loader()
                available_languages = plugin_loader.get_available_languages()

                for lang in available_languages:
                    if lang in pygments_lang or pygments_lang in lang:
                        logger.debug(f"Pygments detected {pygments_lang} mapped to {lang} for {file_path}")
                        return lang
            except Exception:
                pass

            logger.debug(f"Pygments detected {pygments_lang} for {file_path} but no plugin mapping found")
        except:
            pass

        # Check file content patterns
        detected_lang = _detect_by_content_file(file_path)
        if detected_lang:
            logger.debug(f"Language detected by content for {file_path}: {detected_lang}")
            return detected_lang

        # Try using LLM for advanced detection if available
        if use_llm:
            llm_detected = _detect_by_llm_file(file_path)
            if llm_detected:
                logger.debug(f"Language detected by LLM for {file_path}: {llm_detected}")
                return llm_detected

        # Default to 'other' if we can't determine the language
        try:
            file_type = magic.from_file(file_path, mime=True)
            # Check if this is a text file
            if 'text' in file_type:
                return 'data' if 'csv' in file_type or 'plain' in file_type else 'config'
        except:
            pass

        # Binary or unknown file
        return 'other'

    except Exception as e:
        logger.error(f"Error detecting language for {file_path}: {str(e)}")
        return 'other'


def _detect_language_from_content(content: str, filename: Optional[str] = None, use_llm: bool = True) -> str:
    """
    Detect the programming language from content string

    Args:
        content: Code content as string
        filename: Optional filename for extension-based detection
        use_llm: Whether to use LLM for advanced detection

    Returns:
        str: Detected language or 'other' if unknown
    """
    try:
        # Check filename extension first if provided using plugin-based mapping
        if filename:
            extension_mapping = _get_extension_mapping()
            _, ext = os.path.splitext(filename.lower())
            if ext.lower() in extension_mapping:
                logger.debug(f"Language detected by extension for {filename}: {extension_mapping[ext]}")
                return extension_mapping[ext]

        # Check content against language patterns
        detected_lang = _detect_by_content_string(content)
        if detected_lang:
            logger.debug(f"Language detected by content patterns: {detected_lang}")
            return detected_lang

        # Try using LLM for advanced detection if available
        if use_llm and content.strip():
            llm_detected = _detect_by_llm_content(content)
            if llm_detected:
                logger.debug(f"Language detected by LLM: {llm_detected}")
                return llm_detected

        # Default to 'other' if we can't determine the language
        return 'other'

    except Exception as e:
        logger.error(f"Error detecting language from content: {str(e)}")
        return 'other'

def _detect_by_content_file(file_path: str) -> Optional[str]:
    """
    Detect programming language by analyzing file content

    Args:
        file_path: Path to the file

    Returns:
        Optional[str]: Detected language or None if couldn't detect
    """
    try:
        # Try to determine file encoding
        with open(file_path, 'rb') as f:
            raw_data = f.read(4096)  # Read first 4KB
            result = chardet.detect(raw_data)
            encoding = result['encoding'] if result['encoding'] else 'utf-8'

        # Read file with detected encoding
        with open(file_path, 'r', encoding=encoding, errors='replace') as f:
            lines = f.readlines()[:50]  # Examine first 50 lines

        return _detect_by_content_lines(lines)

    except Exception as e:
        logger.error(f"Error in content-based language detection for {file_path}: {str(e)}")
        return None


def _detect_by_content_string(content: str) -> Optional[str]:
    """
    Detect programming language by analyzing content string

    Args:
        content: Code content as string

    Returns:
        Optional[str]: Detected language or None if couldn't detect
    """
    try:
        lines = content.split('\n')[:50]  # Examine first 50 lines
        return _detect_by_content_lines(lines)

    except Exception as e:
        logger.error(f"Error in content-based language detection: {str(e)}")
        return None


def _detect_by_content_lines(lines: List[str]) -> Optional[str]:
    """
    Detect programming language by analyzing lines of code using plugins

    Args:
        lines: List of code lines

    Returns:
        Optional[str]: Detected language or None if couldn't detect
    """
    try:
        # Try plugin-based detection first
        content = '\n'.join(lines)

        try:
            from src.platform.plugins.plugin_loader import get_plugin_loader
            plugin_loader = get_plugin_loader()

            # Get all language plugins and try detection
            best_match = None
            best_confidence = 0.0

            # Use the plugin loader's detect_language method
            detected_language = plugin_loader.detect_language(content)
            if detected_language:
                return detected_language

        except Exception as e:
            logger.warning(f"Plugin-based detection failed: {str(e)}")

        # Fallback to pattern-based detection
        language_patterns = _get_language_patterns()
        lang_matches = {}

        # Get available languages from patterns (no hardcoded priority)
        available_languages = list(language_patterns.keys())
        available_languages.sort()  # Simple alphabetical sort

        for lang in available_languages:
            if lang in language_patterns:
                patterns = language_patterns[lang]
                lang_matches[lang] = 0
                for pattern in patterns:
                    for line in lines:
                        if re.search(pattern, line, re.IGNORECASE):
                            lang_matches[lang] += 1
                            break

        # If we have matches, return the language with most matches
        if any(lang_matches.values()):
            max_matches = max(lang_matches.values())
            # Return first language with max matches (order determined by priority)
            for lang in available_languages:
                if lang in lang_matches and lang_matches[lang] == max_matches:
                    return lang

        return None

    except Exception as e:
        logger.error(f"Error in pattern-based language detection: {str(e)}")
        return None

def _detect_by_llm_file(file_path: str) -> Optional[str]:
    """
    Use LLM to detect programming language of a file

    Args:
        file_path: Path to the file

    Returns:
        Optional[str]: Detected language or None if couldn't detect
    """
    try:
        # Try to determine file encoding
        with open(file_path, 'rb') as f:
            raw_data = f.read(4096)  # Read first 4KB
            result = chardet.detect(raw_data)
            encoding = result['encoding'] if result['encoding'] else 'utf-8'

        # Read file with detected encoding
        with open(file_path, 'r', encoding=encoding, errors='replace') as f:
            content = f.read(8192)  # First 8KB of the file

        return _detect_by_llm_content(content)

    except Exception as e:
        logger.error(f"Error in LLM-based language detection for {file_path}: {str(e)}")
        return None


def _detect_by_llm_content(content: str) -> Optional[str]:
    """
    Use LLM to detect programming language from content

    Args:
        content: Code content as string

    Returns:
        Optional[str]: Detected language or None if couldn't detect
    """
    try:
        # Try to use template for language identification
        try:
            from src.platform.tools.utils.template_manager import get_template_manager
            template_manager = get_template_manager()

            # Get supported languages from plugins
            supported_languages = []
            try:
                from src.platform.plugins.plugin_loader import get_plugin_loader
                plugin_loader = get_plugin_loader()
                supported_languages = plugin_loader.get_available_languages()
            except Exception:
                pass

            # Try to load the language identification template
            prompt = template_manager.render_template(
                "tools/language_identification.j2",
                {
                    "content": content[:4000],
                    "supported_languages": supported_languages
                }
            )
        except Exception as template_error:
            logger.warning(f"Could not load language identification template: {template_error}")
            # Fallback to dynamic prompt based on available plugins
            supported_languages_text = ""
            if supported_languages:
                supported_languages_text = f"Focus on these supported languages: {', '.join(supported_languages).upper()}.\n"

            prompt = f"""Identify the programming language of the following code snippet.
{supported_languages_text}If it's a config file of some kind, respond with "config".
If it's data or plaintext, respond with "data".
If you can't determine the language, respond with "other".

Respond with just one word, the language name in lowercase.

Code snippet:
```
{content[:4000]}
```"""

        messages = [
            SystemMessage(content="You are an expert in identifying programming languages, especially legacy mainframe languages."),
            HumanMessage(content=prompt)
        ]

        response = llm_settings.invoke_llm(messages)
        detected = response.strip().lower()

        # Normalize the response using available plugins
        try:
            from src.platform.plugins.plugin_loader import get_plugin_loader
            plugin_loader = get_plugin_loader()
            available_languages = plugin_loader.get_available_languages()
            # Add common non-plugin categories
            available_languages.extend(['config', 'data', 'other'])
        except Exception:
            available_languages = ['config', 'data', 'other']

        for lang in available_languages:
            if lang in detected:
                return lang

        return None

    except Exception as e:
        logger.error(f"Error in LLM-based language detection: {str(e)}")
        return None