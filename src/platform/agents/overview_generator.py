import os
import shutil
from typing import Dict, Any, List
import logging

import llm_settings
from llm_settings import invoke_llm
from src.platform.agents.base_agent import BaseAgent, AgentInput, AgentOutput

#TODO: refactor me to plugin architecture
class OverviewGeneratorAgent(BaseAgent):
    """
    Agent responsible for generating high-level overviews of COBOL programs
    based on their flow diagrams (dot files).
    """

    def __init__(self):
        super().__init__("overview_generator")

    def set_up(self, config: dict) -> None:
        """
        Set up current Agent using config gathered from a user's input

        Args:
            config: Configuration provided from a user interface
        """
        pass

    def process(self, input_data: AgentInput) -> AgentOutput:
        """
        Process dot files to generate overviews of COBOL programs

        Args:
            input_data: Input data containing working directory and knowledge base

        Returns:
            AgentOutput: Processing results
        """
        self.logger.info("Starting COBOL overview generation")

        try:
            # Define source and output directories
            dot_files_dir = os.path.join(input_data.working_directory, "preprocessed/cobol/rekt/dotfiles")
            flowcharts_dir = os.path.join(input_data.working_directory, "preprocessed/cobol/rekt/flowcharts")
            overview_dir = os.path.join(input_data.working_directory, "programs_overview")
            overview_img_dir = os.path.join(overview_dir, "img")

            # Create output directories if they don't exist
            os.makedirs(overview_dir, exist_ok=True)
            os.makedirs(overview_img_dir, exist_ok=True)

            # Check if source directories exist
            if not os.path.exists(dot_files_dir):
                return AgentOutput(
                    success=False,
                    message="Source directory for dot files not found",
                    errors=[f"Directory not found: {dot_files_dir}"]
                )

            if not os.path.exists(flowcharts_dir):
                return AgentOutput(
                    success=False,
                    message="Source directory for flowcharts not found",
                    errors=[f"Directory not found: {flowcharts_dir}"]
                )

            # Get list of dot files
            dot_files = [f for f in os.listdir(dot_files_dir) if f.endswith('.dot')]

            if not dot_files:
                return AgentOutput(
                    success=False,
                    message="No dot files found to process",
                    errors=["No .dot files found in source directory"]
                )

            self.report_progress(f"Found {len(dot_files)} dot files to process", 0.1)

            # Process each dot file
            processed_files = []
            total_files = len(dot_files)

            for i, dot_file in enumerate(dot_files):
                program_name = os.path.splitext(dot_file)[0]
                self.logger.info(f"Processing program: {program_name} ({i+1}/{total_files})")

                # Define file paths
                dot_file_path = os.path.join(dot_files_dir, dot_file)
                flowchart_file = f"{program_name}.png"
                flowchart_path = os.path.join(flowcharts_dir, flowchart_file)
                overview_path = os.path.join(overview_dir, f"{program_name}.md")
                overview_img_path = os.path.join(overview_img_dir, flowchart_file)

                try:
                    # Read dot file content
                    with open(dot_file_path, 'r') as f:
                        dot_content = f.read()

                    # Generate program overview using LLM
                    overview_text = self._generate_overview(dot_content, program_name)

                    # Copy flowchart image to output directory
                    if os.path.exists(flowchart_path):
                        shutil.copy2(flowchart_path, overview_img_path)

                        # Add relative image reference to the overview
                        overview_text += f"\n\n## Program Flow Diagram\n\n![{program_name} Flow Diagram](img/{flowchart_file})\n"
                    else:
                        self.logger.warning(f"Flowchart image not found for {program_name}")

                    # Write overview to file
                    with open(overview_path, 'w') as f:
                        f.write(overview_text)

                    processed_files.append({
                        "program_name": program_name,
                        "overview_path": overview_path,
                        "flowchart_path": overview_img_path if os.path.exists(flowchart_path) else None
                    })

                    progress = (i + 1) / total_files
                    self.report_progress(f"Generated overview for {program_name}", progress * 0.9)

                except Exception as e:
                    self.logger.error(f"Error processing {program_name}: {str(e)}")

            self.report_progress("COBOL overview generation completed", 1.0)

            return AgentOutput(
                success=True,
                message=f"Successfully generated overviews for {len(processed_files)} COBOL programs",
                knowledge_base_updates={
                    "cobol_overviews": {
                        "directory": overview_dir,
                        "processed_files": processed_files
                    }
                }
            )

        except Exception as e:
            self.logger.exception(f"Error during COBOL overview generation: {str(e)}")
            return AgentOutput(
                success=False,
                message=f"COBOL overview generation failed: {str(e)}",
                errors=[str(e)]
            )

    def _generate_overview(self, dot_content: str, program_name: str) -> str:
        """
        Generate an overview of the COBOL program using the LLM

        Args:
            dot_content: Content of the dot file
            program_name: Name of the COBOL program

        Returns:
            str: Generated overview
        """
        from src.platform.tools.utils.template_manager import get_template_manager
        template_manager = get_template_manager()

        try:
            prompt = template_manager.render_template(
                "overview_generator/program_overview.j2",
                {
                    "program_name": program_name,
                    "dot_content": dot_content
                }
            )
        except Exception as e:
            self.logger.error(f"Could not load template: {str(e)}")
            raise RuntimeError(f"Template loading failed: {str(e)}")

        self.logger.info(f"Generating overview for {program_name} using LLM")

        try:
            # Call the LLM with the prompt
            from langchain.schema import HumanMessage, SystemMessage
            messages = [
                SystemMessage(content="You are an expert COBOL analyst."),
                HumanMessage(content=prompt)
            ]
            self.logger.info(f"OverviewGeneratorAgent: Invoke LLL for {program_name})")

            response = invoke_llm(messages)

            # Return the generated overview
            return response

        except Exception as e:
            self.logger.error(f"OverviewGeneratorAgent: Error calling LLM for {program_name}: {str(e)}")
            # Return a basic overview in case of LLM failure
            return f"# {program_name} Overview\n\nUnable to generate overview: LLM error."
