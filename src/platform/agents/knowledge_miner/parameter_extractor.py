"""
Parameter extraction functionality for KnowledgeMinerAgent.
Handles extraction of input and output parameters from COBOL code.
"""
import json
import re
import logging
from typing import Dict, List, Any

from langchain.schema import HumanMessage, SystemMessage
from config.constants import COBOL_ANALYST_SYSTEM_MSG
from llm_settings import invoke_llm
from src.platform.tools.ims_segment_mapper import get_ims_segment_mapper
from src.platform.plugins.plugin_loader import get_plugin_loader
from src.platform.tools.language_detector import detect_language


class ParameterExtractor:
    """
    Handles extraction of input and output parameters from COBOL code chunks.
    Uses LLM with templates to identify business-relevant parameters.
    """

    def __init__(self, template_manager):
        """
        Initialize the parameter extractor.

        Args:
            template_manager: Template manager for rendering prompts
        """

        self.template_manager = template_manager
        self.logger = logging.getLogger(__name__)
        self.ims_mapper = get_ims_segment_mapper()

    def extract_input_parameters(self, program_id: str, chunk_name: str,
                                 code_content: str, context: str) -> List[Dict[str, Any]]:
        """
        Extract input parameters from COBOL code using LLM.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            code_content: COBOL code content
            context: Combined context about variables and dependencies

        Returns:
            List[Dict[str, Any]]: List of input parameters
        """
        try:
            # Detect language to get appropriate plugin
            detected_language = detect_language(code_content)
            if not detected_language:
                detected_language = "cobol"  # Default fallback

            # Get plugin loader and language plugin
            try:
                plugin_loader = get_plugin_loader()
                language_plugin = plugin_loader.get_language_plugin(detected_language)

                if language_plugin:
                    # Use language-specific template from plugin
                    input_params_prompt = self._get_language_specific_template(
                        language_plugin, detected_language, "input_parameters",
                        {
                            "program_id": program_id,
                            "chunk_name": chunk_name,
                            "code_content": code_content,
                            "context": context
                        }
                    )
                else:
                    raise Exception(f"No plugin available for language: {detected_language}")
            except Exception as e:
                self.logger.warning(f"Plugin-based template loading failed: {e}")
                # Fallback to generic template with IMS segment context
                ims_context = self.ims_mapper.get_segments_for_template_context(code_content)
                ims_context_str = ""
                if ims_context.get('has_ims_segments'):
                    ims_context_str = f"\n\nIMS Segment Context:\n"
                    for segment, business_name in ims_context.get('segment_business_mappings', {}).items():
                        ims_context_str += f"- {segment}: {business_name}\n"

                input_params_prompt = f"""
                Extract input parameters from the following code chunk:

                Program: {program_id}
                Chunk: {chunk_name}

                Code:
                {code_content}

                Context: {context}{ims_context_str}

                Return a JSON array of input parameters with name, type, business_name, and description fields.
                Use the IMS segment business context to enhance business_name and description fields when applicable.
                If no input parameters are found, return an empty array: []
                """

            input_messages = [
                SystemMessage(content=COBOL_ANALYST_SYSTEM_MSG),
                HumanMessage(content=input_params_prompt)
            ]

            input_response = invoke_llm(input_messages)

            # Parse the response to extract the parameters
            return self._parse_parameters_from_response(input_response, "input")

        except Exception as e:
            self.logger.error(f"Error extracting input parameters for {program_id}.{chunk_name}: {str(e)}")
            return []

    def extract_output_parameters(self, program_id: str, chunk_name: str,
                                  code_content: str, context: str) -> List[Dict[str, Any]]:
        """
        Extract output parameters from COBOL code using LLM.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            code_content: COBOL code content
            context: Combined context about variables and dependencies

        Returns:
            List[Dict[str, Any]]: List of output parameters
        """
        try:
            # Detect language to get appropriate plugin
            detected_language = detect_language(code_content)
            if not detected_language:
                detected_language = "cobol"  # Default fallback

            # Get plugin loader and language plugin
            try:
                plugin_loader = get_plugin_loader()
                language_plugin = plugin_loader.get_language_plugin(detected_language)

                if language_plugin:
                    # Use language-specific template from plugin
                    output_params_prompt = self._get_language_specific_template(
                        language_plugin, detected_language, "output_parameters",
                        {
                            "program_id": program_id,
                            "chunk_name": chunk_name,
                            "code_content": code_content,
                            "context": context
                        }
                    )
                else:
                    raise Exception(f"No plugin available for language: {detected_language}")
            except Exception as e:
                self.logger.warning(f"Plugin-based template loading failed: {e}")
                # Fallback to generic template with IMS segment context
                ims_context = self.ims_mapper.get_segments_for_template_context(code_content)
                ims_context_str = ""
                if ims_context.get('has_ims_segments'):
                    ims_context_str = f"\n\nIMS Segment Context:\n"
                    for segment, business_name in ims_context.get('segment_business_mappings', {}).items():
                        ims_context_str += f"- {segment}: {business_name}\n"

                output_params_prompt = f"""
                Extract output parameters from the following code chunk:

                Program: {program_id}
                Chunk: {chunk_name}

                Code:
                {code_content}

                Context: {context}{ims_context_str}

                Return a JSON array of output parameters with name, type, business_name, and description fields.
                Use the IMS segment business context to enhance business_name and description fields when applicable.
                If no output parameters are found, return an empty array: []
                """

            output_messages = [
                SystemMessage(content=COBOL_ANALYST_SYSTEM_MSG),
                HumanMessage(content=output_params_prompt)
            ]

            output_response = invoke_llm(output_messages)

            # Parse the response to extract the parameters
            return self._parse_parameters_from_response(output_response, "output")

        except Exception as e:
            self.logger.error(f"Error extracting output parameters for {program_id}.{chunk_name}: {str(e)}")
            return []

    def _parse_parameters_from_response(self, response: str, param_type: str) -> List[Dict[str, Any]]:
        """
        Parse parameters from LLM response.

        Args:
            response: LLM response text
            param_type: Type of parameters ("input" or "output")

        Returns:
            List[Dict[str, Any]]: Parsed parameters
        """
        try:
            # Try to extract JSON array
            json_match = re.search(r'(\[.*\])', response, re.DOTALL)
            if json_match:
                try:
                    # Try to parse the extracted JSON
                    json_str = json_match.group(1)
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    # If JSON parsing fails, try to clean the string
                    self.logger.warning(f"Invalid JSON in {param_type} parameters response, attempting to clean")
                    cleaned_json = re.sub(r'(?m)^\s*//.*\n?', '', json_str)  # Remove comments
                    cleaned_json = re.sub(r',\s*]', ']', cleaned_json)  # Fix trailing commas
                    try:
                        return json.loads(cleaned_json)
                    except:
                        # If all parsing fails, extract names directly
                        var_matches = re.findall(r'"name":\s*"([^"]+)"', response, re.DOTALL)
                        if var_matches:
                            return [
                                {"name": name, "type": "variable", "business_name": name, "description": f"{param_type.capitalize()} parameter"}
                                for name in var_matches
                            ]
                        else:
                            return []
            else:
                # Try direct extraction of variable names
                var_matches = re.findall(r'"name":\s*"([^"]+)"', response, re.DOTALL)
                if var_matches:
                    return [
                        {"name": name, "type": "variable", "business_name": name, "description": f"{param_type.capitalize()} parameter"}
                        for name in var_matches
                    ]
                else:
                    # Try one more pattern - looking for simple string array
                    simple_matches = re.findall(r'"([A-Za-z0-9-_]+)"', response)
                    if simple_matches:
                        return [
                            {"name": name, "type": "variable", "business_name": name, "description": f"{param_type.capitalize()} parameter"}
                            for name in simple_matches
                        ]
                    else:
                        return []
        except Exception as e:
            self.logger.error(f"Error parsing {param_type} parameters: {str(e)}")
            return []

    def _get_language_specific_template(self, language_plugin, language: str, template_type: str, context: Dict[str, Any]) -> str:
        """
        Get language-specific template from plugin.

        Args:
            language_plugin: The language plugin instance
            language: Language name
            template_type: Type of template (e.g., "input_parameters", "output_parameters")
            context: Template context variables

        Returns:
            str: Rendered template content
        """
        try:
            # Try to get template manager from plugin (generic approach)
            if hasattr(language_plugin, 'get_template_manager'):
                template_manager = language_plugin.get_template_manager()
                template_name = f"knowledge_miner/{template_type}.j2"
                return template_manager.render_template(template_name, context)
            else:
                # Fallback: try to dynamically import language-specific template manager
                try:
                    module_path = f"src.plugins.legacy.{language}.tools.template_manager"
                    module = __import__(module_path, fromlist=[f"get_{language}_template_manager"])
                    get_template_manager_func = getattr(module, f"get_{language}_template_manager")
                    template_manager = get_template_manager_func()
                    template_name = f"knowledge_miner/{template_type}.j2"
                    return template_manager.render_template(template_name, context)
                except Exception as e:
                    self.logger.warning(f"Could not load language-specific template manager for {language}: {e}")
                    raise e
        except Exception as e:
            self.logger.error(f"Error loading template {template_type} for language {language}: {e}")
            raise e
