"""
Utility functions for KnowledgeMinerAgent.
Contains helper functions for database operations, file handling, and data processing.
"""
import os
import re
import sqlite3
import datetime
import logging
from typing import Dict, List, Any, Optional

from src.platform.agents.base_agent import AgentInput


class KnowledgeMinerUtils:
    """
    Utility functions for knowledge mining operations.
    Handles database operations, file processing, and data manipulation.
    """

    def __init__(self, knowledge_db):
        """
        Initialize the utils with knowledge database.

        Args:
            knowledge_db: Knowledge database instance
        """
        self.knowledge_db = knowledge_db
        self.logger = logging.getLogger(__name__)

    def get_all_cobol_programs(self) -> List[Dict[str, Any]]:
        """
        Get all COBOL programs from the database

        Returns:
            List[Dict[str, Any]]: List of COBOL programs
        """
        try:
            conn = sqlite3.connect(self.knowledge_db.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM programs WHERE language = 'cobol'")
            programs = [dict(row) for row in cursor.fetchall()]

            conn.close()
            return programs
        except Exception as e:
            self.logger.error(f"Error getting COBOL programs: {str(e)}")
            return []

    def get_chunk_from_database(self, program_id: str, chunk_name: str) -> Optional[Dict[str, Any]]:
        """
        Get chunk from database to check analysis status.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk

        Returns:
            Optional[Dict[str, Any]]: Chunk data or None if not found
        """
        try:
            conn = sqlite3.connect(self.knowledge_db.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                           SELECT analysis_status FROM chunks
                           WHERE program_id = ? AND chunk_name = ?
                           """, (program_id, chunk_name))

            row = cursor.fetchone()
            conn.close()

            if row:
                return dict(row)
            return None

        except Exception as e:
            self.logger.error(f"Error getting chunk from database: {str(e)}")
            return None

    def get_program_variables(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Get all variables for a program

        Args:
            program_id: ID of the program

        Returns:
            List[Dict[str, Any]]: List of variables
        """
        try:
            conn = sqlite3.connect(self.knowledge_db.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                           SELECT * FROM variables WHERE program_id = ?
                           """, (program_id,))

            variables = [dict(row) for row in cursor.fetchall()]
            conn.close()
            return variables
        except Exception as e:
            self.logger.error(f"Error getting variables for program {program_id}: {str(e)}")
            return []

    def find_variables_in_code(self, code_content: str, variable_dict: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Find variables used in the given code content.

        Args:
            code_content: COBOL code content
            variable_dict: Dictionary of available variables

        Returns:
            List[Dict[str, Any]]: List of variables found in the code
        """
        used_variables = []

        # Convert code to uppercase for matching (COBOL is case-insensitive)
        code_upper = code_content.upper()

        for var_name, var_info in variable_dict.items():
            # Look for the variable name in the code
            # Use word boundaries to avoid partial matches
            pattern = r'\b' + re.escape(var_name.upper()) + r'\b'
            if re.search(pattern, code_upper):
                used_variables.append(var_info)

        return used_variables

    def is_exit_only_chunk(self, code_content: str) -> bool:
        """
        Check if a code chunk contains only EXIT statements or is effectively empty.

        Args:
            code_content: COBOL code content

        Returns:
            bool: True if chunk should be skipped, False otherwise
        """
        if not code_content or not code_content.strip():
            return True

        lines = code_content.split('\n')
        significant_lines = []

        for line in lines:
            stripped_line = line.strip()

            # Skip empty lines and comments
            if not stripped_line or stripped_line.startswith('*'):
                continue

            # Skip line numbers (if present)
            if re.match(r'^\d+\s', stripped_line):
                stripped_line = re.sub(r'^\d+\s+', '', stripped_line)

            # Skip paragraph names (identifiers followed by a period)
            if stripped_line.endswith('.') and len(stripped_line.split()) == 1:
                # Check if it's just an identifier followed by a period (no spaces before the period)
                if ' ' not in stripped_line.rstrip('.'):
                    continue

            significant_lines.append(stripped_line)

        # Check if all significant lines are just EXIT statements
        for line in significant_lines:
            if not line.endswith('EXIT.') and line != 'EXIT.' and line != 'EJECT':
                return False

        # If we only have EXIT statements (or no significant lines), skip this chunk
        return True

    def mark_chunk_as_skipped(self, program_id: str, chunk_name: str, reason: str):
        """
        Mark a chunk as skipped in the database.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            reason: Reason for skipping
        """
        try:
            conn = sqlite3.connect(self.knowledge_db.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                UPDATE chunks
                SET analysis_status = 'skipped', skip_reason = ?
                WHERE program_id = ? AND chunk_name = ?
            """, (reason, program_id, chunk_name))

            conn.commit()
            conn.close()
        except Exception as e:
            self.logger.error(f"Error marking chunk as skipped: {str(e)}")

    def initialize_analysis_result(self, program_id: str, chunk_name: str) -> Dict[str, Any]:
        """
        Initialize the analysis result dictionary with default values.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk

        Returns:
            Dict[str, Any]: Initialized analysis result
        """
        return {
            "input_parameters": [],
            "output_parameters": [],
            "business_name": f"Function in {chunk_name}",
            "business_description": f"Code chunk from {program_id}.{chunk_name}",
            "business_logic": "",
            "functional_spec": ""
        }

    def prepare_variable_context(self, used_variables: List[Dict[str, Any]]) -> str:
        """
        Prepare a context string describing variables used in the code.

        Args:
            used_variables: List of variables used in the code

        Returns:
            str: Context string about variables
        """
        variable_context = ""
        if used_variables:
            variable_context = "Variables used in this chunk:\n"
            for var in used_variables:
                var_desc = f"- {var['name']}: "
                if var.get('business_name'):
                    var_desc += f"Business name: {var['business_name']}, "
                if var.get('data_type'):
                    var_desc += f"Type: {var['data_type']}, "
                if var.get('description'):
                    var_desc += f"Description: {var['description']}"
                variable_context += var_desc + "\n"
        return variable_context

    def prepare_dependency_context(self, referenced_chunks_analysis: List[Dict[str, Any]]) -> str:
        """
        Prepare a context string describing called procedures and their business meaning.

        Args:
            referenced_chunks_analysis: List of analysis results for called procedures

        Returns:
            str: Context string about called procedures
        """
        if not referenced_chunks_analysis:
            return ""

        context = "Called procedures and their business functions:\n"

        for analysis in referenced_chunks_analysis:
            # Extract the procedure name (remove program prefix)
            proc_name = analysis.get('name', '').split('_PROC_')[-1] if analysis.get('name') else 'Unknown'

            chunk_desc = f"- {proc_name}:\n"

            if analysis.get('business_name'):
                chunk_desc += f"  Business function: {analysis['business_name']}\n"

            if analysis.get('business_description'):
                chunk_desc += f"  Description: {analysis['business_description']}\n"

            # Add input parameters
            input_params = analysis.get('input_parameters', [])
            if input_params:
                chunk_desc += "  Inputs: "
                param_strs = []
                for param in input_params:
                    param_str = param.get('name', '')
                    if param.get('business_name'):
                        param_str += f" ({param['business_name']})"
                    param_strs.append(param_str)
                chunk_desc += ", ".join(param_strs) + "\n"

            # Add output parameters
            output_params = analysis.get('output_parameters', [])
            if output_params:
                chunk_desc += "  Outputs: "
                param_strs = []
                for param in output_params:
                    param_str = param.get('name', '')
                    if param.get('business_name'):
                        param_str += f" ({param['business_name']})"
                    param_strs.append(param_str)
                chunk_desc += ", ".join(param_strs) + "\n"

            # Add business logic summary
            if analysis.get('business_logic'):
                chunk_desc += f"  Key business rules: {analysis['business_logic'][:200]}...\n"

            context += chunk_desc + "\n"

        return context

    def aggregate_business_rules(self, referenced_chunks_analysis: List[Dict[str, Any]]) -> str:
        """
        Aggregate business rules from called procedures.

        Args:
            referenced_chunks_analysis: List of analysis results for called procedures

        Returns:
            str: Aggregated business rules context
        """
        if not referenced_chunks_analysis:
            return ""

        business_rules = []

        for analysis in referenced_chunks_analysis:
            logic = analysis.get('business_logic', '')
            if logic and logic != "No explicit business rules found.":
                proc_name = analysis.get('name', '').split('_PROC_')[-1] if analysis.get('name') else 'Unknown'
                business_rules.append(f"From {proc_name}: {logic}")

        if business_rules:
            return "Business rules from called procedures:\n" + "\n".join(business_rules)
        return ""

    def get_analysis_from_database(self, program_id: str, chunk_name: str) -> Optional[Dict[str, Any]]:
        """
        Get existing analysis from database.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk

        Returns:
            Optional[Dict[str, Any]]: Analysis data or None if not found
        """
        try:
            # This would need to be implemented based on your database schema
            # For now, return None to indicate no existing analysis
            return None
        except Exception as e:
            self.logger.error(f"Error getting analysis from database: {str(e)}")
            return None

    def save_analysis_to_database(self, program_id: str, chunk_name: str,
                                  analysis_result: Dict[str, Any], status: str):
        """
        Save analysis results to database.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            analysis_result: Analysis results to save
            status: Analysis status
        """
        try:
            # Prepare analysis data with status
            analysis_data = {
                'business_name': analysis_result.get('business_name', ''),
                'business_description': analysis_result.get('business_description', ''),
                'business_logic': analysis_result.get('business_logic', ''),
                'functional_spec': analysis_result.get('functional_spec', ''),
                'input_parameters': analysis_result.get('input_parameters', []),
                'output_parameters': analysis_result.get('output_parameters', []),
                'analysis_status': status
            }

            # Update the chunk with analysis results using the correct method
            self.knowledge_db.update_chunk_analysis(
                program_id=program_id,
                chunk_name=chunk_name,
                analysis_data=analysis_data
            )

        except Exception as e:
            self.logger.error(f"Error saving analysis to database: {str(e)}")

    def build_knowledge_base_from_database(self) -> Dict[str, Any]:
        """
        Build knowledge base updates from database.

        Returns:
            Dict[str, Any]: Knowledge base updates
        """
        try:
            # Get business logic summaries
            business_logic_summaries = self._get_business_logic_summaries()

            # Get documentation summaries
            documentation_summaries = self._get_documentation_summaries()

            return {
                "business_logic_summaries": business_logic_summaries,
                "documentation_summaries": documentation_summaries
            }
        except Exception as e:
            self.logger.error(f"Error building knowledge base: {str(e)}")
            return {}

    def _get_business_logic_summaries(self) -> Dict[str, Any]:
        """Get business logic summaries from database."""
        try:
            conn = sqlite3.connect(self.knowledge_db.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT program_id, chunk_name, business_name, business_description, business_logic
                FROM chunks
                WHERE analysis_status = 'complete'
                ORDER BY program_id, chunk_name
            """)

            summaries = {}
            for row in cursor.fetchall():
                program_id = row['program_id']
                if program_id not in summaries:
                    summaries[program_id] = []

                summaries[program_id].append({
                    "chunk_name": row['chunk_name'],
                    "business_name": row['business_name'],
                    "business_description": row['business_description'],
                    "business_logic": row['business_logic']
                })

            conn.close()
            return summaries
        except Exception as e:
            self.logger.error(f"Error getting business logic summaries: {str(e)}")
            return {}

    def _get_documentation_summaries(self) -> Dict[str, Any]:
        """Get documentation summaries from database."""
        try:
            conn = sqlite3.connect(self.knowledge_db.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute("""
                SELECT program_id, chunk_name, functional_spec
                FROM chunks
                WHERE analysis_status = 'complete' AND functional_spec IS NOT NULL
                ORDER BY program_id, chunk_name
            """)

            summaries = {}
            for row in cursor.fetchall():
                program_id = row['program_id']
                if program_id not in summaries:
                    summaries[program_id] = []

                summaries[program_id].append({
                    "chunk_name": row['chunk_name'],
                    "functional_spec": row['functional_spec']
                })

            conn.close()
            return summaries
        except Exception as e:
            self.logger.error(f"Error getting documentation summaries: {str(e)}")
            return {}

    def save_analysis_to_markdown(self, input_data: AgentInput, program_id: str,
                                  chunk_name: str, analysis_result: Dict[str, Any]) -> Optional[str]:
        """
        Save analysis results to markdown file.

        Args:
            input_data: Input data containing working directory
            program_id: ID of the program
            chunk_name: Name of the chunk
            analysis_result: Analysis results to save

        Returns:
            Optional[str]: Path to saved markdown file, or None if failed
        """
        try:
            # Create documentation directory
            docs_dir = os.path.join(input_data.working_directory, "documentation_for_chunks", program_id)
            os.makedirs(docs_dir, exist_ok=True)

            # Create markdown content
            markdown_content = self._generate_markdown_content(program_id, chunk_name, analysis_result)

            # Save to file
            filename = f"{chunk_name}.md"
            file_path = os.path.join(docs_dir, filename)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            return file_path

        except Exception as e:
            self.logger.error(f"Error saving analysis to markdown: {str(e)}")
            return None

    def _generate_markdown_content(self, program_id: str, chunk_name: str,
                                   analysis_result: Dict[str, Any]) -> str:
        """Generate markdown content for analysis results."""
        content = f"""# Business Analysis: {program_id}.{chunk_name}

## Business Name
{analysis_result.get('business_name', 'N/A')}

## Business Description
{analysis_result.get('business_description', 'N/A')}

## Input Parameters
"""

        input_params = analysis_result.get('input_parameters', [])
        if input_params:
            content += "| Name | Type | Business Name | Description |\n"
            content += "|------|------|---------------|-------------|\n"
            for param in input_params:
                content += f"| {param.get('name', '')} | {param.get('type', '')} | {param.get('business_name', '')} | {param.get('description', '')} |\n"
        else:
            content += "No input parameters identified.\n"

        content += "\n## Output Parameters\n"

        output_params = analysis_result.get('output_parameters', [])
        if output_params:
            content += "| Name | Type | Business Name | Description |\n"
            content += "|------|------|---------------|-------------|\n"
            for param in output_params:
                content += f"| {param.get('name', '')} | {param.get('type', '')} | {param.get('business_name', '')} | {param.get('description', '')} |\n"
        else:
            content += "No output parameters identified.\n"

        content += f"""
## Business Logic
{analysis_result.get('business_logic', 'N/A')}

## Functional Specification
{analysis_result.get('functional_spec', 'N/A')}

---
*Generated on {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        return content

    def generate_documentation_index(self, input_data: AgentInput, generated_docs: List[str]) -> Optional[str]:
        """
        Generate an index file for all documentation.

        Args:
            input_data: Input data containing working directory
            generated_docs: List of generated documentation file paths

        Returns:
            Optional[str]: Path to index file, or None if failed
        """
        try:
            content = f"""# Business Analysis Documentation Index

Generated on {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Total documents: {len(generated_docs)}

## Documents by Program

"""

            # Group by program
            docs_by_program = {}
            for doc_path in generated_docs:
                filename = os.path.basename(doc_path)
                if '_' in filename:
                    program_id = filename.split('_')[0]
                    if program_id not in docs_by_program:
                        docs_by_program[program_id] = []
                    docs_by_program[program_id].append(doc_path)

            docs_dir = os.path.join(input_data.working_directory, "documentation_for_chunks", program_id)
            # Create documentation directory if it doesn't exist
            os.makedirs(docs_dir, exist_ok=True)
            index_path = os.path.join(docs_dir, "business_analysis_index.md")

            for program_id, docs in sorted(docs_by_program.items()):
                content += f"### {program_id}\n\n"
                for doc_path in sorted(docs):
                    filename = os.path.basename(doc_path)
                    chunk_name = filename.replace('.md', '').replace(f'{program_id}_', '')
                    relative_path = os.path.relpath(doc_path, docs_dir)
                    content += f"- [{chunk_name}]({relative_path})\n"
                content += "\n"

            with open(index_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return index_path

        except Exception as e:
            self.logger.error(f"Error generating documentation index: {str(e)}")
            return None
