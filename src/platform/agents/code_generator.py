"""
Platform wrapper for Code Generator Agent.
This provides a language-agnostic interface to target-specific code generators.
"""
from typing import Dict, Any

from src.platform.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from src.platform.tools.utils.template_manager import get_template_manager
import llm_settings


class CodeGeneratorAgent(BaseAgent):
    """
    Platform wrapper for code generation agents.
    Provides a unified interface for different target technologies.
    """

    def __init__(self):
        super().__init__("code_generator")
        self.template_manager = get_template_manager()



    def set_up(self, config: dict) -> None:
        """Set up the agent with configuration."""
        pass

    def process(self, input_data: AgentInput) -> AgentOutput:
        """
        Process code generation request using plugin system.

        Args:
            input_data: Input data containing working directory and knowledge base

        Returns:
            AgentOutput: Code generation results
        """
        self.logger.info("Starting code generation")

        try:
            # Get target technology from input data or configuration
            target_technology = input_data.knowledge_base.get("target_technology", "java_spring")

            # Use plugin system to get appropriate target plugin
            from src.platform.plugins.plugin_loader import get_plugin_loader
            plugin_loader = get_plugin_loader()

            target_plugin = plugin_loader.get_target_plugin(target_technology)
            if not target_plugin:
                raise ValueError(f"No plugin available for target technology: {target_technology}")

            # Get code generator from target plugin
            if hasattr(target_plugin, 'get_code_generator'):
                code_generator = target_plugin.get_code_generator()
                if code_generator:
                    # Prepare source analysis and config for the code generator
                    # Pass the entire knowledge base as source_analysis
                    source_analysis = {"knowledge_base": input_data.knowledge_base}
                    config = input_data.knowledge_base.get("config", {})

                    # Add working directory to config
                    config["working_directory"] = input_data.working_directory

                    # Delegate to plugin-specific code generator
                    result = code_generator.generate_code(source_analysis, config)

                    # Transform the result to the format expected by code_reviewer
                    generated_code = self._transform_result_for_reviewer(result, target_technology)

                    return AgentOutput(
                        success=True,
                        message=f"Code generation completed successfully for {target_technology}",
                        knowledge_base_updates={
                            "generated_code": generated_code
                        }
                    )
                else:
                    raise ValueError(f"Target plugin {target_technology} does not provide a code generator")
            else:
                raise ValueError(f"Target plugin {target_technology} does not support code generation")

        except Exception as e:
            self.logger.exception(f"Error during code generation: {str(e)}")
            return AgentOutput(
                success=False,
                message=f"Code generation failed: {str(e)}",
                errors=[str(e)]
            )

    def _transform_result_for_reviewer(self, result: Dict[str, Any], target_technology: str) -> Dict[str, Any]:
        """
        Transform plugin result to format expected by code_reviewer.

        Code reviewer expects: {service_name: {file_name: file_content}}
        Plugin returns: {generated_files: {file_name: file_content}}

        Args:
            result: Result from plugin code generator
            target_technology: Target technology name

        Returns:
            Dict in format expected by code_reviewer
        """
        try:
            generated_files = result.get("generated_files", {})

            if not generated_files:
                self.logger.warning("No generated files found in plugin result")
                return {}

            # Group files by service/microservice
            # For now, create a single service with all files
            service_name = f"{target_technology}_microservice"

            return {
                service_name: generated_files
            }

        except Exception as e:
            self.logger.error(f"Error transforming result for reviewer: {str(e)}")
            return {}


# Re-export for backward compatibility
__all__ = ['CodeGeneratorAgent']
