import os
import shutil
import tarfile
import zipfile
import datetime
from collections import defaultdict
from typing import Dict, List

import magic
from dotenv import load_dotenv
from langchain.schema import HumanMessage, SystemMessage

from src.platform.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from llm_settings import invoke_llm
from src.platform.tools.language_detector import detect_language

# Load environment variables
load_dotenv()


class PackageAnalyzerAgent(BaseAgent):
    """
    Agent responsible for analyzing uploaded archives,
    extracting files, and organizing them by language.
    """

    def __init__(self):
        super().__init__("package_analyzer")

        # Initialize Azure OpenAI client
        

    def _create_package_summary(self, file_stats: Dict, file_info_list: List[Dict]) -> str:
        """
        Create a summary of the package using LLM

        Args:
            file_stats: Statistics about the files
            file_info_list: List of file information

        Returns:
            str: Package summary
        """
        # Prepare a summary of the statistics
        languages_summary = "\n".join([f"- {lang}: {count} files" for lang, count in file_stats["languages"].items()])
        file_types_summary = "\n".join(
            [f"- {ftype}: {count} files" for ftype, count in file_stats["file_types"].items()])

        # Create prompt for LLM using template
        from src.platform.tools.utils.template_manager import get_template_manager
        template_manager = get_template_manager()

        try:
            prompt = template_manager.render_template(
                "agents/package_analyzer/generic_analysis_prompt.j2",
                {
                    "file_stats": file_stats,
                    "languages_summary": languages_summary,
                    "file_types_summary": file_types_summary
                }
            )
        except Exception as e:
            self.logger.error(f"Could not load template: {str(e)}")
            raise RuntimeError(f"Template loading failed: {str(e)}")

        # Call LLM for analysis
        try:
            messages = [
                SystemMessage(content="You are an expert in legacy code analysis and migration."),
                HumanMessage(content=prompt)
            ]
            response = invoke_llm(messages)
            summary = response
        except Exception as e:
            self.logger.error(f"Error generating package summary: {str(e)}")
            summary = f"""
            # Package Summary

            - Total files: {file_stats["total_files"]}
            - Main languages: {', '.join([lang for lang, count in file_stats["languages"].items() if count > 0])}

            *Note: Detailed analysis could not be generated due to an error.*
            """

        return summary

    def set_up(self, config: dict) -> None:
        pass

    def process(self, input_data: AgentInput) -> AgentOutput:
        """
        Process the uploaded files and organize them by language

        Args:
            input_data: Input data containing working directory

        Returns:
            AgentOutput: Analysis results
        """
        self.logger.info(f"Starting package analysis in {input_data.working_directory}")

        try:
            # Create a timestamp for this session
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            session_id = os.path.basename(input_data.working_directory)

            # Create out directory structure
            out_base_dir = self.ensure_out_directory(session_id)
            if out_base_dir:
                self.logger.info(f"Using out directory: {out_base_dir}")

                # Create uploads directory in out directory for safekeeping
                uploads_out_dir = os.path.join(out_base_dir, "uploads")
                os.makedirs(uploads_out_dir, exist_ok=True)

                # Move files from working_directory to uploads folder, if any files exist
                try:
                    # Get list of files in working directory (excluding extracted & organized folders)
                    files_to_move = []
                    for item in os.listdir(input_data.working_directory):
                        item_path = os.path.join(input_data.working_directory, item)
                        if os.path.isfile(item_path) and item != 'package_summary.md':
                            files_to_move.append((item_path, os.path.join(uploads_out_dir, item)))

                    # Move files if any exist
                    if files_to_move:
                        for src, dst in files_to_move:
                            shutil.move(src, dst)
                        self.logger.info(f"Moved {len(files_to_move)} files to {uploads_out_dir}")
                    else:
                        self.logger.info("No files to move to uploads directory, will continue processing existing files")
                except Exception as e:
                    self.logger.warning(f"Error moving files to uploads directory: {str(e)}. Will continue with existing files.")

            # Extract archives if needed
            extracted_dir = os.path.join(input_data.working_directory, "extracted")
            os.makedirs(extracted_dir, exist_ok=True)

            # Store file stats
            file_stats = {
                "total_files": 0,
                "languages": defaultdict(int),
                "file_types": defaultdict(int),
                "file_sizes": defaultdict(int),
                "directories": []
            }

            # Extract all archives
            self._extract_all_archives(input_data.working_directory, extracted_dir)

            # Analyze extracted files
            self.report_progress("Analyzing extracted files...", 0.3)

            # Create directories for organized files
            organized_dir = os.path.join(input_data.working_directory, "organized")
            os.makedirs(organized_dir, exist_ok=True)

            # Organize files by language
            file_info_list = self._analyze_and_organize(extracted_dir, organized_dir, file_stats)

            # Create a summary of the package
            self.report_progress("Creating package summary...", 0.8)
            package_summary = self._create_package_summary(file_stats, file_info_list)

            # Save package summary to organized directory if available
            if out_base_dir:
                # Create organized directory if it doesn't exist
                organized_out_dir = os.path.join(out_base_dir, "organized")
                os.makedirs(organized_out_dir, exist_ok=True)

                summary_path = os.path.join(organized_out_dir, "package_summary.md")
                try:
                    with open(summary_path, 'w', encoding='utf-8') as f:
                        f.write(package_summary)
                    self.logger.info(f"Saved package summary to {summary_path}")
                except Exception as e:
                    self.logger.error(f"Error saving package summary to {summary_path}: {str(e)}")

            # Prepare knowledge base updates
            knowledge_base_updates = {
                "file_stats": dict(file_stats),  # Convert defaultdict to regular dict
                "file_info": file_info_list,
                "package_summary": package_summary,
                "organized_directory": organized_dir,
                "out_directory": out_base_dir if out_base_dir else None
            }

            self.report_progress("Package analysis completed", 1.0)

            return AgentOutput(
                success=True,
                message="Package analysis completed successfully",
                knowledge_base_updates=knowledge_base_updates
            )

        except Exception as e:
            self.logger.exception(f"Error during package analysis: {str(e)}")
            return AgentOutput(
                success=False,
                message=f"Package analysis failed: {str(e)}",
                errors=[str(e)]
            )

    def _extract_all_archives(self, source_dir: str, target_dir: str) -> None:
        """
        Extract all archive files in the source directory

        Args:
            source_dir: Directory containing uploaded files
            target_dir: Directory to extract files to
        """
        # Try to get files from the source directory or uploads subdirectory
        files_to_process = []

        # Check main source directory
        main_dir_files = []
        try:
            # First check the source directory directly
            for filename in os.listdir(source_dir):
                file_path = os.path.join(source_dir, filename)
                if os.path.isfile(file_path):
                    main_dir_files.append((file_path, filename))
        except Exception as e:
            self.logger.warning(f"Error accessing source directory {source_dir}: {str(e)}")

        # Check uploads directory if the main dir is empty
        if not main_dir_files:
            uploads_dir = os.path.join(source_dir, "uploads")
            if os.path.isdir(uploads_dir):
                self.logger.info(f"No files found in {source_dir}, looking in uploads directory")
                try:
                    for filename in os.listdir(uploads_dir):
                        file_path = os.path.join(uploads_dir, filename)
                        if os.path.isfile(file_path):
                            files_to_process.append((file_path, filename))
                except Exception as e:
                    self.logger.warning(f"Error accessing uploads directory {uploads_dir}: {str(e)}")
        else:
            files_to_process = main_dir_files

        if not files_to_process:
            self.logger.warning("No files found to extract or process. Will continue with existing files if any.")
            return

        for file_path, filename in files_to_process:
            try:
                file_type = magic.from_file(file_path, mime=True)

                # Extract archives
                if "zip" in file_type or filename.endswith(".zip"):
                    self.logger.info(f"Extracting ZIP archive: {filename}")
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        zip_ref.extractall(target_dir)

                elif "tar" in file_type or any(filename.endswith(ext) for ext in [".tar", ".tar.gz", ".tgz"]):
                    self.logger.info(f"Extracting TAR archive: {filename}")
                    with tarfile.open(file_path, 'r:*') as tar_ref:
                        tar_ref.extractall(target_dir)

                # Copy non-archive files directly
                else:
                    self.logger.info(f"Copying file: {filename}")
                    shutil.copy2(file_path, os.path.join(target_dir, filename))

            except Exception as e:
                self.logger.error(f"Error processing file {filename}: {str(e)}")

    def _analyze_and_organize(self, source_dir: str, target_dir: str, file_stats: Dict) -> List[Dict]:
        """
        Analyze files and organize them by language

        Args:
            source_dir: Directory containing extracted files
            target_dir: Directory to store organized files
            file_stats: Dictionary to update with file statistics

        Returns:
            List[Dict]: List of file information dictionaries
        """
        file_info_list = []

        # Get supported languages from plugin system
        try:
            from src.platform.plugins.plugin_loader import get_plugin_loader
            plugin_loader = get_plugin_loader()
            supported_languages = plugin_loader.get_available_languages()
            # Add common categories that are not language-specific
            all_languages = supported_languages + ["config", "data", "other"]
        except Exception as e:
            self.logger.warning(f"Plugin system not available: {str(e)}")
            # Minimal fallback - only basic categories
            all_languages = ["config", "data", "other"]

        # Create subdirectories for each language
        for lang in all_languages:
            os.makedirs(os.path.join(target_dir, lang), exist_ok=True)

        # Process all files
        for root, dirs, files in os.walk(source_dir):
            # Update directories list
            rel_path = os.path.relpath(root, source_dir)
            if rel_path != ".":
                file_stats["directories"].append(rel_path)

            # Process files
            for filename in files:
                file_path = os.path.join(root, filename)
                rel_file_path = os.path.relpath(file_path, source_dir)

                # Get file size
                file_size = os.path.getsize(file_path)
                file_stats["total_files"] += 1

                # Categorize by size
                size_category = "small" if file_size < 10240 else "medium" if file_size < 102400 else "large"
                file_stats["file_sizes"][size_category] += 1

                # Detect file type
                try:
                    file_type = magic.from_file(file_path, mime=True)
                    file_stats["file_types"][file_type] += 1
                except:
                    file_type = "unknown"
                    file_stats["file_types"]["unknown"] += 1

                # Try to detect language
                language = detect_language(file_path)

                # Store language statistics
                file_stats["languages"][language] += 1

                # Create destination path in organized directory
                dest_dir = os.path.join(target_dir, language)
                dest_path = os.path.join(dest_dir, rel_file_path)

                # Ensure destination directory exists
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)

                # Copy file to organized location
                shutil.copy2(file_path, dest_path)

                # Create file info
                file_info = {
                    "filename": filename,
                    "relative_path": rel_file_path,
                    "language": language,
                    "size": file_size,
                    "mime_type": file_type,
                    "organized_path": dest_path
                }

                # Use plugin system for language-specific processing
                try:
                    from src.platform.plugins.plugin_loader import get_plugin_loader
                    plugin_loader = get_plugin_loader()
                    language_plugin = plugin_loader.get_language_plugin(language)

                    if language_plugin:
                        # Let the plugin handle language-specific file organization
                        analyzer = language_plugin.get_analyzer()
                        if analyzer:
                            analysis = analyzer.analyze(open(file_path, 'r', encoding='utf-8', errors='replace').read(), file_path)
                            file_info.update(analysis)

                            # Handle special file types (e.g., COBOL copybooks)
                            if analysis.get("is_copybook"):
                                # Create copybooks directory if it doesn't exist
                                copybooks_dir = os.path.join(target_dir, language, "copybooks")
                                os.makedirs(copybooks_dir, exist_ok=True)

                                # Update the destination path
                                new_dest_path = os.path.join(copybooks_dir, filename)

                                # Move the file from the previous destination
                                if os.path.exists(dest_path):
                                    shutil.move(dest_path, new_dest_path)

                                # Update the organized path in file info
                                file_info["organized_path"] = new_dest_path
                                self.logger.info(f"Identified {filename} as a {language} copybook, moved to copybooks directory")
                except Exception as e:
                    self.logger.warning(f"Plugin-based analysis failed for {filename}: {str(e)}")
                    # No fallback - plugins are required for language-specific processing

                file_info_list.append(file_info)

        return file_info_list

# Language-specific logic moved to plugins
# The _is_cobol_copybook method has been removed and replaced with plugin-based analysis
# See the plugin system usage in _organize_files_by_language method above