import datetime
import logging
import json
import os
import re
import shutil
from pathlib import Path

from jinja2 import Template, DebugUndefined

from langchain.schema import HumanMessage, SystemMessage

from llm_settings import invoke_llm
from config.constants import TEMPLATE_DIRS
from src.platform.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from src.platform.tools.graph_database import GraphDatabase
from src.platform.tools.knowledge_database import KnowledgeDatabase
from typing import Dict, Tuple, List, Any

from src.platform.tools.utils.template_manager import TemplateManager


# noinspection PyMethodMayBeStatic
class DocumentationGeneratorAgent(BaseAgent):
    """
    Agent responsible for generating documentation based on
    the knowledge base extracted from legacy code.
    """

    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("documentation_gen_agent")

    def __init__(self):
        super().__init__("documentation_generator")

        try:
            # dir paths
            self.template_manager = TemplateManager(TEMPLATE_DIRS['cobol_plugin_templates'])
            self.logger.info(f"Created Template Manager for directory: {TEMPLATE_DIRS['cobol_plugin_templates']}")

            # SERVICES

            self.knowledge_db = KnowledgeDatabase()
            self.graph_db = GraphDatabase()

        except Exception as e:
            self.logger.error("DocumentationGeneratorAgent Initialization Failed (__init__)", exc_info=e)

    def set_up(self, config: dict) -> None:
        pass

    def process(self, input_data: AgentInput) -> AgentOutput:
        """
        Generate documentation based on the knowledge base

        Args:
            input_data: Input data containing working directory and knowledge base

        Returns:
            AgentOutput: Documentation generation results
        """
        self.logger.info("Starting documentation generation")

        try:
            # Get knowledge base - handle both nested and direct structures
            knowledge_base = input_data.knowledge_base.get("knowledge_base")
            if not knowledge_base:
                # Try direct access if not nested
                knowledge_base = input_data.knowledge_base

            if not knowledge_base:
                return AgentOutput(
                    success=False,
                    message="No knowledge base found",
                    errors=["Missing knowledge_base in input data"]
                )

            # Create documentation directory
            working_dir_path = Path(input_data.working_directory)
            docs_dir_path = working_dir_path / "documentation"
            docs_dir_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Created documentation directory in out directory: {docs_dir_path}")

            # Create a timestamp directory to version the documentation
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            timestamped_docs_dir_path = working_dir_path / "documentation" / timestamp
            timestamped_docs_dir_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(
                f"Created timestamped directory to version the documentation in out directory: {timestamped_docs_dir_path}")

            pics_output_dir_path = timestamped_docs_dir_path / "pics"
            pics_output_dir_path.mkdir(parents=True, exist_ok=True)

            self.report_progress("Generating overview documentation", 0.2)

            overview_docs = self._generate_overview_documentation(working_dir_path, timestamped_docs_dir_path)

            self.report_progress("Generating overview documentation", 0.9)

            sorted_by_level_in_call_graph = [(uuid, entry["level"], entry["name"]) for uuid, entry in
                                             overview_docs.items()]
            sorted_by_level_in_call_graph.sort(key=lambda x: x[1], reverse=True)
            documentation_index_lines = [
                "## Documentation index",
                "(ordered from root to leaf level modules)"
            ]
            target_paragraph = "ENTRY-PARAGRAPH"
            entry_paragraph_info = None

            for i, row in enumerate(sorted_by_level_in_call_graph):
                if target_paragraph.lower() in row[2].lower():
                    entry_paragraph_info = sorted_by_level_in_call_graph.pop(i)
                    break

            doc = overview_docs[entry_paragraph_info[0]]
            el_module_id = doc.get("program", "Unknown")
            el_section = doc.get("section", "Unknown")
            el_cobol_name = doc.get("name", "Unknown")
            el_type = doc.get("type", "Unknown")
            el_level = doc.get("level", "Unknown")

            chunk_name = f"{el_module_id}_PROC_{el_section}_SECT_{el_cobol_name}"
            optional_chunk = self.knowledge_db.get_chunk_by_name(el_module_id, chunk_name)
            chunk_business_name = optional_chunk.get("business_name", "")
            file_name = f"Lv.{el_level}-{el_type}-{el_cobol_name}.md"
            documentation_index_lines.append(
                f" - [MAIN PROGRAM - {chunk_business_name} ({el_module_id})](./{file_name})")

            for el_uuid, el_level, el_name in sorted_by_level_in_call_graph:
                doc = overview_docs[el_uuid]
                el_module_id = doc.get("program", "Unknown")
                el_section = doc.get("section", "Unknown")
                el_cobol_name = doc.get("name", "Unknown")
                el_type = doc.get("type", "Unknown")
                file_name = f"Lv.{el_level}-{el_type}-{el_name}.md"
                chunk_name = f"{el_module_id}_PROC_{el_section}_SECT_{el_cobol_name}"

                try:
                    optional_chunk = self.knowledge_db.get_chunk_by_name(el_module_id, chunk_name)
                except Exception as e:
                    self.logger.error(f"Error getting chunk {el_module_id}.{chunk_name}: {str(e)}")
                    continue

                chunk_business_name = optional_chunk.get("business_name", "")
                documentation_index_lines.append(f" - [{chunk_business_name} ({el_cobol_name})](./{file_name})")

                # with open(overview_doc_path, 'w', encoding='utf-8') as file:
                #     file.write(doc["content"])

            # Prepare documentation artifacts
            overview_doc_content = "\n".join(documentation_index_lines)
            overview_doc_path = os.path.join(timestamped_docs_dir_path, "INDEX.md")
            with open(overview_doc_path, 'w', encoding='utf-8') as file:
                file.write(overview_doc_content)

            # Prepare knowledge base updates
            knowledge_base_updates = {
                "documentation_directory": str(working_dir_path / "documentation"),
                "out_documentation_directory": str(timestamped_docs_dir_path) if timestamped_docs_dir_path else None
            }

            documentation_artifacts = {
                "overview": {
                    "payload": overview_doc_content,
                    "path": overview_doc_path
                }
                #         "architecture": {
                #             "payload": architecture_doc,
                #             "path": architecture_path
                #         },
                #         "api": {
                #             "payload": api_doc,
                #             "path": api_path
                #         },
                #         "data_model": {
                #             "payload": data_model_doc,
                #             "path": data_model_path
                #         }
            }

            # Prepare output artifacts
            artifacts = {
                "documentation": documentation_artifacts
            }

            self.report_progress("Documentation generation completed", 1.0)

            return AgentOutput(
                success=True,
                message="Documentation generation completed successfully",
                knowledge_base_updates=knowledge_base_updates,
                artifacts=artifacts
            )

        except Exception as e:
            self.logger.exception(f"Error during documentation generation: {str(e)}")
            return AgentOutput(
                success=False,
                message=f"Documentation generation failed: {str(e)}",
                errors=[str(e)])

    def _process_cobol_callable_paragraph_with_llm(self,
                                                   cobol_element: dict[str, Any],
                                                   working_dir_path: Path,
                                                   doc_output_dir: Path) -> str | None:
        """
        Here we have to prepare parameters for the fixed content of the JSON template to pass to the
        DocumentGenerator.generate_documents(self, template_params: dict[str, str])
        """

        chunked_docs_dir = working_dir_path / "documentation_for_chunks"
        rekt_output_dir = working_dir_path / "programs_overview"

        system_prompt = None
        user_prompt = None
        spec_doc_json = None

        try:
            el_uuid = cobol_element['uuid']
            el_module_id = cobol_element['module_id']
            el_type: str = cobol_element['type_label']
            el_name = cobol_element['name']
            el_full_text = cobol_element['full_text']
            parent_section = cobol_element["parent_name"]

            chunk_name = f"{el_module_id}_PROC_{parent_section}_SECT_{el_name}"
            chunk_doc_file_name = f"{chunk_name}.md"
            optional_chunk = self.knowledge_db.get_chunk_by_name(el_module_id, chunk_name)

            # Initialize with default values
            business_description = "No business description available"
            business_logic = "No business logic available"
            # functional_spec = "No functional specification available"
            input_parameters = []
            output_parameters = []
            source_code = None

            if optional_chunk:
                # Safely handle potentially None values
                raw_business_description = optional_chunk.get("business_description")
                if raw_business_description:
                    business_description = raw_business_description.replace("\n", "<br>").replace("\"", "\\\"")

                raw_business_logic = optional_chunk.get("business_logic")
                if raw_business_logic:
                    business_logic = raw_business_logic.replace("\n", "<br>").replace("\"", "\\\"")

                # functional_spec = optional_chunk.get("functional_spec").replace("\n", "<br>").replace("\"", "\\\"")
                input_parameters = optional_chunk.get("input_parameters") or []
                output_parameters = optional_chunk.get("output_parameters") or []
                source_code = optional_chunk.get("code")
            else:
                source_code = "No chunk source code found. Try to infer the solution without source code."

            with open(chunked_docs_dir / el_module_id / chunk_doc_file_name, 'r') as f:
                chunk_documentation_article = f.read()

            # 1. Prepare a header recap for the chunk
            context_elements_text = self.template_manager.render_template(
                "documentation/user/context_elements.j2",
                {
                    # "business_description": business_description,
                    # "functional_spec": functional_spec,
                    "chunk_documentation_article": chunk_documentation_article
                })
            self.logger.debug(f"context_elements_text successfully generated:\n {context_elements_text}")

            if "entryparagraph" in el_type.lower():
                system_prompt = self.template_manager.render_template(
                    "documentation/system/module_recap_table_prompt.xml")
                recap_params = {
                    "module_id": el_module_id,
                    "context_elements_text": context_elements_text
                }
                user_prompt = self.template_manager.render_template("documentation/user/module_recap_table_prompt.xml",
                                                                    recap_params)
            elif "paragraph" in el_type.lower():
                system_prompt = self.template_manager.render_template(
                    "documentation/system/para_method_recap_table_prompt.xml")
                recap_params = {
                    "module_id": el_module_id,
                    "context_elements_text": context_elements_text,
                    "recap_time_stamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "chunk_name": el_name
                }
                user_prompt = self.template_manager.render_template(
                    "documentation/user/para_method_recap_table_prompt.xml",
                    recap_params
                )

            self.logger.debug(f"system_prompt for recap requests successfully generated:\n {system_prompt}")
            self.logger.debug(f"user_prompt for recap requests successfully generated:\n {user_prompt}")

            self.logger.info(f"Processing: {el_type}:{el_name} id: {el_uuid}")
            header_recap = invoke_llm([
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ])

            recap_params = json.loads(self._check_and_extract_pure_json_if_needed(header_recap))
            recap_table = self._create_key_value_from_dictionary(recap_params)
            document_content = {
                "chunk_name": el_name,
                "document_contents": "{{document_contents}}",
                "business_description": business_description,
                # "functional_spec": functional_spec,
                "recap_table": recap_table
            }

            # 2. Prepare high-level business requirements TODO required only for modules
            system_prompt = """
            You are an expert technical documentation writer specializing in legacy code modernization.
            Extract from the user prompt the functional information about high-level business requirements.
            The response should be in three small parts:
            1. One introductory sentence
            2. Brief description of the activities 
            3. Numbered list of steps. Give each step the name and add description.
               3.1 The first step name should be Initialization 
               3.2 The first step name should be Termination
               3.3 Make name bold using asterisks. E.g., **Termination**
               
            Tell specifically about the program chunk described in the documentation, don't tell about the documentation itself.
            Return only the list of topics no captions needed.
            """
            user_prompt = context_elements_text
            high_level_business_requirements = invoke_llm([
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]).replace("\n", "<br>").replace("\"", "\\\"")

            process_flow_step_sequence_rows = []
            # 3. Prepare the Process Flow step sequence table
            system_prompt = """
            You are an expert technical documentation writer specializing in legacy code modernization.
            Extract from the user prompt the functional information about process-flow use cases.
            The response should be in a table of four columns:
            - "Label": use PR{n} where {n} is the number of the step in the process (equal to the row number in the table)
            - "Change Log ID": always use 1.0
            - "UI / Process": is it a User's or Process' use-case? Populate with "User" or "Process" depending on the case
            - "Requirement Description and Validation Criteria": use-case description and validation criteria. Don't use 
               COBOL names here - replace them with humanized names inferred from their business meaning, 
               e.g., instead of `ACCT-ID` use `Account Identifier`.

            Focus only on the program chunk described in the documentation, not on the documentation itself.
            Return only the list of topics — no captions needed.
            """
            user_prompt = context_elements_text
            process_flow_step_sequence_rows = invoke_llm([
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]).replace("\"", "\\\"").split("\n")
            process_flow_step_sequence = "\\n".join(process_flow_step_sequence_rows)

            perform_relations = self.graph_db.get_nodes_performs_by_uuid(
                el_uuid)  # TODO implement extraction of node paragraphs performed from the node with uuid
            performed_from_the_program = self._create_called_subprograms_list(
                perform_relations)  # TODO incoming_calls_table (callers)

            # 6. Prepare 'Output Parameters' (Passed to callees and returned to the caller)
            # 7. Prepare 'Database Interactions'
            # 8. Prepare 'Step-by-Step Logic Flow'
            algorithm_rows = self._get_algorithm_from_k_miner_chunk_doc(chunk_documentation_article)
            algorithm = "\\n".join(algorithm_rows)

            # 9. Prepare 'Validation Rules' for tech.specs
            validation_rules_table_rows = self._extract_table(chunk_documentation_article, "Table 1: Validation Rules")
            validation_rules_table = "\\n".join(validation_rules_table_rows)

            # 10. Prepare 'Error Handling' for tech.specs
            error_handling_table_rows = self._extract_table(chunk_documentation_article, "Table 2: Error Handling")
            error_handling_table = "\\n".join(error_handling_table_rows)

            # 11. Prepare 'Test Cases' for tech.specs
            test_cases_table_rows = self._extract_table(chunk_documentation_article, "Test Cases")
            test_cases_table = "\\n".join(test_cases_table_rows)

            # 4. Prepare the Business Logic table (replace knowledgebase value if outgoing relations are found) # TODO this version is for module
            if perform_relations:
                system_prompt = """
                You are an expert technical documentation writer specializing in legacy code modernization.
                Extract from the user prompt the functional information about business logic.
                This information effectively is a list of units called (paragraphs/chunks and external programs).
                The response should be in a table of two columns:
                - "Function": the name of the called unit
                - "Description of business logic": a description of the called module responsibility and functional purpose

                Tell specifically about the program chunk described in the documentation, don't tell about the documentation itself.
                Return only the list of topics no captions needed.
                """
                user_prompt = performed_from_the_program
                business_logic_llm_response = invoke_llm([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=user_prompt)
                ]).replace("\"", "\\\"").split("\n")
                business_logic = "\\n".join(business_logic_llm_response)

            # 5. Prepare Main Program Process Flow Diagram
            if "entryparagraph" in el_type.lower():
                rekt_diagram_overview_file = rekt_output_dir / (el_module_id + ".cbl.md")
                with open(rekt_diagram_overview_file, 'r') as f:
                    rekt_diagram_overview = f.read()
                system_prompt = """
                You are an expert technical documentation writer specializing in legacy code modernization.
                Extract from the user prompt the content of the "### Main Business Flow Summary" topic.
                Replace the captions hierarchy of the extracted topic (prefixed with '#' chars, e.g., "### Main Business Flow Summary")
                with a bullet or numbers hierarchy.
                We can't use Markdown captions in the result snippet as it will be inserted as a content part into a larger document.
                """
                user_prompt = rekt_diagram_overview
                rekt_diagram_overview_lines = invoke_llm([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=user_prompt)
                ]).replace("\"", "\\\"").split("\n")
                pic_name = el_module_id + ".cbl.png"
                rekt_flowchart_img = rekt_output_dir / "img" / pic_name
                shutil.copy(str(rekt_flowchart_img), doc_output_dir / "pics" / pic_name)

                process_flow_diagram = f"![Main Program Process Flow](./pics/{pic_name})"

                document_content.update({
                    "process_flow_diagram": process_flow_diagram,
                    "rekt_diagram_overview": "\\n".join(rekt_diagram_overview_lines)
                })

            # 6. Prepare process description - input files # TODO only for modules
            incoming_files = self._get_cobol_file_names_with_paragraphs(el_full_text)
            incoming_files_table = self._create_markdown_table_from_dict(incoming_files,
                                                                         ["File", "Paragraph"],
                                                                         "No input files for the program")

            # 7. Prepare process description - input files
            input_parameters_table = self._create_markdown_table_from_dict(input_parameters,
                                                                           ["Name", "Type", "Business Name",
                                                                            "Description"],
                                                                           "No input parameters for the program")
            # 8. Prepare DB2 tables from COPYBOOK->[tables]
            include_db2_tables = self._get_db2_references(el_full_text, "INCLUDE")
            include_db2_tables_table = self._create_markdown_table_from_dict(include_db2_tables,
                                                                             ["Copybook", "Table Name", "Type"],
                                                                             "No select tables from copybooks")


            # 9. DB2 SELECT tables
            # Create prompt for LLM
            select_db2_tables = self._get_db2_references(el_full_text, "SELECT")
            select_db2_tables_table = self._create_markdown_table_from_dict(select_db2_tables,
                                                                            ["Function", "Table Name", "Code used"],
                                                                            "No select tables in the program")

            # 9. DB2 INSERT tables
            # Create prompt for LLM
            insert_db2_tables = self._get_db2_references(el_full_text, "INSERT")
            insert_db2_tables_table = self._create_markdown_table_from_dict(insert_db2_tables,
                                                                            ["Function", "Table Name", "Code used"],
                                                                            "No insert tables in the program")

            all_db2_tables = include_db2_tables + select_db2_tables + insert_db2_tables
            all_db2_tables_table = self._create_markdown_table_from_dict(all_db2_tables,
                                                                         ["Function", "Table", "Code used"],
                                                                         "No insert tables in the program")

            # include_ims_tables
            include_ims_tables_table = "No IMS tables from copybooks found in the program"
            # select_ims_tables
            select_ims_tables_table = "No IMS select tables found in the program"
            # insert_ims_tables
            insert_ims_tables_table = "No IMS insert tables found in the program"
            # update_ims_tables
            update_ims_tables_table = "No IMS update tables found in the program"
            # all_ims_tables = include_ims_tables + select_ims_tables + insert_ims_tables + update_ims_tables
            all_ims_tables_table = "No IMS update tables found in the program"

            # 10. Subprograms
            call_relations = self.graph_db.get_nodes_calls_by_uuid(el_uuid)
            called_external_programs = self._create_called_subprograms_list(call_relations)

            output_parameters_table = self._create_markdown_table_from_dict(output_parameters,
                                                                            ["Name", "Type", "Business Name",
                                                                             "Description"],
                                                                            "No output parameters for the program")

            ## TODO Now it is used in all types, we also need separation (entry-paragraph / chunks)
            document_content.update({
                "high_level_business_requirements": high_level_business_requirements,
                "business_logic": business_logic,
                "performed_from_the_program": performed_from_the_program,
                "process_flow_step_sequence": process_flow_step_sequence,
                "incoming_files_table": incoming_files_table,
                "input_parameters_table": input_parameters_table,
                "output_parameters_table": output_parameters_table,

                "include_db2_tables_table": include_db2_tables_table,
                "select_db2_tables_table": select_db2_tables_table,
                "insert_db2_tables_table": insert_db2_tables_table,
                "all_db2_tables_table": all_db2_tables_table,

                "include_ims_tables_table": include_ims_tables_table,
                "select_ims_tables_table": select_ims_tables_table,
                "insert_ims_tables_table": insert_ims_tables_table,
                "update_ims_tables_table": update_ims_tables_table,
                "all_ims_tables_table": all_ims_tables_table,

                "any_ims_tables_table": all_ims_tables_table,
                "algorithm": algorithm,
                "validation_rules_table": validation_rules_table,
                "error_handling_table": error_handling_table,
                "test_cases_table": test_cases_table
            })


            if el_type.lower() == "cobolparagraph":
                spec_doc_json = self.template_manager.render_template(
                    "documentation/para_method_spec_doc_template.json",
                    document_content
                )
            elif el_type.lower() == "cobolentryparagraph":
                spec_doc_json = self.template_manager.render_template(
                    "documentation/module_spec_doc_template.json",
                    document_content
                )

            self.logger.debug(f"spec_doc_json initial pass successfully generated:\n {spec_doc_json}")

            markdown_toc_lines, updated_json = self._generate_markdown_toc_with_numbering(spec_doc_json)
            document_contents = "\\n".join(markdown_toc_lines)
            spec_doc_json_template = Template(json.dumps(updated_json))
            spec_doc_json = spec_doc_json_template.render(document_contents=document_contents, undefined=DebugUndefined)
            self.logger.debug(f"spec_doc_json final pass successfully generated:\n {spec_doc_json}")

            return self._gather_markdown_from_json(spec_doc_json, 2)
        except Exception as e:
            self.logger.exception(f"Error during documentation generation: {str(e)}")
            return None

    def _generate_overview_documentation(self,
                                         working_dir_path: Path,
                                         doc_output_dir: Path) -> dict[str, dict[str, str | int]]:
        """
        Generate overview documentation

        Returns:
            str: Overview documentation in Markdown format
        """

        # inputs
        callable_leaves = self.graph_db.get_callable_leaves()
        level = 0
        docs = dict()
        # Graph traversal
        # Start from leaves
        for callable_leaf in callable_leaves:

            el_type: str = callable_leaf['type_label']
            if not "paragraph" in el_type.lower():
                continue
            el_module_id = callable_leaf['module_id']
            el_parent_section = callable_leaf['parent_name']
            el_name = callable_leaf['name']

            md_document_for_node = self._process_cobol_callable_paragraph_with_llm(callable_leaf,
                                                                                   working_dir_path,
                                                                                   doc_output_dir)
            if md_document_for_node:
                file_name = f"Lv.{level}-{el_type}-{el_name}.md"
                overview_doc_path = Path(
                    doc_output_dir) / file_name  ## TODO duplication detected (externalize this to a private class method)
                docs[callable_leaf['uuid']] = {
                    "level": level,
                    "program": el_module_id,
                    "section": el_parent_section,
                    "type": el_type,
                    "name": el_name
                }
                with open(overview_doc_path, 'w', encoding='utf-8') as file:
                    file.write(md_document_for_node)

        self.report_progress("Generating overview documentation", 0.4)

        # Graph BFS from down to top
        processed_node_uuids = {leaf['uuid'] for leaf in callable_leaves}
        current_batch_uuids = {leaf['uuid'] for leaf in callable_leaves}

        while current_batch_uuids:
            callers = self.graph_db.get_direct_callers(list(current_batch_uuids))
            current_batch_uuids = {caller['uuid'] for caller in callers} - processed_node_uuids
            level += 1
            processed_node_uuids.update(current_batch_uuids)
            current_callers = [c for c in callers if c['uuid'] in current_batch_uuids]
            for caller in current_callers:

                el_type = caller['type_label']
                if not "paragraph" in el_type.lower():
                    continue
                el_module_id = caller['module_id']
                el_parent_section = caller['parent_name']
                el_name = caller['name']

                md_document_for_node = self._process_cobol_callable_paragraph_with_llm(caller,
                                                                                       working_dir_path,
                                                                                       doc_output_dir)
                if md_document_for_node:
                    file_name = f"Lv.{level}-{el_type}-{caller['name']}.md"
                    overview_doc_path = Path(
                        doc_output_dir) / file_name  ## TODO duplication detected (externalize this to a private class method)
                    docs[caller['uuid']] = {
                        "level": level,
                        "program": el_module_id,
                        "section": el_parent_section,
                        "type": el_type,
                        "name": el_name
                    }
                    with open(overview_doc_path, 'w', encoding='utf-8') as file:
                        file.write(md_document_for_node)

        return docs

    def _get_algorithm_from_k_miner_chunk_doc(self, chunk_documentation_article: str = None) -> list[str]:
        try:
            if chunk_documentation_article is None:
                return ["No algorithm"]
            return self._extract_topic_content(chunk_documentation_article, "ALGORITHM")
        except Exception as e:
            self.logger.error(f"Step-by-Step Logic Flow content extraction failed: {str(e)}")
            return ["ERROR: Step-by-Step Logic Flow content extraction failed.{str(e)}"]

    # noinspection PyShadowingNames
    def _extract_topic_content(self, markdown_text: str, topic: str) -> list[str]:
        """Extract content from markdown topic until next same-level heading."""
        lines = markdown_text.split('\n')
        topic_found = False
        target_heading_level = 0
        content_lines = []

        def is_heading(line: str) -> bool:
            return line.strip().startswith('#')

        def get_heading_level(line: str) -> int:
            return len(line) - len(line.lstrip('#'))

        def extract_heading_text(line: str) -> str:
            return line.lstrip('#').strip()

        def matches_topic(heading_text: str, topic: str) -> bool:
            return heading_text.lower() == topic.lower()

        for line in lines:
            if is_heading(line):
                current_heading_level = get_heading_level(line)
                heading_text = extract_heading_text(line)

                if not topic_found:
                    if matches_topic(heading_text, topic):
                        topic_found = True
                        target_heading_level = current_heading_level
                        continue
                else:
                    if current_heading_level <= target_heading_level:
                        break

            if topic_found:
                content_lines.append(line.replace("\"", "\\\""))

        return content_lines

    def _extract_table(self, markdown_text: str, caption_phrase: str) -> list[str]:
        """Extract only the markdown table rows, excluding caption and other text."""
        lines = markdown_text.split('\n')
        table_lines = []
        caption_found = False
        table_started = False

        def is_table_row(line: str) -> bool:
            stripped = line.strip()
            return stripped.startswith('|') and stripped.endswith('|')

        def is_table_separator(line: str) -> bool:
            stripped = line.strip()
            return stripped.startswith('|') and all(c in '|-: ' for c in stripped)

        def contains_caption_phrase(line: str, phrase: str) -> bool:
            return phrase.lower() in line.lower()

        for line in lines:
            if not caption_found:
                if contains_caption_phrase(line, caption_phrase):
                    caption_found = True
                continue

            if is_table_row(line) or is_table_separator(line):
                table_started = True
                table_lines.append(line.replace("\"", "\\\""))
            elif table_started and line.strip() == "":
                # Allow empty lines within table
                continue
            elif table_started:
                # Non-table content after table started, stop extraction
                break

        return table_lines

    # TODO implement and test
    def _create_called_subprograms_list(self, perform_relations: list) -> str:
        return "No called subprograms found in the program".replace("\n", "<br>")

    def _check_and_extract_pure_json_if_needed(self, response):

        try:
            json.loads(response.strip())
            return response.strip()
        except json.JSONDecodeError:
            pass

        match = re.search(r'(\{.*}|\[.*])', response, re.DOTALL)
        if match:
            try:
                json.loads(match.group(1))
                return match.group(1)
            except json.JSONDecodeError:
                pass

        raise ValueError("No valid JSON found")

    def _get_cobol_file_names_with_paragraphs(self, cobol_snippet):
        paragraph_pattern = r'([A-Z0-9-]+)\s*\.\s*\n(.*?)(?=\n[A-Z0-9-]+\s*\.|$)'
        paragraph_matches = re.findall(paragraph_pattern, cobol_snippet, re.DOTALL | re.IGNORECASE)

        results = []

        for paragraph_name, paragraph_content in paragraph_matches:
            open_pattern = r'OPEN\s+(?:INPUT|OUTPUT|I-O|EXTEND)\s+([A-Z0-9-]+(?:\s+[A-Z0-9-]+)*)'
            file_matches = re.findall(open_pattern, paragraph_content, re.IGNORECASE)

            for match in file_matches:
                files = re.split(r'\s+', match.strip())
                for file_name in files:
                    results.append({
                        'file_name': file_name,
                        'paragraph': paragraph_name.strip()
                    })

        return results

    def _create_key_value_from_dictionary(self, params: dict):

        header = "|  |  |\\n"
        separator = "| ---- | ---- |\\n"
        rows = ""
        for key, value in params.items():
            rows += "| **" + key + "** | " + value + " |\\n"

        return header + separator + rows

    def _create_markdown_table_from_dict(self, data, headers=None,
                                         empty_data_notification_text="No data provided.") -> str:
        if not data or len(data) == 0:
            return empty_data_notification_text

        if isinstance(data, dict):
            data = [data]

        if headers:
            columns = headers
        else:
            columns = list(data[0].keys())

        header = "| " + " | ".join(columns) + " |\\n"
        separator = "|" + "|".join(["-" * (len(col) + 2) for col in columns]) + "|\\n"

        rows = ""
        for item in data:
            row_values = list(item.values())
            rows += "| " + " | ".join(row_values) + " |\\n"

        return header + separator + rows

    def _get_db2_references(self, cobol_snippet, operation=None) -> list[str]:
        results = []

        db2_patterns = {
            'INCLUDE': r'(EXEC\s+SQL\s+INCLUDE\s+([A-Z0-9-_]+)\s+END-EXEC)',
            'SELECT': r'(EXEC\s+SQL\s+(SELECT\s+.*?\s+FROM\s+([A-Z0-9_]+).*?)\s+END-EXEC)',
            'INSERT': r'(EXEC\s+SQL\s+(INSERT\s+INTO\s+([A-Z0-9_]+).*?)\s+END-EXEC)',
            'UPDATE': r'(EXEC\s+SQL\s+(UPDATE\s+([A-Z0-9_]+).*?)\s+END-EXEC)',
            'DELETE': r'(EXEC\s+SQL\s+(DELETE\s+FROM\s+([A-Z0-9_]+).*?)\s+END-EXEC)'
        }

        for op, pattern in db2_patterns.items():
            if operation and op != operation.upper():
                continue

            for match in re.finditer(pattern, cobol_snippet, re.DOTALL | re.IGNORECASE):
                paragraph = self._find_paragraph_for_position(cobol_snippet, match.start())

                if op == 'INCLUDE':
                    results.append({
                        'copybook_name': match.group(2).upper(),
                        'name': match.group(2).upper(),
                        'type': 'SELECT'
                    })
                else:
                    results.append({
                        'paragraph': paragraph,
                        'name': match.group(3).upper(),
                        'sql_query': re.sub(r'\s+', ' ', match.group(2).strip()),
                    })

        return results

    def _get_ims_references(self, cobol_snippet, operation=None):
        results = []

        ims_patterns = {
            # CALL patterns
            'GU': r'CALL\s+[\'"]CBLTDLI[\'"][^.]*?GU[^.]*?([A-Z0-9-_]+)[^.]*?\.',
            'GN': r'CALL\s+[\'"]CBLTDLI[\'"][^.]*?GN[^.]*?([A-Z0-9-_]+)[^.]*?\.',
            'GNP': r'CALL\s+[\'"]CBLTDLI[\'"][^.]*?GNP[^.]*?([A-Z0-9-_]+)[^.]*?\.',
            'ISRT': r'CALL\s+[\'"]CBLTDLI[\'"][^.]*?ISRT[^.]*?([A-Z0-9-_]+)[^.]*?\.',
            'REPL': r'CALL\s+[\'"]CBLTDLI[\'"][^.]*?REPL[^.]*?([A-Z0-9-_]+)[^.]*?\.',
            'DLET': r'CALL\s+[\'"]CBLTDLI[\'"][^.]*?DLET[^.]*?([A-Z0-9-_]+)[^.]*?\.',

            # EXEC DLI patterns
            'EXEC_GU': r'EXEC\s+DLI\s+GU\s+USING\s+PCB\s+\(([A-Z0-9-_]+)\)',
            'EXEC_GN': r'EXEC\s+DLI\s+GN\s+USING\s+PCB\s+\(([A-Z0-9-_]+)\)',
            'EXEC_GNP': r'EXEC\s+DLI\s+GNP\s+USING\s+PCB\s+\(([A-Z0-9-_]+)\)',
            'EXEC_ISRT': r'EXEC\s+DLI\s+ISRT\s+USING\s+PCB\s+\(([A-Z0-9-_]+)\)',
            'EXEC_REPL': r'EXEC\s+DLI\s+REPL\s+USING\s+PCB\s+\(([A-Z0-9-_]+)\)',
            'EXEC_DLET': r'EXEC\s+DLI\s+DLET\s+USING\s+PCB\s+\(([A-Z0-9-_]+)\)'
        }

        for op, pattern in ims_patterns.items():
            if operation and op != operation.upper():
                continue

            for match in re.finditer(pattern, cobol_snippet, re.DOTALL | re.IGNORECASE):
                paragraph = self._find_paragraph_for_position(cobol_snippet, match.start())

                results.append({
                    'paragraph': paragraph,
                    'name': match.group(1).upper(),
                    'data_type': 'IMS',
                    'operation': op,
                    'full_statement': match.group(0),
                })

        return results

    def _find_paragraph_for_position(self, cobol_snippet, position):
        current_paragraph = None
        paragraph_pattern = r'([A-Z0-9-]+)\s*\.\s*\n'
        paragraph_matches = list(re.finditer(paragraph_pattern, cobol_snippet, re.IGNORECASE))

        for match in paragraph_matches:
            if match.start() <= position:
                current_paragraph = match.group(1).strip()
            else:
                break

        return current_paragraph

    def _get_exceptions_scenarios(self, el_full_text):
        return "{{error_handling_table}}"

    def _generate_markdown_toc_with_numbering(self,
                                              spec_doc_json: str,
                                              content_starts_at: int = 3,
                                              start_numbering_after: str = "",
                                              decimal_point_numbering: bool = False) -> Tuple[List, Dict]:
        markdown_toc_lines = []
        updated_json = json.loads(spec_doc_json)
        stack = [updated_json]
        topic_counter = 1
        try:
            prefix_level_discounters = [int(x) for x in start_numbering_after.split(".")]
        except ValueError:
            prefix_level_discounters = []

        last_prefix = ""

        while stack:
            level_obj = stack.pop()
            if isinstance(level_obj, dict):
                level_obj = [level_obj]
            if isinstance(level_obj, list):
                for level_doc_item in level_obj:
                    for key, value in level_doc_item.items():
                        if key.startswith("level_"):
                            level = int(key.split("_")[1])
                            prefix_parts = last_prefix.split(".")
                            prev_level_prefix = ".".join(prefix_parts[:level - 1])
                            prev_level_prefix += "." if prev_level_prefix else ""
                            if len(last_prefix) / 2 >= level:
                                counter = int(prefix_parts[level - 1]) + 1
                            else:
                                counter = 1
                            next_prefix = prev_level_prefix + str(counter) + "."
                            last_prefix = next_prefix

                            if "caption" in value.keys():
                                next_prefix_levels = [int(x) for x in next_prefix.split(".") if x != ""]
                                if len(next_prefix_levels) >= len(prefix_level_discounters):
                                    for i, d in enumerate(prefix_level_discounters):
                                        next_prefix_levels[i] -= d
                                    space_prefix = " " * int(len(next_prefix_levels) / 2)
                                    prefix = space_prefix + ('.'.join(str(x) for x in next_prefix_levels if x > 0))
                                    if prefix.strip():
                                        topic_counter += 1
                                        if decimal_point_numbering:
                                            value['caption'] = prefix + " " + value['caption'] + "<br>"
                                            if topic_counter >= content_starts_at:
                                                markdown_toc_lines.append(value["caption"])
                                        else:
                                            if topic_counter > content_starts_at:
                                                markdown_toc_lines.append(" " * level * 2 + " - " + value['caption'])

                        # _push_to_stack(stack, value)
                        if isinstance(value, dict):
                            value = [value]
                        if isinstance(value, list):
                            for item in reversed(value):
                                if isinstance(item, dict):
                                    stack.append(item)

        return markdown_toc_lines, updated_json

    def _gather_markdown_from_json(self, json_str: str, start_header_size: int = 1) -> str:

        json_obj = json.loads(json_str)
        markdown_lines = ["[< Back to index](./INDEX.md)"]
        stack = [json_obj]

        while stack:
            level_obj = stack.pop()
            if isinstance(level_obj, dict):
                level_obj = [level_obj]
            if isinstance(level_obj, list):
                for level_doc_item in level_obj:
                    for key, value in level_doc_item.items():
                        if key.startswith("level_"):
                            level = int(key.split("_")[1])
                            discount = start_header_size - 1
                            if "caption" in value.keys():
                                caption = "#" * (level + discount) + " " + value["caption"].strip()
                                markdown_lines.append(caption)
                            if "content" in value.keys():
                                markdown_lines.append(value["content"])

                        # _push_to_stack(stack, value)
                        if isinstance(value, dict):
                            value = [value]
                        if isinstance(value, list):
                            for item in reversed(value):
                                if isinstance(item, dict):
                                    stack.append(item)

        return "\n".join(markdown_lines)

    def _get_from_llm(self, system_prompt: str, user_prompt: str) -> list[str]:
        return invoke_llm([
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]).replace("\"", "\\\"").split("\n")
