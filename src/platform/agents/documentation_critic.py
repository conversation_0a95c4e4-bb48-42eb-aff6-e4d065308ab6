import datetime
import logging
import os
from typing import Dict

from langchain.schema import HumanMessage, SystemMessage

from src.platform.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from llm_settings import invoke_llm


class DocumentationCriticAgent(BaseAgent):
    """
    Agent responsible for reviewing and improving the generated documentation.
    """

    def __init__(self):
        super().__init__("documentation_critic")
        

    def set_up(self, config: dict) -> None:
        pass

    def process(self, input_data: AgentInput) -> AgentOutput:
        """
        Review and improve documentation

        Args:
            input_data: Input data containing working directory and knowledge base

        Returns:
            AgentOutput: Documentation critic results
        """
        self.logger.info("Starting documentation review")

        try:
            # Get knowledge base
            knowledge_base = input_data.knowledge_base.get("knowledge_base")
            if not knowledge_base:
                return AgentOutput(
                    success=False,
                    message="No knowledge base found",
                    errors=["Missing knowledge_base in input data"]
                )

            # Get documentation - check both places
            documentation_artifacts = knowledge_base.get("documentation", {})
            if not documentation_artifacts and "documentation" in input_data.knowledge_base:
                documentation_artifacts = input_data.knowledge_base.get("documentation", {})
            if not documentation_artifacts and "artifacts" in knowledge_base:
                documentation_artifacts = knowledge_base.get("artifacts", {}).get("documentation", {})

            if not documentation_artifacts:
                # Create placeholder documentation instead of failing
                self.logger.warning("No documentation found, generating placeholders")
                documentation_artifacts = {
                    "overview": "# Overview\nBasic overview placeholder.",
                    "architecture": "# Architecture\nBasic architecture placeholder.",
                    "api": "# API\nBasic API documentation placeholder.",
                    "data_model": "# Data Model\nBasic data model placeholder."
                }

            # Create reviewed documentation directory
            docs_dir = os.path.join(input_data.working_directory, "documentation_reviewed")
            os.makedirs(docs_dir, exist_ok=True)

            # Also create reviewed documentation directory in /out if available
            out_dir = input_data.knowledge_base.get("out_directory")
            out_docs_dir = None

            if out_dir and os.path.exists(out_dir):
                # Create documentation directory within the out directory structure
                out_docs_dir = os.path.join(out_dir, "documentation_reviewed")
                os.makedirs(out_docs_dir, exist_ok=True)
                self.logger.info(f"Created reviewed documentation directory in out directory: {out_docs_dir}")

                # Create a timestamp directory to version the documentation
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                timestamped_docs_dir = os.path.join(out_docs_dir, timestamp)
                os.makedirs(timestamped_docs_dir, exist_ok=True)
                out_docs_dir = timestamped_docs_dir

            # Review and improve each documentation type
            self.report_progress("Reviewing documentation", 0.1)

            improved_documentation = {}

            # Review Overview documentation
            if "overview" in documentation_artifacts:
                self.report_progress("Reviewing overview documentation", 0.2)
                improved_overview = self._review_and_improve(
                    "overview",
                    documentation_artifacts["overview"],
                    knowledge_base
                )
                # Save to file
                overview_path = os.path.join(docs_dir, "overview.md")
                with open(overview_path, 'w', encoding='utf-8') as file:
                    file.write(improved_overview)
                improved_documentation["overview"] = {"payload": improved_overview,
                                                      "path": overview_path}

                # Save to out directory if available
                if out_docs_dir:
                    out_overview_path = os.path.join(out_docs_dir, "overview.md")
                    with open(out_overview_path, 'w', encoding='utf-8') as file:
                        file.write(improved_overview)

            # Review Architecture documentation
            if "architecture" in documentation_artifacts:
                self.report_progress("Reviewing architecture documentation", 0.4)
                improved_architecture = self._review_and_improve(
                    "architecture",
                    documentation_artifacts["architecture"],
                    knowledge_base
                )
                # Save to file
                architecture_path = os.path.join(docs_dir, "architecture.md")
                with open(architecture_path, 'w', encoding='utf-8') as file:
                    file.write(improved_architecture)
                improved_documentation["architecture"] = {"payload": improved_architecture,
                                                          "path": architecture_path}

                # Save to out directory if available
                if out_docs_dir:
                    out_architecture_path = os.path.join(out_docs_dir, "architecture.md")
                    with open(out_architecture_path, 'w', encoding='utf-8') as file:
                        file.write(improved_architecture)

            # Review API documentation
            if "api" in documentation_artifacts:
                self.report_progress("Reviewing API documentation", 0.6)
                improved_api = self._review_and_improve(
                    "api",
                    documentation_artifacts["api"],
                    knowledge_base
                )
                # Save to file
                api_path = os.path.join(docs_dir, "api.md")
                with open(api_path, 'w', encoding='utf-8') as file:
                    file.write(improved_api)
                improved_documentation["api"] = {"payload": improved_api,
                                                 "path": api_path}

                # Save to out directory if available
                if out_docs_dir:
                    out_api_path = os.path.join(out_docs_dir, "api.md")
                    with open(out_api_path, 'w', encoding='utf-8') as file:
                        file.write(improved_api)

            # Review Data Model documentation
            if "data_model" in documentation_artifacts:
                self.report_progress("Reviewing data model documentation", 0.8)
                improved_data_model = self._review_and_improve(
                    "data_model",
                    documentation_artifacts["data_model"],
                    knowledge_base
                )
                # Save to file
                data_model_path = os.path.join(docs_dir, "data_model.md")
                with open(data_model_path, 'w', encoding='utf-8') as file:
                    file.write(improved_data_model)
                improved_documentation["data_model"] = {"payload": improved_data_model,
                                                        "path": data_model_path}

                # Save to out directory if available
                if out_docs_dir:
                    out_data_model_path = os.path.join(out_docs_dir, "data_model.md")
                    with open(out_data_model_path, 'w', encoding='utf-8') as file:
                        file.write(improved_data_model)

            # Prepare documentation artifacts
            artifacts = {
                "documentation": improved_documentation
            }

            # Prepare knowledge base updates
            knowledge_base_updates = {
                "documentation_reviewed_directory": docs_dir,
                "out_documentation_reviewed_directory": out_docs_dir if out_docs_dir else None
            }

            self.report_progress("Documentation review completed", 1.0)

            return AgentOutput(
                success=True,
                message="Documentation review completed successfully",
                knowledge_base_updates=knowledge_base_updates,
                artifacts=artifacts
            )

        except Exception as e:
            self.logger.exception(f"Error during documentation review: {str(e)}")
            return AgentOutput(
                success=False,
                message=f"Documentation review failed: {str(e)}",
                errors=[str(e)]
            )

    def _review_and_improve(self, doc_type: str, documentation: str, knowledge_base: Dict) -> str:
        """
        Review and improve a specific documentation type

        Args:
            doc_type: Type of documentation (overview, architecture, api, data_model)
            documentation: Original documentation content
            knowledge_base: Knowledge base with program information

        Returns:
            str: Improved documentation
        """
        # Create generic review criteria
        review_criteria = [
            "Accuracy - Does the documentation correctly reflect the described systems?",
            "Completeness - Are all important aspects covered?",
            "Clarity - Is the documentation easy to understand?",
            "Consistency - Is terminology used consistently?",
            "Structure - Is the documentation well-organized with appropriate headings?",
            "Examples - Are examples provided where appropriate?",
            "Technical Accuracy - Are technical details correct?",
            "Relevance - Is all content relevant to the modernization project?"
        ]

        # Add specific criteria based on documentation type
        if doc_type == "overview":
            review_criteria.extend([
                "Introduction Quality - Does it provide a good high-level overview?",
                "Purpose Clarity - Is the purpose of the systems clearly defined?",
                "Modernization Goals - Are the modernization goals well articulated?"
            ])
        elif doc_type == "architecture":
            review_criteria.extend([
                "Architecture Description - Is the current architecture clearly described?",
                "Microservices Proposal - Is the microservices proposal well-justified?",
                "Component Relationships - Are the relationships between components clear?",
                "Migration Strategy - Is the migration strategy practical and comprehensive?"
            ])
        elif doc_type == "api":
            review_criteria.extend([
                "Endpoint Descriptions - Are endpoints clearly described?",
                "Parameters Documentation - Are parameters well-documented?",
                "Response Format - Are response formats clearly specified?",
                "Error Handling - Is error handling properly documented?",
                "Examples Quality - Are examples realistic and helpful?"
            ])
        elif doc_type == "data_model":
            review_criteria.extend([
                "Entity Definitions - Are entities clearly defined?",
                "Relationships - Are relationships between entities well-explained?",
                "Database Schema - Is the proposed schema appropriate?",
                "Java Implementation - Are suggested Java implementations correct?"
            ])

        # Create prompt for LLM
        prompt = f"""
        You are tasked with reviewing and improving the following {doc_type} documentation for a legacy code modernization project.

        ORIGINAL DOCUMENTATION:
        ```
        {documentation}
        ```

        Please review this documentation based on the following criteria:
        {chr(10).join(['- ' + criterion for criterion in review_criteria])}

        First, provide a brief review of the documentation pointing out strengths and areas for improvement.

        Then, provide an improved version of the documentation that addresses any issues you've identified.
        The improved documentation should:
        1. Fix any inaccuracies or inconsistencies
        2. Add any missing important information
        3. Improve clarity and structure
        4. Enhance examples if needed

        DO NOT RETURN YOUR REVIEW COMMENTS IN THE IMPROVED DOCUMENTATION.
        The improved documentation should be ready for direct use in the project.

        RETURN ONLY THE IMPROVED DOCUMENTATION, not your review or any explanations about what you changed.
        """

        # Call LLM for documentation improvement
        messages = [
            SystemMessage(content="You are an expert technical documentation writer and reviewer."),
            HumanMessage(content=prompt)
        ]

        response = invoke_llm(messages)
        return response