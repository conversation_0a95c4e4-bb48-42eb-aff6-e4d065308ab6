import json
import os
import datetime
from typing import Dict

from langchain.schema import HumanMessage, SystemMessage

from src.platform.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from llm_settings import invoke_llm


class CodeReviewerAgent(BaseAgent):
    """
    Agent responsible for reviewing and validating the generated Java code.
    """

    def __init__(self):
        super().__init__("code_reviewer")
        

    def set_up(self, config: dict) -> None:
        pass

    def process(self, input_data: AgentInput) -> AgentOutput:
        """
        Review and validate the generated Java code

        Args:
            input_data: Input data containing working directory and knowledge base

        Returns:
            AgentOutput: Code review results
        """
        self.logger.info("Starting code review")

        try:
            # Validate input data structure
            if not input_data.knowledge_base:
                return AgentOutput(
                    success=False,
                    message="No knowledge base provided",
                    errors=["Missing knowledge_base in input data"]
                )

            # Get generated code - check both possible locations
            generated_code = input_data.knowledge_base.get("generated_code")
            if not generated_code:
                # Check if it's nested under knowledge_base key (legacy structure)
                nested_kb = input_data.knowledge_base.get("knowledge_base", {})
                generated_code = nested_kb.get("generated_code")

            if not generated_code:
                return AgentOutput(
                    success=False,
                    message="No generated code found to review",
                    errors=["Missing generated_code in knowledge base"]
                )

            # Create reviewed code directory
            reviewed_code_dir = os.path.join(input_data.working_directory, "reviewed_code")
            os.makedirs(reviewed_code_dir, exist_ok=True)

            # Also create reviewed code directory in /out if available
            out_dir = input_data.knowledge_base.get("out_directory")
            out_reviewed_code_dir = None

            if out_dir and os.path.exists(out_dir):
                # Create reviewed code directory within the out directory structure
                out_reviewed_code_dir = os.path.join(out_dir, "reviewed_code")
                os.makedirs(out_reviewed_code_dir, exist_ok=True)
                self.logger.info(f"Created reviewed code directory in out directory: {out_reviewed_code_dir}")

                # Create a timestamp directory to version the reviewed code
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                timestamped_reviewed_code_dir = os.path.join(out_reviewed_code_dir, timestamp)
                os.makedirs(timestamped_reviewed_code_dir, exist_ok=True)
                out_reviewed_code_dir = timestamped_reviewed_code_dir

            # Review each microservice
            self.report_progress("Reviewing generated code", 0.1)

            reviewed_code = {}
            review_reports = {}

            for service_name, files in generated_code.items():
                self.report_progress(f"Reviewing {service_name} microservice",
                                     0.1 + 0.8 * (
                                             list(generated_code.keys()).index(service_name) / len(generated_code)))

                # Create directory for this microservice
                service_dir = os.path.join(reviewed_code_dir, service_name)
                os.makedirs(service_dir, exist_ok=True)

                # Create service directory in out dir if available
                out_service_dir = None
                if out_reviewed_code_dir:
                    out_service_dir = os.path.join(out_reviewed_code_dir, service_name)
                    os.makedirs(out_service_dir, exist_ok=True)

                # Review each file
                service_review = {}
                service_files = {}

                for file_name, file_content in files.items():
                    # Review the file
                    review_result, improved_content = self._review_file(file_name, file_content, service_name)

                    # Save the review result
                    service_review[file_name] = review_result

                    # Save the improved file
                    file_path = os.path.join(service_dir, file_name)
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    with open(file_path, 'w', encoding='utf-8') as file:
                        file.write(improved_content)

                    # Save to out directory if available
                    if out_service_dir:
                        out_file_path = os.path.join(out_service_dir, file_name)
                        os.makedirs(os.path.dirname(out_file_path), exist_ok=True)
                        with open(out_file_path, 'w', encoding='utf-8') as file:
                            file.write(improved_content)

                    # Update service files dict
                    service_files[file_name] = improved_content

                # Generate overall review report for the service
                review_report = self._generate_review_report(service_name, service_review)
                report_path = os.path.join(service_dir, "REVIEW.md")
                with open(report_path, 'w', encoding='utf-8') as file:
                    file.write(review_report)

                # Save to out directory if available
                if out_service_dir:
                    out_report_path = os.path.join(out_service_dir, "REVIEW.md")
                    with open(out_report_path, 'w', encoding='utf-8') as file:
                        file.write(review_report)

                # Add to output dicts
                reviewed_code[service_name] = service_files
                review_reports[service_name] = review_report

            # Generate overall review summary
            review_summary = self._generate_review_summary(review_reports)
            summary_path = os.path.join(reviewed_code_dir, "REVIEW_SUMMARY.md")
            with open(summary_path, 'w', encoding='utf-8') as file:
                file.write(review_summary)

            # Save to out directory if available
            if out_reviewed_code_dir:
                out_summary_path = os.path.join(out_reviewed_code_dir, "REVIEW_SUMMARY.md")
                with open(out_summary_path, 'w', encoding='utf-8') as file:
                    file.write(review_summary)

            # Prepare output artifacts
            artifacts = {
                "generated_code": reviewed_code,
                "review_reports": review_reports,
                "review_summary": review_summary
            }

            # Prepare knowledge base updates
            knowledge_base_updates = {
                "reviewed_code_directory": reviewed_code_dir,
                "out_reviewed_code_directory": out_reviewed_code_dir if out_reviewed_code_dir else None,
                "review_summary": review_summary
            }

            self.report_progress("Code review completed", 1.0)

            return AgentOutput(
                success=True,
                message="Code review completed successfully",
                knowledge_base_updates=knowledge_base_updates,
                artifacts=artifacts
            )

        except Exception as e:
            self.logger.exception(f"Error during code review: {str(e)}")
            return AgentOutput(
                success=False,
                message=f"Code review failed: {str(e)}",
                errors=[str(e)]
            )

    def _review_file(self, file_name: str, file_content: str, service_name: str) -> tuple:
        """
        Review and improve a Java code file

        Args:
            file_name: Name of the file
            file_content: Content of the file
            service_name: Name of the microservice

        Returns:
            tuple: (Review result dict, Improved file content)
        """
        # Skip non-Java files
        if not file_name.endswith('.java'):
            return {
                "status": "SKIPPED",
                "message": "Not a Java file, skipped review",
                "issues": [],
                "improvements": []
            }, file_content

        # Create review prompt using template
        from src.platform.tools.utils.template_manager import get_template_manager
        template_manager = get_template_manager()

        try:
            prompt = template_manager.render_template(
                "agents/code_reviewer/file_review_prompt.j2",
                {
                    "service_name": service_name,
                    "file_name": file_name,
                    "file_content": file_content
                }
            )
        except Exception as e:
            self.logger.error(f"Could not load template: {str(e)}")
            return {
                "status": "FAIL",
                "message": "Template loading failed",
                "issues": [f"Template error: {str(e)}"],
                "improvements": ["Fix template system"]
            }, file_content

        messages = [
            SystemMessage(
                content="You are an expert Java developer and code reviewer specializing in Spring Boot microservices."),
            HumanMessage(content=prompt)
        ]

        response = invoke_llm(messages)
        response_text = response

        # Extract review report JSON
        review_report_json = response_text.split("--- REVIEW REPORT ---")[1].split("--- IMPROVED CODE ---")[0].strip()
        review_report = json.loads(review_report_json)

        # Extract improved code
        improved_code = response_text.split("--- IMPROVED CODE ---")[1].strip()

        return review_report, improved_code

    def _generate_review_report(self, service_name: str, file_reviews: Dict[str, Dict]) -> str:
        """
        Generate an overall review report for a microservice

        Args:
            service_name: Name of the microservice
            file_reviews: Dict of file review results

        Returns:
            str: Review report in markdown format
        """
        # Count statuses
        statuses = {"PASS": 0, "PASS_WITH_WARNINGS": 0, "NEEDS_IMPROVEMENT": 0, "FAIL": 0, "SKIPPED": 0}
        for review in file_reviews.values():
            if review["status"] in statuses:
                statuses[review["status"]] += 1

        # Count total issues and improvements
        total_issues = sum(len(review.get("issues", [])) for review in file_reviews.values())
        total_improvements = sum(len(review.get("improvements", [])) for review in file_reviews.values())

        # Create prompt
        prompt = f"""
        Generate a comprehensive code review report for the microservice {service_name}.

        Review Summary:
        - Total files reviewed: {len(file_reviews)}
        - Files that passed: {statuses["PASS"]}
        - Files that passed with warnings: {statuses["PASS_WITH_WARNINGS"]}
        - Files that need improvement: {statuses["NEEDS_IMPROVEMENT"]}
        - Files that failed: {statuses["FAIL"]}
        - Files skipped: {statuses["SKIPPED"]}
        - Total issues found: {total_issues}
        - Total improvements suggested: {total_improvements}

        Detailed file reviews:
        ```json
        {json.dumps(file_reviews, indent=2)}
        ```

        Create a well-formatted Markdown report that includes:
        1. An executive summary
        2. Overall assessment and grade (A-F)
        3. Key strengths of the codebase
        4. Key areas for improvement
        5. File-by-file breakdown of significant issues
        6. Recommendations for addressing critical issues

        Format the report in Markdown with appropriate headings, lists, and formatting.
        """

        messages = [
            SystemMessage(
                content="You are an expert Java developer and code reviewer specializing in Spring Boot microservices."),
            HumanMessage(content=prompt)
        ]

        response = invoke_llm(messages)
        return response

    def _generate_review_summary(self, review_reports: Dict[str, str]) -> str:
        """
        Generate an overall review summary for all microservices

        Args:
            review_reports: Dict of service review reports

        Returns:
            str: Review summary in markdown format
        """
        # Create prompt
        prompt = f"""
        Generate a comprehensive review summary for {len(review_reports)} microservices.

        Microservices reviewed:
        {', '.join(review_reports.keys())}

        Based on the individual service review reports, create a high-level summary of the code quality across all microservices.

        The summary should include:
        1. Executive Summary - Overall assessment of the codebase
        2. Strengths - Common strengths found across microservices
        3. Areas for Improvement - Common issues found across microservices
        4. Recommendations - High-level recommendations for improving the codebase
        5. Next Steps - Suggested next steps for the development team

        Format the summary in Markdown with appropriate headings, lists, and formatting.
        """

        messages = [
            SystemMessage(
                content="You are an expert Java developer and code reviewer specializing in Spring Boot microservices."),
            HumanMessage(content=prompt)
        ]

        response = invoke_llm(messages)
        return response