import logging
import os
import threading
from datetime import datetime
from typing import Dict, Any, List

from ui.state_holder import StateHolder
from .base_agent import AgentInput
from config.project_types import ProjectType, get_project_type_by_name
from config.agent_registry import AgentType, create_agent_instance, get_agent_config_by_name
from config.workflow_assembly import get_workflow_for_project_type, get_workflow_definition


# Configure a custom log handler to capture logs in memory
class LogCapture(logging.Handler):
    def __init__(self):
        super().__init__()
        self.log_records = []
        self.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

    def emit(self, record):
        self.log_records.append(self.format(record))

    def get_logs(self):
        return self.log_records

class OrchestratorAgent:
    """
    The Orchestrator Agent coordinates the conversion workflow
    by running agents in sequence and managing the overall state.
    """

    def __init__(self):
        # Set up logging with capture
        self.log_capture = LogCapture()
        logging.getLogger().addHandler(self.log_capture)
        self.logger = logging.getLogger("agent.orchestrator")

        self.state_holder = StateHolder()

        self.current_state = {
            "status": "idle",
            "current_agent": None,
            "progress": 0,
            "working_directory": None,
            "knowledge_base": {},
            "documentation": {},
            "generated_code": {},
            "errors": [],
            "logs": [],
            "start_time": None,
            "agent_progress": {}
        }
        self.processing_thread = None

        # Initialize agents using centralized registry
        self.agents = self._initialize_agents()

        # Define default workflow (will be overridden by project type)
        self.workflow = []
        self.current_workflow_definition = None
        self.project_config = {}

    def _initialize_agents(self) -> Dict[str, Any]:
        """
        Initialize all agents using the centralized agent registry.

        Returns:
            Dict[str, Any]: Dictionary of agent name to agent instance
        """
        agents = {}

        # Create instances of all registered agents
        for agent_type in AgentType:
            try:
                agent_instance = create_agent_instance(agent_type)
                config = get_agent_config_by_name(agent_type.value)
                agents[config.name] = agent_instance
                self.logger.info(f"Initialized agent: {config.display_name}")
            except Exception as e:
                self.logger.error(f"Failed to initialize agent {agent_type.value}: {str(e)}")

        return agents

    def set_project_type(self, project_type_name: str) -> None:
        """
        Set the project type and configure the appropriate workflow.

        Args:
            project_type_name: Name of the project type
        """
        try:
            # Get project type configuration
            project_config = get_project_type_by_name(project_type_name)

            # Get workflow for this project type
            project_type_enum = ProjectType(project_type_name)
            workflow_definition = get_workflow_for_project_type(project_type_enum)

            # Set the workflow
            self.current_workflow_definition = workflow_definition
            self.workflow = [step.name for step in workflow_definition.steps]

            self.logger.info(f"Set project type to: {project_config.display_name}")
            self.logger.info(f"Using workflow: {workflow_definition.display_name}")
            self.logger.info(f"Workflow steps: {', '.join(self.workflow)}")

        except Exception as e:
            self.logger.error(f"Failed to set project type {project_type_name}: {str(e)}")
            raise ValueError(f"Invalid project type: {project_type_name}")

    def set_workflow(self, workflow: List[str]) -> None:
        """
        Set the workflow steps to execute

        Args:
            workflow: List of agent names to run in sequence
        """
        # Validate agent names
        for agent_name in workflow:
            if agent_name not in self.agents:
                self.logger.error(f"Invalid agent name in workflow: {agent_name}")
                raise ValueError(f"Invalid agent name in workflow: {agent_name}")

        self.workflow = workflow
        self.logger.info(f"Set workflow to: {', '.join(workflow)}")

    def get_available_project_types(self) -> List[Dict[str, Any]]:
        """
        Get all available project types.

        Returns:
            List[Dict[str, Any]]: List of project type configurations
        """
        from config.project_types import get_all_project_types
        return [
            {
                "name": config.name,
                "display_name": config.display_name,
                "description": config.description,
                "icon": config.icon,
                "estimated_duration": self._get_workflow_duration(config.workflow_name)
            }
            for config in get_all_project_types()
        ]

    def get_available_agents(self) -> List[Dict[str, Any]]:
        """
        Get all available agents.

        Returns:
            List[Dict[str, Any]]: List of agent configurations
        """
        from config.agent_registry import get_all_agent_configs
        return [
            {
                "name": config.name,
                "display_name": config.display_name,
                "description": config.description,
                "category": config.category,
                "icon": config.icon,
                "estimated_duration": config.estimated_duration,
                "parallel_capable": config.parallel_capable
            }
            for config in get_all_agent_configs()
        ]

    def _get_workflow_duration(self, workflow_name: str) -> str:
        """
        Get estimated duration for a workflow.

        Args:
            workflow_name: Name of the workflow

        Returns:
            str: Estimated duration
        """
        try:
            workflow_def = get_workflow_definition(workflow_name)
            return workflow_def.estimated_duration
        except KeyError:
            return "Unknown"

    def set_config(self, project_config: dict) -> None:
        """
        Set the workflow steps to execute

        Args:
            project_config: Project config for generation.
        """

        self.project_config = project_config
        self.logger.info(f"Set config to: {project_config}")

    def start_conversion(self, working_directory: str) -> None:
        """
        Start the conversion process in a separate thread

        Args:
            working_directory: Path to the directory containing uploaded files
        """
        if self.processing_thread and self.processing_thread.is_alive():
            self.logger.warning("Conversion already in progress")
            return

        # Reset state
        self.current_state["status"] = "running"
        self.current_state["working_directory"] = working_directory
        self.current_state["progress"] = 0
        self.current_state["current_agent"] = self.workflow[0] if self.workflow else None
        self.current_state["errors"] = []
        self.current_state["agent_progress"] = {agent: 0 for agent in self.workflow}
        self.current_state["start_time"] = datetime.now()
        
        # Include any uploaded files in the state
        if "uploaded_files" in self.current_state:
            # Make sure the path is preserved for session restoration
            uploaded_files = self.current_state["uploaded_files"]
            if uploaded_files and isinstance(uploaded_files, list):
                # Ensure all paths are absolute and include working directory
                self.current_state["uploaded_files"] = [
                    path if os.path.isabs(path) else os.path.join(working_directory, os.path.basename(path))
                    for path in uploaded_files
                ]

        # Log the start of conversion
        self.logger.info(f"Starting conversion process in {working_directory}")
        self.current_state["logs"] = self.log_capture.get_logs()

        # Start processing in a separate thread
        self.processing_thread = threading.Thread(
            target=self._run_workflow,
            args=(working_directory,)
        )
        self.processing_thread.daemon = True
        self.state_holder.save_state(self.current_state, self.project_config)
        self.processing_thread.start()

    def resume_conversion(self, working_directory: str, knowledge_base: Dict[str, Any]) -> None:
        """
        Resume the conversion process with existing knowledge base

        Args:
            working_directory: Path to the working directory
            knowledge_base: Existing knowledge base to use
        """
        if self.processing_thread and self.processing_thread.is_alive():
            self.logger.warning("Conversion already in progress")
            return

        # Reset state but keep the knowledge base
        self.current_state["status"] = "running"
        self.current_state["working_directory"] = working_directory
        self.current_state["progress"] = 0
        self.current_state["current_agent"] = self.workflow[0] if self.workflow else None
        self.current_state["errors"] = []
        self.current_state["agent_progress"] = {agent: 0 for agent in self.workflow}
        self.current_state["start_time"] = datetime.now()
        self.current_state["knowledge_base"] = knowledge_base

        # Log the resumption of conversion
        self.logger.info(f"Resuming conversion process in {working_directory}")
        self.current_state["logs"] = self.log_capture.get_logs()

        # Start processing in a separate thread
        self.processing_thread = threading.Thread(
            target=self._run_workflow_with_existing_knowledge,
            args=(working_directory, knowledge_base)
        )
        self.processing_thread.daemon = True
        self.save_state()
        self.processing_thread.start()

    def _run_workflow(self, working_directory: str) -> None:
        """
        Run the conversion workflow

        Args:
            working_directory: Path to working directory
        """
        try:
            # Ensure start_time is set if not already
            if "start_time" not in self.current_state or self.current_state["start_time"] is None:
                self.current_state["start_time"] = datetime.now()
            # Process each step in the workflow
            for i, agent_name in enumerate(self.workflow):
                self.logger.info(f"Starting agent: {agent_name}")
                self.current_state["current_agent"] = agent_name
                self.current_state["progress"] = i / len(self.workflow)
                self.current_state["logs"] = self.log_capture.get_logs()

                # Calculate estimated completion time
                if i > 0 and "start_time" in self.current_state and self.current_state["start_time"] is not None:
                    elapsed = datetime.now() - self.current_state["start_time"]
                    progress_fraction = i / len(self.workflow)
                    if progress_fraction > 0:
                        estimated_total_seconds = elapsed.total_seconds() / progress_fraction
                        remaining_seconds = estimated_total_seconds - elapsed.total_seconds()

                        # Format estimated completion as string
                        if remaining_seconds < 60:
                            eta = "Less than a minute"
                        elif remaining_seconds < 3600:
                            eta = f"~{int(remaining_seconds / 60)} minutes"
                        else:
                            eta = f"~{int(remaining_seconds / 3600)} hours {int((remaining_seconds % 3600) / 60)} minutes"

                        self.current_state["estimated_completion"] = eta

                self.save_state()

                # Prepare input for the agent
                agent_input = AgentInput(
                    working_directory=working_directory,
                    knowledge_base=self.current_state["knowledge_base"]
                )

                # Run the agent
                agent = self.agents[agent_name]
                agent.set_up(self.project_config)

                # Patch the agent's report_progress method to update our agent_progress tracker
                original_report_progress = agent.report_progress

                def patched_report_progress(message, progress):
                    # Update the agent-specific progress
                    self.current_state["agent_progress"][agent_name] = progress
                    # Update logs
                    self.current_state["logs"] = self.log_capture.get_logs()
                    # Call the original method
                    original_report_progress(message, progress)

                    self.save_state()

                agent.report_progress = patched_report_progress

                # Run the agent with progress tracking
                result = agent.process(agent_input)

                # Restore original method
                agent.report_progress = original_report_progress

                # Update logs after agent completes
                self.current_state["logs"] = self.log_capture.get_logs()

                # Handle agent results
                if not result.success:
                    self.logger.error(f"Agent {agent_name} failed: {result.message}")
                    self.current_state["errors"].append(f"{agent_name}: {result.message}")

                    # Add detailed errors if available
                    if result.errors:
                        for error in result.errors:
                            self.current_state["errors"].append(f"{agent_name} - {error}")

                    # In case of errors, ask for user intervention
                    user_decision = self._handle_error(agent_name, result.errors)
                    if user_decision == "abort":
                        self.current_state["status"] = "failed"
                        self.save_state()
                        return

                # Update state with agent results
                self.current_state["knowledge_base"].update(result.knowledge_base_updates)

                # Update specific state sections based on agent type
                if agent_name == "documentation_generator" or agent_name == "documentation_critic":
                    if "documentation" in result.artifacts:
                        self.current_state["documentation"].update(result.artifacts["documentation"])

                if agent_name == "code_generator" or agent_name == "code_reviewer":
                    if "generated_code" in result.artifacts:
                        self.current_state["generated_code"].update(result.artifacts["generated_code"])

                # Update progress
                self.current_state["progress"] = (i + 1) / len(self.workflow)
                # Mark this agent as complete
                self.current_state["agent_progress"][agent_name] = 1.0

                self.save_state()

            # All steps completed successfully
            self.current_state["status"] = "completed"
            self.current_state["progress"] = 1.0
            self.logger.info("Conversion workflow completed successfully")
            self.current_state["logs"] = self.log_capture.get_logs()

            self.save_state()

        except Exception as e:
            self.logger.exception(f"Error in conversion workflow: {str(e)}")
            self.current_state["status"] = "failed"
            self.current_state["errors"].append(f"Orchestrator: {str(e)}")
            self.current_state["logs"] = self.log_capture.get_logs()

            self.save_state()

    def save_state(self):
        self.state_holder.update_state(self.current_state)

    def reset(self):
        self.current_state["status"] = "reset"
        self.save_state()

    def _run_workflow_with_existing_knowledge(self, working_directory: str, knowledge_base: Dict[str, Any]) -> None:
        """
        Run the workflow using existing knowledge base

        Args:
            working_directory: Path to working directory
            knowledge_base: Existing knowledge base
        """
        try:
            # Process each step in the workflow
            for i, agent_name in enumerate(self.workflow):
                self.logger.info(f"Starting agent: {agent_name}")
                self.current_state["current_agent"] = agent_name
                self.current_state["progress"] = i / len(self.workflow)
                self.current_state["logs"] = self.log_capture.get_logs()

                self.save_state()

                # Prepare input for the agent
                agent_input = AgentInput(
                    working_directory=working_directory,
                    knowledge_base=knowledge_base
                )

                # Run the agent
                agent = self.agents[agent_name]
                agent.set_up(self.project_config)

                # Patch the agent's report_progress method
                original_report_progress = agent.report_progress

                def patched_report_progress(message, progress):
                    # Update the agent-specific progress
                    self.current_state["agent_progress"][agent_name] = progress
                    # Update logs
                    self.current_state["logs"] = self.log_capture.get_logs()
                    # Call the original method
                    original_report_progress(message, progress)

                    self.save_state()

                agent.report_progress = patched_report_progress

                # Run the agent with progress tracking
                result = agent.process(agent_input)

                # Restore original method
                agent.report_progress = original_report_progress

                # Update logs after agent completes
                self.current_state["logs"] = self.log_capture.get_logs()

                # Handle agent results
                if not result.success:
                    self.logger.error(f"Agent {agent_name} failed: {result.message}")
                    self.current_state["errors"].append(f"{agent_name}: {result.message}")

                    # Add detailed errors if available
                    if result.errors:
                        for error in result.errors:
                            self.current_state["errors"].append(f"{agent_name} - {error}")

                    # In case of errors, ask for user intervention
                    user_decision = self._handle_error(agent_name, result.errors)
                    if user_decision == "abort":
                        self.current_state["status"] = "failed"
                        self.save_state()
                        return

                # Update specific state sections based on agent type
                if agent_name == "documentation_generator" or agent_name == "documentation_critic":
                    if "documentation" in result.artifacts:
                        self.current_state["documentation"].update(result.artifacts["documentation"])
                        self.save_state()

                if agent_name == "code_generator" or agent_name == "code_reviewer":
                    if "generated_code" in result.artifacts:
                        self.current_state["generated_code"].update(result.artifacts["generated_code"])
                        self.save_state()

                # Update progress
                self.current_state["progress"] = (i + 1) / len(self.workflow)
                # Mark this agent as complete
                self.current_state["agent_progress"][agent_name] = 1.0

                self.save_state()

            # All steps completed successfully
            self.current_state["status"] = "completed"
            self.current_state["progress"] = 1.0
            self.logger.info("Conversion workflow completed successfully")
            self.current_state["logs"] = self.log_capture.get_logs()

            self.save_state()

        except Exception as e:
            self.logger.exception(f"Error in conversion workflow: {str(e)}")
            self.current_state["status"] = "failed"
            self.current_state["errors"].append(f"Orchestrator: {str(e)}")
            self.current_state["logs"] = self.log_capture.get_logs()

            self.save_state()

    def _handle_error(self, agent_name: str, errors: List[str]) -> str:
        """
        Handle errors that occur during processing

        Args:
            agent_name: Name of the agent that encountered errors
            errors: List of error messages

        Returns:
            str: User decision ("retry", "skip", or "abort")
        """
        # In a real implementation, this would interact with the UI
        # For now, we'll just log the errors and decide to continue
        self.logger.error(f"Errors during {agent_name} processing:")
        for error in errors:
            self.logger.error(f" - {error}")

        # Default to continuing to the next step
        return "skip"

    def get_states(self):
        """
        Retrieves all states from the state holder.

        This method accesses the state_holder property of the current instance and returns
        all states that have been stored or tracked by the state holder.

        Returns:
            A collection of states from the state holder
        """
        return self.state_holder.get_states()

    def load_state_by_id(self, id_: int):
        """
        Loads a specific state by ID and updates the current state with its values.

        This method retrieves a state using the provided ID from the state_holder, then
        updates the current_state dictionary with all key-value pairs from the retrieved state.
        The method returns the retrieved state after updating the current state.

        Params:
            id_: The identifier of the state to load
        Returns:
            The state that was retrieved by the given ID
        """
        state_by_id = self.state_holder.get_state_by_id(id_)
        if state_by_id:
            # Update our current state with stored values
            for k in state_by_id:
                self.current_state[k] = state_by_id[k]
                
            # Special handling for working directory - critical for recovery
            if "working_directory" in state_by_id and state_by_id["working_directory"]:
                self.current_state["working_directory"] = state_by_id["working_directory"]
                # Log for debugging purposes
                self.logger.info(f"Restored working directory from state {id_}: {state_by_id['working_directory']}")
                
            # Special handling for uploaded files - critical for recovery
            if "file_stats" in state_by_id and isinstance(state_by_id["file_stats"], dict) and "uploaded_files" in state_by_id["file_stats"]:
                self.current_state["uploaded_files"] = state_by_id["file_stats"]["uploaded_files"]
                self.logger.info(f"Restored uploaded files from state {id_} (file_stats): {len(state_by_id['file_stats']['uploaded_files'])} files")
                
            # Check knowledge_base.file_stats too if needed
            if ("knowledge_base" in state_by_id and 
                isinstance(state_by_id["knowledge_base"], dict) and 
                "file_stats" in state_by_id["knowledge_base"] and 
                isinstance(state_by_id["knowledge_base"]["file_stats"], dict) and 
                "uploaded_files" in state_by_id["knowledge_base"]["file_stats"]):
                
                self.current_state["uploaded_files"] = state_by_id["knowledge_base"]["file_stats"]["uploaded_files"]
                self.logger.info(f"Restored uploaded files from state {id_} (knowledge_base): {len(state_by_id['knowledge_base']['file_stats']['uploaded_files'])} files")

        return state_by_id

    def load_current_state(self) -> Dict[str, Any]:
        """
        Get the running state of the conversion process

        Returns:
            Dict: Current state
        """
        running_states = self.state_holder.get_running_states()
        if running_states:
            stored_state = running_states[0]

            # Update our current state with stored values
            for k in stored_state:
                self.current_state[k] = stored_state[k]
                
            # Special handling for working directory - critical for recovery
            if "working_directory" in stored_state and stored_state["working_directory"]:
                self.current_state["working_directory"] = stored_state["working_directory"]
                # Log for debugging purposes
                self.logger.debug(f"Restored working directory from stored state: {stored_state['working_directory']}")
                
            # Special handling for uploaded files - critical for recovery
            if "file_stats" in stored_state and isinstance(stored_state["file_stats"], dict) and "uploaded_files" in stored_state["file_stats"]:
                self.current_state["uploaded_files"] = stored_state["file_stats"]["uploaded_files"]
                self.logger.debug(f"Restored uploaded files from stored state (file_stats): {len(stored_state['file_stats']['uploaded_files'])} files")
                
            # Check knowledge_base.file_stats too if needed
            if ("knowledge_base" in stored_state and 
                isinstance(stored_state["knowledge_base"], dict) and 
                "file_stats" in stored_state["knowledge_base"] and 
                isinstance(stored_state["knowledge_base"]["file_stats"], dict) and 
                "uploaded_files" in stored_state["knowledge_base"]["file_stats"]):
                
                self.current_state["uploaded_files"] = stored_state["knowledge_base"]["file_stats"]["uploaded_files"]
                self.logger.debug(f"Restored uploaded files from stored state (knowledge_base): {len(stored_state['knowledge_base']['file_stats']['uploaded_files'])} files")

        return self.current_state
