import importlib
import os
import sys
import json
from collections import defaultdict

from src.platform.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from src.platform.tools.knowledge_database import KnowledgeDatabase

class CodePreprocessorAgent(BaseAgent):
    """
    Agent responsible for preprocessing code files based on their language
    and chunking them for efficient analysis.
    """

    def __init__(self):
        super().__init__("code_preprocessor")
        # Initialize the knowledge database
        self.knowledge_db = KnowledgeDatabase()

    def set_up(self, config: dict) -> None:
        pass

    def process(self, input_data: AgentInput) -> AgentOutput:
        """
        Preprocess code files based on their detected language

        Args:
            input_data: Input data containing working directory and knowledge base

        Returns:
            AgentOutput: Preprocessing results
        """
        self.logger.info("Starting code preprocessing")

        try:
            # Get organized directory from knowledge base
            organized_dir = input_data.knowledge_base.get("organized_directory")
            if not organized_dir:
                return AgentOutput(
                    success=False,
                    message="No organized directory found in knowledge base",
                    errors=["Missing organized_directory in knowledge base"]
                )

            # Create output directory for preprocessed files
            preprocessed_dir = os.path.join(input_data.working_directory, "preprocessed")

            os.makedirs(preprocessed_dir, exist_ok=True)

            # Create output directory for chunked files
            chunked_dir = os.path.join(input_data.working_directory, "chunked")
            os.makedirs(chunked_dir, exist_ok=True)

            # Get file info from knowledge base
            file_info_list = input_data.knowledge_base.get("file_info", [])

            # Stats for tracking
            preprocessing_stats = defaultdict(int)
            chunk_stats = defaultdict(int)
            data_extraction_stats = defaultdict(int)  # New stats for data extraction

            # Process each language directory
            self.report_progress("Starting preprocessing files by language", 0.1)

            # Process files by language
            processed_files = []

            # Get available languages from plugin system
            try:
                from src.platform.plugins.plugin_loader import get_plugin_loader
                plugin_loader = get_plugin_loader()
                supported_languages = plugin_loader.get_available_languages()
                self.logger.info(f"Available language plugins: {supported_languages}")
            except Exception as e:
                self.logger.error(f"Plugin system not available: {str(e)}")
                return AgentOutput(
                    success=False,
                    message="Plugin system not available",
                    errors=[f"Plugin system error: {str(e)}"]
                )

            for language in supported_languages:
                language_dir = os.path.join(organized_dir, language)
                if not os.path.exists(language_dir):
                    continue

                # Process files for this language
                language_files = [f for f in file_info_list if f["language"] == language]
                self.logger.info(f"Processing {len(language_files)} {language} files")

                # Get preprocessor and chunker from plugin system
                try:
                    plugin_loader = get_plugin_loader()
                    language_plugin = plugin_loader.get_language_plugin(language)

                    if language_plugin:
                        preprocessor = language_plugin.get_preprocessor()
                        chunker = language_plugin.get_chunker()
                    else:
                        self.logger.warning(f"No plugin available for language: {language}")
                        preprocessor = None
                        chunker = None
                except Exception as e:
                    self.logger.error(f"Plugin system error for {language}: {str(e)}")
                    preprocessor = None
                    chunker = None

                if not preprocessor:
                    self.logger.warning(f"No preprocessor available for {language}")
                    continue

                if not chunker:
                    self.logger.warning(f"No chunker available for {language}")
                    continue

                # Preprocess each file
                for file_info in language_files:
                    try:
                        # Skip copybooks for COBOL (this could be moved to plugin later)
                        if language == "cobol" and (file_info["filename"].endswith(".cpy") or file_info["filename"].endswith(".CPY")):
                            continue

                        # Create output path for preprocessed file
                        rel_path = file_info["relative_path"]
                        self.logger.info(f"Processing {rel_path}")
                        preprocessed_path = os.path.join(preprocessed_dir, language, rel_path)
                        os.makedirs(os.path.dirname(preprocessed_path), exist_ok=True)

                        if os.path.exists(preprocessed_path):
                            self.logger.info(f"Preprocessed file already exists, skipping preprocessing: {preprocessed_path}")
                            continue

                        # Preprocess file
                        source_path = file_info["organized_path"]
                        self.logger.info(f"source_path {source_path}")
                        preprocessed = preprocessor.preprocess_file(source_path, preprocessed_path)
                        preprocessing_stats[language] += 1

                        if preprocessed:
                            # Chunk preprocessed file
                            file_chunks_dir = os.path.join(chunked_dir, language, os.path.splitext(rel_path)[0])
                            os.makedirs(file_chunks_dir, exist_ok=True)

                            # Get chunks from chunker
                            chunk_results = chunker.chunk_file(preprocessed_path, file_chunks_dir)
                            chunk_stats[language] += len(chunk_results)

                            # get project name
                            project_name = input_data.working_directory.split('/')[-1]

                            program_name = os.path.splitext(file_info["filename"])[0]

                            self.logger.info(f"Insert Program to knowledge DB: {program_name}")

                            # Insert program information into the database
                            self.knowledge_db.insert_program({
                                'program_id': program_name,
                                'project_name': project_name,
                                'program_type': 'source_code',
                                'file_path': source_path,
                                'language': language,
                                'size': os.path.getsize(source_path),
                                'metadata': {
                                    'original_filename': file_info["filename"],
                                    'preprocessed_path': preprocessed_path
                                }
                            })

                            # Insert chunks into the database
                            # Convert chunk_results to list of chunk dictionaries
                            chunks = []
                            extracted_items_count = 0  # Counter for extracted data items

                            self.logger.info(f"Number of chunks for {program_name}: {len(chunk_results)}")
                            for chunk_result in chunk_results:
                                code = chunk_result['code']

                                # Prepare chunk dictionary for database insertion
                                chunk_dict = {
                                    'program_id': program_name,
                                    'chunk_type': chunk_result['chunk_type'],
                                    'chunk_name': chunk_result['chunk_name'],
                                    'chunk_size': len(code.splitlines()),
                                    'code': code,
                                    'metadata': chunk_result.get('metadata', {})
                                }
                                chunks.append(chunk_dict)

                                # Extract data definitions using language plugins
                                try:
                                    if language_plugin and hasattr(language_plugin, 'get_analyzer'):
                                        analyzer = language_plugin.get_analyzer()
                                        if analyzer and hasattr(analyzer, 'can_extract_data_definitions'):
                                            # Check if this analyzer can extract data from this chunk type
                                            if analyzer.can_extract_data_definitions(chunk_result['chunk_type']):
                                                self.logger.info(f"Extracting data definitions from {chunk_result['chunk_name']} using {language} plugin")

                                                # Use the analyzer to extract data definitions
                                                # Pass the actual program name to ensure correct program_id in extracted data
                                                analysis_result = analyzer.analyze(chunk_result['code'], chunk_result['chunk_name'])
                                                data_items = analysis_result.get('data_items', [])

                                                if data_items:
                                                    extracted_items_count += len(data_items)

                                                    # Save to database using the appropriate method based on language
                                                    if language == "cobol":
                                                        self.knowledge_db.insert_cobol_data_definitions(program_name, data_items)
                                                    else:
                                                        # For other languages, use generic data definition insertion
                                                        for item in data_items:
                                                            item['program_id'] = program_name
                                                        self.knowledge_db.insert_data_definitions(program_name, data_items)

                                                    self.logger.info(f"Stored {len(data_items)} data items in database from {chunk_result['chunk_name']} for program {program_name}")
                                except Exception as e:
                                    self.logger.exception(f"Error extracting data definitions for chunk {chunk_result['chunk_name']} for program {program_name}: {str(e)}")

                            # Update data extraction stats
                            if extracted_items_count > 0:
                                data_extraction_stats[language] += extracted_items_count
                                self.logger.info(f"Total {extracted_items_count} data items extracted from {program_name}")

                            # Insert chunks into the database
                            if chunks:
                                for chunk in chunks:
                                    # First, create JSON metadata
                                    chunk['metadata'] = chunk.get('metadata', {})

                                    # Note: the knowledge_database method already handles JSON conversion
                                    self.knowledge_db.insert_chunks(
                                        chunk['program_id'],
                                        [chunk]
                                    )

                            # Store processed file information
                            processed_files.append({
                                "original_path": source_path,
                                "preprocessed_path": preprocessed_path,
                                "chunks_dir": file_chunks_dir,
                                "language": language,
                                "chunk_count": len(chunk_results),
                                "extracted_data_items": extracted_items_count,
                                "relative_path": rel_path
                            })
                    except Exception as e:
                        self.logger.exception(f"Error processing file {file_info['filename']}: {str(e)}")

            # Create summary of preprocessing
            self.report_progress("Creating preprocessing summary", 0.9)

            # Prepare knowledge base updates
            knowledge_base_updates = {
                "preprocessed_directory": preprocessed_dir,
                "chunked_directory": chunked_dir,
                "processed_files": processed_files,
                "preprocessing_stats": dict(preprocessing_stats),
                "chunk_stats": dict(chunk_stats),
                "data_extraction_stats": dict(data_extraction_stats)  # Add data extraction stats
            }

            self.report_progress("Code preprocessing completed", 1.0)

            return AgentOutput(
                success=True,
                message="Code preprocessing completed successfully",
                knowledge_base_updates=knowledge_base_updates
            )

        except Exception as e:
            self.logger.exception(f"Error during code preprocessing: {str(e)}")
            return AgentOutput(
                success=False,
                message=f"Code preprocessing failed: {str(e)}",
                errors=[str(e)]
            )

