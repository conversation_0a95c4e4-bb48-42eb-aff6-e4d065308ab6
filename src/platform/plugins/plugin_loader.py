"""
Plugin loader system for dynamically loading and managing plugins.
"""
import os
import importlib
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path

from src.platform.interfaces.language_plugin import LanguagePlugin
from src.platform.interfaces.target_plugin import TargetPlugin
from config.plugin_registry import PLUGIN_REGISTRY, PluginConfig, PluginType

logger = logging.getLogger(__name__)


class PluginLoader:
    """Manages loading and discovery of plugins."""

    def __init__(self):
        self._language_plugins: Dict[str, LanguagePlugin] = {}
        self._target_plugins: Dict[str, TargetPlugin] = {}
        self._loaded_plugins: Dict[str, Any] = {}

    def load_all_plugins(self) -> None:
        """Load all registered plugins."""
        for plugin_name, plugin_config in PLUGIN_REGISTRY.items():
            if plugin_config.enabled:
                try:
                    self.load_plugin(plugin_name, plugin_config)
                except Exception as e:
                    logger.error(f"Failed to load plugin {plugin_name}: {str(e)}")

    def load_plugin(self, plugin_name: str, plugin_config: PluginConfig) -> Any:
        """
        Load a specific plugin.

        Args:
            plugin_name: Name of the plugin
            plugin_config: Plugin configuration

        Returns:
            Any: Loaded plugin instance
        """
        if plugin_name in self._loaded_plugins:
            return self._loaded_plugins[plugin_name]

        try:
            # Import the plugin module
            module = importlib.import_module(plugin_config.module_path)

            # Get the plugin class
            plugin_class = getattr(module, plugin_config.class_name)

            # Create plugin instance
            plugin_instance = plugin_class()

            # Initialize the plugin
            if hasattr(plugin_instance, 'initialize'):
                plugin_instance.initialize(plugin_config.config or {})

            # Store the plugin
            self._loaded_plugins[plugin_name] = plugin_instance

            # Store in appropriate category
            if plugin_config.plugin_type == PluginType.LANGUAGE:
                self._language_plugins[plugin_name] = plugin_instance
            elif plugin_config.plugin_type == PluginType.TARGET:
                self._target_plugins[plugin_name] = plugin_instance

            logger.info(f"Successfully loaded plugin: {plugin_name}")
            return plugin_instance

        except Exception as e:
            logger.error(f"Failed to load plugin {plugin_name}: {str(e)}")
            raise

    def get_language_plugin(self, language_name: str) -> Optional[LanguagePlugin]:
        """
        Get a language plugin by name.

        Args:
            language_name: Name of the language

        Returns:
            Optional[LanguagePlugin]: Language plugin instance or None
        """
        return self._language_plugins.get(language_name)

    def get_target_plugin(self, target_name: str) -> Optional[TargetPlugin]:
        """
        Get a target plugin by name.

        Args:
            target_name: Name of the target technology

        Returns:
            Optional[TargetPlugin]: Target plugin instance or None
        """
        return self._target_plugins.get(target_name)

    def get_available_languages(self) -> List[str]:
        """
        Get list of available language plugins.

        Returns:
            List[str]: List of language names
        """
        return list(self._language_plugins.keys())

    def get_available_targets(self) -> List[str]:
        """
        Get list of available target plugins.

        Returns:
            List[str]: List of target names
        """
        return list(self._target_plugins.keys())

    def get_language_plugins(self) -> Dict[str, LanguagePlugin]:
        """
        Get all loaded language plugins.

        Returns:
            Dict[str, LanguagePlugin]: Dictionary of language name to plugin instance
        """
        return self._language_plugins.copy()

    def get_target_plugins(self) -> Dict[str, TargetPlugin]:
        """
        Get all loaded target plugins.

        Returns:
            Dict[str, TargetPlugin]: Dictionary of target name to plugin instance
        """
        return self._target_plugins.copy()

    def detect_language(self, content: str, filename: Optional[str] = None) -> Optional[str]:
        """
        Detect language using all available language plugins.

        Args:
            content: Source code content
            filename: Optional filename

        Returns:
            Optional[str]: Detected language name or None
        """
        best_language = None
        best_confidence = 0.0

        for language_name, plugin in self._language_plugins.items():
            detector = plugin.get_detector()
            if detector:
                try:
                    confidence = detector.get_confidence_score(content, filename)
                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_language = language_name
                except Exception as e:
                    logger.warning(f"Error in language detection for {language_name}: {str(e)}")

        return best_language if best_confidence > 0.5 else None

    def can_process_file(self, file_path: str) -> List[str]:
        """
        Get list of languages that can process the given file.

        Args:
            file_path: Path to the file

        Returns:
            List[str]: List of language names that can process the file
        """
        capable_languages = []

        for language_name, plugin in self._language_plugins.items():
            try:
                if plugin.can_process_file(file_path):
                    capable_languages.append(language_name)
            except Exception as e:
                logger.warning(f"Error checking file capability for {language_name}: {str(e)}")

        return capable_languages

    def get_plugin_info(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a plugin.

        Args:
            plugin_name: Name of the plugin

        Returns:
            Optional[Dict[str, Any]]: Plugin information or None
        """
        if plugin_name in PLUGIN_REGISTRY:
            config = PLUGIN_REGISTRY[plugin_name]
            plugin_instance = self._loaded_plugins.get(plugin_name)

            info = {
                "name": config.name,
                "display_name": config.display_name,
                "description": config.description,
                "version": config.version,
                "author": config.author,
                "plugin_type": config.plugin_type.value,
                "enabled": config.enabled,
                "loaded": plugin_instance is not None
            }

            if plugin_instance:
                info["instance_info"] = {
                    "class_name": plugin_instance.__class__.__name__,
                    "module": plugin_instance.__class__.__module__
                }

            return info

        return None


# Global plugin loader instance
_plugin_loader = None


def get_plugin_loader() -> PluginLoader:
    """
    Get the global plugin loader instance.

    Returns:
        PluginLoader: Global plugin loader instance
    """
    global _plugin_loader
    if _plugin_loader is None:
        _plugin_loader = PluginLoader()
        _plugin_loader.load_all_plugins()
    return _plugin_loader
