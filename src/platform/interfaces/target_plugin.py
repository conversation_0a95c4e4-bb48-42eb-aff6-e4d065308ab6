"""
Target plugin interface for target technology generation.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from .base_plugin import BasePlugin


class CodeGenerator(ABC):
    """Interface for target-specific code generators."""
    
    @abstractmethod
    def generate_code(self, source_analysis: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate target code from source analysis.
        
        Args:
            source_analysis: Analysis results from source language
            config: Generation configuration
            
        Returns:
            Dict[str, Any]: Generated code and metadata
        """
        pass


class ProjectManager(ABC):
    """Interface for target-specific project management."""
    
    @abstractmethod
    def create_project_structure(self, project_name: str, output_dir: str, config: Dict[str, Any]) -> bool:
        """
        Create target project structure.
        
        Args:
            project_name: Name of the project
            output_dir: Output directory
            config: Project configuration
            
        Returns:
            bool: True if successful
        """
        pass
    
    @abstractmethod
    def add_dependencies(self, dependencies: List[str]) -> bool:
        """
        Add dependencies to the project.
        
        Args:
            dependencies: List of dependencies to add
            
        Returns:
            bool: True if successful
        """
        pass


class DocumentationGenerator(ABC):
    """Interface for target-specific documentation generation."""
    
    @abstractmethod
    def generate_documentation(self, analysis: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate documentation for the target technology.
        
        Args:
            analysis: Source code analysis
            config: Documentation configuration
            
        Returns:
            Dict[str, str]: Generated documentation files (filename -> content)
        """
        pass


class TargetPlugin(BasePlugin):
    """Base interface for target technology plugins."""
    
    @abstractmethod
    def get_target_name(self) -> str:
        """Get the target technology name this plugin supports."""
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> List[str]:
        """Get list of source languages this target can handle."""
        pass
    
    @abstractmethod
    def get_code_generator(self) -> Optional[CodeGenerator]:
        """Get the code generator."""
        pass
    
    @abstractmethod
    def get_project_manager(self) -> Optional[ProjectManager]:
        """Get the project manager."""
        pass
    
    @abstractmethod
    def get_documentation_generator(self) -> Optional[DocumentationGenerator]:
        """Get the documentation generator."""
        pass
    
    @abstractmethod
    def can_handle_language(self, language: str) -> bool:
        """
        Check if this plugin can handle the given source language.
        
        Args:
            language: Source language name
            
        Returns:
            bool: True if plugin can handle the language
        """
        pass
