"""
Language plugin interface for source language processing.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from .base_plugin import BasePlugin


class LanguagePreprocessor(ABC):
    """Interface for language-specific preprocessors."""

    @abstractmethod
    def preprocess(self, content: str, file_path: str) -> str:
        """
        Preprocess source code content.

        Args:
            content: Raw source code content
            file_path: Path to the source file

        Returns:
            str: Preprocessed content
        """
        pass


class LanguageChunker(ABC):
    """Interface for language-specific chunkers."""

    @abstractmethod
    def chunk(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """
        Chunk source code into logical segments.

        Args:
            content: Preprocessed source code content
            file_path: Path to the source file

        Returns:
            List[Dict[str, Any]]: List of code chunks with metadata
        """
        pass


class LanguageAnalyzer(ABC):
    """Interface for language-specific analyzers."""

    @abstractmethod
    def analyze(self, content: str, file_path: str) -> Dict[str, Any]:
        """
        Analyze source code to extract metadata and structure.

        Args:
            content: Source code content
            file_path: Path to the source file

        Returns:
            Dict[str, Any]: Analysis results with optional 'data_items' key for data extraction
        """
        pass

    def can_extract_data_definitions(self, chunk_type: str) -> bool:
        """
        Check if this analyzer can extract data definitions from the given chunk type.

        Args:
            chunk_type: Type of the code chunk

        Returns:
            bool: True if analyzer can extract data definitions from this chunk type
        """
        return False


class LanguageDetector(ABC):
    """Interface for language detection."""

    @abstractmethod
    def detect_language(self, content: str, filename: Optional[str] = None) -> Optional[str]:
        """
        Detect the programming language of content.

        Args:
            content: Source code content
            filename: Optional filename for extension-based detection

        Returns:
            Optional[str]: Detected language name or None
        """
        pass

    @abstractmethod
    def get_supported_extensions(self) -> List[str]:
        """
        Get list of file extensions supported by this language.

        Returns:
            List[str]: List of file extensions (including the dot)
        """
        pass

    @abstractmethod
    def get_confidence_score(self, content: str, filename: Optional[str] = None) -> float:
        """
        Get confidence score for language detection.

        Args:
            content: Source code content
            filename: Optional filename

        Returns:
            float: Confidence score between 0.0 and 1.0
        """
        pass


class LanguagePlugin(BasePlugin):
    """Base interface for language plugins."""

    @abstractmethod
    def get_language_name(self) -> str:
        """Get the language name this plugin supports."""
        pass

    @abstractmethod
    def get_supported_extensions(self) -> List[str]:
        """Get list of file extensions supported by this language."""
        pass

    @abstractmethod
    def get_preprocessor(self) -> Optional[LanguagePreprocessor]:
        """Get the language preprocessor."""
        pass

    @abstractmethod
    def get_chunker(self) -> Optional[LanguageChunker]:
        """Get the language chunker."""
        pass

    @abstractmethod
    def get_analyzer(self) -> Optional[LanguageAnalyzer]:
        """Get the language analyzer."""
        pass

    @abstractmethod
    def get_detector(self) -> Optional[LanguageDetector]:
        """Get the language detector."""
        pass

    @abstractmethod
    def can_process_file(self, file_path: str) -> bool:
        """
        Check if this plugin can process the given file.

        Args:
            file_path: Path to the file

        Returns:
            bool: True if plugin can process the file
        """
        pass
