"""
Integration tests for database operation detection and template selection.
Tests the complete flow from COBOL code analysis to Java code generation.
"""
import unittest
import tempfile
import os
from unittest.mock import Mock, patch

from src.plugins.targets.java_spring.tools.database_operation_detector import DatabaseOperationDetector
from src.plugins.targets.java_spring.agents.code_generator import JavaCodeGenerator
from src.plugins.targets.java_spring.agents.repository_generator import JavaRepositoryGenerator


class TestDatabaseOperationIntegration(unittest.TestCase):
    """Test database operation detection and code generation integration."""

    def setUp(self):
        """Set up test fixtures."""
        self.detector = DatabaseOperationDetector()
        self.code_generator = JavaCodeGenerator()
        self.repository_generator = JavaRepositoryGenerator()
        
        # Sample COBOL code with various database operations
        self.sample_cobol_dli = """
        EXEC DLI GU SEGMENT(CUSTOMER) WHERE(CUST-ID = :WS-CUST-ID) INTO(CUSTOMER-RECORD)
        EXEC DLI ISRT SEGMENT(ORDER) FROM(ORDER-RECORD)
        EXEC DLI REPL SEGMENT(CUSTOMER) FROM(CUSTOMER-RECORD) WHERE(CUST-ID = :WS-CUST-ID)
        """
        
        self.sample_cobol_db2 = """
        EXEC SQL SELECT CUST_ID, CUST_NAME, CUST_ADDR 
                 FROM CUSTOMER 
                 WHERE CUST_ID = :WS-CUST-ID 
                 END-EXEC
        EXEC SQL INSERT INTO ORDER (ORDER_ID, CUST_ID, ORDER_DATE) 
                 VALUES (:WS-ORDER-ID, :WS-CUST-ID, :WS-ORDER-DATE) 
                 END-EXEC
        """
        
        self.sample_cobol_gsam = """
        CALL 'CBLTDLI' USING 'GU', CUSTOMER-PCB, CUSTOMER-IO-AREA
        OPEN INPUT CUSTOMER-FILE
        READ CUSTOMER-FILE INTO CUSTOMER-RECORD
        CLOSE CUSTOMER-FILE
        """

    def test_dli_operation_detection(self):
        """Test detection of IMS DLI operations."""
        operations = self.detector.detect_database_operations(self.sample_cobol_dli, "TEST_CHUNK")
        
        self.assertEqual(len(operations), 3)
        
        # Check GU operation
        gu_op = next((op for op in operations if op.operation_subtype == 'GU'), None)
        self.assertIsNotNone(gu_op)
        self.assertEqual(gu_op.operation_type, 'IMS_DLI')
        self.assertEqual(gu_op.table_or_segment, 'CUSTOMER')
        self.assertIn('CUST-ID', gu_op.parameters.get('where_clause', ''))
        
        # Check ISRT operation
        isrt_op = next((op for op in operations if op.operation_subtype == 'ISRT'), None)
        self.assertIsNotNone(isrt_op)
        self.assertEqual(isrt_op.table_or_segment, 'ORDER')
        
        # Check REPL operation
        repl_op = next((op for op in operations if op.operation_subtype == 'REPL'), None)
        self.assertIsNotNone(repl_op)
        self.assertEqual(repl_op.table_or_segment, 'CUSTOMER')

    def test_db2_operation_detection(self):
        """Test detection of IBM DB2 SQL operations."""
        operations = self.detector.detect_database_operations(self.sample_cobol_db2, "TEST_CHUNK")
        
        self.assertEqual(len(operations), 2)
        
        # Check SELECT operation
        select_op = next((op for op in operations if op.operation_subtype == 'SELECT'), None)
        self.assertIsNotNone(select_op)
        self.assertEqual(select_op.operation_type, 'DB2_SQL')
        self.assertEqual(select_op.table_or_segment, 'CUSTOMER')
        self.assertIn('WS-CUST-ID', select_op.parameters.get('host_variables', []))
        
        # Check INSERT operation
        insert_op = next((op for op in operations if op.operation_subtype == 'INSERT'), None)
        self.assertIsNotNone(insert_op)
        self.assertEqual(insert_op.table_or_segment, 'ORDER')
        self.assertIn('WS-ORDER-ID', insert_op.parameters.get('host_variables', []))

    def test_gsam_operation_detection(self):
        """Test detection of GSAM file operations."""
        operations = self.detector.detect_database_operations(self.sample_cobol_gsam, "TEST_CHUNK")
        
        self.assertGreaterEqual(len(operations), 2)  # At least CALL_CBLTDLI and file operations
        
        # Check CBLTDLI operation
        cbltdli_op = next((op for op in operations if op.operation_subtype == 'CALL_CBLTDLI'), None)
        self.assertIsNotNone(cbltdli_op)
        self.assertEqual(cbltdli_op.operation_type, 'GSAM_FILE')

    def test_operation_classification(self):
        """Test classification of operations for template selection."""
        # Test DLI operations
        dli_operations = self.detector.detect_database_operations(self.sample_cobol_dli, "TEST_CHUNK")
        dli_classification = self.detector.classify_operations_for_template_selection(dli_operations)
        
        self.assertTrue(dli_classification['has_database_operations'])
        self.assertIn('IMS_DLI', dli_classification['operation_types'])
        self.assertEqual(dli_classification['primary_operation_type'], 'IMS_DLI')
        self.assertTrue(dli_classification['requires_repository'])
        self.assertTrue(dli_classification['requires_service_layer'])
        self.assertIn('database_operations/ims_dli_conversion.j2', dli_classification['template_recommendations'])
        
        # Test DB2 operations
        db2_operations = self.detector.detect_database_operations(self.sample_cobol_db2, "TEST_CHUNK")
        db2_classification = self.detector.classify_operations_for_template_selection(db2_operations)
        
        self.assertTrue(db2_classification['has_database_operations'])
        self.assertIn('DB2_SQL', db2_classification['operation_types'])
        self.assertEqual(db2_classification['primary_operation_type'], 'DB2_SQL')
        self.assertIn('database_operations/db2_sql_conversion.j2', db2_classification['template_recommendations'])

    def test_template_context_preparation(self):
        """Test preparation of template context from detected operations."""
        operations = self.detector.detect_database_operations(self.sample_cobol_dli, "TEST_CHUNK")
        classification = self.detector.classify_operations_for_template_selection(operations)
        context = self.detector.prepare_template_context(operations, classification)
        
        self.assertTrue(context['has_database_operations'])
        self.assertEqual(len(context['database_operations']), 3)
        self.assertEqual(len(context['dli_operations']), 3)
        self.assertEqual(len(context['db2_operations']), 0)
        self.assertEqual(len(context['gsam_operations']), 0)
        self.assertEqual(context['primary_operation_type'], 'IMS_DLI')

    def test_code_generator_database_detection_integration(self):
        """Test integration between database detection and code generator."""
        # Mock chunk document with database operations
        chunk_doc = {
            'chunk_name': 'TEST_CHUNK',
            'program_id': 'TEST_PROGRAM',
            'functional_spec': self.sample_cobol_dli,
            'cobol_code': self.sample_cobol_dli
        }
        
        # Test database operation detection in code generator
        db_context = self.code_generator._detect_database_operations(chunk_doc)
        
        self.assertTrue(db_context['has_database_operations'])
        self.assertEqual(db_context['primary_operation_type'], 'IMS_DLI')
        self.assertEqual(len(db_context['database_operations']), 3)

    def test_template_selection_with_database_operations(self):
        """Test template selection based on detected database operations."""
        # Test DLI operations lead to database service template
        dli_context = {
            'has_database_operations': True,
            'primary_operation_type': 'IMS_DLI',
            'business_description': 'Customer management service'
        }
        
        template = self.code_generator._select_template_by_context(dli_context)
        self.assertEqual(template, 'services/database_service.j2')
        
        # Test DB2 operations lead to database service template
        db2_context = {
            'has_database_operations': True,
            'primary_operation_type': 'DB2_SQL',
            'business_description': 'Order processing service'
        }
        
        template = self.code_generator._select_template_by_context(db2_context)
        self.assertEqual(template, 'services/database_service.j2')
        
        # Test GSAM operations lead to file processing template
        gsam_context = {
            'has_database_operations': True,
            'primary_operation_type': 'GSAM_FILE',
            'business_description': 'File processing service'
        }
        
        template = self.code_generator._select_template_by_context(gsam_context)
        self.assertEqual(template, 'file_processing_service.j2')

    def test_repository_generator_context_building(self):
        """Test repository generator context building."""
        entity_info = {
            'entity_name': 'Customer',
            'program_id': 'TEST_PROGRAM',
            'id_type': 'Long',
            'table_name': 'CUSTOMER',
            'business_purpose': 'Customer entity for CRM system',
            'package_name': 'com.test.cobol'
        }
        
        operations = self.detector.detect_database_operations(self.sample_cobol_dli, "TEST_CHUNK")
        generation_state = {'generation_date': '2024-01-01'}
        
        context = self.repository_generator._build_repository_context(entity_info, operations, generation_state)
        
        self.assertEqual(context['entity_name'], 'Customer')
        self.assertEqual(context['table_name'], 'CUSTOMER')
        self.assertTrue(context['has_database_operations'])
        self.assertEqual(context['primary_operation_type'], 'IMS_DLI')
        self.assertEqual(len(context['database_operations']), 3)

    def test_operation_filtering_for_entity(self):
        """Test filtering operations relevant to specific entity."""
        entity_info = {
            'entity_name': 'Customer',
            'table_name': 'CUSTOMER'
        }
        
        # Mix operations for different entities
        mixed_cobol = """
        EXEC DLI GU SEGMENT(CUSTOMER) WHERE(CUST-ID = :WS-CUST-ID)
        EXEC DLI ISRT SEGMENT(ORDER) FROM(ORDER-RECORD)
        EXEC DLI REPL SEGMENT(CUSTOMER) FROM(CUSTOMER-RECORD)
        """
        
        all_operations = self.detector.detect_database_operations(mixed_cobol, "TEST_CHUNK")
        filtered_operations = self.repository_generator._filter_operations_for_entity(all_operations, entity_info)
        
        # Should only get CUSTOMER operations
        self.assertEqual(len(filtered_operations), 2)
        for op in filtered_operations:
            self.assertEqual(op.table_or_segment, 'CUSTOMER')

    def test_host_variable_extraction(self):
        """Test extraction of host variables from SQL code."""
        sql_code = "SELECT * FROM CUSTOMER WHERE CUST_ID = :WS-CUST-ID AND STATUS = :WS-STATUS"
        host_vars = self.detector._extract_host_variables(sql_code)
        
        self.assertIn('WS-CUST-ID', host_vars)
        self.assertIn('WS-STATUS', host_vars)
        self.assertEqual(len(host_vars), 2)

    def test_cbltdli_parameter_extraction(self):
        """Test extraction of parameters from CBLTDLI calls."""
        cbltdli_params = "'GU', CUSTOMER-PCB, CUSTOMER-IO-AREA"
        
        function_code = self.detector._extract_function_code(cbltdli_params)
        pcb_name = self.detector._extract_pcb_name(cbltdli_params)
        io_area = self.detector._extract_io_area(cbltdli_params)
        
        self.assertEqual(function_code, 'GU')
        self.assertEqual(pcb_name, 'CUSTOMER-PCB')
        self.assertEqual(io_area, 'CUSTOMER-IO-AREA')

    def test_error_handling_in_detection(self):
        """Test error handling in database operation detection."""
        # Test with invalid COBOL code
        invalid_cobol = "INVALID COBOL CODE WITH NO DATABASE OPERATIONS"
        operations = self.detector.detect_database_operations(invalid_cobol, "TEST_CHUNK")
        
        self.assertEqual(len(operations), 0)
        
        # Test with empty code
        empty_operations = self.detector.detect_database_operations("", "TEST_CHUNK")
        self.assertEqual(len(empty_operations), 0)
        
        # Test classification with no operations
        classification = self.detector.classify_operations_for_template_selection([])
        self.assertFalse(classification['has_database_operations'])
        self.assertIsNone(classification['primary_operation_type'])

    def test_comprehensive_integration_flow(self):
        """Test the complete integration flow from detection to template selection."""
        # Start with COBOL code containing multiple operation types
        comprehensive_cobol = """
        EXEC DLI GU SEGMENT(CUSTOMER) WHERE(CUST-ID = :WS-CUST-ID) INTO(CUSTOMER-RECORD)
        EXEC SQL SELECT ORDER_ID FROM ORDER WHERE CUST_ID = :WS-CUST-ID END-EXEC
        CALL 'CBLTDLI' USING 'GN', ORDER-PCB, ORDER-IO-AREA
        """
        
        # Step 1: Detect operations
        operations = self.detector.detect_database_operations(comprehensive_cobol, "COMPREHENSIVE_TEST")
        self.assertGreaterEqual(len(operations), 3)
        
        # Step 2: Classify operations
        classification = self.detector.classify_operations_for_template_selection(operations)
        self.assertTrue(classification['has_database_operations'])
        self.assertGreater(len(classification['operation_types']), 1)
        
        # Step 3: Prepare template context
        context = self.detector.prepare_template_context(operations, classification)
        self.assertTrue(context['has_database_operations'])
        self.assertGreater(len(context['database_operations']), 0)
        
        # Step 4: Test template selection
        template = self.code_generator._select_template_by_context(context)
        self.assertIn('service', template.lower())  # Should select a service template


if __name__ == '__main__':
    unittest.main()
