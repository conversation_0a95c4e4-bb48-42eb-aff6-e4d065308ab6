# Compiled class files
*.class

# Log files
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Mac
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Spring Boot
spring-boot-*.log

# H2 Database
*.db
*.trace.db
*.lock.db

# Application specific
{{ app_name | snakeCase }}.log
{{ app_name | snakeCase }}-*.log

# COBOL conversion specific
/input/
/output/
/archive/
/error/
/temp/
/generated/

# Test coverage
/target/site/jacoco/
*.exec

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE specific
*.swp
*.swo
*~

# Backup files
*.bak
*.backup
*.old

# Custom directories
{% if custom_ignore_patterns %}
{% for pattern in custom_ignore_patterns %}
{{ pattern }}
{% endfor %}
{% endif %}
