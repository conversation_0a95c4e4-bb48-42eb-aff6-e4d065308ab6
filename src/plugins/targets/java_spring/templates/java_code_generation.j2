Generate Java Spring Boot code for the following COBOL function:

**Business Name:** {{ chunk_docs.business_name | default('Unknown') }}
**Business Description:** {{ chunk_docs.business_description | default('No description') }}

**Functional Specification:**
{{ chunk_docs.functional_spec | default('No specification available') }}

**Input Parameters:**
{% if chunk_docs.input_parameters %}
{% for param in chunk_docs.input_parameters %}
- {{ param.name | default('') }}: {{ param.description | default('') }}
{% endfor %}
{% else %}
None
{% endif %}

**Output Parameters:**
{% if chunk_docs.output_parameters %}
{% for param in chunk_docs.output_parameters %}
- {{ param.name | default('') }}: {{ param.description | default('') }}
{% endfor %}
{% else %}
None
{% endif %}

{% if existing_mappings %}
**Existing Variable Mappings:**
{{ existing_mappings | tojson(indent=2) }}
{% endif %}

{% if java_structures %}
**Available Java Data Structures:**
{{ java_structures | tojson(indent=2) }}
{% endif %}

**Requirements:**
1. Generate a Spring Boot service class
2. Use appropriate Spring annotations (@Service, @Component, etc.)
3. Follow Java naming conventions
4. Include proper error handling
5. Add JavaDoc comments
6. Return both the Java code and COBOL-to-Java variable mappings

**Response Format:**
```java
// Your Java code here
```

```json
{
  "COBOL-VARIABLE": "javaVariable",
  "ANOTHER-COBOL-VAR": "anotherJavaVar"
}
```
