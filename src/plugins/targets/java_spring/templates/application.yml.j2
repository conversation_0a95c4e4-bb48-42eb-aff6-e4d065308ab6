spring:
  application:
    name: {{ app_name }}
  
  profiles:
    active: {{ active_profile | default("dev") }}
  
  {% if database_type == "h2" %}
  datasource:
    url: jdbc:h2:mem:{{ app_name | snakeCase }}db
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  h2:
    console:
      enabled: true
      path: /h2-console
  {% elif database_type == "mysql" %}
  datasource:
    url: ***************************/{{ app_name | snakeCase }}db
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
  {% elif database_type == "postgresql" %}
  datasource:
    url: ********************************/{{ app_name | snakeCase }}db
    driver-class-name: org.postgresql.Driver
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:password}
  {% endif %}
  
  jpa:
    hibernate:
      ddl-auto: {{ jpa_ddl_auto | default("update") }}
    show-sql: {{ show_sql | default("false") }}
    properties:
      hibernate:
        dialect: {{ hibernate_dialect | default("org.hibernate.dialect.H2Dialect") }}
        format_sql: true
  
  servlet:
    multipart:
      max-file-size: {{ max_file_size | default("10MB") }}
      max-request-size: {{ max_request_size | default("10MB") }}

server:
  port: {{ server_port }}
  servlet:
    context-path: {{ context_path | default("") }}

logging:
  level:
    root: {{ log_level | default("INFO") }}
    {{ package_name }}: {{ app_log_level | default("DEBUG") }}
    org.springframework.web: {{ web_log_level | default("INFO") }}
    org.hibernate.SQL: {{ sql_log_level | default("DEBUG") }}
    org.hibernate.type.descriptor.sql.BasicBinder: {{ sql_param_log_level | default("TRACE") }}
  
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  
  {% if log_file %}
  file:
    name: {{ log_file }}
  {% endif %}

management:
  endpoints:
    web:
      exposure:
        include: {{ actuator_endpoints | default("health,info,metrics") }}
  endpoint:
    health:
      show-details: {{ health_details | default("when-authorized") }}

# Custom Application Configuration
{% if custom_config %}
{{ custom_config | tojson }}
{% endif %}

# COBOL Conversion Configuration
cobol:
  conversion:
    batch-size: {{ batch_size | default("1000") }}
    max-retries: {{ max_retries | default("3") }}
    timeout-seconds: {{ timeout_seconds | default("300") }}
    
    # File processing settings
    file-processing:
      input-directory: {{ input_directory | default("./input") }}
      output-directory: {{ output_directory | default("./output") }}
      archive-directory: {{ archive_directory | default("./archive") }}
      error-directory: {{ error_directory | default("./error") }}
    
    # Business logic settings
    business-rules:
      validation-enabled: {{ validation_enabled | default("true") }}
      strict-mode: {{ strict_mode | default("false") }}
      
    # Performance settings
    performance:
      thread-pool-size: {{ thread_pool_size | default("10") }}
      queue-capacity: {{ queue_capacity | default("100") }}

---
# Development Profile
spring:
  profiles: dev
  
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  h2:
    console:
      enabled: true

logging:
  level:
    {{ package_name }}: DEBUG
    org.hibernate.SQL: DEBUG

---
# Production Profile  
spring:
  profiles: prod
  
  jpa:
    show-sql: false
    properties:
      hibernate:
        format_sql: false

logging:
  level:
    root: WARN
    {{ package_name }}: INFO
