# {{ project_name }}

{{ description }}

## Overview

This Spring Boot application was generated from legacy COBOL code using automated conversion tools. The application maintains the original business logic while providing a modern Java-based implementation.

## Getting Started

### Prerequisites
- Java {{ java_version }} or higher
- Maven {{ maven_version }} or higher
- Spring Boot {{ spring_boot_version }}

### Installation

1. Clone the repository
2. Navigate to the project directory
3. Build the application:
   ```bash
   mvn clean compile
   ```

### Running the Application

#### Development Mode
```bash
mvn spring-boot:run
```

#### Production Mode
```bash
mvn clean package
java -jar target/{{ project_name }}-1.0.0.jar
```

### Configuration

The application can be configured through `application.properties` or environment variables:

- `server.port` - Server port (default: 8080)
- `logging.level.{{ package_name }}` - Logging level for application packages
- `spring.profiles.active` - Active Spring profile

## Project Structure

```
src/
├── main/
│   ├── java/
│   │   └── {{ package_name.replace('.', '/') }}/
│   │       ├── Application.java          # Main application class
│   │       ├── service/                  # Business logic services
│   │       ├── controller/               # REST controllers
│   │       └── config/                   # Configuration classes
│   └── resources/
│       ├── application.properties        # Application configuration
│       └── logback-spring.xml           # Logging configuration
└── test/
    └── java/                            # Unit tests
```

## Business Logic Conversion

{% if analysis.knowledge_base and analysis.knowledge_base.business_logic_summaries %}
The following COBOL programs were converted:

{% for program_id, chunks in analysis.knowledge_base.business_logic_summaries.items() %}
### {{ program_id }}
{% for chunk_name, logic in chunks.items() %}
- **{{ chunk_name }}**: {{ logic[:100] }}...
{% endfor %}

{% endfor %}
{% endif %}

## API Documentation

See [API.md](API.md) for detailed API documentation.

## Development

See [DEVELOPMENT.md](DEVELOPMENT.md) for development guidelines.

## Deployment

See [DEPLOYMENT.md](DEPLOYMENT.md) for deployment instructions.

## Support

For issues and questions, please check the logs and refer to the development documentation.

## Generated Information

- **Generated on**: {{ "now" | strftime("%Y-%m-%d %H:%M:%S") }}
- **Source**: Legacy COBOL conversion
- **Framework**: Spring Boot {{ spring_boot_version }}
- **Java Version**: {{ java_version }}
