# API Documentation

## Base URL
{{ base_url }}

## Overview

This API provides access to the business logic converted from legacy COBOL programs. Each endpoint corresponds to a specific business function from the original system.

## Authentication

Currently, no authentication is required for these endpoints. In production, consider implementing appropriate security measures.

## Common Response Format

All endpoints return JSON responses with the following structure:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "success": false,
  "message": "Invalid request parameters",
  "errors": ["Specific error details"],
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error",
  "error": "Error details",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Health Check Endpoints

### GET /actuator/health
Returns the health status of the application.

**Response:**
```json
{
  "status": "UP",
  "components": {
    "diskSpace": {
      "status": "UP"
    }
  }
}
```

### GET /actuator/info
Returns application information.

**Response:**
```json
{
  "app": {
    "name": "{{ project_name }}",
    "version": "1.0.0"
  }
}
```

## Business Logic Endpoints

{% if endpoints %}
{% for endpoint in endpoints %}
### {{ endpoint.method }} {{ endpoint.path }}

{{ endpoint.description }}

**Business Logic:**
{{ endpoint.business_logic }}

**Request:**
```bash
curl -X {{ endpoint.method }} {{ base_url }}{{ endpoint.path }} \
  -H "Content-Type: application/json" \
  -d '{
    "input": "your_input_data"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Processing completed",
  "data": {
    "result": "processed_output"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

---

{% endfor %}
{% else %}
### GET /
Returns application status.

**Response:**
```
Application is running
```
{% endif %}

## Rate Limiting

Currently, no rate limiting is implemented. Consider adding rate limiting for production use.

## Monitoring

The application includes Spring Boot Actuator endpoints for monitoring:

- `/actuator/health` - Health check
- `/actuator/metrics` - Application metrics
- `/actuator/info` - Application information

## Testing

Use the following tools for API testing:

- **Postman**: Import the API collection (if available)
- **curl**: Use the examples provided above
- **Swagger UI**: Available at `/swagger-ui.html` (if enabled)

## Support

For API issues:
1. Check the application logs
2. Verify request format and parameters
3. Ensure all required fields are provided
4. Check the health endpoints for system status
