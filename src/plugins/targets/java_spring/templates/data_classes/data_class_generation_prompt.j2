Generate a Java {% if is_entity %}JPA Entity{% else %}Data{% endif %} class from the following COBOL data structure:

**Structure Name:** {{ structure_name }}
**Program ID:** {{ program_id }}
**Chunk Type:** {{ chunk_type }}
{% if parent_structure %}**Parent Structure:** {{ parent_structure }}{% endif %}
{% if level %}**COBOL Level:** {{ level }}{% endif %}
{% if chunk_number %}**Part Number:** {{ chunk_number }}{% endif %}

**Requirements:**
1. {% if is_entity %}Use JPA Entity annotations (@Entity, @Table, @Id, @Column){% else %}Use Data class annotations (@Data, @NoArgsConstructor, @AllArgsConstructor, @Builder){% endif %}
2. Use Lombok for getters/setters and constructors
3. Use Spring Data JPA annotations{% if is_entity %} for database mapping{% endif %}
4. Convert COBOL data types to appropriate Java types:
   - PIC X(n) -> String
   - PIC 9(n) -> Integer (if n <= 9), <PERSON> (if n <= 18), BigDecimal (if n > 18)
   - PIC S9(n) -> Integer/Long/BigDecimal (signed)
   - PIC 9(n)V9(m) -> BigDecimal
   - COMP-3 -> BigDecimal
5. Convert 88-level items to Java enums
6. Include comprehensive Javadoc for each field with original COBOL field name
7. Use business names for Java field names when available
8. {% if needs_string_constructor %}Add a constructor that parses a single input string into class fields for file/database reading{% else %}Standard constructors only{% endif %}
9. Use proper Java naming conventions (camelCase)
10. Include all necessary imports

{% if variables_mapping %}
**Business Name Mappings:**
{% for cobol_name, business_name in variables_mapping.items() %}
- {{ cobol_name }} -> {{ business_name }}
{% endfor %}
{% endif %}

**COBOL Variables ({{ variables|length }} fields):**
{% for var in variables %}
- **{{ var.name }}** (Level {{ var.level }})
  - Type: {{ var.data_type }}
  - Length: {{ var.length }}{% if var.decimals %}, Decimals: {{ var.decimals }}{% endif %}
  - {% if var.business_name %}Business Name: {{ var.business_name }}{% else %}No business name{% endif %}
  - {% if var.description %}Description: {{ var.description }}{% else %}No description{% endif %}
  - {% if var.possible_values %}Possible Values: {{ var.possible_values }}{% endif %}
  - {% if var.default_value %}Default: {{ var.default_value }}{% endif %}
  - {% if var.is_signed %}Signed{% else %}Unsigned{% endif %}
  - {% if var.occurs %}Occurs: {{ var.occurs }}{% endif %}
  - {% if var.redefines %}Redefines: {{ var.redefines }}{% endif %}
{% endfor %}

**Additional Instructions:**
- Generate COMPLETE WORKING CODE with no placeholders or TODO comments
- Use appropriate package declaration (com.generated.cobol.entities or com.generated.cobol.data)
- Include proper error handling in string parsing constructor if needed
- Document the splitting strategy if this is part of a larger structure
- Use composition pattern to link to other parts if this is a split structure
- Ensure all field mappings are clear and documented

**Expected Output:**
Provide the complete Java class code wrapped in ```java ... ``` blocks.
