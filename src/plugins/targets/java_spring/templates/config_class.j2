package {{ package_name }};

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Profile;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * Configuration Class: {{ class_name }}
 * 
 * Spring configuration for application settings and beans.
 * 
 * Generated on: {{ generation_date }}
 */
@Configuration
@EnableConfigurationProperties
public class {{ class_name }} {

    /**
     * Configuration properties for the application
     */
    @Bean
    @ConfigurationProperties(prefix = "app")
    public ApplicationProperties applicationProperties() {
        return new ApplicationProperties();
    }

    /**
     * Development profile specific configuration
     */
    @Bean
    @Profile("dev")
    public String developmentConfig() {
        return "Development configuration active";
    }

    /**
     * Production profile specific configuration
     */
    @Bean
    @Profile("prod")
    public String productionConfig() {
        return "Production configuration active";
    }

    /**
     * Application properties class
     */
    public static class ApplicationProperties {
        {% if config_properties %}
        {% for key, value in config_properties.items() %}
        private {{ value.type | default("String") }} {{ key | camelCase }};
        {% endfor %}

        // Getters and setters
        {% for key, value in config_properties.items() %}
        public {{ value.type | default("String") }} get{{ key | pascalCase }}() {
            return {{ key | camelCase }};
        }

        public void set{{ key | pascalCase }}({{ value.type | default("String") }} {{ key | camelCase }}) {
            this.{{ key | camelCase }} = {{ key | camelCase }};
        }
        {% endfor %}
        {% else %}
        // No configuration properties defined
        {% endif %}
    }
}
