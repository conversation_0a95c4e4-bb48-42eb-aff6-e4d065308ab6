# Application Configuration
spring.application.name={{ app_name }}
server.port={{ server_port }}

# Database Configuration
{% if database_type == "h2" %}
spring.datasource.url=jdbc:h2:mem:{{ app_name | snakeCase }}db
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
{% elif database_type == "mysql" %}
spring.datasource.url=***************************/{{ app_name | snakeCase }}db
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:password}
{% elif database_type == "postgresql" %}
spring.datasource.url=********************************/{{ app_name | snakeCase }}db
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:password}
{% endif %}

# JPA Configuration
spring.jpa.hibernate.ddl-auto={{ jpa_ddl_auto | default("update") }}
spring.jpa.show-sql={{ show_sql | default("false") }}
spring.jpa.properties.hibernate.dialect={{ hibernate_dialect | default("org.hibernate.dialect.H2Dialect") }}
spring.jpa.properties.hibernate.format_sql=true

# Logging Configuration
logging.level.root={{ log_level | default("INFO") }}
logging.level.{{ package_name }}={{ app_log_level | default("DEBUG") }}
logging.level.org.springframework.web={{ web_log_level | default("INFO") }}
logging.level.org.hibernate.SQL={{ sql_log_level | default("DEBUG") }}
logging.level.org.hibernate.type.descriptor.sql.BasicBinder={{ sql_param_log_level | default("TRACE") }}

# File upload configuration
spring.servlet.multipart.max-file-size={{ max_file_size | default("10MB") }}
spring.servlet.multipart.max-request-size={{ max_request_size | default("10MB") }}

# Actuator Configuration
management.endpoints.web.exposure.include={{ actuator_endpoints | default("health,info,metrics") }}
management.endpoint.health.show-details={{ health_details | default("when-authorized") }}

# Custom Application Properties
{% if custom_properties %}
{% for key, value in custom_properties.items() %}
{{ key }}={{ value }}
{% endfor %}
{% endif %}

# COBOL Conversion Specific Properties
cobol.conversion.batch-size={{ batch_size | default("1000") }}
cobol.conversion.max-retries={{ max_retries | default("3") }}
cobol.conversion.timeout-seconds={{ timeout_seconds | default("300") }}

# File Processing Configuration
file.processing.input-directory={{ input_directory | default("./input") }}
file.processing.output-directory={{ output_directory | default("./output") }}
file.processing.archive-directory={{ archive_directory | default("./archive") }}
file.processing.error-directory={{ error_directory | default("./error") }}
