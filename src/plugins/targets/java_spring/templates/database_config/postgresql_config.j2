# PostgreSQL Database Configuration for Production
# Converts COBOL mainframe database connections to modern PostgreSQL

spring:
  application:
    name: {{ app_name }}
  
  profiles:
    active: {{ active_profile | default("prod") }}
  
  # PostgreSQL Database Configuration
  datasource:
    url: jdbc:postgresql://{{ db_host | default("localhost") }}:{{ db_port | default("5432") }}/{{ db_name }}
    driver-class-name: org.postgresql.Driver
    username: ${DB_USERNAME:{{ db_username | default("postgres") }}}
    password: ${DB_PASSWORD:{{ db_password | default("password") }}}
    
    # Connection Pool Configuration (HikariCP)
    hikari:
      connection-timeout: {{ connection_timeout | default("30000") }}
      idle-timeout: {{ idle_timeout | default("600000") }}
      max-lifetime: {{ max_lifetime | default("1800000") }}
      maximum-pool-size: {{ max_pool_size | default("20") }}
      minimum-idle: {{ min_idle | default("5") }}
      pool-name: {{ app_name }}ConnectionPool
      
      # Connection validation
      connection-test-query: SELECT 1
      validation-timeout: {{ validation_timeout | default("5000") }}
      
      # Performance tuning
      leak-detection-threshold: {{ leak_detection_threshold | default("60000") }}
      
      # PostgreSQL specific properties
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
  
  # JPA Configuration
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQL95Dialect
    hibernate:
      ddl-auto: {{ jpa_ddl_auto | default("validate") }}
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
    
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQL95Dialect
        format_sql: {{ format_sql | default("false") }}
        use_sql_comments: {{ use_sql_comments | default("false") }}
        
        # Performance optimization
        jdbc:
          batch_size: {{ batch_size | default("25") }}
          fetch_size: {{ fetch_size | default("50") }}
          time_zone: {{ time_zone | default("UTC") }}
        
        # Connection handling
        connection:
          provider_disables_autocommit: true
          autocommit: false
        
        # Query optimization
        query:
          plan_cache_max_size: {{ plan_cache_max_size | default("2048") }}
          plan_parameter_metadata_max_size: {{ plan_parameter_metadata_max_size | default("128") }}
        
        # Statistics and monitoring
        generate_statistics: {{ generate_statistics | default("false") }}
        session:
          events:
            log:
              LOG_QUERIES_SLOWER_THAN_MS: {{ slow_query_threshold | default("1000") }}
    
    show-sql: {{ show_sql | default("false") }}
    open-in-view: false
  
  # Transaction Configuration
  transaction:
    default-timeout: {{ transaction_timeout | default("30") }}
    rollback-on-commit-failure: true

# Server Configuration
server:
  port: {{ server_port | default("8080") }}
  servlet:
    context-path: {{ context_path | default("") }}
  
  # Connection tuning
  tomcat:
    max-connections: {{ max_connections | default("8192") }}
    accept-count: {{ accept_count | default("100") }}
    threads:
      max: {{ max_threads | default("200") }}
      min-spare: {{ min_spare_threads | default("10") }}

# Logging Configuration
logging:
  level:
    root: {{ log_level | default("INFO") }}
    {{ package_name }}: {{ app_log_level | default("INFO") }}
    org.springframework.web: {{ web_log_level | default("INFO") }}
    org.hibernate.SQL: {{ sql_log_level | default("WARN") }}
    org.hibernate.type.descriptor.sql.BasicBinder: {{ sql_param_log_level | default("WARN") }}
    org.springframework.transaction: {{ transaction_log_level | default("INFO") }}
    org.springframework.orm.jpa: {{ jpa_log_level | default("INFO") }}
    com.zaxxer.hikari: {{ hikari_log_level | default("INFO") }}
  
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  
  file:
    name: {{ log_file_name | default("application.log") }}
    max-size: {{ log_max_size | default("10MB") }}
    max-history: {{ log_max_history | default("30") }}

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: {{ actuator_endpoints | default("health,info,metrics,prometheus") }}
  
  endpoint:
    health:
      show-details: {{ health_details | default("when-authorized") }}
      show-components: always
    
    metrics:
      enabled: true
  
  metrics:
    export:
      prometheus:
        enabled: {{ prometheus_enabled | default("true") }}
    
    distribution:
      percentiles-histogram:
        http.server.requests: true
        spring.data.repository.invocations: true
      
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
        spring.data.repository.invocations: 0.5, 0.95, 0.99

# COBOL Conversion Specific Configuration
cobol:
  conversion:
    # Database operation settings
    database:
      batch-size: {{ db_batch_size | default("1000") }}
      max-retries: {{ db_max_retries | default("3") }}
      retry-delay-ms: {{ db_retry_delay | default("1000") }}
      timeout-seconds: {{ db_timeout_seconds | default("300") }}
      
      # Connection pool for batch operations
      batch-pool-size: {{ batch_pool_size | default("5") }}
      
      # Lock timeout for pessimistic locking (COBOL GHU/GHN)
      lock-timeout-seconds: {{ lock_timeout_seconds | default("30") }}
    
    # Error handling settings
    error-handling:
      max-retry-attempts: {{ error_max_retries | default("3") }}
      retry-backoff-ms: {{ error_retry_backoff | default("2000") }}
      circuit-breaker-enabled: {{ circuit_breaker_enabled | default("true") }}
      circuit-breaker-failure-threshold: {{ circuit_breaker_threshold | default("5") }}
      circuit-breaker-timeout-ms: {{ circuit_breaker_timeout | default("60000") }}
    
    # Audit trail settings
    audit:
      enabled: {{ audit_enabled | default("true") }}
      include-sql-parameters: {{ audit_include_sql_params | default("false") }}
      log-slow-operations-ms: {{ audit_slow_operations_threshold | default("5000") }}
    
    # Performance monitoring
    performance:
      monitoring-enabled: {{ performance_monitoring_enabled | default("true") }}
      slow-query-threshold-ms: {{ performance_slow_query_threshold | default("1000") }}
      metrics-collection-interval-seconds: {{ performance_metrics_interval | default("60") }}

# Security Configuration (if applicable)
{% if security_enabled | default(false) %}
spring:
  security:
    user:
      name: ${SECURITY_USERNAME:admin}
      password: ${SECURITY_PASSWORD:admin}
      roles: ADMIN
{% endif %}

# Custom Application Properties
{% if custom_properties %}
{% for key, value in custom_properties.items() %}
{{ key }}: {{ value }}
{% endfor %}
{% endif %}

---
# Development Profile Override
spring:
  config:
    activate:
      on-profile: dev
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
  
  datasource:
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2

logging:
  level:
    {{ package_name }}: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

---
# Staging Profile Override
spring:
  config:
    activate:
      on-profile: staging
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  
  datasource:
    hikari:
      maximum-pool-size: 15
      minimum-idle: 3

logging:
  level:
    root: INFO
    {{ package_name }}: INFO

---
# Production Profile Override
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        use_sql_comments: false
  
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      leak-detection-threshold: 60000

logging:
  level:
    root: WARN
    {{ package_name }}: INFO
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
