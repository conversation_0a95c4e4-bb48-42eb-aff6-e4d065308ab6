# H2 In-Memory Database Configuration for Testing
# Provides fast, isolated testing environment for COBOL database operation conversions

spring:
  application:
    name: {{ app_name }}-test
  
  profiles:
    active: test
  
  # H2 In-Memory Database Configuration
  datasource:
    url: jdbc:h2:mem:{{ app_name | snakeCase }}testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
    # Connection Pool Configuration (minimal for testing)
    hikari:
      connection-timeout: 5000
      idle-timeout: 300000
      max-lifetime: 600000
      maximum-pool-size: 10
      minimum-idle: 1
      pool-name: {{ app_name }}TestConnectionPool
      
      # Fast validation for testing
      connection-test-query: SELECT 1
      validation-timeout: 1000
      
      # Disable leak detection for faster tests
      leak-detection-threshold: 0
  
  # H2 Console Configuration (for debugging tests)
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: false
        trace: false
  
  # JPA Configuration for Testing
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
    
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: true
        
        # Optimized for testing speed
        jdbc:
          batch_size: 50
          fetch_size: 100
          time_zone: UTC
        
        # Fast connection handling
        connection:
          provider_disables_autocommit: true
          autocommit: false
        
        # Disable query cache for consistent test results
        query:
          plan_cache_max_size: 0
        
        # Enable statistics for test verification
        generate_statistics: true
        
        # Fast schema operations
        hbm2ddl:
          auto: create-drop
          import_files_sql_extractor: org.hibernate.tool.hbm2ddl.MultipleLinesSqlCommandExtractor
    
    show-sql: {{ show_sql_in_tests | default("true") }}
    open-in-view: false
    defer-datasource-initialization: true
  
  # SQL Initialization for Test Data
  sql:
    init:
      mode: always
      schema-locations: classpath:schema-test.sql
      data-locations: classpath:data-test.sql
      continue-on-error: false
      separator: ;
  
  # Transaction Configuration for Testing
  transaction:
    default-timeout: 10
    rollback-on-commit-failure: true

# Test Server Configuration
server:
  port: 0  # Random port for parallel test execution
  servlet:
    context-path: 

# Logging Configuration for Tests
logging:
  level:
    root: {{ test_log_level | default("INFO") }}
    {{ package_name }}: {{ test_app_log_level | default("DEBUG") }}
    org.springframework.web: WARN
    org.springframework.test: {{ test_framework_log_level | default("INFO") }}
    org.springframework.transaction: {{ test_transaction_log_level | default("DEBUG") }}
    org.springframework.orm.jpa: {{ test_jpa_log_level | default("DEBUG") }}
    org.springframework.boot.test: INFO
    
    # Database logging for test verification
    org.hibernate.SQL: {{ test_sql_log_level | default("DEBUG") }}
    org.hibernate.type.descriptor.sql.BasicBinder: {{ test_sql_param_log_level | default("TRACE") }}
    org.hibernate.stat: {{ test_hibernate_stats_log_level | default("DEBUG") }}
    
    # H2 specific logging
    org.h2: {{ test_h2_log_level | default("WARN") }}
    
    # Connection pool logging
    com.zaxxer.hikari: {{ test_hikari_log_level | default("WARN") }}
  
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  
  # Disable file logging in tests
  file:
    name: 

# Test-specific Management Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  
  endpoint:
    health:
      show-details: always
      show-components: always
  
  metrics:
    export:
      # Disable external metrics export in tests
      prometheus:
        enabled: false
    
    # Enable detailed metrics for test verification
    distribution:
      percentiles-histogram:
        http.server.requests: true
        spring.data.repository.invocations: true

# COBOL Conversion Test Configuration
cobol:
  conversion:
    # Database operation settings for testing
    database:
      batch-size: 100  # Smaller batches for faster tests
      max-retries: 1   # Fewer retries for faster test execution
      retry-delay-ms: 100
      timeout-seconds: 10
      
      # Minimal connection pool for tests
      batch-pool-size: 2
      
      # Short lock timeout for tests
      lock-timeout-seconds: 5
    
    # Error handling settings for testing
    error-handling:
      max-retry-attempts: 1
      retry-backoff-ms: 100
      circuit-breaker-enabled: false  # Disable for predictable test behavior
    
    # Audit trail settings for testing
    audit:
      enabled: true
      include-sql-parameters: true  # Full logging for test verification
      log-slow-operations-ms: 100   # Lower threshold for test monitoring
    
    # Performance monitoring for testing
    performance:
      monitoring-enabled: true
      slow-query-threshold-ms: 100
      metrics-collection-interval-seconds: 5

# Test Data Configuration
test:
  data:
    # Test entity creation settings
    entities:
      default-batch-size: 10
      max-test-entities: 1000
    
    # Test scenario configuration
    scenarios:
      concurrency-thread-count: 5
      performance-test-iterations: 100
      load-test-duration-seconds: 30
    
    # Test database settings
    database:
      auto-cleanup: true
      preserve-data-between-tests: false
      enable-test-transactions: true

# JUnit 5 Configuration
junit:
  jupiter:
    execution:
      parallel:
        enabled: {{ parallel_test_execution | default("true") }}
        mode:
          default: concurrent
          classes: concurrent
          methods: concurrent
    
    testinstance:
      lifecycle:
        default: per_class

# Test Profiles for Different Scenarios

---
# Integration Test Profile
spring:
  config:
    activate:
      on-profile: integration-test
  
  jpa:
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        use_sql_comments: false

logging:
  level:
    {{ package_name }}: INFO
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN

test:
  data:
    scenarios:
      performance-test-iterations: 1000
      load-test-duration-seconds: 60

---
# Performance Test Profile
spring:
  config:
    activate:
      on-profile: performance-test
  
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
  
  jpa:
    show-sql: false
    properties:
      hibernate:
        jdbc:
          batch_size: 100
          fetch_size: 200

logging:
  level:
    root: WARN
    {{ package_name }}: INFO

test:
  data:
    entities:
      default-batch-size: 100
      max-test-entities: 10000
    scenarios:
      concurrency-thread-count: 10
      performance-test-iterations: 5000
      load-test-duration-seconds: 120

---
# Debug Test Profile
spring:
  config:
    activate:
      on-profile: debug-test
  
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        generate_statistics: true

logging:
  level:
    root: DEBUG
    {{ package_name }}: TRACE
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.transaction: TRACE
    org.springframework.orm.jpa: TRACE

test:
  data:
    scenarios:
      concurrency-thread-count: 1  # Single-threaded for debugging
      performance-test-iterations: 10

# Test Database Schema and Data Files Configuration
---
# Schema and Data File Locations
spring:
  sql:
    init:
      # Test schema file (creates tables, indexes, constraints)
      schema-locations: 
        - classpath:db/test/schema.sql
        - classpath:db/test/indexes.sql
        - classpath:db/test/constraints.sql
      
      # Test data files (inserts reference data)
      data-locations:
        - classpath:db/test/reference-data.sql
        - classpath:db/test/test-data.sql
      
      # Encoding and execution settings
      encoding: UTF-8
      continue-on-error: false
      separator: ;

# Custom Test Properties
{% if test_custom_properties %}
{% for key, value in test_custom_properties.items() %}
{{ key }}: {{ value }}
{% endfor %}
{% endif %}
