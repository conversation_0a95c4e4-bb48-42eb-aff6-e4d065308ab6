package {{ package_name }};

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

/**
 * Base Service Class
 * 
 * Provides common functionality for all service classes.
 * 
 * Generated on: {{ generation_date }}
 */
@Transactional
public abstract class {{ class_name }} {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * Log the start of a business operation
     * 
     * @param operationName Name of the operation
     */
    protected void logOperationStart(String operationName) {
        logger.info("Starting operation: {}", operationName);
    }

    /**
     * Log the successful completion of a business operation
     * 
     * @param operationName Name of the operation
     */
    protected void logOperationSuccess(String operationName) {
        logger.info("Operation completed successfully: {}", operationName);
    }

    /**
     * Log an error in a business operation
     * 
     * @param operationName Name of the operation
     * @param error The error that occurred
     */
    protected void logOperationError(String operationName, Exception error) {
        logger.error("Operation failed: {} - {}", operationName, error.getMessage(), error);
    }

    /**
     * Validate that a required parameter is not null
     * 
     * @param parameter The parameter to validate
     * @param parameterName Name of the parameter for error messages
     * @throws IllegalArgumentException if parameter is null
     */
    protected void validateRequired(Object parameter, String parameterName) {
        if (parameter == null) {
            throw new IllegalArgumentException(parameterName + " cannot be null");
        }
    }

    /**
     * Validate that a string parameter is not null or empty
     * 
     * @param parameter The string parameter to validate
     * @param parameterName Name of the parameter for error messages
     * @throws IllegalArgumentException if parameter is null or empty
     */
    protected void validateRequiredString(String parameter, String parameterName) {
        if (parameter == null || parameter.trim().isEmpty()) {
            throw new IllegalArgumentException(parameterName + " cannot be null or empty");
        }
    }
}
