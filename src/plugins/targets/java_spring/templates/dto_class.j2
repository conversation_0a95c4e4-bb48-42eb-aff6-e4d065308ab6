package {{ package_name }};

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
{% if fields %}
{% for field in fields %}
{% if field.type == "Date" or field.type == "LocalDate" %}
import java.time.LocalDate;
{% elif field.type == "DateTime" or field.type == "LocalDateTime" %}
import java.time.LocalDateTime;
{% elif field.type == "BigDecimal" %}
import java.math.BigDecimal;
{% endif %}
{% endfor %}
{% endif %}

/**
 * Data Transfer Object: {{ class_name }}
 * 
 * Generated on: {{ generation_date }}
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class {{ class_name }} implements Serializable {

    private static final long serialVersionUID = 1L;

{% if fields %}
{% for field in fields %}
    /**
     * {{ field.description | default("Field description not provided") }}
     */
    @JsonProperty("{{ field.name | snakeCase }}")
    private {{ field.type | default("String") }} {{ field.name | camelCase }};

{% endfor %}
{% else %}
    // No fields defined - add fields as needed
{% endif %}
}
