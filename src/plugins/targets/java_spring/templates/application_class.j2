package {{ package_name }};

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main Spring Boot Application Class
 * 
 * This application was generated from legacy COBOL code conversion.
 * It maintains the original business logic while providing a modern Java-based implementation.
 * 
 * Generated on: {{ generation_date }}
 */
@SpringBootApplication
@ComponentScan(basePackages = "{{ package_name }}")
@EnableJpaRepositories(basePackages = "{{ package_name }}.repository")
@EnableTransactionManagement
public class {{ class_name }} {

    /**
     * Main method to start the Spring Boot application.
     * 
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        SpringApplication.run({{ class_name }}.class, args);
    }
}
