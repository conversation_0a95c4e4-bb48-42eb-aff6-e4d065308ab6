**DATABASE EXCEPTION HANDLING CONVERSION GUIDE**

Convert COBOL database error handling (DIBSTAT, SQLCODE) to modern Spring Boot exception handling:

**COBOL ERROR CONDITIONS DETECTED:**
{% if error_conditions %}
{% for condition in error_conditions %}
- **{{ condition.type }}**: {{ condition.description }}
  - COBOL Check: `{{ condition.cobol_check }}`
  - Error Code: {{ condition.error_code }}
  - Recovery Action: {{ condition.recovery_action }}
{% endfor %}
{% endif %}

**EXCEPTION HIERARCHY:**

**1. Custom Exception Classes:**
```java
package {{ package_name }}.exception;

/**
 * Base exception for all database operations.
 * Converts COBOL: General database error handling
 */
public class DatabaseOperationException extends RuntimeException {
    private final String operationName;
    private final String originalCobolError;
    
    public DatabaseOperationException(String message) {
        super(message);
        this.operationName = null;
        this.originalCobolError = null;
    }
    
    public DatabaseOperationException(String message, Throwable cause) {
        super(message, cause);
        this.operationName = null;
        this.originalCobolError = null;
    }
    
    public DatabaseOperationException(String message, String operationName, String originalCobolError) {
        super(message);
        this.operationName = operationName;
        this.originalCobolError = originalCobolError;
    }
    
    public DatabaseOperationException(String message, Throwable cause, String operationName, String originalCobolError) {
        super(message, cause);
        this.operationName = operationName;
        this.originalCobolError = originalCobolError;
    }
    
    public String getOperationName() { return operationName; }
    public String getOriginalCobolError() { return originalCobolError; }
}

/**
 * Exception for database constraint violations.
 * Converts COBOL: SQLCODE -803 (duplicate key), -532 (foreign key), etc.
 */
public class DatabaseConstraintException extends DatabaseOperationException {
    private final String constraintName;
    private final String constraintType;
    
    public DatabaseConstraintException(String message, Throwable cause) {
        super(message, cause);
        this.constraintName = extractConstraintName(cause);
        this.constraintType = extractConstraintType(cause);
    }
    
    public DatabaseConstraintException(String message, Throwable cause, String constraintName, String constraintType) {
        super(message, cause);
        this.constraintName = constraintName;
        this.constraintType = constraintType;
    }
    
    private String extractConstraintName(Throwable cause) {
        // Extract constraint name from exception message
        // Implementation depends on database vendor
        return "UNKNOWN_CONSTRAINT";
    }
    
    private String extractConstraintType(Throwable cause) {
        // Determine constraint type (PRIMARY_KEY, FOREIGN_KEY, UNIQUE, CHECK)
        return "UNKNOWN_TYPE";
    }
    
    public String getConstraintName() { return constraintName; }
    public String getConstraintType() { return constraintType; }
}

/**
 * Exception for database concurrency issues.
 * Converts COBOL: Optimistic locking failures, deadlocks
 */
public class DatabaseConcurrencyException extends DatabaseOperationException {
    private final String lockType;
    private final Object entityId;
    
    public DatabaseConcurrencyException(String message, Throwable cause) {
        super(message, cause);
        this.lockType = "OPTIMISTIC";
        this.entityId = null;
    }
    
    public DatabaseConcurrencyException(String message, Throwable cause, String lockType, Object entityId) {
        super(message, cause);
        this.lockType = lockType;
        this.entityId = entityId;
    }
    
    public String getLockType() { return lockType; }
    public Object getEntityId() { return entityId; }
}

/**
 * Exception for database lock acquisition failures.
 * Converts COBOL: DIBSTAT indicating lock conflicts
 */
public class DatabaseLockException extends DatabaseOperationException {
    private final String lockMode;
    private final long timeoutMillis;
    
    public DatabaseLockException(String message, Throwable cause) {
        super(message, cause);
        this.lockMode = "PESSIMISTIC_WRITE";
        this.timeoutMillis = 0;
    }
    
    public DatabaseLockException(String message, Throwable cause, String lockMode, long timeoutMillis) {
        super(message, cause);
        this.lockMode = lockMode;
        this.timeoutMillis = timeoutMillis;
    }
    
    public String getLockMode() { return lockMode; }
    public long getTimeoutMillis() { return timeoutMillis; }
}

/**
 * Exception for business validation failures.
 * Converts COBOL: Business rule validation errors
 */
public class BusinessValidationException extends RuntimeException {
    private final String validationRule;
    private final Object invalidValue;
    
    public BusinessValidationException(String message) {
        super(message);
        this.validationRule = null;
        this.invalidValue = null;
    }
    
    public BusinessValidationException(String message, String validationRule, Object invalidValue) {
        super(message);
        this.validationRule = validationRule;
        this.invalidValue = invalidValue;
    }
    
    public String getValidationRule() { return validationRule; }
    public Object getInvalidValue() { return invalidValue; }
}

/**
 * Exception for system-level errors.
 * Converts COBOL: Unexpected system errors, resource unavailable
 */
public class SystemException extends RuntimeException {
    private final String systemComponent;
    private final String errorCode;
    
    public SystemException(String message) {
        super(message);
        this.systemComponent = null;
        this.errorCode = null;
    }
    
    public SystemException(String message, Throwable cause) {
        super(message, cause);
        this.systemComponent = null;
        this.errorCode = null;
    }
    
    public SystemException(String message, String systemComponent, String errorCode) {
        super(message);
        this.systemComponent = systemComponent;
        this.errorCode = errorCode;
    }
    
    public SystemException(String message, Throwable cause, String systemComponent, String errorCode) {
        super(message, cause);
        this.systemComponent = systemComponent;
        this.errorCode = errorCode;
    }
    
    public String getSystemComponent() { return systemComponent; }
    public String getErrorCode() { return errorCode; }
}
```

**2. Exception Handler Service:**
```java
package {{ package_name }}.service;

import {{ package_name }}.exception.*;
import org.springframework.dao.*;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.EntityNotFoundException;
import javax.persistence.OptimisticLockException;
import javax.persistence.PessimisticLockException;
import java.sql.SQLException;

/**
 * Service for handling and converting database exceptions.
 * Converts Spring Data exceptions to business-specific exceptions with COBOL traceability.
 */
@Service
public class DatabaseExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseExceptionHandler.class);
    
    /**
     * Convert Spring Data exception to business exception.
     * Equivalent to COBOL DIBSTAT/SQLCODE checking
     * 
     * @param e the Spring Data exception
     * @param operationName the operation that failed
     * @param cobolOperation the original COBOL operation
     * @return converted business exception
     */
    public RuntimeException convertException(DataAccessException e, String operationName, String cobolOperation) {
        logger.error("Database exception in operation: {} (COBOL: {})", operationName, cobolOperation, e);
        
        // Data integrity violations (COBOL: SQLCODE -803, -532, etc.)
        if (e instanceof DataIntegrityViolationException) {
            return handleDataIntegrityViolation((DataIntegrityViolationException) e, operationName, cobolOperation);
        }
        
        // Optimistic locking failures (COBOL: Version mismatch)
        if (e instanceof OptimisticLockingFailureException) {
            return new DatabaseConcurrencyException(
                "Record was modified by another transaction. Original COBOL operation: " + cobolOperation,
                e, "OPTIMISTIC", null);
        }
        
        // Pessimistic locking failures (COBOL: DIBSTAT lock conflict)
        if (e instanceof PessimisticLockingFailureException) {
            return new DatabaseLockException(
                "Failed to acquire database lock. Original COBOL operation: " + cobolOperation,
                e, "PESSIMISTIC_WRITE", 0);
        }
        
        // Deadlock detection (COBOL: SQLCODE -911)
        if (e instanceof DeadlockLoserDataAccessException) {
            return new DatabaseConcurrencyException(
                "Database deadlock detected. Original COBOL operation: " + cobolOperation,
                e, "DEADLOCK", null);
        }
        
        // Timeout exceptions (COBOL: SQLCODE -905)
        if (e instanceof QueryTimeoutException) {
            return new DatabaseOperationException(
                "Database operation timed out. Original COBOL operation: " + cobolOperation,
                e, operationName, cobolOperation);
        }
        
        // Connection issues (COBOL: SQLCODE -30081)
        if (e instanceof DataAccessResourceFailureException) {
            return new SystemException(
                "Database connection failure. Original COBOL operation: " + cobolOperation,
                e, "DATABASE", "CONNECTION_FAILURE");
        }
        
        // Generic data access exception
        return new DatabaseOperationException(
            "Database operation failed. Original COBOL operation: " + cobolOperation,
            e, operationName, cobolOperation);
    }
    
    private DatabaseConstraintException handleDataIntegrityViolation(
            DataIntegrityViolationException e, String operationName, String cobolOperation) {
        
        String message = e.getMessage().toLowerCase();
        
        // Primary key violation (COBOL: SQLCODE -803)
        if (message.contains("primary key") || message.contains("duplicate key")) {
            return new DatabaseConstraintException(
                "Duplicate key violation. Record already exists. Original COBOL operation: " + cobolOperation,
                e, extractConstraintName(e), "PRIMARY_KEY");
        }
        
        // Foreign key violation (COBOL: SQLCODE -532)
        if (message.contains("foreign key") || message.contains("referential integrity")) {
            return new DatabaseConstraintException(
                "Foreign key constraint violation. Referenced record not found. Original COBOL operation: " + cobolOperation,
                e, extractConstraintName(e), "FOREIGN_KEY");
        }
        
        // Unique constraint violation (COBOL: SQLCODE -803)
        if (message.contains("unique constraint") || message.contains("unique index")) {
            return new DatabaseConstraintException(
                "Unique constraint violation. Duplicate value not allowed. Original COBOL operation: " + cobolOperation,
                e, extractConstraintName(e), "UNIQUE");
        }
        
        // Check constraint violation (COBOL: SQLCODE -545)
        if (message.contains("check constraint")) {
            return new DatabaseConstraintException(
                "Check constraint violation. Invalid data value. Original COBOL operation: " + cobolOperation,
                e, extractConstraintName(e), "CHECK");
        }
        
        // Not null constraint violation (COBOL: SQLCODE -407)
        if (message.contains("not null") || message.contains("null value")) {
            return new DatabaseConstraintException(
                "Not null constraint violation. Required field is missing. Original COBOL operation: " + cobolOperation,
                e, extractConstraintName(e), "NOT_NULL");
        }
        
        // Generic constraint violation
        return new DatabaseConstraintException(
            "Data integrity constraint violation. Original COBOL operation: " + cobolOperation,
            e, "UNKNOWN", "UNKNOWN");
    }
    
    private String extractConstraintName(DataIntegrityViolationException e) {
        // Extract constraint name from exception message
        // This is database-specific and may need customization
        String message = e.getMessage();
        if (message != null) {
            // PostgreSQL pattern: constraint "constraint_name"
            if (message.contains("constraint \"")) {
                int start = message.indexOf("constraint \"") + 12;
                int end = message.indexOf("\"", start);
                if (end > start) {
                    return message.substring(start, end);
                }
            }
            // H2 pattern: constraint "constraint_name"
            if (message.contains("Unique index or primary key violation:")) {
                // Extract table and constraint info
                return "UNIQUE_CONSTRAINT";
            }
        }
        return "UNKNOWN_CONSTRAINT";
    }
}
```

**3. Error Code Mapping:**
```java
package {{ package_name }}.exception;

/**
 * Maps COBOL error codes to modern exception types.
 * Provides traceability between COBOL DIBSTAT/SQLCODE and Java exceptions.
 */
public enum CobolErrorCode {
    
    // DIBSTAT codes for IMS DLI operations
    DIBSTAT_SPACES("  ", "Success", "Normal completion"),
    DIBSTAT_GE("GE", "Not Found", "Segment not found"),
    DIBSTAT_GB("GB", "End of Database", "End of database reached"),
    DIBSTAT_GK("GK", "Segment Exists", "Segment already exists"),
    DIBSTAT_II("II", "Invalid Function", "Invalid function code"),
    DIBSTAT_IX("IX", "Invalid SSA", "Invalid segment search argument"),
    
    // SQLCODE codes for DB2 operations
    SQLCODE_0("0", "Success", "Successful execution"),
    SQLCODE_100("100", "Not Found", "No data found"),
    SQLCODE_NEG_407("-407", "Null Value", "Null value in required field"),
    SQLCODE_NEG_532("-532", "Foreign Key", "Foreign key constraint violation"),
    SQLCODE_NEG_545("-545", "Check Constraint", "Check constraint violation"),
    SQLCODE_NEG_803("-803", "Duplicate Key", "Duplicate key violation"),
    SQLCODE_NEG_805("-805", "Package Not Found", "Package not found"),
    SQLCODE_NEG_811("-811", "Multiple Rows", "Multiple rows returned for single row select"),
    SQLCODE_NEG_904("-904", "Resource Unavailable", "Resource unavailable"),
    SQLCODE_NEG_905("-905", "Timeout", "Resource limit exceeded"),
    SQLCODE_NEG_911("-911", "Deadlock", "Deadlock or timeout occurred"),
    SQLCODE_NEG_913("-913", "Deadlock", "Deadlock occurred"),
    SQLCODE_NEG_30081("-30081", "Connection Failed", "Communication failure");
    
    private final String code;
    private final String shortDescription;
    private final String longDescription;
    
    CobolErrorCode(String code, String shortDescription, String longDescription) {
        this.code = code;
        this.shortDescription = shortDescription;
        this.longDescription = longDescription;
    }
    
    public String getCode() { return code; }
    public String getShortDescription() { return shortDescription; }
    public String getLongDescription() { return longDescription; }
    
    /**
     * Convert COBOL error code to appropriate Java exception.
     */
    public RuntimeException toException(String operationName, String additionalInfo) {
        switch (this) {
            case DIBSTAT_GE:
            case SQLCODE_100:
                return new EntityNotFoundException(
                    longDescription + " in operation: " + operationName + ". " + additionalInfo);
                
            case SQLCODE_NEG_407:
                return new BusinessValidationException(
                    longDescription + " in operation: " + operationName + ". " + additionalInfo);
                
            case SQLCODE_NEG_532:
                return new DatabaseConstraintException(
                    longDescription + " in operation: " + operationName + ". " + additionalInfo,
                    null, "UNKNOWN", "FOREIGN_KEY");
                
            case SQLCODE_NEG_803:
                return new DatabaseConstraintException(
                    longDescription + " in operation: " + operationName + ". " + additionalInfo,
                    null, "UNKNOWN", "PRIMARY_KEY");
                
            case SQLCODE_NEG_911:
            case SQLCODE_NEG_913:
                return new DatabaseConcurrencyException(
                    longDescription + " in operation: " + operationName + ". " + additionalInfo,
                    null, "DEADLOCK", null);
                
            case SQLCODE_NEG_905:
                return new DatabaseOperationException(
                    longDescription + " in operation: " + operationName + ". " + additionalInfo);
                
            case SQLCODE_NEG_30081:
                return new SystemException(
                    longDescription + " in operation: " + operationName + ". " + additionalInfo,
                    null, "DATABASE", code);
                
            default:
                return new DatabaseOperationException(
                    "Database operation failed with code: " + code + " in operation: " + operationName + ". " + additionalInfo);
        }
    }
    
    public static CobolErrorCode fromDibstat(String dibstat) {
        for (CobolErrorCode code : values()) {
            if (code.getCode().equals(dibstat)) {
                return code;
            }
        }
        return null;
    }
    
    public static CobolErrorCode fromSqlcode(int sqlcode) {
        String sqlcodeStr = String.valueOf(sqlcode);
        for (CobolErrorCode code : values()) {
            if (code.getCode().equals(sqlcodeStr)) {
                return code;
            }
        }
        return null;
    }
}
```

**4. Exception Handling Patterns:**
```java
// COBOL Pattern: Check DIBSTAT after DLI operation
// IF DIBSTAT = SPACES
//     CONTINUE PROCESSING
// ELSE
//     PERFORM ERROR-HANDLING
// END-IF

// Java Pattern: Try-catch with specific exception handling
try {
    Entity result = repository.findById(id);
    // Continue processing - equivalent to DIBSTAT = SPACES
} catch (EntityNotFoundException e) {
    // Handle not found - equivalent to DIBSTAT = 'GE'
    logger.warn("Entity not found: {}", e.getMessage());
    throw e;
} catch (DatabaseOperationException e) {
    // Handle other database errors
    logger.error("Database operation failed: {}", e.getMessage(), e);
    throw e;
}

// COBOL Pattern: Check SQLCODE after SQL operation
// IF SQLCODE = 0
//     CONTINUE PROCESSING
// ELSE IF SQLCODE = 100
//     PERFORM NOT-FOUND-PROCESSING
// ELSE IF SQLCODE < 0
//     PERFORM ERROR-PROCESSING
// END-IF

// Java Pattern: Try-catch with SQLCODE equivalent handling
try {
    List<Entity> results = repository.findAll();
    if (results.isEmpty()) {
        // Handle no data found - equivalent to SQLCODE = 100
        logger.info("No data found");
        return Collections.emptyList();
    }
    // Continue processing - equivalent to SQLCODE = 0
    return results;
} catch (DataAccessException e) {
    // Handle SQL errors - equivalent to SQLCODE < 0
    RuntimeException businessException = exceptionHandler.convertException(e, "findAll", "EXEC SQL SELECT");
    throw businessException;
}
```

**REQUIRED IMPORTS:**
```java
import org.springframework.dao.*;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.persistence.EntityNotFoundException;
import javax.persistence.OptimisticLockException;
import javax.persistence.PessimisticLockException;
import java.sql.SQLException;
import java.util.Collections;
```
