Generate a Java data class that preserves COBOL hierarchical structure.

**COBOL STRUCTURE:** {{ structure.name }}
{% if is_partial_structure %}
**PARTIAL STRUCTURE:** This is part {{ structure.name.split('_PART_')[-1] if '_PART_' in structure.name else '1' }} of {{ parent_structure_name }}
{% endif %}

{% if has_ims_segments %}
**IMS SEGMENT CONTEXT:**
This structure is associated with the following IMS database segments:
{% for segment in ims_segments %}
- **{{ segment }}**: {{ segment_business_mappings.get(segment, 'Unknown business context') }}
{% endfor %}

**HIERARCHY INFORMATION:**
{% if hierarchy_info %}
- Root Level: {{ hierarchy_info.root }}
- Maximum Depth: {{ hierarchy_info.max_depth }}
- Total Fields: {{ hierarchy_info.field_count }}
- Level Groups: 
{% for level, fields in hierarchy_info.levels.items() %}
  - Level {{ '%02d' | format(level) }}: {{ fields | join(', ') }}
{% endfor %}
{% endif %}

**FIELD DEFINITIONS WITH HIERARCHY:**
{% for field in structure.fields %}
Level {{ '%02d' | format(field.level if field.level is defined else 1) }}: {{ field.name }}
  - Raw: {{ field.raw_definition if field.raw_definition is defined else field.name }}
  {% if field.parent is defined and field.parent %}
  - Parent: {{ field.parent }} (Level {{ (structure.fields | selectattr('name', 'equalto', field.parent) | first).level if (structure.fields | selectattr('name', 'equalto', field.parent) | first) else 'Unknown' }})
  {% endif %}
  {% if field.children is defined and field.children %}
  - Children: {{ field.children | join(', ') }}
  {% endif %}
  {% if field.name in business_mappings %}
  - Business Name: {{ business_mappings[field.name].business_name }}
  - Description: {{ business_mappings[field.name].description }}
  {% endif %}
{% endfor %}

{% if existing_mappings %}
**EXISTING JAVA MAPPINGS (MAINTAIN CONSISTENCY):**
{% for cobol_name, java_info in existing_mappings.items() %}
- {{ cobol_name }} → {{ java_info }}
{% endfor %}
{% endif %}

**HIERARCHICAL DESIGN APPROACHES:**

Choose ONE of the following approaches based on the structure complexity:

**APPROACH 1: NESTED CLASSES** (Recommended for deep hierarchies with 3+ levels)
- Create inner static classes for each major COBOL group level (05, 10, etc.)
- Outer class represents the 01-level structure
- Inner classes represent group items with their own fields
- Use composition to include inner class instances as fields

**APPROACH 2: COMPOSITION PATTERN** (Recommended for moderate hierarchies)
- Create separate Java classes for each COBOL group level
- Establish parent-child relationships through field references
- Each class represents a logical grouping from the COBOL structure
- Use @JsonProperty to maintain field name mapping

**APPROACH 3: FIELD GROUPING** (Recommended for simple hierarchies or single classes)
- Organize fields within a single class using logical grouping
- Use clear naming conventions that indicate hierarchical relationships
- Group related fields together with comments indicating COBOL levels
- Preserve the hierarchical context through field organization and naming

**GENERATION REQUIREMENTS:**

**1. Hierarchy Preservation:**
- Maintain exact COBOL level relationships (01 → 05 → 10 → 15, etc.)
- Preserve parent-child relationships from the original COBOL structure
- Ensure Java class structure allows reconstruction of original COBOL hierarchy
- Use field names that reflect the hierarchical context

**2. Class Structure:**
- Generate meaningful class names that reflect business purpose and hierarchy level
- Use standard Lombok annotations (@Data, @NoArgsConstructor, @AllArgsConstructor, @Builder)
- Include @JsonProperty annotations for field mapping when needed
- For nested classes: use static inner classes with proper access modifiers

**3. JavaDoc Documentation:**
- **MANDATORY Class-level JavaDoc:**
  - "Generated from COBOL structure: {{ structure.name }}"
  {% if is_partial_structure %}
  - "Part of larger structure: {{ parent_structure_name }}"
  {% endif %}
  - Document the hierarchical approach used (nested/composition/grouping)
  - Include original COBOL level structure overview
- **MANDATORY Field-level JavaDoc:**
  - "COBOL Field: [original-cobol-field-name] (Level [field-level])"
  - Include parent field information: "Parent: [parent-field-name]"
  - Document business purpose and field descriptions
  - For group fields: list immediate children

**4. Hierarchy-Specific Requirements:**

**For Nested Classes Approach:**
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MainStructureName {
    // 01-level fields here
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class GroupLevel05Name {
        // 05-level fields here
        
        @Data
        @NoArgsConstructor  
        @AllArgsConstructor
        @Builder
        public static class GroupLevel10Name {
            // 10-level fields here
        }
        
        private GroupLevel10Name groupLevel10Instance;
    }
    
    private GroupLevel05Name groupLevel05Instance;
}
```

**For Composition Pattern:**
- Create separate classes for each major level group
- Use clear naming: [StructureName][LevelDescription]
- Include references to child group classes
- Maintain bidirectional relationships where appropriate

**For Field Grouping:**
- Group fields by their COBOL level hierarchy
- Use comments to separate level groups
- Maintain field order that reflects COBOL structure
- Use descriptive field names that indicate hierarchy

**5. Field Mapping:**
- Use exact Java names and types from existing mappings when available
- Generate business-meaningful field names for new fields
- Preserve COBOL field name traceability in JavaDoc
- Handle FILLER fields appropriately (skip or use placeholder names)

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "class_info": {
    "cobol_structure_name": "{{ structure.name }}",
    "java_class_name": "generated name",
    "package": "com.generated.cobol.model",
    "business_purpose": "what this represents",
    "hierarchy_approach": "nested_classes|composition|field_grouping",
    "has_string_constructor": {{ 'true' if file_operations.requires_string_constructor else 'false' }},
    {% if is_partial_structure %}
    "is_partial_structure": true,
    "parent_structure": "{{ parent_structure_name }}",
    "part_number": {{ structure.name.split('_PART_')[-1] if '_PART_' in structure.name else 1 }},
    {% endif %}
    "cobol_levels_represented": {{ hierarchy_info.levels.keys() | list if hierarchy_info else [1] }}
  },
  "hierarchy_mappings": {
    {% for field in structure.fields %}
    "{{ field.name }}": {
      "java_field_name": "generated name",
      "java_type": "type",
      "cobol_level": {{ field.level if field.level is defined else 1 }},
      {% if field.parent is defined and field.parent %}
      "parent_field": "{{ field.parent }}",
      {% endif %}
      {% if field.children is defined and field.children %}
      "child_fields": {{ field.children }},
      {% endif %}
      "business_name": "business description",
      "hierarchy_path": "path from root to this field"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
  },
  "nested_classes": {
    // Only include if using nested classes approach
    // Map of inner class names to their field mappings
  }
}
```

**IMPORTANT NOTES:**
- Choose the most appropriate hierarchical approach based on structure complexity
- Maintain COBOL level numbering context in all generated code
- Ensure generated classes can be used to reconstruct original COBOL structure
- When chunking large structures, maintain hierarchy boundaries
- Include comprehensive traceability back to original COBOL levels
