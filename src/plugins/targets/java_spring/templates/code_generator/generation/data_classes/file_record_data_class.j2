Generate a Java data class for COBOL file record structure.

**COBOL STRUCTURE:** {{ structure.name }}
**FILE OPERATIONS DETECTED:** This structure is used in file read/write operations
**REQUIRES STRING CONSTRUCTOR:** YES - for parsing file records

**FIELD DEFINITIONS:**
{% for field in structure.fields %}
Level {{ '%02d' | format(field.level) }}: {{ field.name }}
  - Raw: {{ field.raw_definition }}
  {% if field.name in business_mappings %}
  - Business Name: {{ business_mappings[field.name].business_name }}
  - Description: {{ business_mappings[field.name].description }}
  {% endif %}
{% endfor %}

**NAMING PATTERNS (MAINTAIN CONSISTENCY):**
{% for pattern in naming_patterns %}
- {{ pattern.cobol_example }} → {{ pattern.java_example }}
{% endfor %}

**EXISTING FIELD MAPPINGS:**
{% for cobol_name, java_name in existing_mappings.items() %}
- {{ cobol_name }} → {{ java_name }}
{% endfor %}

**FILE USAGE CONTEXT:**
{% for usage in file_operations.file_operations %}
- Used in {{ usage.chunk_name }}: {{ usage.operation }}
{% endfor %}

**REQUIREMENTS:**
1. Generate meaningful class name from business context
2. Standard Lombok annotations (@Data, @Builder, etc.)
3. **STRING PARSING CONSTRUCTOR:**
```java
public static ClassName parseFromString(String recordLine) {
    // Parse fixed-width fields from positions (use counters for positions, not hardcoded numbers)
    // Handle all validations
    // Return populated object
}
```
4. Generate field names from business names
5. Use appropriate Java types
6. **MANDATORY JavaDoc with COBOL traceability:**
   - Class-level JavaDoc must include: "Generated from COBOL structure: {{ structure.name }}"
   - Each field JavaDoc must include: "COBOL Field: [original-cobol-field-name]"
   - Include business purpose and field descriptions
   - Document parseFromString method with field positions

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "class_info": {
    "cobol_structure_name": "{{ structure.name }}",
    "java_class_name": "generated name",
    "package": "com.generated.cobol",
    "business_purpose": "what this represents",
    "has_string_constructor": true
  },
  "field_mappings": {
    "COBOL-FIELD": {
      "java_field_name": "generated name",
      "java_type": "type",
      "business_name": "business description",
      "field_position": "start-end for parsing"
    }
  },
  "constructor_info": {
    "type": "string_parser",
    "purpose": "Parse fixed-width file records",
    "field_positions": [
      {"fieldName": "fieldName", "start": 0, "end": 10}
    ]
  }
}
```
