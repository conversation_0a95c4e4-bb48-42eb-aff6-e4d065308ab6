Generate an aggregate wrapper Java class that composes chunked COBOL data structure parts.

**ORIGINAL COBOL STRUCTURE:** {{ structure_name }}
**BUSINESS PURPOSE:** Aggregate wrapper for chunked COBOL structure preserving business semantics

**CHUNKED PARTS INFORMATION:**
{% for chunk_class in chunked_classes %}
- Part {{ loop.index }}: {{ chunk_class.name }} ({{ chunk_class.mappings.class_info.java_class_name if chunk_class.mappings and chunk_class.mappings.class_info else 'Unknown' }})
{% endfor %}

**STRUCTURE OVERVIEW:**
- Original Structure: {{ structure_name }}
- Total Fields: {{ total_field_count }}
- Number of Chunks: {{ chunk_count }}
- Program ID: {{ program_id }}

**HIERARCHY INFORMATION:**
{% if hierarchy_info %}
- Root Level: {{ hierarchy_info.root }}
- Maximum Depth: {{ hierarchy_info.max_depth }}
- Total Fields: {{ hierarchy_info.field_count }}
- Level Groups: 
{% for level, fields in hierarchy_info.levels.items() %}
  - Level {{ '%02d' | format(level) }}: {{ fields | join(', ') }}
{% endfor %}
{% endif %}

**BUSINESS CONTEXT:**
{% if business_mappings %}
Business Name Mappings:
{% for cobol_name, business_info in business_mappings.items() %}
- {{ cobol_name }}: {{ business_info.business_name if business_info.business_name else 'No business name' }}
{% endfor %}
{% endif %}

**FILE OPERATIONS:**
{% if file_operations.used_in_file_operations %}
This structure is used in file operations:
{% for operation in file_operations.file_operations %}
- {{ operation.chunk_name }}: {{ operation.operation }}
{% endfor %}
{% endif %}

**REQUIREMENTS:**

1. **Aggregate Class Structure:**
   - Create a main class named after the original COBOL structure (e.g., `{{ structure_name }}`)
   - Use composition pattern to include all chunked parts as fields
   - Provide proper initialization and delegation methods
   - Hide technical chunking implementation details from business logic

2. **Composition Pattern Implementation:**
   ```java
   @Data
   @NoArgsConstructor
   @AllArgsConstructor
   @Builder
   public class {{ structure_name }} {
       // Include all chunked parts as composed fields
       {% for chunk_class in chunked_classes %}
       private {{ chunk_class.mappings.class_info.java_class_name if chunk_class.mappings and chunk_class.mappings.class_info else 'ChunkPart' + loop.index }} part{{ loop.index }};
       {% endfor %}
       
       // Convenience constructor for business logic
       // Delegation methods for common operations
       // Business-focused accessor methods
   }
   ```

3. **Business Service Integration:**
   - Provide methods that work with complete business entities
   - Include convenience methods for common business operations
   - Ensure services can work with the aggregate class without knowing about chunking

4. **Data Consistency:**
   - Ensure all parts are properly initialized
   - Provide validation methods to ensure data consistency across chunks
   - Include methods to convert to/from individual chunks when needed

5. **Annotations and Features:**
   - Use Lombok annotations (@Data, @NoArgsConstructor, @AllArgsConstructor, @Builder)
   - Include proper package declaration (com.generated.cobol.model or com.generated.cobol.entities)
   - Add comprehensive JavaDoc explaining the aggregate pattern
   - Include original COBOL structure name in documentation

6. **String Constructor (if needed):**
{% if file_operations.requires_string_constructor %}
   - Include parseFromString() method that delegates to all parts
   - Provide toString() method that combines all parts appropriately
{% endif %}

7. **Business Methods:**
   - Create business-focused methods that operate on the complete structure
   - Provide convenience accessors for commonly used field combinations
   - Include validation methods for business rules

**DELEGATION PATTERN:**
Implement delegation methods that:
- Forward calls to appropriate chunked parts
- Combine results from multiple parts when needed
- Provide unified access to fields across all chunks
- Maintain business logic coherence

**EXPECTED OUTPUT:**
Generate COMPLETE Java code with:
- Proper package declaration
- All necessary imports
- Complete class implementation with all required methods
- Comprehensive JavaDoc with COBOL traceability
- No placeholders or TODO comments

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "class_info": {
    "cobol_structure_name": "{{ structure_name }}",
    "java_class_name": "generated aggregate class name",
    "package": "com.generated.cobol.model",
    "business_purpose": "Aggregate wrapper for {{ structure_name }} preserving business semantics",
    "is_aggregate_wrapper": true,
    "original_structure": "{{ structure_name }}",
    "chunk_count": {{ chunk_count }},
    "chunked_parts": [
      {% for chunk_class in chunked_classes %}
      "{{ chunk_class.mappings.class_info.java_class_name if chunk_class.mappings and chunk_class.mappings.class_info else 'ChunkPart' + loop.index }}"{% if not loop.last %},{% endif %}
      {% endfor %}
    ],
    "has_string_constructor": {{ 'true' if file_operations.requires_string_constructor else 'false' }},
    "is_entity": false
  },
  "composition_mappings": {
    {% for chunk_class in chunked_classes %}
    "part{{ loop.index }}": {
      "chunk_class_name": "{{ chunk_class.mappings.class_info.java_class_name if chunk_class.mappings and chunk_class.mappings.class_info else 'ChunkPart' + loop.index }}",
      "original_chunk_name": "{{ chunk_class.structure_name }}",
      "field_name": "part{{ loop.index }}",
      "business_purpose": "Part {{ loop.index }} of {{ structure_name }}"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
  },
  "business_methods": {
    // Map of business method names to their purposes
    // These should provide unified access to the complete structure
  }
}
```

**IMPORTANT NOTES:**
- This aggregate class should be the primary interface for business services
- Individual chunked parts should not be directly referenced in service generation
- The aggregate class preserves the original COBOL structure semantics
- All business operations should work through the aggregate class
- Technical chunking details are completely hidden from business logic
