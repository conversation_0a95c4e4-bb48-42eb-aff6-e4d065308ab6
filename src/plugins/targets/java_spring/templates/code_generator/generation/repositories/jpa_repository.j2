Generate a Spring Data JPA Repository interface for COBOL database operations.

**ENTITY INFORMATION:**
- **Entity Class:** {{ entity_name }}
- **Primary Key Type:** {{ id_type }}
- **Table Name:** {{ table_name }}
- **Business Purpose:** {{ business_purpose }}

**COBOL DATABASE OPERATIONS DETECTED:**
{% if database_operations %}
{% for operation in database_operations %}
- **{{ operation.type }}**: {{ operation.description }}
  - Original COBOL: `{{ operation.cobol_code }}`
  - Fields: {{ operation.fields | join(', ') if operation.fields else 'None' }}
  - Conditions: {{ operation.conditions | join(', ') if operation.conditions else 'None' }}
{% endfor %}
{% endif %}

**FIELD MAPPINGS:**
{% if field_mappings %}
{% for cobol_field, java_info in field_mappings.items() %}
- **{{ cobol_field }}** → {{ java_info.java_field_name }} ({{ java_info.java_type }})
  - Business Name: {{ java_info.business_name }}
{% endfor %}
{% endif %}

**REQUIREMENTS:**

1. **Generate Spring Data JPA Repository Interface**
2. **Include all necessary query methods based on COBOL operations**
3. **Use proper JPA annotations (@Query, @Modifying, @Lock, @Param)**
4. **Implement pagination and sorting for large result sets**
5. **Add comprehensive JavaDoc with COBOL traceability**
6. **Include custom query methods for complex operations**
7. **Implement proper transaction management annotations**

**REPOSITORY INTERFACE STRUCTURE:**
```java
package {{ package_name }}.repository;

import {{ package_name }}.model.{{ entity_name }};
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.repository.query.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA Repository for {{ entity_name }} entity.
 * 
 * Generated from COBOL database operations for table: {{ table_name }}
 * Business Purpose: {{ business_purpose }}
 * 
 * Original COBOL Operations Converted:
{% if database_operations %}
{% for operation in database_operations %}
 * - {{ operation.type }}: {{ operation.description }}
{% endfor %}
{% endif %}
 * 
 * <AUTHOR> Code
 * @since {{ generation_date }}
 */
@Repository
@Transactional
public interface {{ entity_name }}Repository extends JpaRepository<{{ entity_name }}, {{ id_type }}> {

    // ========== FIND OPERATIONS (COBOL SELECT/GU/GN) ==========
    
    /**
     * Find entity by primary business key.
     * Converts COBOL: EXEC DLI GU or EXEC SQL SELECT with primary key
     * 
     * @param {{ primary_key_param }} the primary business key
     * @return Optional containing the entity if found
     */
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ primary_key_field }} = :{{ primary_key_param }}")
    Optional<{{ entity_name }}> findBy{{ primary_key_field | title }}(@Param("{{ primary_key_param }}") {{ primary_key_type }} {{ primary_key_param }});

    /**
     * Find all entities matching criteria with pagination.
     * Converts COBOL: EXEC DLI GN or EXEC SQL SELECT with cursor
     * 
     * @param {{ search_param }} search criteria
     * @param pageable pagination information
     * @return Page of entities
     */
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ search_field }} = :{{ search_param }} ORDER BY e.{{ sort_field }}")
    Page<{{ entity_name }}> findAllBy{{ search_field | title }}(@Param("{{ search_param }}") {{ search_type }} {{ search_param }}, Pageable pageable);

    /**
     * Find entities with slice for memory-efficient processing.
     * Converts COBOL: EXEC DLI GN with large result sets
     * 
     * @param {{ search_param }} search criteria
     * @param pageable pagination information
     * @return Slice of entities
     */
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ search_field }} = :{{ search_param }} ORDER BY e.{{ sort_field }}")
    Slice<{{ entity_name }}> findSliceBy{{ search_field | title }}(@Param("{{ search_param }}") {{ search_type }} {{ search_param }}, Pageable pageable);

    /**
     * Find entities within date range.
     * Converts COBOL: EXEC SQL SELECT with date range conditions
     * 
     * @param startDate start of date range
     * @param endDate end of date range
     * @return List of entities within date range
     */
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ date_field }} BETWEEN :startDate AND :endDate ORDER BY e.{{ date_field }}")
    List<{{ entity_name }}> findByDateRange(@Param("startDate") LocalDateTime startDate, 
                                          @Param("endDate") LocalDateTime endDate);

    /**
     * Find entities with multiple criteria.
     * Converts COBOL: EXEC SQL SELECT with complex WHERE clause
     * 
     * @param {{ param1 }} first search criteria
     * @param {{ param2 }} second search criteria
     * @return List of matching entities
     */
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ field1 }} = :{{ param1 }} AND e.{{ field2 }} = :{{ param2 }} ORDER BY e.{{ sort_field }}")
    List<{{ entity_name }}> findBy{{ field1 | title }}And{{ field2 | title }}(@Param("{{ param1 }}") {{ type1 }} {{ param1 }}, 
                                                                             @Param("{{ param2 }}") {{ type2 }} {{ param2 }});

    // ========== LOCKING OPERATIONS (COBOL GHU/GHN) ==========
    
    /**
     * Find and lock entity for update.
     * Converts COBOL: EXEC DLI GHU (Get Hold Unique)
     * 
     * @param {{ primary_key_param }} the primary key
     * @return Optional containing the locked entity
     */
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ primary_key_field }} = :{{ primary_key_param }}")
    Optional<{{ entity_name }}> findAndLockBy{{ primary_key_field | title }}(@Param("{{ primary_key_param }}") {{ primary_key_type }} {{ primary_key_param }});

    /**
     * Find and lock multiple entities.
     * Converts COBOL: EXEC DLI GHN (Get Hold Next)
     * 
     * @param {{ search_param }} search criteria
     * @return List of locked entities
     */
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ search_field }} = :{{ search_param }} ORDER BY e.{{ sort_field }}")
    List<{{ entity_name }}> findAndLockAllBy{{ search_field | title }}(@Param("{{ search_param }}") {{ search_type }} {{ search_param }});

    // ========== COUNT OPERATIONS ==========
    
    /**
     * Count entities by criteria.
     * Converts COBOL: EXEC SQL SELECT COUNT(*) FROM table
     * 
     * @param {{ search_param }} search criteria
     * @return count of matching entities
     */
    @Query("SELECT COUNT(e) FROM {{ entity_name }} e WHERE e.{{ search_field }} = :{{ search_param }}")
    Long countBy{{ search_field | title }}(@Param("{{ search_param }}") {{ search_type }} {{ search_param }});

    /**
     * Check if entity exists by criteria.
     * Converts COBOL: EXEC SQL SELECT 1 FROM table WHERE condition
     * 
     * @param {{ search_param }} search criteria
     * @return true if entity exists
     */
    @Query("SELECT CASE WHEN COUNT(e) > 0 THEN true ELSE false END FROM {{ entity_name }} e WHERE e.{{ search_field }} = :{{ search_param }}")
    boolean existsBy{{ search_field | title }}(@Param("{{ search_param }}") {{ search_type }} {{ search_param }});

    // ========== UPDATE OPERATIONS (COBOL REPL/UPDATE) ==========
    
    /**
     * Update specific field by criteria.
     * Converts COBOL: EXEC DLI REPL or EXEC SQL UPDATE
     * 
     * @param {{ new_value }} new value to set
     * @param {{ condition_param }} condition for update
     * @param modifiedDate timestamp of modification
     * @return number of updated records
     */
    @Modifying
    @Query("UPDATE {{ entity_name }} e SET e.{{ update_field }} = :{{ new_value }}, e.lastModifiedDate = :modifiedDate " +
           "WHERE e.{{ condition_field }} = :{{ condition_param }}")
    int updateBy{{ condition_field | title }}(@Param("{{ new_value }}") {{ update_type }} {{ new_value }},
                                            @Param("modifiedDate") LocalDateTime modifiedDate,
                                            @Param("{{ condition_param }}") {{ condition_type }} {{ condition_param }});

    /**
     * Bulk update with multiple conditions.
     * Converts COBOL: EXEC SQL UPDATE with complex WHERE clause
     * 
     * @param {{ new_value }} new value to set
     * @param {{ param1 }} first condition parameter
     * @param {{ param2 }} second condition parameter
     * @param modifiedDate timestamp of modification
     * @return number of updated records
     */
    @Modifying
    @Query("UPDATE {{ entity_name }} e SET e.{{ update_field }} = :{{ new_value }}, e.lastModifiedDate = :modifiedDate " +
           "WHERE e.{{ field1 }} = :{{ param1 }} AND e.{{ field2 }} = :{{ param2 }}")
    int updateBy{{ field1 | title }}And{{ field2 | title }}(@Param("{{ new_value }}") {{ update_type }} {{ new_value }},
                                                           @Param("modifiedDate") LocalDateTime modifiedDate,
                                                           @Param("{{ param1 }}") {{ type1 }} {{ param1 }},
                                                           @Param("{{ param2 }}") {{ type2 }} {{ param2 }});

    /**
     * Update with version checking for optimistic locking.
     * Converts COBOL: EXEC SQL UPDATE with version control
     * 
     * @param id entity ID
     * @param {{ new_value }} new value to set
     * @param modifiedDate timestamp of modification
     * @param version current version for optimistic locking
     * @return number of updated records (0 if version mismatch)
     */
    @Modifying
    @Query("UPDATE {{ entity_name }} e SET e.{{ update_field }} = :{{ new_value }}, e.version = e.version + 1, " +
           "e.lastModifiedDate = :modifiedDate WHERE e.id = :id AND e.version = :version")
    int updateWithVersionCheck(@Param("id") {{ id_type }} id,
                              @Param("{{ new_value }}") {{ update_type }} {{ new_value }},
                              @Param("modifiedDate") LocalDateTime modifiedDate,
                              @Param("version") Long version);

    // ========== DELETE OPERATIONS (COBOL DLET/DELETE) ==========
    
    /**
     * Delete entities by criteria.
     * Converts COBOL: EXEC DLI DLET or EXEC SQL DELETE
     * 
     * @param {{ delete_param }} criteria for deletion
     * @return number of deleted records
     */
    @Modifying
    @Query("DELETE FROM {{ entity_name }} e WHERE e.{{ delete_field }} = :{{ delete_param }}")
    int deleteBy{{ delete_field | title }}(@Param("{{ delete_param }}") {{ delete_type }} {{ delete_param }});

    /**
     * Delete entities by multiple criteria.
     * Converts COBOL: EXEC SQL DELETE with complex WHERE clause
     * 
     * @param {{ param1 }} first deletion criteria
     * @param {{ param2 }} second deletion criteria
     * @return number of deleted records
     */
    @Modifying
    @Query("DELETE FROM {{ entity_name }} e WHERE e.{{ field1 }} = :{{ param1 }} AND e.{{ field2 }} = :{{ param2 }}")
    int deleteBy{{ field1 | title }}And{{ field2 | title }}(@Param("{{ param1 }}") {{ type1 }} {{ param1 }},
                                                           @Param("{{ param2 }}") {{ type2 }} {{ param2 }});

    /**
     * Soft delete (logical delete) by setting deleted flag.
     * Converts COBOL: UPDATE to set delete flag instead of physical delete
     * 
     * @param {{ delete_param }} criteria for soft deletion
     * @param deletedDate timestamp of deletion
     * @param deletedBy user who performed deletion
     * @return number of soft deleted records
     */
    @Modifying
    @Query("UPDATE {{ entity_name }} e SET e.deleted = true, e.deletedDate = :deletedDate, e.deletedBy = :deletedBy " +
           "WHERE e.{{ delete_field }} = :{{ delete_param }} AND e.deleted = false")
    int softDeleteBy{{ delete_field | title }}(@Param("{{ delete_param }}") {{ delete_type }} {{ delete_param }},
                                             @Param("deletedDate") LocalDateTime deletedDate,
                                             @Param("deletedBy") String deletedBy);

    // ========== NATIVE QUERIES (Complex COBOL Operations) ==========
    
    /**
     * Complex native query for operations that cannot be expressed in JPQL.
     * Converts COBOL: Complex SQL with database-specific features
     * 
     * @param {{ param1 }} first parameter
     * @param {{ param2 }} second parameter
     * @return List of entities
     */
    @Query(value = "SELECT * FROM {{ table_name }} WHERE {{ native_condition }} ORDER BY {{ native_sort }}", 
           nativeQuery = true)
    List<{{ entity_name }}> findWithNativeQuery(@Param("{{ param1 }}") {{ type1 }} {{ param1 }},
                                               @Param("{{ param2 }}") {{ type2 }} {{ param2 }});

    /**
     * Native query for database-specific operations.
     * Converts COBOL: DB2-specific SQL features
     * 
     * @param {{ param }} parameter for native query
     * @return List of entities
     */
    @Query(value = "SELECT * FROM {{ table_name }} WHERE {{ native_where_clause }} WITH UR", 
           nativeQuery = true)
    List<{{ entity_name }}> findWithUncommittedRead(@Param("{{ param }}") {{ param_type }} {{ param }});

    // ========== CUSTOM FINDER METHODS (Method Naming Convention) ==========
    
    /**
     * Find by single field using method naming convention.
     * Alternative to @Query for simple operations
     */
    List<{{ entity_name }}> findBy{{ field_name | title }}({{ field_type }} {{ field_name }});
    
    /**
     * Find first record by field with ordering.
     */
    Optional<{{ entity_name }}> findFirstBy{{ field_name | title }}OrderBy{{ sort_field | title }}Asc({{ field_type }} {{ field_name }});
    
    /**
     * Find top N records by field.
     */
    List<{{ entity_name }}> findTop{{ limit }}By{{ field_name | title }}OrderBy{{ sort_field | title }}Desc({{ field_type }} {{ field_name }});
    
    /**
     * Check existence using method naming convention.
     */
    boolean existsBy{{ field_name | title }}({{ field_type }} {{ field_name }});
    
    /**
     * Count using method naming convention.
     */
    Long countBy{{ field_name | title }}({{ field_type }} {{ field_name }});
    
    /**
     * Delete using method naming convention.
     */
    void deleteBy{{ field_name | title }}({{ field_type }} {{ field_name }});
}
```

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "repository_info": {
    "repository_name": "{{ entity_name }}Repository",
    "entity_class": "{{ entity_name }}",
    "package": "{{ package_name }}.repository",
    "table_name": "{{ table_name }}",
    "business_purpose": "{{ business_purpose }}"
  },
  "method_mappings": {
    "COBOL-OPERATION": {
      "java_method_name": "generated method name",
      "method_signature": "complete signature with types",
      "query_type": "JPQL/Native/MethodNaming",
      "business_purpose": "method purpose"
    }
  },
  "field_mappings": {
    "COBOL-FIELD": {
      "java_field_name": "generated field name",
      "java_type": "field type",
      "is_primary_key": false,
      "is_searchable": true,
      "business_name": "business description"
    }
  }
}
```

**IMPORTANT NOTES:**
- All query methods include proper parameter validation through @Param annotations
- Locking operations use @Lock annotation for database-level locking
- Update and delete operations use @Modifying annotation
- Complex operations use native queries when JPQL is insufficient
- All methods include comprehensive JavaDoc with COBOL traceability
- Repository extends JpaRepository for basic CRUD operations
- Custom finder methods use Spring Data method naming conventions where appropriate
