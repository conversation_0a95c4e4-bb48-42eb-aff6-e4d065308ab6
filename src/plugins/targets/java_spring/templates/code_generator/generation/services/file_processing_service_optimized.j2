Generate a Java Spring Boot service for file processing operations.

**BUSINESS FUNCTION:** {{ business_name }}
**DESCRIPTION:** {{ business_description }}

**FUNCTIONAL SPECIFICATION:**
{{ functional_spec }}

**EXACT METHOD SIGNATURE REQUIRED:**
{% if input_parameters %}
Input Parameters (MUST match exactly):
{% for param in input_parameters %}
- {{ param.name }}: {{ param.business_name }} ({{ param.type }})
  {% if param.name in existing_mappings %}Existing mapping: {{ existing_mappings[param.name] }}{% endif %}
{% endfor %}
{% endif %}

{% if output_parameters %}
Output Parameters (MUST be return type):
{% for param in output_parameters %}
- {{ param.name }}: {{ param.business_name }} ({{ param.type }})
  {% if param.name in existing_mappings %}Existing mapping: {{ existing_mappings[param.name] }}{% endif %}
{% endfor %}
{% endif %}

**AVAILABLE BUSINESS DATA CLASSES:**

{% if has_aggregate_wrappers %}
**AGGREGATE WRAPPER CLASSES (COMPLETE BUSINESS ENTITIES - USE THESE PRIMARILY):**
{% for data_class in aggregate_wrapper_classes %}
- {{ data_class.java_class_name }} ({{ data_class.package_name }})
  - **AGGREGATE WRAPPER** for COBOL Structure: {{ data_class.cobol_structure_name }}
  - **BUSINESS ENTITY** - Use for complete file record operations
  {% if data_class.has_string_constructor %}
  - **HAS parseFromString() method** - Use for file parsing
  {% endif %}
{% endfor %}
{% endif %}

{% if regular_data_classes %}
**REGULAR DATA CLASSES (NON-CHUNKED STRUCTURES):**
{% for data_class in regular_data_classes %}
- {{ data_class.java_class_name }} ({{ data_class.package_name }})
  - COBOL Structure: {{ data_class.cobol_structure_name }}
  - Complete business entity (not chunked)
  {% if data_class.has_string_constructor %}
  - **HAS parseFromString() method** - Use for file parsing
  {% endif %}
  - Fields: {{ data_class.field_count }}
  {% if data_class.fields %}
  - Field Details:
    {% for field in data_class.fields[:5] %}
    - {{ field.java_name }}: {{ field.java_data_type }} (COBOL: {{ field.cobol_name }})
    {% endfor %}
    {% if data_class.fields|length > 5 %}
    - ... and {{ data_class.fields|length - 5 }} more fields
    {% endif %}
  {% endif %}
{% endfor %}
{% endif %}

**FILE OPERATIONS DETECTED:**
{% if file_operations.detected %}
Operations: {{ file_operations.operations | join(', ') }}

Implement using standard Java I/O:
- READ file → BufferedReader + [dataStructureName].parseFromString()
- WRITE file → BufferedWriter + toString()
- File status → IOException handling
{% endif %}

{% if stored_mappings and stored_mappings.service_dependencies %}
**SERVICE DEPENDENCIES (INJECT THESE BEANS):**
{% for cobol_call, service_info in stored_mappings.service_dependencies.items() %}
- @Autowired {{ service_info.java_service_name }} {{ service_info.field_name }}
  - Method: {{ service_info.method_signature }}
  - Purpose: {{ service_info.business_purpose }}
  - For COBOL: {{ cobol_call }}
{% endfor %}
{% endif %}

**VARIABLE MAPPINGS (USE EXACT NAMES):**
{% if variable_java_mappings %}
{% for cobol_name, java_info in variable_java_mappings.items() %}
- {{ cobol_name }} → {{ java_info.java_name }} ({{ java_info.java_data_type }})
  {% if java_info.java_class %}- Class: {{ java_info.java_class }}{% endif %}
{% endfor %}
{% endif %}

**REQUIREMENTS:**
1. Generate service class name from business function
2. Use exact method signature with parameters above
3. Use data classes with parseFromString() for file reading
4. Implement complete file handling logic
5. Generate all variable names from business context
6. Document all naming decisions in JSON response

**IMPLEMENTATION ESSENTIALS:**
- **MUST use existing Java data classes** listed above
- **NO VSAM operations** - use standard Java I/O (FileInputStream, BufferedReader, etc.)
- Generate complete Spring Boot service class with @Service annotation
- Use @Slf4j for logging and @RequiredArgsConstructor for dependency injection
- Package: com.generated.cobol.service
- **MANDATORY JavaDoc:** Include "Generated from COBOL chunk: {{ chunk_name }}"
- **MUST inject service dependencies** as final fields
- Use exact input_parameters and output_parameters specified
- **MUST USE Variable Java Mappings** from above section
- Convert functional specification to equivalent Java implementation
- **NO TODO comments** - generate complete working code
- Add comprehensive logging and error handling

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "service_info": {
    "service_name": "generated class name",
    "package": "com.generated.cobol.service",
    "business_purpose": "what this service does"
  },
  "method_mappings": {
    "{{ chunk_name }}": {
      "java_method_name": "generated method name",
      "method_signature": "complete signature with types",
      "business_purpose": "method purpose"
    }
  },
  "parameter_mappings": {
    "COBOL-PARAM": {
      "java_name": "generated name",
      "java_type": "type used",
      "parameter_type": "input/output",
      "business_name": "business description"
    }
  },
  "variable_mappings": {
    "COBOL-VAR": {
      "java_variable_name": "generated name",
      "business_name": "business purpose",
      "scope": "method/class"
    }
  },
  "data_class_usage": {
    "StructureName": {
      "java_class_used": "ClassName",
      "usage_pattern": "parseFromString for file reading"
    }
  },
  "service_dependencies": {
    "COBOL-PERFORM-TARGET": {
      "java_service_name": "GeneratedServiceName",
      "java_method_name": "generatedMethodName",
      "method_signature": "ReturnType methodName(ParamType param)",
      "business_purpose": "what this service call does"
    }
  }
}
```
