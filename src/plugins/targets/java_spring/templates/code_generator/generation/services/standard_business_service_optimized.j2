Generate a Java Spring Boot service for business logic implementation.

**BUSINESS FUNCTION:** {{ business_name }}
**DESCRIPTION:** {{ business_description }}

{% if has_ims_segments %}
**IMS SEGMENT CONTEXT:**
This service processes the following IMS database segments:
{% for segment in ims_segments %}
- **{{ segment }}**: {{ segment_business_mappings.get(segment, 'Unknown business context') }}
{% endfor %}

**BUSINESS CONTEXT ENHANCEMENT:**
{% if javadoc_enhancement %}
- JavaDoc: {{ javadoc_enhancement }}
{% endif %}
{% if traceability_comments %}
- Traceability Comments:
{% for comment in traceability_comments %}
  - {{ comment }}
{% endfor %}
{% endif %}
{% endif %}

**FUNCTIONAL SPECIFICATION:**
{{ functional_spec }}

**EXACT METHOD SIGNATURE REQUIRED:**
{% if input_parameters %}
Input Parameters:
{% for param in input_parameters %}
- {{ param.name }}: {{ param.business_name }} ({{ param.type }})
{% endfor %}
{% endif %}

{% if output_parameters %}
Output Parameters:
{% for param in output_parameters %}
- {{ param.name }}: {{ param.business_name }} ({{ param.type }})
{% endfor %}
{% endif %}

**AVAILABLE BUSINESS DATA CLASSES:**

{% if has_aggregate_wrappers %}
**AGGREGATE WRAPPER CLASSES (COMPLETE BUSINESS ENTITIES - USE THESE PRIMARILY):**
{% for data_class in aggregate_wrapper_classes %}
- {{ data_class.java_class_name }} ({{ data_class.package_name }})
  - **AGGREGATE WRAPPER** for COBOL Structure: {{ data_class.cobol_structure_name }}
  - **BUSINESS ENTITY** - Use for complete business operations
  {% if data_class.has_string_constructor %}
  - **HAS parseFromString() method** - Use for file parsing
  {% endif %}
{% endfor %}
{% endif %}

{% if regular_data_classes %}
**REGULAR DATA CLASSES (NON-CHUNKED STRUCTURES):**
{% for data_class in regular_data_classes %}
- {{ data_class.java_class_name }} ({{ data_class.package_name }})
  - COBOL Structure: {{ data_class.cobol_structure_name }}
  - Complete business entity (not chunked)
  {% if data_class.has_string_constructor %}
  - **HAS parseFromString() method** - Use for file parsing
  {% endif %}
  - Fields: {{ data_class.field_count }}
  {% if data_class.fields %}
  - Field Details:
    {% for field in data_class.fields[:5] %}
    - {{ field.java_name }}: {{ field.java_data_type }} (COBOL: {{ field.cobol_name }})
    {% endfor %}
    {% if data_class.fields|length > 5 %}
    - ... and {{ data_class.fields|length - 5 }} more fields
    {% endif %}
  {% endif %}
{% endfor %}
{% endif %}

{% if stored_mappings and stored_mappings.service_dependencies %}
**SERVICE DEPENDENCIES (INJECT THESE BEANS):**
{% for cobol_call, service_info in stored_mappings.service_dependencies.items() %}
- @Autowired {{ service_info.java_service_name }} {{ service_info.field_name }}
  - Method: {{ service_info.method_signature }}
  - Purpose: {{ service_info.business_purpose }}
  - For COBOL: {{ cobol_call }}
{% endfor %}
{% endif %}

**VARIABLE MAPPINGS (USE EXACT NAMES):**
{% if variable_java_mappings %}
{% for cobol_name, java_info in variable_java_mappings.items() %}
- {{ cobol_name }} → {{ java_info.java_name }} ({{ java_info.java_data_type }})
{% endfor %}
{% endif %}

**REQUIREMENTS:**
1. Generate service class name from business function
2. Use exact method signature
3. Use existing data classes
4. Generate all names from business context
5. Complete implementation (no TODOs)

**IMPLEMENTATION ESSENTIALS:**
- **MUST use existing Java data classes** listed above
- **NO VSAM operations** - use standard Java I/O when needed
- Generate complete Spring Boot service class with @Service annotation
- Use @Slf4j for logging and @RequiredArgsConstructor for dependency injection
- Package: com.generated.cobol.service
- **MANDATORY JavaDoc:** Include "Generated from COBOL chunk: {{ chunk_name }}"
- **MUST inject service dependencies** as final fields
- Use exact input_parameters and output_parameters specified
- **MUST USE Variable Java Mappings** from above section
- Convert functional specification to equivalent Java implementation
- **NO TODO comments** - generate complete working code
- Add comprehensive logging and error handling

**DATABASE OPERATIONS:**
- IMS DLI: Convert to JPA repository operations
- DB2 SQL: Convert to JPA @Query annotations or repository methods
- File operations: Use standard Java I/O (FileInputStream, BufferedReader, etc.)

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "service_info": {
    "service_name": "generated class name",
    "package": "com.generated.cobol.service",
    "business_purpose": "what this service does"
  },
  "method_mappings": {
    "{{ chunk_name }}": {
      "java_method_name": "generated method name",
      "method_signature": "complete signature with types",
      "business_purpose": "method purpose"
    }
  },
  "parameter_mappings": {
    "COBOL-PARAM": {
      "java_name": "generated name",
      "java_type": "type used",
      "parameter_type": "input/output",
      "business_name": "business description"
    }
  },
  "variable_mappings": {
    "COBOL-VAR": {
      "java_variable_name": "generated name",
      "business_name": "business purpose",
      "scope": "method/class"
    }
  },
  "data_class_usage": {
    "StructureName": {
      "java_class_used": "ClassName",
      "usage_pattern": "parseFromString for file reading"
    }
  },
  "service_dependencies": {
    "COBOL-PERFORM-TARGET": {
      "java_service_name": "GeneratedServiceName",
      "java_method_name": "generatedMethodName",
      "method_signature": "ReturnType methodName(ParamType param)",
      "business_purpose": "what this service call does"
    }
  }
}
```
