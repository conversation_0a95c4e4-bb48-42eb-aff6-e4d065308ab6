Generate a Spring Boot Service class for COBOL database operations.

**SERVICE INFORMATION:**
- **Service Class:** {{ service_name }}
- **Entity:** {{ entity_name }}
- **Business Purpose:** {{ business_purpose }}
- **COBOL Program:** {{ cobol_program }}

**COBOL DATABASE OPERATIONS:**
{% if database_operations %}
{% for operation in database_operations %}
- **{{ operation.type }}**: {{ operation.description }}
  - Original COBOL: `{{ operation.cobol_code }}`
  - Business Logic: {{ operation.business_logic }}
  - Error Handling: {{ operation.error_handling }}
{% endfor %}
{% endif %}

**REPOSITORY DEPENDENCIES:**
{% if repository_dependencies %}
{% for repo in repository_dependencies %}
- {{ repo.repository_name }} for {{ repo.entity_name }}
{% endfor %}
{% endif %}

**REQUIREMENTS:**

1. **Generate complete Spring Boot Service class**
2. **Convert all COBOL database operations to Spring Data JPA calls**
3. **Implement comprehensive error handling with proper exception mapping**
4. **Add transaction management with appropriate isolation levels**
5. **Include business validation and logging**
6. **Implement audit trail functionality**
7. **Add performance monitoring and metrics**

**SERVICE CLASS STRUCTURE:**
```java
package {{ package_name }}.service;

import {{ package_name }}.model.{{ entity_name }};
import {{ package_name }}.repository.{{ entity_name }}Repository;
import {{ package_name }}.exception.*;
import {{ package_name }}.dto.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.dao.PessimisticLockingFailureException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.EntityNotFoundException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service class for {{ entity_name }} database operations.
 * 
 * Converts COBOL database operations from program: {{ cobol_program }}
 * Business Purpose: {{ business_purpose }}
 * 
 * Original COBOL Operations Converted:
{% if database_operations %}
{% for operation in database_operations %}
 * - {{ operation.type }}: {{ operation.description }}
{% endfor %}
{% endif %}
 * 
 * <AUTHOR> Code
 * @since {{ generation_date }}
 */
@Service
@Transactional
public class {{ service_name }} {

    private static final Logger logger = LoggerFactory.getLogger({{ service_name }}.class);

    @Autowired
    private {{ entity_name }}Repository repository;

{% if repository_dependencies %}
{% for repo in repository_dependencies %}
    @Autowired
    private {{ repo.repository_name }} {{ repo.field_name }};
{% endfor %}
{% endif %}

    // ========== CREATE OPERATIONS (COBOL ISRT) ==========

    /**
     * Create new {{ entity_name }} record.
     * Converts COBOL: EXEC DLI ISRT or EXEC SQL INSERT
     * 
     * @param entity the entity to create
     * @return created entity with generated ID
     * @throws DatabaseOperationException if creation fails
     * @throws BusinessValidationException if business rules are violated
     */
    @Transactional
    public {{ entity_name }} create{{ entity_name }}({{ entity_name }} entity) {
        String operationName = "create{{ entity_name }}";
        logOperationStart(operationName);
        
        try {
            // Input validation
            validateRequired(entity, "{{ entity_name | lower }}");
            
            // Business validation
            validateCreateBusinessRules(entity);
            
            // Set audit fields
            entity.setCreatedDate(LocalDateTime.now());
            entity.setCreatedBy(getCurrentUser());
            entity.setVersion(0L);
            
            // Save entity
            {{ entity_name }} savedEntity = repository.save(entity);
            
            logOperationSuccess(operationName);
            logger.info("Successfully created {{ entity_name }} with ID: {}", savedEntity.getId());
            
            return savedEntity;
            
        } catch (DataIntegrityViolationException e) {
            logOperationError(operationName, e);
            throw new DatabaseConstraintException(
                "Failed to create {{ entity_name }} due to constraint violation: " + e.getMessage(), e);
        } catch (BusinessValidationException e) {
            logOperationError(operationName, e);
            throw e; // Re-throw business validation exceptions
        } catch (DataAccessException e) {
            logOperationError(operationName, e);
            throw new DatabaseOperationException("Failed to create {{ entity_name }}: " + e.getMessage(), e);
        } catch (Exception e) {
            logOperationError(operationName, e);
            throw new SystemException("Unexpected error creating {{ entity_name }}: " + e.getMessage(), e);
        }
    }

    /**
     * Batch create multiple {{ entity_name }} records.
     * Converts COBOL: Multiple EXEC DLI ISRT operations
     * 
     * @param entities list of entities to create
     * @return list of created entities
     * @throws DatabaseOperationException if batch creation fails
     */
    @Transactional
    public List<{{ entity_name }}> createBatch{{ entity_name }}(List<{{ entity_name }}> entities) {
        String operationName = "createBatch{{ entity_name }}";
        logOperationStart(operationName);
        
        try {
            validateRequired(entities, "entities list");
            
            // Validate each entity
            entities.forEach(this::validateCreateBusinessRules);
            
            // Set audit fields for all entities
            LocalDateTime now = LocalDateTime.now();
            String currentUser = getCurrentUser();
            entities.forEach(entity -> {
                entity.setCreatedDate(now);
                entity.setCreatedBy(currentUser);
                entity.setVersion(0L);
            });
            
            // Batch save
            List<{{ entity_name }}> savedEntities = repository.saveAll(entities);
            
            logOperationSuccess(operationName);
            logger.info("Successfully created {} {{ entity_name }} records", savedEntities.size());
            
            return savedEntities;
            
        } catch (DataAccessException e) {
            logOperationError(operationName, e);
            throw new DatabaseOperationException("Failed to batch create {{ entity_name }} records: " + e.getMessage(), e);
        } catch (Exception e) {
            logOperationError(operationName, e);
            throw new SystemException("Unexpected error in batch create: " + e.getMessage(), e);
        }
    }

    // ========== READ OPERATIONS (COBOL GU/GN/SELECT) ==========

    /**
     * Find {{ entity_name }} by primary key.
     * Converts COBOL: EXEC DLI GU or EXEC SQL SELECT with primary key
     * 
     * @param id the primary key
     * @return the entity if found
     * @throws EntityNotFoundException if entity not found
     */
    @Transactional(readOnly = true)
    public {{ entity_name }} findById({{ id_type }} id) {
        String operationName = "findById";
        logOperationStart(operationName);
        
        try {
            validateRequired(id, "id");
            
            {{ entity_name }} entity = repository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("{{ entity_name }} not found with ID: " + id));
            
            logOperationSuccess(operationName);
            return entity;
            
        } catch (EntityNotFoundException e) {
            logger.warn("{{ entity_name }} not found with ID: {}", id);
            throw e;
        } catch (DataAccessException e) {
            logOperationError(operationName, e);
            throw new DatabaseOperationException("Failed to find {{ entity_name }} by ID: " + e.getMessage(), e);
        }
    }

    /**
     * Find {{ entity_name }} by business key.
     * Converts COBOL: EXEC DLI GU with business key
     * 
     * @param {{ business_key_param }} the business key
     * @return the entity if found
     * @throws EntityNotFoundException if entity not found
     */
    @Transactional(readOnly = true)
    public {{ entity_name }} findBy{{ business_key_field | title }}({{ business_key_type }} {{ business_key_param }}) {
        String operationName = "findBy{{ business_key_field | title }}";
        logOperationStart(operationName);
        
        try {
            validateRequired({{ business_key_param }}, "{{ business_key_param }}");
            
            {{ entity_name }} entity = repository.findBy{{ business_key_field | title }}({{ business_key_param }})
                .orElseThrow(() -> new EntityNotFoundException(
                    "{{ entity_name }} not found with {{ business_key_field }}: " + {{ business_key_param }}));
            
            logOperationSuccess(operationName);
            return entity;
            
        } catch (EntityNotFoundException e) {
            logger.warn("{{ entity_name }} not found with {{ business_key_field }}: {}", {{ business_key_param }});
            throw e;
        } catch (DataAccessException e) {
            logOperationError(operationName, e);
            throw new DatabaseOperationException("Failed to find {{ entity_name }}: " + e.getMessage(), e);
        }
    }

    /**
     * Find all {{ entity_name }} records with pagination.
     * Converts COBOL: EXEC DLI GN or EXEC SQL SELECT with cursor
     * 
     * @param {{ search_param }} search criteria
     * @param page page number (0-based)
     * @param size page size
     * @return page of entities
     */
    @Transactional(readOnly = true)
    public Page<{{ entity_name }}> findAllBy{{ search_field | title }}({{ search_type }} {{ search_param }}, int page, int size) {
        String operationName = "findAllBy{{ search_field | title }}";
        logOperationStart(operationName);
        
        try {
            validateRequired({{ search_param }}, "{{ search_param }}");
            
            Pageable pageable = PageRequest.of(page, size, Sort.by("{{ sort_field }}"));
            Page<{{ entity_name }}> result = repository.findAllBy{{ search_field | title }}({{ search_param }}, pageable);
            
            logOperationSuccess(operationName);
            logger.info("Found {} {{ entity_name }} records for {{ search_field }}: {}", result.getTotalElements(), {{ search_param }});
            
            return result;
            
        } catch (DataAccessException e) {
            logOperationError(operationName, e);
            throw new DatabaseOperationException("Failed to find {{ entity_name }} records: " + e.getMessage(), e);
        }
    }

    // ========== UPDATE OPERATIONS (COBOL REPL/UPDATE) ==========

    /**
     * Update {{ entity_name }} record.
     * Converts COBOL: EXEC DLI REPL or EXEC SQL UPDATE
     * 
     * @param entity the entity to update
     * @return updated entity
     * @throws EntityNotFoundException if entity not found
     * @throws DatabaseConcurrencyException if optimistic locking fails
     */
    @Transactional
    public {{ entity_name }} update{{ entity_name }}({{ entity_name }} entity) {
        String operationName = "update{{ entity_name }}";
        logOperationStart(operationName);
        
        try {
            validateRequired(entity, "{{ entity_name | lower }}");
            validateRequired(entity.getId(), "{{ entity_name | lower }} ID");
            
            // Verify entity exists
            {{ entity_name }} existingEntity = findById(entity.getId());
            
            // Business validation
            validateUpdateBusinessRules(entity, existingEntity);
            
            // Set audit fields
            entity.setLastModifiedDate(LocalDateTime.now());
            entity.setLastModifiedBy(getCurrentUser());
            
            // Save updated entity
            {{ entity_name }} updatedEntity = repository.save(entity);
            
            logOperationSuccess(operationName);
            logger.info("Successfully updated {{ entity_name }} with ID: {}", updatedEntity.getId());
            
            return updatedEntity;
            
        } catch (OptimisticLockingFailureException e) {
            logOperationError(operationName, e);
            throw new DatabaseConcurrencyException(
                "{{ entity_name }} was modified by another transaction. Please refresh and try again.", e);
        } catch (EntityNotFoundException e) {
            logOperationError(operationName, e);
            throw e;
        } catch (DataAccessException e) {
            logOperationError(operationName, e);
            throw new DatabaseOperationException("Failed to update {{ entity_name }}: " + e.getMessage(), e);
        }
    }

    /**
     * Bulk update operation.
     * Converts COBOL: EXEC SQL UPDATE with WHERE clause
     * 
     * @param {{ new_value }} new value to set
     * @param {{ condition_param }} condition for update
     * @return number of updated records
     */
    @Transactional
    public int bulkUpdate{{ update_field | title }}({{ update_type }} {{ new_value }}, {{ condition_type }} {{ condition_param }}) {
        String operationName = "bulkUpdate{{ update_field | title }}";
        logOperationStart(operationName);
        
        try {
            validateRequired({{ new_value }}, "{{ new_value }}");
            validateRequired({{ condition_param }}, "{{ condition_param }}");
            
            int updatedCount = repository.updateBy{{ condition_field | title }}({{ new_value }}, LocalDateTime.now(), {{ condition_param }});
            
            logOperationSuccess(operationName);
            logger.info("Successfully updated {} {{ entity_name }} records", updatedCount);
            
            return updatedCount;
            
        } catch (DataAccessException e) {
            logOperationError(operationName, e);
            throw new DatabaseOperationException("Failed to bulk update {{ entity_name }}: " + e.getMessage(), e);
        }
    }

    // ========== DELETE OPERATIONS (COBOL DLET/DELETE) ==========

    /**
     * Delete {{ entity_name }} by ID.
     * Converts COBOL: EXEC DLI DLET or EXEC SQL DELETE
     * 
     * @param id the entity ID to delete
     * @throws EntityNotFoundException if entity not found
     * @throws DatabaseConstraintException if foreign key constraints prevent deletion
     */
    @Transactional
    public void delete{{ entity_name }}({{ id_type }} id) {
        String operationName = "delete{{ entity_name }}";
        logOperationStart(operationName);
        
        try {
            validateRequired(id, "id");
            
            // Verify entity exists
            {{ entity_name }} entity = findById(id);
            
            // Business validation before deletion
            validateDeleteBusinessRules(entity);
            
            // Delete entity
            repository.deleteById(id);
            
            logOperationSuccess(operationName);
            logger.info("Successfully deleted {{ entity_name }} with ID: {}", id);
            
        } catch (DataIntegrityViolationException e) {
            logOperationError(operationName, e);
            throw new DatabaseConstraintException(
                "Cannot delete {{ entity_name }} due to foreign key constraints: " + e.getMessage(), e);
        } catch (EntityNotFoundException e) {
            logOperationError(operationName, e);
            throw e;
        } catch (DataAccessException e) {
            logOperationError(operationName, e);
            throw new DatabaseOperationException("Failed to delete {{ entity_name }}: " + e.getMessage(), e);
        }
    }

    /**
     * Soft delete {{ entity_name }} (logical delete).
     * Converts COBOL: UPDATE to set delete flag
     * 
     * @param id the entity ID to soft delete
     */
    @Transactional
    public void softDelete{{ entity_name }}({{ id_type }} id) {
        String operationName = "softDelete{{ entity_name }}";
        logOperationStart(operationName);
        
        try {
            validateRequired(id, "id");
            
            // Verify entity exists and is not already deleted
            {{ entity_name }} entity = findById(id);
            if (entity.isDeleted()) {
                throw new BusinessValidationException("{{ entity_name }} is already deleted");
            }
            
            // Perform soft delete
            int updatedCount = repository.softDeleteBy{{ primary_key_field | title }}(id, LocalDateTime.now(), getCurrentUser());
            
            if (updatedCount == 0) {
                throw new EntityNotFoundException("{{ entity_name }} not found for soft delete with ID: " + id);
            }
            
            logOperationSuccess(operationName);
            logger.info("Successfully soft deleted {{ entity_name }} with ID: {}", id);
            
        } catch (DataAccessException e) {
            logOperationError(operationName, e);
            throw new DatabaseOperationException("Failed to soft delete {{ entity_name }}: " + e.getMessage(), e);
        }
    }

    // ========== LOCKING OPERATIONS (COBOL GHU/GHN) ==========

    /**
     * Find and lock {{ entity_name }} for update.
     * Converts COBOL: EXEC DLI GHU (Get Hold Unique)
     * 
     * @param id the entity ID to lock
     * @return locked entity
     * @throws EntityNotFoundException if entity not found
     * @throws DatabaseLockException if lock cannot be acquired
     */
    @Transactional
    public {{ entity_name }} findAndLock{{ entity_name }}({{ id_type }} id) {
        String operationName = "findAndLock{{ entity_name }}";
        logOperationStart(operationName);
        
        try {
            validateRequired(id, "id");
            
            {{ entity_name }} entity = repository.findAndLockBy{{ primary_key_field | title }}(id)
                .orElseThrow(() -> new EntityNotFoundException("{{ entity_name }} not found for locking with ID: " + id));
            
            logOperationSuccess(operationName);
            logger.info("Successfully locked {{ entity_name }} with ID: {}", id);
            
            return entity;
            
        } catch (PessimisticLockingFailureException e) {
            logOperationError(operationName, e);
            throw new DatabaseLockException("Failed to acquire lock on {{ entity_name }} with ID: " + id, e);
        } catch (EntityNotFoundException e) {
            logOperationError(operationName, e);
            throw e;
        } catch (DataAccessException e) {
            logOperationError(operationName, e);
            throw new DatabaseOperationException("Failed to lock {{ entity_name }}: " + e.getMessage(), e);
        }
    }

    // ========== BUSINESS VALIDATION METHODS ==========

    private void validateCreateBusinessRules({{ entity_name }} entity) {
        // Implement COBOL business validation rules for create operations
        if (entity.get{{ required_field | title }}() == null || entity.get{{ required_field | title }}().trim().isEmpty()) {
            throw new BusinessValidationException("{{ required_field | title }} is required for {{ entity_name }}");
        }
        
        // Add additional business rules as needed
        // Example: Check for duplicate business keys, validate field formats, etc.
    }

    private void validateUpdateBusinessRules({{ entity_name }} entity, {{ entity_name }} existingEntity) {
        // Implement COBOL business validation rules for update operations
        validateCreateBusinessRules(entity); // Include create validations
        
        // Add update-specific validations
        // Example: Check if certain fields can be modified, validate state transitions, etc.
    }

    private void validateDeleteBusinessRules({{ entity_name }} entity) {
        // Implement COBOL business validation rules for delete operations
        // Example: Check if entity can be deleted based on status, relationships, etc.
    }

    // ========== UTILITY METHODS ==========

    private void validateRequired(Object parameter, String parameterName) {
        if (parameter == null) {
            throw new IllegalArgumentException(parameterName + " cannot be null");
        }
        if (parameter instanceof String && ((String) parameter).trim().isEmpty()) {
            throw new IllegalArgumentException(parameterName + " cannot be empty");
        }
    }

    private String getCurrentUser() {
        // Implement user context retrieval
        // This could be from Spring Security context, JWT token, etc.
        return "SYSTEM"; // Placeholder
    }

    private void logOperationStart(String operationName) {
        logger.info("Starting operation: {}", operationName);
    }

    private void logOperationSuccess(String operationName) {
        logger.info("Operation completed successfully: {}", operationName);
    }

    private void logOperationError(String operationName, Exception error) {
        logger.error("Operation failed: {} - {}", operationName, error.getMessage(), error);
    }
}
```

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "service_info": {
    "service_name": "{{ service_name }}",
    "entity_class": "{{ entity_name }}",
    "package": "{{ package_name }}.service",
    "business_purpose": "{{ business_purpose }}",
    "cobol_program": "{{ cobol_program }}"
  },
  "method_mappings": {
    "COBOL-OPERATION": {
      "java_method_name": "generated method name",
      "method_signature": "complete signature with types",
      "transaction_type": "read/write",
      "business_purpose": "method purpose"
    }
  },
  "repository_dependencies": [
    {
      "repository_name": "EntityRepository",
      "field_name": "entityRepository",
      "entity_name": "Entity"
    }
  ]
}
```
