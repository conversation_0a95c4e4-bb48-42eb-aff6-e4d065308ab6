Generate a Java Spring Boot service for business logic implementation.

**BUSINESS FUNCTION:** {{ business_name }}
**DESCRIPTION:** {{ business_description }}

{% if has_ims_segments %}
**IMS SEGMENT CONTEXT:**
This service processes the following IMS database segments:
{% for segment in ims_segments %}
- **{{ segment }}**: {{ segment_business_mappings.get(segment, 'Unknown business context') }}
{% endfor %}

**BUSINESS CONTEXT ENHANCEMENT:**
{% if javadoc_enhancement %}
- JavaDoc: {{ javadoc_enhancement }}
{% endif %}
{% if traceability_comments %}
- Traceability Comments:
{% for comment in traceability_comments %}
  - {{ comment }}
{% endfor %}
{% endif %}
{% endif %}

**FUNCTIONAL SPECIFICATION:**
{{ functional_spec }}

**EXACT METHOD SIGNATURE REQUIRED:**
{% if input_parameters %}
Input Parameters:
{% for param in input_parameters %}
- {{ param.name }}: {{ param.business_name }} ({{ param.type }})
{% endfor %}
{% endif %}

{% if output_parameters %}
Output Parameters:
{% for param in output_parameters %}
- {{ param.name }}: {{ param.business_name }} ({{ param.type }})
{% endfor %}
{% endif %}

**AVAILABLE BUSINESS DATA CLASSES:**

{% if has_aggregate_wrappers %}
**AGGREGATE WRAPPER CLASSES (COMPLETE BUSINESS ENTITIES - USE THESE PRIMARILY):**
{% for data_class in aggregate_wrapper_classes %}
- {{ data_class.java_class_name }} ({{ data_class.package_name }})
  - **AGGREGATE WRAPPER** for COBOL Structure: {{ data_class.cobol_structure_name }}
  - **BUSINESS ENTITY** - Use for complete business operations
  - **RECOMMENDED** for service method parameters and return types
  - Provides unified access to complete business data structure
  {% if data_class.has_string_constructor %}
  - **HAS parseFromString() method** - Use for file parsing
  {% endif %}
{% endfor %}
{% endif %}

{% if regular_data_classes %}
**REGULAR DATA CLASSES (NON-CHUNKED STRUCTURES):**
{% for data_class in regular_data_classes %}
- {{ data_class.java_class_name }} ({{ data_class.package_name }})
  - COBOL Structure: {{ data_class.cobol_structure_name }}
  - Complete business entity (not chunked)
  {% if data_class.has_string_constructor %}
  - **HAS parseFromString() method** - Use for file parsing
  {% endif %}
  - Fields: {{ data_class.field_count }}
  {% if data_class.fields %}
  - Field Details:
    {% for field in data_class.fields[:5] %}
    - {{ field.java_name }}: {{ field.java_data_type }} (COBOL: {{ field.cobol_name }})
    {% endfor %}
    {% if data_class.fields|length > 5 %}
    - ... and {{ data_class.fields|length - 5 }} more fields
    {% endif %}
  {% endif %}
{% endfor %}
{% endif %}

**IMPORTANT DATA CLASS USAGE GUIDELINES:**
- **PREFER aggregate wrapper classes** for method parameters and return types
- Aggregate wrappers provide complete business semantics and hide technical chunking
- Use aggregate classes for business logic operations and data processing
- Individual chunked parts are internal implementation details - avoid direct usage
- Focus on business entity completeness rather than technical structure fragments

{% if stored_mappings and stored_mappings.service_dependencies %}
**SERVICE DEPENDENCIES (INJECT THESE BEANS):**
{% for cobol_call, service_info in stored_mappings.service_dependencies.items() %}
- @Autowired {{ service_info.java_service_name }} {{ service_info.field_name }}
  - Method: {{ service_info.method_signature }}
  - Purpose: {{ service_info.business_purpose }}
  - For COBOL: {{ cobol_call }}
{% endfor %}
{% endif %}

**VARIABLE MAPPINGS (USE EXACT NAMES):**
{% if variable_java_mappings %}
{% for cobol_name, java_info in variable_java_mappings.items() %}
- {{ cobol_name }} → {{ java_info.java_name }} ({{ java_info.java_data_type }})
{% endfor %}
{% endif %}

**REQUIREMENTS:**
1. Generate service class name from business function
2. Use exact method signature
3. Use existing data classes
4. Generate all names from business context
5. Complete implementation (no TODOs)

**CRITICAL REQUIREMENTS:**

**1. Data Class Usage (MANDATORY):**
- **MUST use the existing Java data classes listed above**
- **DO NOT create new data structures if existing ones are available**
- **Import all required data classes from com.generated.cobol.model package**
- Check if any input/output parameters correspond to existing data classes
- Use existing data classes for method parameters and return types when applicable
- Map COBOL data structures to the appropriate existing Java classes

**2. File Operations (NO VSAM):**
- **AVOID ALL VSAM operations completely (no KSDS, ESDS, RRDS, vsam packages)**
- **Use standard Java I/O operations:**
- FileInputStream, BufferedReader for reading files
- Files.readAllLines() for simple file reading
- FileOutputStream, BufferedWriter for writing files
- RandomAccessFile for random access if needed
- **Implement actual file reading/writing logic, NOT simulated operations**
- Handle file formats properly (fixed-width records, delimited, etc.)
- For COBOL file operations like "READ NEXT", implement sequential file reading
- For file status checks, use Java file operations and exceptions

**3. Class Structure:**
- Generate a complete Spring Boot service class with @Service annotation
- Use @Slf4j for logging and @RequiredArgsConstructor for dependency injection
- Include comprehensive method entry/exit logging using log.info()
- Choose a meaningful class name based on the business function
- Use proper package: com.generated.cobol.service
- **MANDATORY JavaDoc with COBOL traceability:**
  - Class-level JavaDoc must include: "Generated from COBOL chunk: {{ chunk_name }}"
  - Include business function description and purpose
  - Document all injected dependencies

**4. Dependency Injection (CRITICAL):**
- **MUST inject all service dependencies as final fields**
- Convert COBOL PERFORM/CALL statements to Java service method calls
- Use @Autowired (via @RequiredArgsConstructor) for all service dependencies
- Field naming: convert ServiceName to serviceName (camelCase)
- **NEVER create new instances** - always use injected services
- Follow exact method signatures from stored mappings above

**5. Method Implementation:**
- **CRITICAL:** Use the exact input_parameters and output_parameters specified above
- **MUST USE Variable Java Mappings**: For any COBOL variable in the chunk, use the exact Java name, type, and class from "Variable Java Mappings" section above
- Use existing Java data classes for parameters when they match COBOL structures
- Method signature: ReturnType methodName(InputParam1 param1, InputParam2 param2, ...)
- **MANDATORY Method JavaDoc:**
  - Include: "Implements COBOL procedure: {{ chunk_name }}"
  - Document each algorithm step with comments referencing the functional specification
  - Add inline comments for each major step from the algorithm section
  - Document all parameters and return values with COBOL traceability

**6. Business Logic Implementation:**
- Convert the functional specification to equivalent Java implementation
- Use modern Java best practices and Spring Boot patterns
- Implement the complete algorithm as described in the functional specification
- Handle all validation rules and error conditions as specified

**7. Database Operations Conversion:**
- **IMS DLI Operations:** Use templates from database_operations/ims_dli_conversion.j2
- **IBM DB2 SQL Operations:** Use templates from database_operations/db2_sql_conversion.j2
- **GSAM File Operations:** Use templates from database_operations/gsam_file_conversion.j2
- **Repository Generation:** Use templates from generation/repositories/jpa_repository.j2
- **Service Layer:** Use templates from generation/services/database_service.j2
- **Error Handling:** Use templates from error_handling/database_exception_handling.j2

**8. File Operations Conversion:**
- OPEN file → new FileInputStream() or Files.newBufferedReader()
- READ NEXT → reader.readLine() or iterate through lines
- CLOSE file → reader.close() or try-with-resources
- File status checks → IOException handling

**7. Error Handling:**
- Implement comprehensive error handling with try-catch blocks
- Use proper exception handling for file operations (IOException, etc.)
- Log all errors appropriately with context information
- Return meaningful error responses or throw appropriate exceptions

**8. Implementation Standards:**
- **NO TODO comments, placeholders, or incomplete implementations**
- Generate complete working code that produces same output as COBOL
- Use proper Java naming conventions and Spring Boot patterns
- Add comprehensive logging for debugging and monitoring
- Use dependency injection for all external services

**REQUIRED JSON MAPPING FORMAT:**
```json
{
  "service_info": {
    "service_name": "generated class name",
    "package": "com.generated.cobol.service",
    "business_purpose": "what this service does"
  },
  "method_mappings": {
    "{{ chunk_name }}": {
      "java_method_name": "generated method name",
      "method_signature": "complete signature with types",
      "business_purpose": "method purpose"
    }
  },
  "parameter_mappings": {
    "COBOL-PARAM": {
      "java_name": "generated name",
      "java_type": "type used",
      "parameter_type": "input/output",
      "business_name": "business description"
    }
  },
  "variable_mappings": {
    "COBOL-VAR": {
      "java_variable_name": "generated name",
      "business_name": "business purpose",
      "scope": "method/class"
    }
  },
  "data_class_usage": {
    "StructureName": {
      "java_class_used": "ClassName",
      "usage_pattern": "parseFromString for file reading"
    }
  },
  "service_dependencies": {
    "COBOL-PERFORM-TARGET": {
      "java_service_name": "GeneratedServiceName",
      "java_method_name": "generatedMethodName",
      "method_signature": "ReturnType methodName(ParamType param)",
      "business_purpose": "what this service call does"
    }
  }
}
```

