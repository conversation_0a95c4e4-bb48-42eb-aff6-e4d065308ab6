Generate comprehensive unit tests for COBOL database operation conversions.

**TEST INFORMATION:**
- **Service Class:** {{ service_name }}
- **Entity:** {{ entity_name }}
- **Repository:** {{ repository_name }}
- **Business Purpose:** {{ business_purpose }}

**COBOL OPERATIONS TO TEST:**
{% if database_operations %}
{% for operation in database_operations %}
- **{{ operation.type }}**: {{ operation.description }}
  - Original COBOL: `{{ operation.cobol_code }}`
  - Expected Behavior: {{ operation.expected_behavior }}
  - Error Conditions: {{ operation.error_conditions | join(', ') if operation.error_conditions else 'None' }}
{% endfor %}
{% endif %}

**REQUIREMENTS:**

1. **Generate complete JUnit 5 test class**
2. **Use H2 in-memory database for testing**
3. **Test all CRUD operations converted from COBOL**
4. **Test error handling and exception scenarios**
5. **Test transaction behavior and rollback scenarios**
6. **Test locking operations and concurrency**
7. **Include performance and load testing**

**TEST CLASS STRUCTURE:**
```java
package {{ package_name }}.service;

import {{ package_name }}.model.{{ entity_name }};
import {{ package_name }}.repository.{{ entity_name }}Repository;
import {{ package_name }}.exception.*;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.context.annotation.Import;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.assertj.core.api.Assertions.*;

/**
 * Comprehensive unit tests for {{ service_name }}.
 * 
 * Tests conversion of COBOL database operations:
{% if database_operations %}
{% for operation in database_operations %}
 * - {{ operation.type }}: {{ operation.description }}
{% endfor %}
{% endif %}
 * 
 * Uses H2 in-memory database for testing.
 * 
 * <AUTHOR> Test Code
 * @since {{ generation_date }}
 */
@DataJpaTest
@Import({{ service_name }}.class)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.jpa.show-sql=true",
    "logging.level.org.hibernate.SQL=DEBUG"
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class {{ service_name }}Test {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private {{ entity_name }}Repository repository;

    @Autowired
    private {{ service_name }} service;

    private {{ entity_name }} testEntity;
    private {{ entity_name }} secondTestEntity;

    @BeforeEach
    void setUp() {
        // Create test data
        testEntity = createTestEntity("TEST001", "Test Entity 1");
        secondTestEntity = createTestEntity("TEST002", "Test Entity 2");
    }

    @AfterEach
    void tearDown() {
        // Clean up test data
        repository.deleteAll();
        entityManager.flush();
        entityManager.clear();
    }

    // ========== CREATE OPERATIONS TESTS (COBOL ISRT) ==========

    @Nested
    @DisplayName("Create Operations (COBOL ISRT)")
    class CreateOperationsTest {

        @Test
        @DisplayName("Should create new entity successfully")
        void shouldCreateEntitySuccessfully() {
            // Given
            {{ entity_name }} newEntity = createTestEntity("NEW001", "New Test Entity");

            // When
            {{ entity_name }} result = service.create{{ entity_name }}(newEntity);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getId()).isNotNull();
            assertThat(result.get{{ business_key_field | title }}()).isEqualTo("NEW001");
            assertThat(result.getCreatedDate()).isNotNull();
            assertThat(result.getCreatedBy()).isNotNull();
            assertThat(result.getVersion()).isEqualTo(0L);

            // Verify entity is persisted
            Optional<{{ entity_name }}> persisted = repository.findById(result.getId());
            assertThat(persisted).isPresent();
            assertThat(persisted.get().get{{ business_key_field | title }}()).isEqualTo("NEW001");
        }

        @Test
        @DisplayName("Should throw exception for null entity")
        void shouldThrowExceptionForNullEntity() {
            // When & Then
            assertThrows(IllegalArgumentException.class, () -> {
                service.create{{ entity_name }}(null);
            });
        }

        @Test
        @DisplayName("Should throw exception for duplicate business key")
        void shouldThrowExceptionForDuplicateBusinessKey() {
            // Given
            entityManager.persistAndFlush(testEntity);

            {{ entity_name }} duplicateEntity = createTestEntity("TEST001", "Duplicate Entity");

            // When & Then
            assertThrows(DatabaseConstraintException.class, () -> {
                service.create{{ entity_name }}(duplicateEntity);
            });
        }

        @Test
        @DisplayName("Should create multiple entities in batch")
        void shouldCreateMultipleEntitiesInBatch() {
            // Given
            List<{{ entity_name }}> entities = List.of(
                createTestEntity("BATCH001", "Batch Entity 1"),
                createTestEntity("BATCH002", "Batch Entity 2"),
                createTestEntity("BATCH003", "Batch Entity 3")
            );

            // When
            List<{{ entity_name }}> results = service.createBatch{{ entity_name }}(entities);

            // Then
            assertThat(results).hasSize(3);
            assertThat(results).allMatch(entity -> entity.getId() != null);
            assertThat(results).allMatch(entity -> entity.getCreatedDate() != null);

            // Verify all entities are persisted
            long count = repository.count();
            assertThat(count).isEqualTo(3);
        }
    }

    // ========== READ OPERATIONS TESTS (COBOL GU/GN/SELECT) ==========

    @Nested
    @DisplayName("Read Operations (COBOL GU/GN/SELECT)")
    class ReadOperationsTest {

        @BeforeEach
        void setUpReadTests() {
            entityManager.persistAndFlush(testEntity);
            entityManager.persistAndFlush(secondTestEntity);
        }

        @Test
        @DisplayName("Should find entity by ID successfully")
        void shouldFindEntityByIdSuccessfully() {
            // When
            {{ entity_name }} result = service.findById(testEntity.getId());

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(testEntity.getId());
            assertThat(result.get{{ business_key_field | title }}()).isEqualTo("TEST001");
        }

        @Test
        @DisplayName("Should throw EntityNotFoundException for non-existent ID")
        void shouldThrowEntityNotFoundExceptionForNonExistentId() {
            // Given
            {{ id_type }} nonExistentId = {{ non_existent_id_value }};

            // When & Then
            assertThrows(EntityNotFoundException.class, () -> {
                service.findById(nonExistentId);
            });
        }

        @Test
        @DisplayName("Should find entity by business key")
        void shouldFindEntityByBusinessKey() {
            // When
            {{ entity_name }} result = service.findBy{{ business_key_field | title }}("TEST001");

            // Then
            assertThat(result).isNotNull();
            assertThat(result.get{{ business_key_field | title }}()).isEqualTo("TEST001");
        }

        @Test
        @DisplayName("Should find all entities with pagination")
        void shouldFindAllEntitiesWithPagination() {
            // Given
            String searchValue = "TEST";

            // When
            Page<{{ entity_name }}> result = service.findAllBy{{ search_field | title }}(searchValue, 0, 10);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(2);
            assertThat(result.getTotalElements()).isEqualTo(2);
            assertThat(result.getNumber()).isEqualTo(0);
            assertThat(result.getSize()).isEqualTo(10);
        }

        @Test
        @DisplayName("Should return empty page when no entities match criteria")
        void shouldReturnEmptyPageWhenNoEntitiesMatchCriteria() {
            // Given
            String nonExistentValue = "NONEXISTENT";

            // When
            Page<{{ entity_name }}> result = service.findAllBy{{ search_field | title }}(nonExistentValue, 0, 10);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).isEmpty();
            assertThat(result.getTotalElements()).isEqualTo(0);
        }
    }

    // ========== UPDATE OPERATIONS TESTS (COBOL REPL/UPDATE) ==========

    @Nested
    @DisplayName("Update Operations (COBOL REPL/UPDATE)")
    class UpdateOperationsTest {

        @BeforeEach
        void setUpUpdateTests() {
            testEntity = entityManager.persistAndFlush(testEntity);
        }

        @Test
        @DisplayName("Should update entity successfully")
        void shouldUpdateEntitySuccessfully() {
            // Given
            testEntity.set{{ update_field | title }}("Updated Value");

            // When
            {{ entity_name }} result = service.update{{ entity_name }}(testEntity);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.get{{ update_field | title }}()).isEqualTo("Updated Value");
            assertThat(result.getLastModifiedDate()).isNotNull();
            assertThat(result.getLastModifiedBy()).isNotNull();
            assertThat(result.getVersion()).isGreaterThan(testEntity.getVersion());
        }

        @Test
        @DisplayName("Should throw exception for non-existent entity update")
        void shouldThrowExceptionForNonExistentEntityUpdate() {
            // Given
            {{ entity_name }} nonExistentEntity = createTestEntity("NONEXISTENT", "Non-existent Entity");
            nonExistentEntity.setId({{ non_existent_id_value }});

            // When & Then
            assertThrows(EntityNotFoundException.class, () -> {
                service.update{{ entity_name }}(nonExistentEntity);
            });
        }

        @Test
        @DisplayName("Should handle optimistic locking failure")
        void shouldHandleOptimisticLockingFailure() {
            // Given
            {{ entity_name }} entity1 = service.findById(testEntity.getId());
            {{ entity_name }} entity2 = service.findById(testEntity.getId());

            // Modify and save first entity
            entity1.set{{ update_field | title }}("First Update");
            service.update{{ entity_name }}(entity1);

            // Try to modify and save second entity (should fail due to version mismatch)
            entity2.set{{ update_field | title }}("Second Update");

            // When & Then
            assertThrows(DatabaseConcurrencyException.class, () -> {
                service.update{{ entity_name }}(entity2);
            });
        }

        @Test
        @DisplayName("Should perform bulk update successfully")
        void shouldPerformBulkUpdateSuccessfully() {
            // Given
            {{ update_type }} newValue = {{ new_update_value }};
            {{ condition_type }} conditionValue = {{ condition_value }};

            // When
            int updatedCount = service.bulkUpdate{{ update_field | title }}(newValue, conditionValue);

            // Then
            assertThat(updatedCount).isGreaterThan(0);

            // Verify update was applied
            {{ entity_name }} updatedEntity = service.findById(testEntity.getId());
            assertThat(updatedEntity.get{{ update_field | title }}()).isEqualTo(newValue);
        }
    }

    // ========== DELETE OPERATIONS TESTS (COBOL DLET/DELETE) ==========

    @Nested
    @DisplayName("Delete Operations (COBOL DLET/DELETE)")
    class DeleteOperationsTest {

        @BeforeEach
        void setUpDeleteTests() {
            testEntity = entityManager.persistAndFlush(testEntity);
        }

        @Test
        @DisplayName("Should delete entity successfully")
        void shouldDeleteEntitySuccessfully() {
            // Given
            {{ id_type }} entityId = testEntity.getId();

            // When
            service.delete{{ entity_name }}(entityId);

            // Then
            assertThrows(EntityNotFoundException.class, () -> {
                service.findById(entityId);
            });

            // Verify entity is deleted from database
            Optional<{{ entity_name }}> deleted = repository.findById(entityId);
            assertThat(deleted).isEmpty();
        }

        @Test
        @DisplayName("Should throw exception when deleting non-existent entity")
        void shouldThrowExceptionWhenDeletingNonExistentEntity() {
            // Given
            {{ id_type }} nonExistentId = {{ non_existent_id_value }};

            // When & Then
            assertThrows(EntityNotFoundException.class, () -> {
                service.delete{{ entity_name }}(nonExistentId);
            });
        }

        @Test
        @DisplayName("Should perform soft delete successfully")
        void shouldPerformSoftDeleteSuccessfully() {
            // Given
            {{ id_type }} entityId = testEntity.getId();

            // When
            service.softDelete{{ entity_name }}(entityId);

            // Then
            {{ entity_name }} softDeletedEntity = repository.findById(entityId).orElse(null);
            assertThat(softDeletedEntity).isNotNull();
            assertThat(softDeletedEntity.isDeleted()).isTrue();
            assertThat(softDeletedEntity.getDeletedDate()).isNotNull();
            assertThat(softDeletedEntity.getDeletedBy()).isNotNull();
        }
    }

    // ========== LOCKING OPERATIONS TESTS (COBOL GHU/GHN) ==========

    @Nested
    @DisplayName("Locking Operations (COBOL GHU/GHN)")
    class LockingOperationsTest {

        @BeforeEach
        void setUpLockingTests() {
            testEntity = entityManager.persistAndFlush(testEntity);
        }

        @Test
        @DisplayName("Should acquire pessimistic lock successfully")
        void shouldAcquirePessimisticLockSuccessfully() {
            // When
            {{ entity_name }} lockedEntity = service.findAndLock{{ entity_name }}(testEntity.getId());

            // Then
            assertThat(lockedEntity).isNotNull();
            assertThat(lockedEntity.getId()).isEqualTo(testEntity.getId());
        }

        @Test
        @DisplayName("Should throw exception when locking non-existent entity")
        void shouldThrowExceptionWhenLockingNonExistentEntity() {
            // Given
            {{ id_type }} nonExistentId = {{ non_existent_id_value }};

            // When & Then
            assertThrows(EntityNotFoundException.class, () -> {
                service.findAndLock{{ entity_name }}(nonExistentId);
            });
        }
    }

    // ========== CONCURRENCY TESTS ==========

    @Nested
    @DisplayName("Concurrency Tests")
    @Execution(ExecutionMode.CONCURRENT)
    class ConcurrencyTest {

        @Test
        @DisplayName("Should handle concurrent updates correctly")
        void shouldHandleConcurrentUpdatesCorrectly() throws Exception {
            // Given
            testEntity = entityManager.persistAndFlush(testEntity);
            ExecutorService executor = Executors.newFixedThreadPool(5);

            // When
            List<CompletableFuture<Void>> futures = IntStream.range(0, 5)
                .mapToObj(i -> CompletableFuture.runAsync(() -> {
                    try {
                        {{ entity_name }} entity = service.findById(testEntity.getId());
                        entity.set{{ update_field | title }}("Update " + i);
                        service.update{{ entity_name }}(entity);
                    } catch (DatabaseConcurrencyException e) {
                        // Expected for some threads due to optimistic locking
                    }
                }, executor))
                .toList();

            // Wait for all futures to complete
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // Then
            {{ entity_name }} finalEntity = service.findById(testEntity.getId());
            assertThat(finalEntity.get{{ update_field | title }}()).startsWith("Update");
            assertThat(finalEntity.getVersion()).isGreaterThan(0L);

            executor.shutdown();
        }
    }

    // ========== PERFORMANCE TESTS ==========

    @Nested
    @DisplayName("Performance Tests")
    class PerformanceTest {

        @Test
        @DisplayName("Should handle batch operations efficiently")
        void shouldHandleBatchOperationsEfficiently() {
            // Given
            int batchSize = 1000;
            List<{{ entity_name }}> entities = IntStream.range(0, batchSize)
                .mapToObj(i -> createTestEntity("PERF" + String.format("%04d", i), "Performance Test Entity " + i))
                .toList();

            // When
            long startTime = System.currentTimeMillis();
            List<{{ entity_name }}> results = service.createBatch{{ entity_name }}(entities);
            long endTime = System.currentTimeMillis();

            // Then
            assertThat(results).hasSize(batchSize);
            assertThat(endTime - startTime).isLessThan(5000); // Should complete within 5 seconds

            // Verify all entities are persisted
            long count = repository.count();
            assertThat(count).isEqualTo(batchSize);
        }
    }

    // ========== HELPER METHODS ==========

    private {{ entity_name }} createTestEntity(String businessKey, String description) {
        {{ entity_name }} entity = new {{ entity_name }}();
        entity.set{{ business_key_field | title }}(businessKey);
        entity.set{{ description_field | title }}(description);
        entity.setCreatedDate(LocalDateTime.now());
        entity.setCreatedBy("TEST_USER");
        entity.setVersion(0L);
        entity.setDeleted(false);
        return entity;
    }
}
```

**H2 TEST CONFIGURATION:**
```properties
# application-test.properties
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=true

spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.{{ package_name }}=DEBUG
```

**REQUIRED DEPENDENCIES (pom.xml):**
```xml
<dependencies>
    <!-- JUnit 5 -->
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <scope>test</scope>
    </dependency>
    
    <!-- AssertJ -->
    <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <scope>test</scope>
    </dependency>
    
    <!-- Spring Boot Test -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
    
    <!-- H2 Database -->
    <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <scope>test</scope>
    </dependency>
</dependencies>
```

**IMPORTANT NOTES:**
- Tests use H2 in-memory database for isolation and speed
- Each test method is independent and cleans up after itself
- Concurrency tests verify proper handling of optimistic locking
- Performance tests ensure batch operations meet acceptable thresholds
- All COBOL error scenarios are tested with appropriate exception assertions
- Tests include comprehensive validation of audit fields and business rules
