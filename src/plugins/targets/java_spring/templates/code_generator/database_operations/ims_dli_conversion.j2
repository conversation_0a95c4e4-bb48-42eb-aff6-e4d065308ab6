**IMS DLI DATABASE OPERATION CONVERSION GUIDE**

Convert the following COBOL IMS DLI database operations to modern Spring Data JPA implementations:

**COBOL DLI OPERATIONS DETECTED:**
{% if dli_operations %}
{% for operation in dli_operations %}
- **{{ operation.type }}**: {{ operation.segment }} → {{ operation.description }}
  - COBOL: `{{ operation.cobol_code }}`
  - WHERE: {{ operation.where_clause | default('None') }}
  - INTO: {{ operation.into_clause | default('None') }}
  {% if operation.business_name %}
  - **Business Context**: {{ operation.business_name }}
  - **JavaDoc**: {{ operation.javadoc_comment }}
  {% endif %}
{% endfor %}
{% endif %}

{% if ims_segment_mappings %}
**IMS SEGMENT BUSINESS MAPPINGS:**
{% for segment, business_name in ims_segment_mappings.items() %}
- **{{ segment }}**: {{ business_name }}
{% endfor %}
{% endif %}

**CONVERSION PATTERNS:**

**1. EXEC DLI GU (Get Unique) Operations:**
```java
// COBOL: EXEC DLI GU SEGMENT(segment-name) WHERE(field = value) INTO(work-area)
// Java: Convert to JPA findBy method with single result
@Repository
public interface {{ entity_name }}Repository extends JpaRepository<{{ entity_name }}, {{ id_type }}> {
    
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ field_name }} = :{{ param_name }}")
    Optional<{{ entity_name }}> findBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }});
    
    // Alternative using method naming convention
    Optional<{{ entity_name }}> findFirstBy{{ field_name | title }}({{ param_type }} {{ param_name }});
}

// Service implementation
@Service
@Transactional
public class {{ service_name }} {
    
    @Autowired
    private {{ entity_name }}Repository repository;
    
    public {{ entity_name }} get{{ entity_name }}By{{ field_name | title }}({{ param_type }} {{ param_name }}) {
        try {
            return repository.findBy{{ field_name | title }}({{ param_name }})
                .orElseThrow(() -> new EntityNotFoundException(
                    "{{ entity_name }} not found with {{ field_name }}: " + {{ param_name }}));
        } catch (DataAccessException e) {
            logger.error("Database error retrieving {{ entity_name }} with {{ field_name }}: {}", {{ param_name }}, e);
            throw new DatabaseOperationException("Failed to retrieve {{ entity_name }}", e);
        }
    }
}
```

**2. EXEC DLI GN (Get Next) Operations:**
```java
// COBOL: EXEC DLI GN SEGMENT(segment-name) WHERE(condition) INTO(work-area)
// Java: Convert to JPA findAll with pagination or streaming
@Repository
public interface {{ entity_name }}Repository extends JpaRepository<{{ entity_name }}, {{ id_type }}> {
    
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ field_name }} = :{{ param_name }} ORDER BY e.{{ sort_field }}")
    List<{{ entity_name }}> findAllBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }});
    
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ field_name }} = :{{ param_name }} ORDER BY e.{{ sort_field }}")
    Page<{{ entity_name }}> findAllBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }}, Pageable pageable);
    
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ field_name }} = :{{ param_name }} ORDER BY e.{{ sort_field }}")
    Slice<{{ entity_name }}> findSliceBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }}, Pageable pageable);
}

// Service implementation for batch processing
public List<{{ entity_name }}> getNext{{ entity_name }}Records({{ param_type }} {{ param_name }}, int batchSize) {
    try {
        Pageable pageable = PageRequest.of(0, batchSize, Sort.by("{{ sort_field }}"));
        return repository.findAllBy{{ field_name | title }}({{ param_name }}, pageable).getContent();
    } catch (DataAccessException e) {
        logger.error("Database error retrieving next {{ entity_name }} records", e);
        throw new DatabaseOperationException("Failed to retrieve next {{ entity_name }} records", e);
    }
}
```

**3. EXEC DLI GHU/GHN (Get Hold) Operations:**
```java
// COBOL: EXEC DLI GHU SEGMENT(segment-name) WHERE(condition) INTO(work-area)
// Java: Convert to JPA with pessimistic locking
@Repository
public interface {{ entity_name }}Repository extends JpaRepository<{{ entity_name }}, {{ id_type }}> {
    
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ field_name }} = :{{ param_name }}")
    Optional<{{ entity_name }}> findAndLockBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }});
    
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT e FROM {{ entity_name }} e WHERE e.{{ field_name }} = :{{ param_name }} ORDER BY e.{{ sort_field }}")
    List<{{ entity_name }}> findAndLockAllBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }});
}

// Service implementation with transaction management
@Transactional
public {{ entity_name }} getAndLock{{ entity_name }}({{ param_type }} {{ param_name }}) {
    try {
        return repository.findAndLockBy{{ field_name | title }}({{ param_name }})
            .orElseThrow(() -> new EntityNotFoundException(
                "{{ entity_name }} not found for locking with {{ field_name }}: " + {{ param_name }}));
    } catch (PessimisticLockingFailureException e) {
        logger.error("Lock acquisition failed for {{ entity_name }} with {{ field_name }}: {}", {{ param_name }}, e);
        throw new DatabaseLockException("Failed to acquire lock on {{ entity_name }}", e);
    } catch (DataAccessException e) {
        logger.error("Database error locking {{ entity_name }} with {{ field_name }}: {}", {{ param_name }}, e);
        throw new DatabaseOperationException("Failed to lock {{ entity_name }}", e);
    }
}
```

**4. EXEC DLI ISRT (Insert) Operations:**
```java
// COBOL: EXEC DLI ISRT SEGMENT(segment-name) FROM(work-area)
// Java: Convert to JPA save operation
@Service
@Transactional
public class {{ service_name }} {
    
    @Autowired
    private {{ entity_name }}Repository repository;
    
    public {{ entity_name }} insert{{ entity_name }}({{ entity_name }} entity) {
        try {
            validateRequired(entity, "{{ entity_name | lower }}");
            
            // Business validation before insert
            validateBusinessRules(entity);
            
            {{ entity_name }} savedEntity = repository.save(entity);
            logger.info("Successfully inserted {{ entity_name }} with ID: {}", savedEntity.getId());
            return savedEntity;
            
        } catch (DataIntegrityViolationException e) {
            logger.error("Data integrity violation inserting {{ entity_name }}: {}", entity, e);
            throw new DatabaseConstraintException("Failed to insert {{ entity_name }} due to constraint violation", e);
        } catch (DataAccessException e) {
            logger.error("Database error inserting {{ entity_name }}: {}", entity, e);
            throw new DatabaseOperationException("Failed to insert {{ entity_name }}", e);
        }
    }
    
    private void validateBusinessRules({{ entity_name }} entity) {
        // Implement COBOL business validation rules here
        // Example: Check required fields, validate formats, etc.
        if (entity.get{{ required_field | title }}() == null || entity.get{{ required_field | title }}().trim().isEmpty()) {
            throw new BusinessValidationException("{{ required_field | title }} is required for {{ entity_name }}");
        }
    }
}
```

**5. EXEC DLI DLET (Delete) Operations:**
```java
// COBOL: EXEC DLI DLET SEGMENT(segment-name) WHERE(condition)
// Java: Convert to JPA delete operation
@Repository
public interface {{ entity_name }}Repository extends JpaRepository<{{ entity_name }}, {{ id_type }}> {
    
    @Modifying
    @Query("DELETE FROM {{ entity_name }} e WHERE e.{{ field_name }} = :{{ param_name }}")
    int deleteBy{{ field_name | title }}(@Param("{{ param_name }}") {{ param_type }} {{ param_name }});
    
    // Alternative using method naming convention
    void deleteBy{{ field_name | title }}({{ param_type }} {{ param_name }});
}

// Service implementation
@Transactional
public void delete{{ entity_name }}By{{ field_name | title }}({{ param_type }} {{ param_name }}) {
    try {
        int deletedCount = repository.deleteBy{{ field_name | title }}({{ param_name }});
        if (deletedCount == 0) {
            throw new EntityNotFoundException("No {{ entity_name }} found to delete with {{ field_name }}: " + {{ param_name }});
        }
        logger.info("Successfully deleted {} {{ entity_name }} record(s) with {{ field_name }}: {}", deletedCount, {{ param_name }});
        
    } catch (DataAccessException e) {
        logger.error("Database error deleting {{ entity_name }} with {{ field_name }}: {}", {{ param_name }}, e);
        throw new DatabaseOperationException("Failed to delete {{ entity_name }}", e);
    }
}
```

**6. EXEC DLI REPL (Replace/Update) Operations:**
```java
// COBOL: EXEC DLI REPL SEGMENT(segment-name) FROM(work-area) WHERE(condition)
// Java: Convert to JPA save/update operation
@Service
@Transactional
public class {{ service_name }} {
    
    public {{ entity_name }} update{{ entity_name }}({{ entity_name }} entity) {
        try {
            validateRequired(entity, "{{ entity_name | lower }}");
            validateRequired(entity.getId(), "{{ entity_name | lower }} ID");
            
            // Verify entity exists before update
            if (!repository.existsById(entity.getId())) {
                throw new EntityNotFoundException("{{ entity_name }} not found with ID: " + entity.getId());
            }
            
            // Business validation before update
            validateUpdateBusinessRules(entity);
            
            {{ entity_name }} updatedEntity = repository.save(entity);
            logger.info("Successfully updated {{ entity_name }} with ID: {}", updatedEntity.getId());
            return updatedEntity;
            
        } catch (OptimisticLockingFailureException e) {
            logger.error("Optimistic locking failure updating {{ entity_name }}: {}", entity.getId(), e);
            throw new DatabaseConcurrencyException("{{ entity_name }} was modified by another transaction", e);
        } catch (DataAccessException e) {
            logger.error("Database error updating {{ entity_name }}: {}", entity, e);
            throw new DatabaseOperationException("Failed to update {{ entity_name }}", e);
        }
    }
}
```

**ERROR HANDLING PATTERNS:**

**DIBSTAT Checking Conversion:**
```java
// COBOL: Check DIBSTAT for operation status
// Java: Use try-catch with specific Spring Data exceptions

// Replace DIBSTAT = SPACES (success)
try {
    // Database operation
    result = repository.operation();
    // Success - continue processing
} catch (DataAccessException e) {
    // Handle database errors
}

// Replace DIBSTAT = 'GE' (not found)
try {
    Optional<Entity> result = repository.findById(id);
    if (result.isEmpty()) {
        // Handle not found case - equivalent to DIBSTAT = 'GE'
        throw new EntityNotFoundException("Record not found");
    }
} catch (DataAccessException e) {
    // Handle other database errors
}
```

**REQUIRED IMPORTS:**
```java
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.repository.query.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Slice;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.dao.PessimisticLockingFailureException;
import javax.persistence.LockModeType;
import javax.persistence.EntityNotFoundException;
```
