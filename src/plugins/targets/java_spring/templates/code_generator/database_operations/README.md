# Database Operations Conversion Templates

This directory contains comprehensive templates for converting COBOL mainframe database operations to modern PostgreSQL implementations using Spring Data JPA and JDBC.

## Overview

The templates in this directory provide complete conversion patterns for:

1. **IMS DLI Database Operations** - Convert EXEC DLI commands to Spring Data JPA
2. **IBM DB2 SQL Operations** - Convert EXEC SQL commands to JPA queries
3. **IMS GSAM File Operations** - Convert CALL CBLTDLI to Spring Boot file I/O
4. **Repository Generation** - Generate Spring Data JPA repositories
5. **Service Layer Generation** - Generate service classes with database operations
6. **Error Handling** - Convert COBOL error checking to Spring exceptions
7. **Testing** - Generate comprehensive unit tests with H2 database

## Template Files

### Core Conversion Templates

#### `ims_dli_conversion.j2`
Converts COBOL IMS DLI database operations to Spring Data JPA:

- **EXEC DLI GU** (Get Unique) → JPA `findBy` methods with `Optional<Entity>`
- **EXEC DLI GN** (Get Next) → JPA `findAll` with `Pageable` or `Slice`
- **EXEC DLI GHU** (Get Hold Unique) → JPA with `@Lock(LockModeType.PESSIMISTIC_WRITE)`
- **EXEC DLI GHN** (Get Hold Next) → JPA with pessimistic locking and pagination
- **EXEC DLI ISRT** (Insert) → JPA `save()` operations with validation
- **EXEC DLI DLET** (Delete) → JPA `delete()` or `@Modifying` queries
- **EXEC DLI REPL** (Replace) → JPA `save()` for updates with optimistic locking

**Usage Example:**
```java
// COBOL: EXEC DLI GU SEGMENT(CUSTOMER) WHERE(CUST-ID = :WS-CUST-ID) INTO(CUSTOMER-RECORD)
// Java: 
Optional<Customer> customer = customerRepository.findByCustomerId(customerId);
```

#### `db2_sql_conversion.j2`
Converts COBOL IBM DB2 SQL operations to Spring Data JPA:

- **EXEC SQL SELECT** → JPA `@Query` annotations or repository methods
- **EXEC SQL INSERT** → JPA `save()` operations
- **EXEC SQL UPDATE** → JPA `save()` or `@Modifying` queries
- **EXEC SQL DELETE** → JPA `delete()` or `@Modifying` queries
- **Host Variables** (`:variable-name`) → Method parameters with `@Param`
- **Cursor Operations** → JPA `Slice` or `Page` for large result sets
- **Transaction Isolation** (`WITH UR`) → `@Transactional` with isolation levels

**Usage Example:**
```java
// COBOL: EXEC SQL SELECT * FROM CUSTOMER WHERE CUST_ID = :WS-CUST-ID END-EXEC
// Java:
@Query("SELECT c FROM Customer c WHERE c.customerId = :customerId")
Optional<Customer> findByCustomerId(@Param("customerId") String customerId);
```

#### `gsam_file_conversion.j2`
Converts COBOL IMS GSAM file operations to Spring Boot file I/O:

- **CALL CBLTDLI** file operations → `BufferedReader`/`BufferedWriter`
- **File Open** → `Files.newBufferedReader()` with try-with-resources
- **File Read** → `readLine()` with proper parsing
- **File Write** → `write()` with proper formatting
- **File Status Checking** → Exception handling and status enums
- **File Archiving** → Automatic file lifecycle management

**Usage Example:**
```java
// COBOL: CALL 'CBLTDLI' USING 'GN', PCB, IO-AREA
// Java:
try (BufferedReader reader = openFileForReading(fileName)) {
    CustomerRecord record = readNextRecord(reader);
    // Process record
}
```

### Generation Templates

#### `generation/repositories/jpa_repository.j2`
Generates Spring Data JPA repository interfaces:

- Extends `JpaRepository<Entity, ID>`
- Custom query methods with `@Query` annotations
- Pagination and sorting support
- Locking operations with `@Lock` annotations
- Bulk operations with `@Modifying` annotations
- Native queries for complex operations

#### `generation/services/database_service.j2`
Generates service classes for database operations:

- Complete CRUD operations
- Transaction management with `@Transactional`
- Business validation and error handling
- Audit trail functionality
- Performance monitoring and logging
- Batch processing capabilities

#### `error_handling/database_exception_handling.j2`
Provides comprehensive error handling:

- Custom exception hierarchy
- COBOL error code mapping (DIBSTAT, SQLCODE)
- Spring Data exception conversion
- Comprehensive logging with COBOL traceability
- Circuit breaker and retry patterns

#### `tests/database_operation_test.j2`
Generates comprehensive unit tests:

- JUnit 5 test classes with H2 database
- Tests for all CRUD operations
- Concurrency and locking tests
- Performance and load tests
- Error scenario testing
- Transaction rollback testing

### Configuration Templates

#### `database_config/postgresql_config.j2`
Production PostgreSQL configuration:

- Connection pooling with HikariCP
- JPA and Hibernate optimization
- Transaction management
- Performance monitoring
- Environment-specific profiles

#### `database_config/h2_test_config.j2`
H2 in-memory database for testing:

- Fast test execution
- Isolated test environment
- PostgreSQL compatibility mode
- Test data initialization
- Parallel test execution support

## Usage Guidelines

### 1. Template Selection

Choose the appropriate template based on the COBOL operation type:

- **Database Operations**: Use `ims_dli_conversion.j2` or `db2_sql_conversion.j2`
- **File Operations**: Use `gsam_file_conversion.j2`
- **Repository Generation**: Use `generation/repositories/jpa_repository.j2`
- **Service Generation**: Use `generation/services/database_service.j2`

### 2. Error Handling Integration

Always include error handling patterns from `error_handling/database_exception_handling.j2`:

```java
try {
    // Database operation
    Entity result = repository.operation();
} catch (DataAccessException e) {
    RuntimeException businessException = exceptionHandler.convertException(e, "operation", "COBOL-OPERATION");
    throw businessException;
}
```

### 3. Testing Integration

Generate tests using `tests/database_operation_test.j2` for every service:

- Include tests for all converted COBOL operations
- Test error scenarios and exception handling
- Verify transaction behavior
- Test concurrency and locking

### 4. Configuration Setup

Use the appropriate configuration template:

- **Production**: `database_config/postgresql_config.j2`
- **Testing**: `database_config/h2_test_config.j2`

## Conversion Patterns

### COBOL to Java Mapping

| COBOL Operation | Java Implementation | Template Reference |
|----------------|-------------------|-------------------|
| `EXEC DLI GU` | `repository.findBy()` | `ims_dli_conversion.j2` |
| `EXEC SQL SELECT` | `@Query` annotation | `db2_sql_conversion.j2` |
| `CALL CBLTDLI` | `BufferedReader/Writer` | `gsam_file_conversion.j2` |
| `DIBSTAT checking` | Exception handling | `error_handling/database_exception_handling.j2` |
| `SQLCODE checking` | Spring Data exceptions | `error_handling/database_exception_handling.j2` |

### Error Code Mapping

| COBOL Error | Java Exception | Description |
|-------------|---------------|-------------|
| `DIBSTAT = 'GE'` | `EntityNotFoundException` | Record not found |
| `SQLCODE = 100` | `EntityNotFoundException` | No data found |
| `SQLCODE = -803` | `DatabaseConstraintException` | Duplicate key |
| `SQLCODE = -911` | `DatabaseConcurrencyException` | Deadlock |

## Best Practices

### 1. Repository Design
- Use method naming conventions for simple queries
- Use `@Query` for complex operations
- Implement pagination for large result sets
- Add proper parameter validation

### 2. Service Layer
- Include comprehensive business validation
- Implement audit trail functionality
- Add performance monitoring
- Use appropriate transaction boundaries

### 3. Error Handling
- Convert all Spring Data exceptions to business exceptions
- Include original COBOL operation context in error messages
- Implement retry logic for transient failures
- Add comprehensive logging

### 4. Testing
- Use H2 in-memory database for unit tests
- Test all CRUD operations
- Include concurrency and error scenario tests
- Verify transaction behavior

### 5. Performance
- Use connection pooling for production
- Implement batch processing for bulk operations
- Add query optimization and indexing
- Monitor slow queries and performance metrics

## Integration with Code Generation

These templates are integrated with the main code generation system through:

1. **Service Generation System** (`system_prompts/service_generation_system.j2`)
2. **Data Class Generation** (`generation/data_classes/`)
3. **Repository Generation** (`generation/repositories/`)
4. **Test Generation** (`tests/`)

The templates provide comprehensive patterns that ensure:

- **Identical Functionality**: Converted code behaves exactly like original COBOL
- **Modern Architecture**: Uses Spring Boot best practices and patterns
- **Comprehensive Testing**: Full test coverage with H2 database
- **Production Ready**: Optimized for PostgreSQL with proper configuration
- **COBOL Traceability**: Maintains references to original COBOL operations

## Example Usage

To convert a COBOL program with database operations:

1. Identify database operation types (DLI, DB2, GSAM)
2. Apply appropriate conversion templates
3. Generate repository interfaces
4. Generate service classes with business logic
5. Include comprehensive error handling
6. Generate unit tests with H2 database
7. Configure for PostgreSQL production deployment

This ensures a complete, production-ready conversion from COBOL mainframe database operations to modern Spring Boot applications.
