Review the following Java code file for a microservice named {{ service_name }}.

File: {{ file_name }}

```java
{{ file_content }}
```

Please review this code for:
1. Correctness - Is the code syntactically correct and free of errors?
2. Best Practices - Does it follow Java and Spring Boot best practices?
3. Style - Does it follow standard Java code style?
4. Security - Are there any security concerns?
5. Performance - Are there any performance issues?
6. Maintainability - Is the code maintainable?

First, provide a review report with the following structure:
- Status: (PASS, PASS_WITH_WARNINGS, NEEDS_IMPROVEMENT, FAIL)
- Message: Brief summary of the review
- Issues: List of issues found (if any)
- Improvements: Suggested improvements (if any)

This should be in JSON format.

Then, provide an improved version of the code that addresses any issues.

RESPONSE FORMAT:
Your response should be structured as follows:

--- REVIEW REPORT ---
{
"status": "STATUS",
  "message": "Summary message",
  "issues": ["Issue 1", "Issue 2", ...],
  "improvements": ["Improvement 1", "Improvement 2", ...]
}
--- IMPROVED CODE ---
<Improved Java code here>
