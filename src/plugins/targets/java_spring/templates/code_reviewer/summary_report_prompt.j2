Create a comprehensive code review summary for the microservice: {{ service_name }}

Total files reviewed: {{ total_files }}
Average score: {{ average_score }}

Detailed file reviews:
```json
{{ file_reviews_json }}
```

Create a well-formatted Markdown report that includes:
1. An executive summary
2. Overall assessment and grade (A-F)
3. Key strengths of the codebase
4. Key areas for improvement
5. File-by-file breakdown of significant issues
6. Recommendations for addressing critical issues

Format the report in Markdown with appropriate headings, lists, and formatting.
