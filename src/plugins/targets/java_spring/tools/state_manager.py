"""
Java Spring Boot generation state management.
Extracted from plugin.py for better separation of concerns.
"""
import json
import os
import datetime
import logging
from typing import Dict, Any, List


class JavaStateManager:
    """
    Handles generation state management and persistence for Java Spring Boot code generation.
    Manages loading, saving, and updating generation state throughout the React agent process.
    """

    def __init__(self):
        """Initialize the Java state manager."""
        self.logger = logging.getLogger(__name__)

    def load_or_initialize_generation_state(self, working_directory: str) -> Dict[str, Any]:
        """
        Load existing generation state or initialize a new one.

        Args:
            working_directory: Working directory path

        Returns:
            Dict[str, Any]: Generation state
        """
        state_file = os.path.join(working_directory, "generation_state.json")

        if os.path.exists(state_file):
            try:
                with open(state_file, 'r') as f:
                    state = json.load(f)
                self.logger.info("Loaded existing generation state")
                return state
            except Exception as e:
                self.logger.warning(f"Could not load generation state: {e}. Initializing new state.")

        # Initialize new state
        state = {
            "completed": False,
            "current_phase": "analysis",
            "analyzed_programs": [],
            "dependency_graph": None,
            "generation_order": [],
            "generated_chunks": [],
            "failed_chunks": [],
            "current_chunk_index": 0,
            "java_classes_mapping": {},  # Maps chunk_id to java class info
            "iteration_count": 0,
            "last_updated": datetime.datetime.now().isoformat()
        }

        self.logger.info("Initialized new generation state")
        return state

    def save_generation_state(self, generation_state: Dict[str, Any], working_directory: str) -> None:
        """
        Save generation state to file.

        Args:
            generation_state: Generation state to save
            working_directory: Working directory path
        """
        try:
            state_file = os.path.join(working_directory, "generation_state.json")
            with open(state_file, 'w') as f:
                json.dump(generation_state, f, indent=2)
            self.logger.debug("Saved generation state")
        except Exception as e:
            self.logger.error(f"Error saving generation state: {str(e)}")

    def update_generation_state(self, generation_state: Dict[str, Any], action: Dict[str, Any], result: Dict[str, Any]) -> None:
        """
        Update generation state based on action result.
        Now includes mapping information when chunks are generated.

        Args:
            generation_state: Current generation state
            action: Executed action
            result: Action result
        """
        generation_state["last_updated"] = datetime.datetime.now().isoformat()
        generation_state["iteration_count"] = generation_state.get("iteration_count", 0) + 1

        if action["type"] == "analyze_dependencies" and result["success"]:
            generation_state["dependency_graph"] = result["dependency_graph"]
            generation_state["generation_order"] = result["generation_order"]
            generation_state["analyzed_programs"] = result["analyzed_chunks"]

        elif action["type"] == "generate_data_classes":
            if result["success"]:
                generation_state["data_classes_generated"] = True
                generation_state["generated_data_classes"] = result.get("saved_files", [])
                generation_state["data_class_mappings"] = result.get("mappings", {})
                generation_state["total_copybooks_processed"] = result.get("total_copybooks_processed", 0)
            else:
                generation_state["data_classes_failed"] = True
                generation_state["data_classes_error"] = result.get("error", "Unknown error")

        elif action["type"] == "generate_chunk":
            if result["success"]:
                chunk_info = action["chunk_info"]
                chunk_id = f"{chunk_info['program_id']}.{chunk_info['chunk_name']}"
                generation_state["generated_chunks"].append(chunk_id)

                # Store both class info and mappings
                java_class_info = result.get("java_class_info", {})
                java_class_info["cobol_java_mappings"] = result.get("mappings", {})
                generation_state["java_classes_mapping"][chunk_id] = java_class_info
            else:
                chunk_id = f"{action['chunk_info']['program_id']}.{action['chunk_info']['chunk_name']}"
                generation_state["failed_chunks"].append({
                    "chunk_id": chunk_id,
                    "error": result.get("error", "Unknown error")
                })

            generation_state["current_chunk_index"] = generation_state.get("current_chunk_index", 0) + 1

        elif action["type"] == "complete":
            generation_state["completed"] = True

    def transition_to_data_classes(self, generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transition from analysis phase to data classes generation phase.

        Args:
            generation_state: Current generation state

        Returns:
            Dict[str, Any]: Transition result
        """
        generation_state["current_phase"] = "data_classes"
        self.logger.info("Transitioned to data classes generation phase")
        return {"success": True, "phase": "data_classes"}

    def transition_to_generation(self, generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transition from data classes phase to generation phase.

        Args:
            generation_state: Current generation state

        Returns:
            Dict[str, Any]: Transition result
        """
        generation_state["current_phase"] = "generation"
        self.logger.info("Transitioned to generation phase")
        return {"success": True, "phase": "generation"}

    def transition_to_finalization(self, generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transition from generation phase to finalization phase.

        Args:
            generation_state: Current generation state

        Returns:
            Dict[str, Any]: Transition result
        """
        generation_state["current_phase"] = "finalization"
        self.logger.info("Transitioned to finalization phase")
        return {"success": True, "phase": "finalization"}

    def mark_finalization_complete(self, generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Mark finalization as complete.

        Args:
            generation_state: Current generation state

        Returns:
            Dict[str, Any]: Completion result
        """
        generation_state["current_phase"] = "finalization"
        generation_state["finalization_complete"] = True
        self.logger.info("Project finalization completed successfully")
        return {
            "success": True,
            "phase": "finalization",
            "finalization_complete": True
        }

    def should_restart_due_to_context(self) -> bool:
        """
        Check if agent should restart due to context size limitations.

        Returns:
            bool: True if should restart
        """
        # This is a placeholder - implement actual context size checking if needed
        return False

    def get_generation_summary(self, generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get a summary of the current generation state.

        Args:
            generation_state: Current generation state

        Returns:
            Dict[str, Any]: Generation summary
        """
        return {
            "current_phase": generation_state.get("current_phase", "unknown"),
            "completed": generation_state.get("completed", False),
            "total_chunks": len(generation_state.get("generation_order", [])),
            "generated_chunks": len(generation_state.get("generated_chunks", [])),
            "failed_chunks": len(generation_state.get("failed_chunks", [])),
            "current_chunk_index": generation_state.get("current_chunk_index", 0),
            "iteration_count": generation_state.get("iteration_count", 0),
            "last_updated": generation_state.get("last_updated", "unknown"),
            "finalization_complete": generation_state.get("finalization_complete", False)
        }

    def is_generation_complete(self, generation_state: Dict[str, Any]) -> bool:
        """
        Check if generation is complete.

        Args:
            generation_state: Current generation state

        Returns:
            bool: True if generation is complete
        """
        return generation_state.get("completed", False)

    def get_next_chunk_to_generate(self, generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get the next chunk that needs to be generated.

        Args:
            generation_state: Current generation state

        Returns:
            Dict[str, Any]: Next chunk info or None if no more chunks
        """
        current_index = generation_state.get("current_chunk_index", 0)
        generation_order = generation_state.get("generation_order", [])

        if current_index < len(generation_order):
            return generation_order[current_index]
        
        return None

    def get_failed_chunks(self, generation_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get list of failed chunks.

        Args:
            generation_state: Current generation state

        Returns:
            List[Dict[str, Any]]: List of failed chunks
        """
        return generation_state.get("failed_chunks", [])

    def get_generated_chunks(self, generation_state: Dict[str, Any]) -> List[str]:
        """
        Get list of successfully generated chunks.

        Args:
            generation_state: Current generation state

        Returns:
            List[str]: List of generated chunk IDs
        """
        return generation_state.get("generated_chunks", [])

    def get_java_classes_mapping(self, generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get mapping of chunks to Java class information.

        Args:
            generation_state: Current generation state

        Returns:
            Dict[str, Any]: Java classes mapping
        """
        return generation_state.get("java_classes_mapping", {})
