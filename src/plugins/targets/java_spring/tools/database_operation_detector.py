"""
Database Operation Detection Tool for COBOL to Java Spring conversion.
Detects and classifies COBOL database operations (IMS DLI, IBM DB2, GSAM) for template selection.
"""
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from src.platform.tools.ims_segment_mapper import get_ims_segment_mapper


@dataclass
class DatabaseOperation:
    """Represents a detected database operation."""
    operation_type: str  # 'IMS_DLI', 'DB2_SQL', 'GSAM_FILE'
    operation_subtype: str  # 'GU', 'GN', 'SELECT', 'INSERT', etc.
    cobol_code: str  # Original COBOL code
    table_or_segment: str  # Table/segment name
    description: str  # Human-readable description
    parameters: Dict[str, Any]  # Operation-specific parameters
    line_number: Optional[int] = None


class DatabaseOperationDetector:
    """
    Detects and classifies COBOL database operations for conversion to Spring Data JPA.
    """

    def __init__(self):
        """Initialize the database operation detector."""
        self.logger = logging.getLogger(__name__)
        self.ims_mapper = get_ims_segment_mapper()

        # IMS DLI operation patterns
        self.dli_patterns = {
            'GU': r'EXEC\s+DLI\s+GU\s+SEGMENT\s*\(\s*([^)]+)\s*\)(?:\s+WHERE\s*\(\s*([^)]+)\s*\))?(?:\s+INTO\s*\(\s*([^)]+)\s*\))?',
            'GN': r'EXEC\s+DLI\s+GN\s+SEGMENT\s*\(\s*([^)]+)\s*\)(?:\s+WHERE\s*\(\s*([^)]+)\s*\))?(?:\s+INTO\s*\(\s*([^)]+)\s*\))?',
            'GHU': r'EXEC\s+DLI\s+GHU\s+SEGMENT\s*\(\s*([^)]+)\s*\)(?:\s+WHERE\s*\(\s*([^)]+)\s*\))?(?:\s+INTO\s*\(\s*([^)]+)\s*\))?',
            'GHN': r'EXEC\s+DLI\s+GHN\s+SEGMENT\s*\(\s*([^)]+)\s*\)(?:\s+WHERE\s*\(\s*([^)]+)\s*\))?(?:\s+INTO\s*\(\s*([^)]+)\s*\))?',
            'ISRT': r'EXEC\s+DLI\s+ISRT\s+SEGMENT\s*\(\s*([^)]+)\s*\)(?:\s+FROM\s*\(\s*([^)]+)\s*\))?',
            'DLET': r'EXEC\s+DLI\s+DLET\s+SEGMENT\s*\(\s*([^)]+)\s*\)(?:\s+WHERE\s*\(\s*([^)]+)\s*\))?',
            'REPL': r'EXEC\s+DLI\s+REPL\s+SEGMENT\s*\(\s*([^)]+)\s*\)(?:\s+FROM\s*\(\s*([^)]+)\s*\))?(?:\s+WHERE\s*\(\s*([^)]+)\s*\))?'
        }
        
        # IBM DB2 SQL operation patterns
        self.db2_patterns = {
            'SELECT': r'EXEC\s+SQL\s+SELECT\s+([^F]+?)\s+FROM\s+([^\s]+)(?:\s+WHERE\s+([^E]+?))?(?:\s+WITH\s+([^E]+?))?\s+END-EXEC',
            'INSERT': r'EXEC\s+SQL\s+INSERT\s+INTO\s+([^\s(]+)(?:\s*\(\s*([^)]+)\s*\))?\s+VALUES\s*\(\s*([^)]+)\s*\)\s+END-EXEC',
            'UPDATE': r'EXEC\s+SQL\s+UPDATE\s+([^\s]+)\s+SET\s+([^W]+?)(?:\s+WHERE\s+([^E]+?))?\s+END-EXEC',
            'DELETE': r'EXEC\s+SQL\s+DELETE\s+FROM\s+([^\s]+)(?:\s+WHERE\s+([^E]+?))?\s+END-EXEC'
        }
        
        # GSAM file operation patterns
        self.gsam_patterns = {
            'CALL_CBLTDLI': r'CALL\s+[\'"]CBLTDLI[\'"](?:\s+USING\s+([^.]+?))?',
            'FILE_OPEN': r'OPEN\s+(INPUT|OUTPUT|I-O)\s+([^\s]+)',
            'FILE_READ': r'READ\s+([^\s]+)(?:\s+INTO\s+([^\s]+))?',
            'FILE_WRITE': r'WRITE\s+([^\s]+)(?:\s+FROM\s+([^\s]+))?',
            'FILE_CLOSE': r'CLOSE\s+([^\s]+)'
        }

    def detect_database_operations(self, cobol_code: str, chunk_name: str = "") -> List[DatabaseOperation]:
        """
        Detect all database operations in COBOL code.
        
        Args:
            cobol_code: COBOL source code to analyze
            chunk_name: Name of the chunk being analyzed
            
        Returns:
            List[DatabaseOperation]: List of detected database operations
        """
        operations = []
        
        # Detect IMS DLI operations
        operations.extend(self._detect_dli_operations(cobol_code))
        
        # Detect IBM DB2 SQL operations
        operations.extend(self._detect_db2_operations(cobol_code))
        
        # Detect GSAM file operations
        operations.extend(self._detect_gsam_operations(cobol_code))
        
        self.logger.info(f"Detected {len(operations)} database operations in chunk: {chunk_name}")
        for op in operations:
            self.logger.debug(f"  - {op.operation_type}.{op.operation_subtype}: {op.table_or_segment}")
        
        return operations

    def _detect_dli_operations(self, cobol_code: str) -> List[DatabaseOperation]:
        """Detect IMS DLI database operations."""
        operations = []
        
        for operation_type, pattern in self.dli_patterns.items():
            matches = re.finditer(pattern, cobol_code, re.IGNORECASE | re.MULTILINE)
            
            for match in matches:
                segment = match.group(1).strip() if match.group(1) else ""
                where_clause = match.group(2).strip() if len(match.groups()) > 1 and match.group(2) else None
                into_clause = match.group(3).strip() if len(match.groups()) > 2 and match.group(3) else None
                
                operation = DatabaseOperation(
                    operation_type='IMS_DLI',
                    operation_subtype=operation_type,
                    cobol_code=match.group(0),
                    table_or_segment=segment,
                    description=self._get_dli_description(operation_type, segment),
                    parameters={
                        'segment': segment,
                        'where_clause': where_clause,
                        'into_clause': into_clause
                    }
                )
                operations.append(operation)
        
        return operations

    def _detect_db2_operations(self, cobol_code: str) -> List[DatabaseOperation]:
        """Detect IBM DB2 SQL operations."""
        operations = []
        
        for operation_type, pattern in self.db2_patterns.items():
            matches = re.finditer(pattern, cobol_code, re.IGNORECASE | re.MULTILINE | re.DOTALL)
            
            for match in matches:
                if operation_type == 'SELECT':
                    columns = match.group(1).strip() if match.group(1) else "*"
                    table = match.group(2).strip() if match.group(2) else ""
                    where_clause = match.group(3).strip() if len(match.groups()) > 2 and match.group(3) else None
                    isolation = match.group(4).strip() if len(match.groups()) > 3 and match.group(4) else None
                    
                    parameters = {
                        'columns': columns,
                        'table': table,
                        'where_clause': where_clause,
                        'isolation': isolation,
                        'host_variables': self._extract_host_variables(match.group(0))
                    }
                elif operation_type == 'INSERT':
                    table = match.group(1).strip() if match.group(1) else ""
                    columns = match.group(2).strip() if len(match.groups()) > 1 and match.group(2) else None
                    values = match.group(3).strip() if len(match.groups()) > 2 and match.group(3) else ""
                    
                    parameters = {
                        'table': table,
                        'columns': columns,
                        'values': values,
                        'host_variables': self._extract_host_variables(match.group(0))
                    }
                elif operation_type in ['UPDATE', 'DELETE']:
                    table = match.group(1).strip() if match.group(1) else ""
                    set_clause = match.group(2).strip() if operation_type == 'UPDATE' and len(match.groups()) > 1 and match.group(2) else None
                    where_clause = match.group(2 if operation_type == 'DELETE' else 3).strip() if len(match.groups()) > (1 if operation_type == 'DELETE' else 2) else None
                    
                    parameters = {
                        'table': table,
                        'set_clause': set_clause,
                        'where_clause': where_clause,
                        'host_variables': self._extract_host_variables(match.group(0))
                    }
                else:
                    parameters = {}
                
                operation = DatabaseOperation(
                    operation_type='DB2_SQL',
                    operation_subtype=operation_type,
                    cobol_code=match.group(0),
                    table_or_segment=parameters.get('table', ''),
                    description=self._get_db2_description(operation_type, parameters.get('table', '')),
                    parameters=parameters
                )
                operations.append(operation)
        
        return operations

    def _detect_gsam_operations(self, cobol_code: str) -> List[DatabaseOperation]:
        """Detect GSAM file operations."""
        operations = []
        
        for operation_type, pattern in self.gsam_patterns.items():
            matches = re.finditer(pattern, cobol_code, re.IGNORECASE | re.MULTILINE)
            
            for match in matches:
                if operation_type == 'CALL_CBLTDLI':
                    parameters_str = match.group(1).strip() if match.group(1) else ""
                    parameters = {
                        'function_code': self._extract_function_code(parameters_str),
                        'pcb_name': self._extract_pcb_name(parameters_str),
                        'io_area': self._extract_io_area(parameters_str)
                    }
                    file_name = parameters.get('pcb_name', 'UNKNOWN_FILE')
                elif operation_type.startswith('FILE_'):
                    file_name = match.group(2 if operation_type == 'FILE_OPEN' else 1).strip() if len(match.groups()) >= (2 if operation_type == 'FILE_OPEN' else 1) else ""
                    parameters = {
                        'file_name': file_name,
                        'mode': match.group(1).strip() if operation_type == 'FILE_OPEN' and match.group(1) else None,
                        'into_area': match.group(2).strip() if operation_type == 'FILE_READ' and len(match.groups()) > 1 and match.group(2) else None,
                        'from_area': match.group(2).strip() if operation_type == 'FILE_WRITE' and len(match.groups()) > 1 and match.group(2) else None
                    }
                else:
                    file_name = ""
                    parameters = {}
                
                operation = DatabaseOperation(
                    operation_type='GSAM_FILE',
                    operation_subtype=operation_type,
                    cobol_code=match.group(0),
                    table_or_segment=file_name,
                    description=self._get_gsam_description(operation_type, file_name),
                    parameters=parameters
                )
                operations.append(operation)
        
        return operations

    def _extract_host_variables(self, sql_code: str) -> List[str]:
        """Extract host variables from SQL code."""
        host_var_pattern = r':([A-Za-z][A-Za-z0-9\-_]*)'
        matches = re.findall(host_var_pattern, sql_code)
        return list(set(matches))  # Remove duplicates

    def _extract_function_code(self, parameters_str: str) -> str:
        """Extract function code from CBLTDLI parameters."""
        # Look for quoted function codes like 'GU', 'GN', etc.
        func_pattern = r'[\'"]([A-Z]{1,4})[\'"]'
        match = re.search(func_pattern, parameters_str)
        return match.group(1) if match else ""

    def _extract_pcb_name(self, parameters_str: str) -> str:
        """Extract PCB name from CBLTDLI parameters."""
        # PCB is typically the second parameter
        parts = [p.strip() for p in parameters_str.split(',')]
        return parts[1] if len(parts) > 1 else ""

    def _extract_io_area(self, parameters_str: str) -> str:
        """Extract I/O area from CBLTDLI parameters."""
        # I/O area is typically the third parameter
        parts = [p.strip() for p in parameters_str.split(',')]
        return parts[2] if len(parts) > 2 else ""

    def _get_dli_description(self, operation_type: str, segment: str) -> str:
        """Get human-readable description for DLI operation with business context."""
        # Get business name for the segment if available
        business_name = self.ims_mapper.get_business_name(segment)
        segment_context = f"{business_name} ({segment})" if business_name else segment

        descriptions = {
            'GU': f"Get unique record from {segment_context}",
            'GN': f"Get next record from {segment_context}",
            'GHU': f"Get and hold unique record from {segment_context}",
            'GHN': f"Get and hold next record from {segment_context}",
            'ISRT': f"Insert record into {segment_context}",
            'DLET': f"Delete record from {segment_context}",
            'REPL': f"Replace record in {segment_context}"
        }
        return descriptions.get(operation_type, f"Unknown DLI operation on {segment_context}")

    def _get_db2_description(self, operation_type: str, table: str) -> str:
        """Get human-readable description for DB2 operation."""
        descriptions = {
            'SELECT': f"Select data from {table} table",
            'INSERT': f"Insert data into {table} table",
            'UPDATE': f"Update data in {table} table",
            'DELETE': f"Delete data from {table} table"
        }
        return descriptions.get(operation_type, f"Unknown DB2 operation on {table}")

    def _get_gsam_description(self, operation_type: str, file_name: str) -> str:
        """Get human-readable description for GSAM operation."""
        descriptions = {
            'CALL_CBLTDLI': f"GSAM file operation on {file_name}",
            'FILE_OPEN': f"Open file {file_name}",
            'FILE_READ': f"Read from file {file_name}",
            'FILE_WRITE': f"Write to file {file_name}",
            'FILE_CLOSE': f"Close file {file_name}"
        }
        return descriptions.get(operation_type, f"Unknown file operation on {file_name}")

    def classify_operations_for_template_selection(self, operations: List[DatabaseOperation]) -> Dict[str, Any]:
        """
        Classify detected operations for template selection.
        
        Args:
            operations: List of detected database operations
            
        Returns:
            Dict[str, Any]: Classification results for template selection
        """
        classification = {
            'has_database_operations': len(operations) > 0,
            'operation_types': set(),
            'primary_operation_type': None,
            'requires_repository': False,
            'requires_service_layer': False,
            'requires_transaction_management': False,
            'template_recommendations': []
        }
        
        if not operations:
            return classification
        
        # Analyze operation types
        dli_ops = [op for op in operations if op.operation_type == 'IMS_DLI']
        db2_ops = [op for op in operations if op.operation_type == 'DB2_SQL']
        gsam_ops = [op for op in operations if op.operation_type == 'GSAM_FILE']
        
        if dli_ops:
            classification['operation_types'].add('IMS_DLI')
            classification['requires_repository'] = True
            classification['requires_service_layer'] = True
            classification['requires_transaction_management'] = True
            classification['template_recommendations'].append('database_operations/ims_dli_conversion.j2')
        
        if db2_ops:
            classification['operation_types'].add('DB2_SQL')
            classification['requires_repository'] = True
            classification['requires_service_layer'] = True
            classification['requires_transaction_management'] = True
            classification['template_recommendations'].append('database_operations/db2_sql_conversion.j2')
        
        if gsam_ops:
            classification['operation_types'].add('GSAM_FILE')
            classification['template_recommendations'].append('database_operations/gsam_file_conversion.j2')
        
        # Determine primary operation type
        if len(dli_ops) >= len(db2_ops) and len(dli_ops) >= len(gsam_ops):
            classification['primary_operation_type'] = 'IMS_DLI'
        elif len(db2_ops) >= len(gsam_ops):
            classification['primary_operation_type'] = 'DB2_SQL'
        else:
            classification['primary_operation_type'] = 'GSAM_FILE'
        
        # Add repository and service generation recommendations
        if classification['requires_repository']:
            classification['template_recommendations'].append('generation/repositories/jpa_repository.j2')
        
        if classification['requires_service_layer']:
            classification['template_recommendations'].append('generation/services/database_service.j2')
        
        # Always add error handling for database operations
        classification['template_recommendations'].append('error_handling/database_exception_handling.j2')
        
        return classification

    def prepare_template_context(self, operations: List[DatabaseOperation], classification: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare context data for database operation templates.

        Args:
            operations: List of detected database operations
            classification: Operation classification results

        Returns:
            Dict[str, Any]: Template context data
        """
        context = {
            'database_operations': [],
            'dli_operations': [],
            'db2_operations': [],
            'gsam_operations': [],
            'has_database_operations': classification['has_database_operations'],
            'operation_types': list(classification['operation_types']),
            'primary_operation_type': classification['primary_operation_type'],
            'requires_repository': classification['requires_repository'],
            'requires_service_layer': classification['requires_service_layer'],
            'requires_transaction_management': classification['requires_transaction_management'],
            'ims_segment_mappings': {}  # Will be populated with business mappings
        }
        
        # Group operations by type and collect IMS segment mappings
        ims_segments = set()
        for operation in operations:
            op_dict = {
                'type': operation.operation_subtype,
                'description': operation.description,
                'cobol_code': operation.cobol_code,
                'table_or_segment': operation.table_or_segment,
                **operation.parameters
            }

            # Add business context for IMS segments
            if operation.operation_type == 'IMS_DLI':
                segment_name = operation.table_or_segment
                business_name = self.ims_mapper.get_business_name(segment_name)
                if business_name:
                    op_dict['business_name'] = business_name
                    op_dict['javadoc_comment'] = self.ims_mapper.generate_javadoc_comment(segment_name)
                    op_dict['traceability_comment'] = self.ims_mapper.generate_traceability_comment(segment_name)
                    ims_segments.add(segment_name)

            context['database_operations'].append(op_dict)

            if operation.operation_type == 'IMS_DLI':
                context['dli_operations'].append(op_dict)
            elif operation.operation_type == 'DB2_SQL':
                context['db2_operations'].append(op_dict)
            elif operation.operation_type == 'GSAM_FILE':
                context['gsam_operations'].append(op_dict)

        # Add IMS segment business mappings to context
        context['ims_segment_mappings'] = self.ims_mapper.get_business_context_for_segments(ims_segments)

        return context
