"""
Project structure management for JavaProjectManager.
Handles creation and validation of Maven Spring Boot project directory structure.
"""
import os
import logging
from typing import Dict, Any, List


class ProjectStructureManager:
    """
    Manages the creation and validation of Java Spring Boot project directory structure.
    Ensures proper Maven conventions and Spring Boot best practices.
    """
    
    def __init__(self, default_package: str):
        """
        Initialize the structure manager.
        
        Args:
            default_package: Default Java package name
        """
        self.default_package = default_package
        self.logger = logging.getLogger(__name__)
    
    def set_logger(self, logger):
        """Set logger for the structure manager."""
        self.logger = logger
    
    def create_project_structure(self, project_dir: str) -> None:
        """
        Create standard Maven Spring Boot project structure.

        Args:
            project_dir: Root directory for the Java project
        """
        try:
            # Create main directory structure
            directories = [
                "src/main/java",
                "src/main/resources",
                "src/main/resources/static",
                "src/main/resources/templates",
                "src/test/java",
                "src/test/resources",
                "target",
                "logs",
                "docs"
            ]

            for directory in directories:
                full_path = os.path.join(project_dir, directory)
                os.makedirs(full_path, exist_ok=True)
                self.logger.debug(f"Created directory: {full_path}")

            # Create package structure
            self._create_package_structure(project_dir)
            
            self.logger.info(f"Successfully created project structure in {project_dir}")
            
        except Exception as e:
            self.logger.error(f"Error creating project structure: {str(e)}")
            raise
    
    def _create_package_structure(self, project_dir: str) -> None:
        """
        Create Java package directory structure.
        
        Args:
            project_dir: Root directory for the Java project
        """
        # Create main package structure
        package_path = self.default_package.replace(".", "/")
        base_package_dir = os.path.join(project_dir, "src/main/java", package_path)
        
        # Create package subdirectories
        package_subdirs = [
            "",  # Base package
            "service",  # Business logic services
            "model",    # Data transfer objects (DTOs)
            "config",   # Configuration classes
            "controller",  # REST controllers (if needed)
            "repository",  # Data access layer (if needed)
            "exception",   # Custom exceptions
            "util"      # Utility classes
        ]
        
        for subdir in package_subdirs:
            full_path = os.path.join(base_package_dir, subdir)
            os.makedirs(full_path, exist_ok=True)
            self.logger.debug(f"Created package directory: {full_path}")
        
        # Create test package structure
        test_package_dir = os.path.join(project_dir, "src/test/java", package_path)
        test_subdirs = [
            "",
            "service",
            "controller",
            "integration"
        ]
        
        for subdir in test_subdirs:
            full_path = os.path.join(test_package_dir, subdir)
            os.makedirs(full_path, exist_ok=True)
            self.logger.debug(f"Created test package directory: {full_path}")
    
    def validate_project_structure(self, project_dir: str) -> Dict[str, Any]:
        """
        Validate the generated project structure.

        Args:
            project_dir: Root directory for the Java project

        Returns:
            Dict[str, Any]: Validation results
        """
        validation_result = {
            "valid": True,
            "missing_directories": [],
            "missing_files": [],
            "warnings": []
        }
        
        try:
            # Check required directories
            required_dirs = [
                "src/main/java",
                "src/main/resources",
                "src/test/java",
                "src/test/resources"
            ]
            
            for req_dir in required_dirs:
                full_path = os.path.join(project_dir, req_dir)
                if not os.path.exists(full_path):
                    validation_result["missing_directories"].append(req_dir)
                    validation_result["valid"] = False
            
            # Check package structure
            package_path = self.default_package.replace(".", "/")
            package_dirs = [
                f"src/main/java/{package_path}",
                f"src/main/java/{package_path}/service",
                f"src/main/java/{package_path}/model",
                f"src/main/java/{package_path}/config"
            ]
            
            for pkg_dir in package_dirs:
                full_path = os.path.join(project_dir, pkg_dir)
                if not os.path.exists(full_path):
                    validation_result["missing_directories"].append(pkg_dir)
                    validation_result["valid"] = False
            
            # Check for essential files
            essential_files = [
                "pom.xml",
                "src/main/resources/application.properties"
            ]
            
            for essential_file in essential_files:
                full_path = os.path.join(project_dir, essential_file)
                if not os.path.exists(full_path):
                    validation_result["missing_files"].append(essential_file)
                    validation_result["valid"] = False
            
            # Check for recommended files
            recommended_files = [
                "README.md",
                ".gitignore",
                f"src/main/java/{package_path}/CobolConversionApplication.java"
            ]
            
            for rec_file in recommended_files:
                full_path = os.path.join(project_dir, rec_file)
                if not os.path.exists(full_path):
                    validation_result["warnings"].append(f"Recommended file missing: {rec_file}")
            
            self.logger.info(f"Project structure validation completed. Valid: {validation_result['valid']}")
            
        except Exception as e:
            self.logger.error(f"Error validating project structure: {str(e)}")
            validation_result["valid"] = False
            validation_result["warnings"].append(f"Validation error: {str(e)}")
        
        return validation_result
    
    def get_package_directory(self, project_dir: str, package_name: str = None) -> str:
        """
        Get the full path to a package directory.
        
        Args:
            project_dir: Root directory for the Java project
            package_name: Package name (uses default if not provided)
            
        Returns:
            str: Full path to package directory
        """
        package_name = package_name or self.default_package
        package_path = package_name.replace(".", "/")
        return os.path.join(project_dir, "src/main/java", package_path)
    
    def get_test_package_directory(self, project_dir: str, package_name: str = None) -> str:
        """
        Get the full path to a test package directory.
        
        Args:
            project_dir: Root directory for the Java project
            package_name: Package name (uses default if not provided)
            
        Returns:
            str: Full path to test package directory
        """
        package_name = package_name or self.default_package
        package_path = package_name.replace(".", "/")
        return os.path.join(project_dir, "src/test/java", package_path)
    
    def get_resources_directory(self, project_dir: str) -> str:
        """
        Get the full path to the resources directory.
        
        Args:
            project_dir: Root directory for the Java project
            
        Returns:
            str: Full path to resources directory
        """
        return os.path.join(project_dir, "src/main/resources")
    
    def get_test_resources_directory(self, project_dir: str) -> str:
        """
        Get the full path to the test resources directory.
        
        Args:
            project_dir: Root directory for the Java project
            
        Returns:
            str: Full path to test resources directory
        """
        return os.path.join(project_dir, "src/test/resources")
    
    def list_generated_files(self, project_dir: str) -> Dict[str, List[str]]:
        """
        List all generated files in the project.
        
        Args:
            project_dir: Root directory for the Java project
            
        Returns:
            Dict[str, List[str]]: Categorized list of generated files
        """
        files = {
            "java_files": [],
            "resource_files": [],
            "config_files": [],
            "documentation": []
        }
        
        try:
            # Find Java files
            java_src_dir = os.path.join(project_dir, "src/main/java")
            if os.path.exists(java_src_dir):
                for root, dirs, filenames in os.walk(java_src_dir):
                    for filename in filenames:
                        if filename.endswith('.java'):
                            rel_path = os.path.relpath(os.path.join(root, filename), project_dir)
                            files["java_files"].append(rel_path)
            
            # Find resource files
            resources_dir = os.path.join(project_dir, "src/main/resources")
            if os.path.exists(resources_dir):
                for root, dirs, filenames in os.walk(resources_dir):
                    for filename in filenames:
                        rel_path = os.path.relpath(os.path.join(root, filename), project_dir)
                        files["resource_files"].append(rel_path)
            
            # Find config files
            config_files = ["pom.xml", ".gitignore"]
            for config_file in config_files:
                full_path = os.path.join(project_dir, config_file)
                if os.path.exists(full_path):
                    files["config_files"].append(config_file)
            
            # Find documentation
            doc_files = ["README.md"]
            docs_dir = os.path.join(project_dir, "docs")
            if os.path.exists(docs_dir):
                for root, dirs, filenames in os.walk(docs_dir):
                    for filename in filenames:
                        rel_path = os.path.relpath(os.path.join(root, filename), project_dir)
                        files["documentation"].append(rel_path)
            
            for doc_file in doc_files:
                full_path = os.path.join(project_dir, doc_file)
                if os.path.exists(full_path):
                    files["documentation"].append(doc_file)
            
        except Exception as e:
            self.logger.error(f"Error listing generated files: {str(e)}")
        
        return files
