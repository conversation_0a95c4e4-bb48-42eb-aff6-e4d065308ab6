"""
Documentation generation functionality for JavaProjectManager.
Handles creation of README files and project documentation.
"""
import os
import datetime
import logging
from typing import Dict, Any, List


class DocumentationGenerator:
    """
    Handles generation of project documentation including README files.
    """

    def __init__(self):
        """Initialize the documentation generator."""
        self.logger = logging.getLogger(__name__)

    def set_logger(self, logger):
        """Set logger for the documentation generator."""
        self.logger = logger

    def generate_readme(self, project_dir: str, generation_state: Dict[str, Any], 
                       artifact_id: str, package_name: str, version: str) -> str:
        """
        Generate README.md file with actual project information based on generated classes.

        Args:
            project_dir: Root directory for the Java project
            generation_state: Current generation state
            artifact_id: Maven artifact ID
            package_name: Java package name
            version: Project version

        Returns:
            str: Path to the generated README.md file
        """
        try:
            readme_file = os.path.join(project_dir, "README.md")
            
            # Generate README content
            readme_content = self._generate_readme_content(
                generation_state, artifact_id, package_name, version
            )
            
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            self.logger.info(f"Generated README.md: {readme_file}")
            return readme_file
            
        except Exception as e:
            self.logger.error(f"Error generating README.md: {str(e)}")
            raise

    def generate_project_documentation(self, project_dir: str, generation_state: Dict[str, Any],
                                     artifact_id: str, package_name: str, version: str) -> List[str]:
        """
        Generate comprehensive project documentation.

        Args:
            project_dir: Root directory for the Java project
            generation_state: Current generation state
            artifact_id: Maven artifact ID
            package_name: Java package name
            version: Project version

        Returns:
            List[str]: List of paths to generated documentation files
        """
        try:
            docs_dir = os.path.join(project_dir, "docs")
            os.makedirs(docs_dir, exist_ok=True)
            
            generated_files = []
            
            # Generate README
            readme_path = self.generate_readme(project_dir, generation_state, artifact_id, package_name, version)
            generated_files.append(readme_path)
            
            # Generate API documentation
            api_doc_path = self._generate_api_documentation(docs_dir, generation_state)
            if api_doc_path:
                generated_files.append(api_doc_path)
            
            # Generate deployment guide
            deploy_doc_path = self._generate_deployment_guide(docs_dir, artifact_id)
            if deploy_doc_path:
                generated_files.append(deploy_doc_path)
            
            return generated_files
            
        except Exception as e:
            self.logger.error(f"Error generating project documentation: {str(e)}")
            raise

    def _generate_readme_content(self, generation_state: Dict[str, Any], 
                                artifact_id: str, package_name: str, version: str) -> str:
        """Generate README.md content."""

        try:
        
            generated_classes = generation_state.get("generated_chunks", [])

            content = f"""# {artifact_id.replace('-', ' ').title()}

Generated Java Spring Boot application from COBOL code conversion.

## Project Information

- **Version**: {version}
- **Package**: {package_name}
- **Generated on**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Overview

This Spring Boot application was automatically generated from COBOL source code. 
It maintains the business logic and functionality of the original COBOL programs 
while providing a modern Java-based implementation.

## Building and Running

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher

### Build

```bash
mvn clean compile
```

### Run

```bash
mvn spring-boot:run
```

### Package

```bash
mvn clean package
```


## Configuration

Application configuration can be modified in:
- `src/main/resources/application.properties`
- `src/main/resources/application.yml`

## Monitoring

The application includes Spring Boot Actuator for monitoring:
- Health check: `http://localhost:8080/actuator/health`
- Metrics: `http://localhost:8080/actuator/metrics`

"""
        except Exception as e:
            self.logger.error(f"Error generating README.md: {str(e)}")
            raise

        return content

    def _generate_api_documentation(self, docs_dir: str, generation_state: Dict[str, Any]) -> str:
        """Generate API documentation."""
        try:
            api_doc_file = os.path.join(docs_dir, "api.md")
            
            content = f"""# API Documentation

Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Overview

This document describes the REST API endpoints available in the converted application.

## Endpoints

"""
            
            generated_classes = generation_state.get("generated_chunks", [])
            if generated_classes:
                for chunk in generated_classes:
                    chunk_name = chunk.get('chunk_name', 'Unknown')
                    program_id = chunk.get('program_id', 'Unknown')
                    content += f"""
### {chunk_name}

**Program**: {program_id}

- **Endpoint**: `POST /api/{chunk_name.lower()}`
- **Description**: Converted from COBOL program {program_id}
- **Content-Type**: `application/json`

"""
            else:
                content += "No API endpoints have been generated yet.\n"
            
            with open(api_doc_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return api_doc_file
            
        except Exception as e:
            self.logger.error(f"Error generating API documentation: {str(e)}")
            return None

    def _generate_deployment_guide(self, docs_dir: str, artifact_id: str) -> str:
        """Generate deployment guide."""
        try:
            deploy_doc_file = os.path.join(docs_dir, "deployment.md")
            
            content = f"""# Deployment Guide

## Building for Production

1. Build the application:
   ```bash
   mvn clean package -Pprod
   ```

2. The executable JAR will be created in `target/{artifact_id}-{{version}}.jar`

## Running in Production

### As a JAR file

```bash
java -jar target/{artifact_id}-{{version}}.jar
```

### With specific profile

```bash
java -jar target/{artifact_id}-{{version}}.jar --spring.profiles.active=prod
```

### With custom configuration

```bash
java -jar target/{artifact_id}-{{version}}.jar --server.port=8090
```

## Docker Deployment

Create a Dockerfile:

```dockerfile
FROM openjdk:17-jre-slim
COPY target/{artifact_id}-{{version}}.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

Build and run:

```bash
docker build -t {artifact_id} .
docker run -p 8080:8080 {artifact_id}
```

## Environment Variables

- `SERVER_PORT`: Server port (default: 8080)
- `SPRING_PROFILES_ACTIVE`: Active Spring profile
- `LOGGING_LEVEL_ROOT`: Root logging level

## Health Checks

- Health endpoint: `/actuator/health`
- Ready endpoint: `/actuator/health/readiness`
- Live endpoint: `/actuator/health/liveness`
"""
            
            with open(deploy_doc_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return deploy_doc_file
            
        except Exception as e:
            self.logger.error(f"Error generating deployment guide: {str(e)}")
            return None
