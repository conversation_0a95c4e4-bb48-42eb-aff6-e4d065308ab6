"""
File generation functionality for JavaProjectManager.
Handles creation of Java files, configuration files, and project templates.
"""
import os
import datetime
import logging
from typing import Dict, Any, List


class ProjectFileGenerator:
    """
    Handles generation of Java files, configuration files, and project templates.
    Uses template manager for consistent file generation.
    """

    def __init__(self, template_manager, default_package: str, default_group_id: str,
                 default_artifact_id: str, default_version: str, java_version: str, spring_boot_version: str):
        """
        Initialize the file generator.

        Args:
            template_manager: Template manager instance
            default_package: Default Java package name
            default_group_id: Default Maven group ID
            default_artifact_id: Default Maven artifact ID
            default_version: Default project version
            java_version: Java version to use
            spring_boot_version: Spring Boot version to use
        """
        self.template_manager = template_manager
        self.default_package = default_package
        self.default_group_id = default_group_id
        self.default_artifact_id = default_artifact_id
        self.default_version = default_version
        self.java_version = java_version
        self.spring_boot_version = spring_boot_version
        self.logger = logging.getLogger(__name__)

    def set_logger(self, logger):
        """Set logger for the file generator."""
        self.logger = logger

    def generate_application_class(self, project_dir: str, package_name: str, class_name: str) -> str:
        """
        Generate the main Spring Boot application class.

        Args:
            project_dir: Root directory for the Java project
            package_name: Java package name
            class_name: Application class name

        Returns:
            str: Path to the generated application class
        """
        try:
            package_path = package_name.replace(".", "/")
            app_dir = os.path.join(project_dir, "src/main/java", package_path)
            app_file = os.path.join(app_dir, f"{class_name}.java")

            # Render template
            app_content = self.template_manager.render_template(
                "application_class.j2",
                {
                    "package_name": package_name,
                    "class_name": class_name,
                    "generation_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            )

            with open(app_file, 'w', encoding='utf-8') as f:
                f.write(app_content)

            self.logger.info(f"Generated application class: {app_file}")
            return app_file

        except Exception as e:
            self.logger.error(f"Error generating application class: {str(e)}")
            raise

    def generate_application_properties(self, project_dir: str, config: Dict[str, Any] = None) -> str:
        """
        Generate application.properties file with comprehensive configuration.

        Args:
            project_dir: Root directory for the Java project
            config: Additional configuration options

        Returns:
            str: Path to the generated application.properties file
        """
        try:
            resources_dir = os.path.join(project_dir, "src/main/resources")
            props_file = os.path.join(resources_dir, "application.properties")

            # Prepare template context
            template_context = {
                "app_name": self.default_artifact_id,
                "server_port": 8080,
                "package_name": self.default_package,
                "version": self.default_version,
                "generation_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # Merge with provided config
            if config:
                template_context.update(config)

            # Validate template context to prevent template errors
            template_context = self._validate_template_context(template_context)

            # Render template
            props_content = self.template_manager.render_template(
                "application.properties.j2",
                template_context
            )

            with open(props_file, 'w', encoding='utf-8') as f:
                f.write(props_content)

            self.logger.info(f"Generated application.properties: {props_file}")
            return props_file

        except Exception as e:
            self.logger.error(f"Error generating application.properties: {str(e)}")
            raise

    def generate_application_yml(self, project_dir: str, config: Dict[str, Any] = None) -> str:
        """
        Generate application.yml file with comprehensive configuration.

        Args:
            project_dir: Root directory for the Java project
            config: Additional configuration options

        Returns:
            str: Path to the generated application.yml file
        """
        try:
            resources_dir = os.path.join(project_dir, "src/main/resources")
            yml_file = os.path.join(resources_dir, "application.yml")

            # Prepare template context
            template_context = {
                "app_name": self.default_artifact_id,
                "server_port": 8080,
                "package_name": self.default_package,
                "version": self.default_version,
                "active_profile": "dev"
            }

            # Merge with provided config
            if config:
                template_context.update(config)

            # Validate template context to prevent template errors
            template_context = self._validate_template_context(template_context)

            # Render template
            yml_content = self.template_manager.render_template(
                "application.yml.j2",
                template_context
            )

            with open(yml_file, 'w', encoding='utf-8') as f:
                f.write(yml_content)

            self.logger.info(f"Generated application.yml: {yml_file}")
            return yml_file

        except Exception as e:
            self.logger.error(f"Error generating application.yml: {str(e)}")
            raise

    def generate_pom_xml(self, project_dir: str, config: Dict[str, Any] = None) -> str:
        """
        Generate Maven pom.xml file with required dependencies.

        Args:
            project_dir: Root directory for the Java project
            config: Additional configuration options

        Returns:
            str: Path to the generated pom.xml file
        """
        try:
            pom_file = os.path.join(project_dir, "pom.xml")

            # Prepare template context
            template_context = {
                "group_id": self.default_group_id,
                "artifact_id": self.default_artifact_id,
                "version": self.default_version,
                "project_name": "COBOL to Java Conversion",
                "project_description": "Spring Boot application generated from COBOL code",
                "java_version": self.java_version,
                "spring_boot_version": self.spring_boot_version,
                "include_file_processing": True,
                "include_json_processing": True,
                "include_xml_processing": False
            }

            # Merge with provided config
            if config:
                template_context.update(config)

            # Validate template context to prevent template errors
            template_context = self._validate_template_context(template_context)

            # Render template
            pom_content = self.template_manager.render_template(
                "pom.xml.j2",
                template_context
            )

            with open(pom_file, 'w', encoding='utf-8') as f:
                f.write(pom_content)

            self.logger.info(f"Generated pom.xml: {pom_file}")
            return pom_file

        except Exception as e:
            self.logger.error(f"Error generating pom.xml: {str(e)}")
            raise

    def generate_gitignore(self, project_dir: str, additional_patterns: List[str] = None) -> str:
        """
        Generate .gitignore file for Java/Maven project.

        Args:
            project_dir: Root directory for the Java project
            additional_patterns: Additional patterns to ignore

        Returns:
            str: Path to the generated .gitignore file
        """
        try:
            gitignore_file = os.path.join(project_dir, ".gitignore")

            # Prepare template context
            template_context = {
                "app_name": self.default_artifact_id,
                "custom_ignore_patterns": additional_patterns or []
            }

            # Validate template context to prevent template errors
            template_context = self._validate_template_context(template_context)

            # Render template
            gitignore_content = self.template_manager.render_template(
                "gitignore.j2",
                template_context
            )

            with open(gitignore_file, 'w', encoding='utf-8') as f:
                f.write(gitignore_content)

            self.logger.info(f"Generated .gitignore: {gitignore_file}")
            return gitignore_file

        except Exception as e:
            self.logger.error(f"Error generating .gitignore: {str(e)}")
            raise

    def save_java_file(self, java_code: str, package_name: str, class_name: str, project_dir: str) -> str:
        """
        Save Java code to file in proper package structure.

        Args:
            java_code: Java code to save
            package_name: Java package name
            class_name: Java class name
            project_dir: Root directory for the Java project

        Returns:
            str: Path to saved file
        """
        try:
            # Create package directory structure
            package_path = package_name.replace('.', os.sep)
            full_dir = os.path.join(project_dir, "src", "main", "java", package_path)
            os.makedirs(full_dir, exist_ok=True)

            # Save file
            file_path = os.path.join(full_dir, f"{class_name}.java")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(java_code)

            self.logger.info(f"Saved Java file: {file_path}")
            return file_path

        except Exception as e:
            self.logger.error(f"Error saving Java file: {str(e)}")
            raise

    def create_service_class(self, project_dir: str, class_name: str, business_name: str,
                           input_params: List[Dict[str, Any]], output_params: List[Dict[str, Any]],
                           business_logic: str, functional_spec: str) -> str:
        """
        Create a Spring Boot service class from COBOL business logic.

        Args:
            project_dir: Root directory for the Java project
            class_name: Name of the service class
            business_name: Business-oriented name for the service
            input_params: List of input parameters
            output_params: List of output parameters
            business_logic: Business logic description
            functional_spec: Functional specification

        Returns:
            str: Path to the generated service class
        """
        try:
            # Render template
            template_context = {
                "package_name": f"{self.default_package}.service",
                "class_name": class_name,
                "business_name": business_name,
                "input_params": input_params,
                "output_params": output_params,
                "business_logic": business_logic,
                "functional_spec": functional_spec,
                "generation_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            template_context = self._validate_template_context(template_context)
            service_content = self.template_manager.render_template(
                "service_class.j2",
                template_context
            )

            return self.save_java_file(service_content, f"{self.default_package}.service", class_name, project_dir)

        except Exception as e:
            self.logger.error(f"Error creating service class: {str(e)}")
            raise

    def create_dto_class(self, project_dir: str, class_name: str, fields: List[Dict[str, Any]]) -> str:
        """
        Create a Data Transfer Object (DTO) class.

        Args:
            project_dir: Root directory for the Java project
            class_name: Name of the DTO class
            fields: List of field definitions

        Returns:
            str: Path to the generated DTO class
        """
        try:
            # Render template
            template_context = {
                "package_name": f"{self.default_package}.model",
                "class_name": class_name,
                "fields": fields,
                "generation_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            template_context = self._validate_template_context(template_context)
            dto_content = self.template_manager.render_template(
                "dto_class.j2",
                template_context
            )

            return self.save_java_file(dto_content, f"{self.default_package}.model", class_name, project_dir)

        except Exception as e:
            self.logger.error(f"Error creating DTO class: {str(e)}")
            raise

    def create_configuration_class(self, project_dir: str, class_name: str, config_properties: Dict[str, Any]) -> str:
        """
        Create a Spring configuration class.

        Args:
            project_dir: Root directory for the Java project
            class_name: Name of the configuration class
            config_properties: Configuration properties

        Returns:
            str: Path to the generated configuration class
        """
        try:
            # Render template
            template_context = {
                "package_name": f"{self.default_package}.config",
                "class_name": class_name,
                "config_properties": config_properties,
                "generation_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            template_context = self._validate_template_context(template_context)
            config_content = self.template_manager.render_template(
                "config_class.j2",
                template_context
            )

            return self.save_java_file(config_content, f"{self.default_package}.config", class_name, project_dir)

        except Exception as e:
            self.logger.error(f"Error creating configuration class: {str(e)}")
            raise

    def _validate_template_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and fix template context to prevent template rendering errors.

        Args:
            context: Template context dictionary

        Returns:
            Dict[str, Any]: Validated and fixed context
        """
        validated_context = context.copy()

        # Ensure custom_properties is a dictionary, not a list
        if 'custom_properties' in validated_context:
            custom_props = validated_context['custom_properties']
            if isinstance(custom_props, list):
                self.logger.warning("custom_properties is a list, converting to empty dict to prevent template error")
                validated_context['custom_properties'] = {}
            elif not isinstance(custom_props, dict):
                self.logger.warning(f"custom_properties is {type(custom_props)}, converting to empty dict")
                validated_context['custom_properties'] = {}

        # Ensure custom_config is properly formatted for YAML template
        if 'custom_config' in validated_context:
            custom_config = validated_context['custom_config']
            if isinstance(custom_config, list):
                self.logger.warning("custom_config is a list, converting to empty dict to prevent template error")
                validated_context['custom_config'] = {}
            elif not isinstance(custom_config, dict):
                self.logger.warning(f"custom_config is {type(custom_config)}, converting to empty dict")
                validated_context['custom_config'] = {}

        return validated_context

    def generate_base_service_class(self, project_dir: str) -> str:
        """
        Generate a base service class with common functionality.

        Args:
            project_dir: Root directory for the Java project

        Returns:
            str: Path to the generated base service class
        """
        try:
            # Render template
            template_context = {
                "package_name": f"{self.default_package}.service",
                "class_name": "BaseService",
                "generation_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            template_context = self._validate_template_context(template_context)

            base_service_content = self.template_manager.render_template(
                "base_service_class.j2",
                template_context
            )

            return self.save_java_file(base_service_content, f"{self.default_package}.service", "BaseService", project_dir)

        except Exception as e:
            self.logger.error(f"Error creating base service class: {str(e)}")
            raise

    def generate_logback_config(self, project_dir: str) -> str:
        """
        Generate logback-spring.xml configuration file.

        Args:
            project_dir: Root directory for the Java project

        Returns:
            str: Path to the generated logback configuration file
        """
        try:
            resources_dir = os.path.join(project_dir, "src/main/resources")
            logback_file = os.path.join(resources_dir, "logback-spring.xml")

            # Prepare template context
            template_context = {
                "app_name": self.default_artifact_id,
                "package_name": self.default_package,
                "generation_date": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # Validate template context to prevent template errors
            template_context = self._validate_template_context(template_context)

            # Render template
            logback_content = self.template_manager.render_template(
                "logback-spring.xml.j2",
                template_context
            )

            with open(logback_file, 'w', encoding='utf-8') as f:
                f.write(logback_content)

            self.logger.info(f"Generated logback configuration: {logback_file}")
            return logback_file

        except Exception as e:
            self.logger.error(f"Error generating logback configuration: {str(e)}")
            raise
