"""
Core JavaProjectManager implementation.
Main orchestration for Java Spring Boot project structure management.
"""
import os
import logging
from typing import Dict, Any, List

from src.platform.interfaces.target_plugin import ProjectManager
from src.platform.tools.utils.template_manager import get_template_manager
from config.constants import (
    DEFAULT_JAVA_PACKAGE, DEFAULT_GROUP_ID, DEFAULT_ARTIFACT_ID,
    DEFAULT_VERSION, DEFAULT_JAVA_VERSION, SPRING_BOOT_VERSION
)

from .structure_manager import ProjectStructureManager
from .file_generator import ProjectFileGenerator
from .documentation_generator import DocumentationGenerator


class JavaProjectManager(ProjectManager):
    """
    Manages Java Spring Boot project structure, configuration files, and templates.
    Coordinates between structure management, file generation, and documentation.
    """

    def __init__(self):
        """Initialize the Java Project Manager with default configuration."""
        self.default_package = DEFAULT_JAVA_PACKAGE
        self.default_group_id = DEFAULT_GROUP_ID
        self.default_artifact_id = DEFAULT_ARTIFACT_ID
        self.default_version = DEFAULT_VERSION
        self.java_version = DEFAULT_JAVA_VERSION
        self.spring_boot_version = SPRING_BOOT_VERSION

        self.logger = logging.getLogger(__name__)
        self.template_manager = get_template_manager()

        # Initialize specialized components
        self.structure_manager = ProjectStructureManager(self.default_package)
        self.file_generator = ProjectFileGenerator(
            self.template_manager,
            self.default_package,
            self.default_group_id,
            self.default_artifact_id,
            self.default_version,
            self.java_version,
            self.spring_boot_version
        )
        self.documentation_generator = DocumentationGenerator()

    def set_logger(self, logger):
        """Set logger for the JavaProjectManager and its components."""
        self.logger = logger
        self.structure_manager.set_logger(logger)
        self.file_generator.set_logger(logger)
        self.documentation_generator.set_logger(logger)

    def create_project_structure(self, project_name: str, output_dir: str, config: Dict[str, Any]) -> bool:
        """
        Create target project structure.

        Args:
            project_name: Name of the project
            output_dir: Output directory
            config: Project configuration

        Returns:
            bool: True if successful
        """
        try:
            project_dir = os.path.join(output_dir, project_name)
            self.ensure_project_structure(project_dir)
            return True
        except Exception as e:
            self.logger.error(f"Error creating project structure: {str(e)}")
            return False

    def add_dependencies(self, dependencies: List[str]) -> bool:
        """
        Add dependencies to the project.

        Args:
            dependencies: List of dependencies to add

        Returns:
            bool: True if successful
        """
        try:
            # For now, dependencies are handled in pom.xml generation
            # This could be extended to dynamically modify pom.xml
            self.logger.info(f"Dependencies to add: {dependencies}")
            return True
        except Exception as e:
            self.logger.error(f"Error adding dependencies: {str(e)}")
            return False

    def ensure_project_structure(self, project_dir: str) -> None:
        """
        Create standard Maven Spring Boot project structure.

        Args:
            project_dir: Root directory for the Java project
        """
        self.logger.info(f"Creating Java project structure in {project_dir}")
        self.structure_manager.create_project_structure(project_dir)



    def create_main_application_class(self, project_dir: str, package_name: str = None, class_name: str = None) -> str:
        """
        Generate the main Spring Boot application class.

        Args:
            project_dir: Root directory for the Java project
            package_name: Java package name (optional, uses default if not provided)
            class_name: Application class name (optional, uses default if not provided)

        Returns:
            str: Path to the generated application class
        """
        package_name = package_name or self.default_package
        class_name = class_name or "CobolConversionApplication"

        return self.file_generator.generate_application_class(project_dir, package_name, class_name)

    def generate_application_properties(self, project_dir: str, config: Dict[str, Any] = None) -> str:
        """
        Generate application.properties file with comprehensive configuration.

        Args:
            project_dir: Root directory for the Java project
            config: Additional configuration options

        Returns:
            str: Path to the generated application.properties file
        """
        return self.file_generator.generate_application_properties(project_dir, config)

    def generate_application_yml(self, project_dir: str, config: Dict[str, Any] = None) -> str:
        """
        Generate application.yml file with comprehensive configuration.

        Args:
            project_dir: Root directory for the Java project
            config: Additional configuration options

        Returns:
            str: Path to the generated application.yml file
        """
        return self.file_generator.generate_application_yml(project_dir, config)

    def generate_pom_xml(self, project_dir: str, config: Dict[str, Any] = None) -> str:
        """
        Generate Maven pom.xml file with required dependencies.

        Args:
            project_dir: Root directory for the Java project
            config: Additional configuration options

        Returns:
            str: Path to the generated pom.xml file
        """
        return self.file_generator.generate_pom_xml(project_dir, config)

    def generate_gitignore(self, project_dir: str, additional_patterns: List[str] = None) -> str:
        """
        Generate .gitignore file for Java/Maven project.

        Args:
            project_dir: Root directory for the Java project
            additional_patterns: Additional patterns to ignore

        Returns:
            str: Path to the generated .gitignore file
        """
        return self.file_generator.generate_gitignore(project_dir, additional_patterns)

    def generate_readme(self, project_dir: str, generation_state: Dict[str, Any]) -> str:
        """
        Generate README.md file with actual project information based on generated classes.

        Args:
            project_dir: Root directory for the Java project
            generation_state: Current generation state

        Returns:
            str: Path to the generated README.md file
        """
        return self.documentation_generator.generate_readme(
            project_dir, generation_state, self.default_artifact_id, self.default_package, self.default_version
        )

    def save_java_file(self, java_code: str, package_name: str, class_name: str, project_dir: str) -> str:
        """
        Save Java code to file in proper package structure.

        Args:
            java_code: Java code to save
            package_name: Java package name
            class_name: Java class name
            project_dir: Root directory for the Java project

        Returns:
            str: Path to saved file
        """
        return self.file_generator.save_java_file(java_code, package_name, class_name, project_dir)

    def create_service_class(self, project_dir: str, class_name: str, business_name: str,
                           input_params: List[Dict[str, Any]], output_params: List[Dict[str, Any]],
                           business_logic: str, functional_spec: str) -> str:
        """
        Create a Spring Boot service class from COBOL business logic.

        Args:
            project_dir: Root directory for the Java project
            class_name: Name of the service class
            business_name: Business-oriented name for the service
            input_params: List of input parameters
            output_params: List of output parameters
            business_logic: Business logic description
            functional_spec: Functional specification

        Returns:
            str: Path to the generated service class
        """
        return self.file_generator.create_service_class(
            project_dir, class_name, business_name, input_params, output_params, business_logic, functional_spec
        )

    def create_dto_class(self, project_dir: str, class_name: str, fields: List[Dict[str, Any]]) -> str:
        """
        Create a Data Transfer Object (DTO) class.

        Args:
            project_dir: Root directory for the Java project
            class_name: Name of the DTO class
            fields: List of field definitions

        Returns:
            str: Path to the generated DTO class
        """
        return self.file_generator.create_dto_class(project_dir, class_name, fields)

    def create_configuration_class(self, project_dir: str, class_name: str, config_properties: Dict[str, Any]) -> str:
        """
        Create a Spring configuration class.

        Args:
            project_dir: Root directory for the Java project
            class_name: Name of the configuration class
            config_properties: Configuration properties

        Returns:
            str: Path to the generated configuration class
        """
        return self.file_generator.create_configuration_class(project_dir, class_name, config_properties)

    def generate_project_documentation(self, project_dir: str, generation_state: Dict[str, Any]) -> List[str]:
        """
        Generate comprehensive project documentation.

        Args:
            project_dir: Root directory for the Java project
            generation_state: Current generation state

        Returns:
            List[str]: List of paths to generated documentation files
        """
        return self.documentation_generator.generate_project_documentation(
            project_dir, generation_state, self.default_artifact_id, self.default_package, self.default_version
        )

    def validate_project_structure(self, project_dir: str) -> Dict[str, Any]:
        """
        Validate the generated project structure.

        Args:
            project_dir: Root directory for the Java project

        Returns:
            Dict[str, Any]: Validation results
        """
        return self.structure_manager.validate_project_structure(project_dir)

    def get_project_info(self, project_dir: str) -> Dict[str, Any]:
        """
        Get information about the generated project.

        Args:
            project_dir: Root directory for the Java project

        Returns:
            Dict[str, Any]: Project information
        """
        return {
            "project_dir": project_dir,
            "package": self.default_package,
            "group_id": self.default_group_id,
            "artifact_id": self.default_artifact_id,
            "version": self.default_version,
            "java_version": self.java_version,
            "spring_boot_version": self.spring_boot_version,
            "structure_valid": self.validate_project_structure(project_dir)
        }

    def save_java_code_to_file(self, java_code: str, chunk_info: Dict[str, Any], java_project_dir: str) -> Dict[str, Any]:
        """
        Save generated Java code to file with proper business naming and structure.

        Args:
            java_code: Generated Java code
            chunk_info: Chunk information
            java_project_dir: Java project directory

        Returns:
            Dict[str, Any]: Java class information
        """
        try:
            # Extract actual class name from generated code
            class_info = self._extract_class_info_from_java_code(java_code)
            actual_class_name = class_info["class_name"]

            # Determine file path using actual class name
            package_dir = self.get_service_package_path(java_project_dir)

            # Create directory if it doesn't exist
            os.makedirs(package_dir, exist_ok=True)

            # Write Java file with actual class name
            file_path = os.path.join(package_dir, f"{actual_class_name}.java")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(java_code)

            self.logger.info(f"Saved Java class {actual_class_name} to {file_path}")

            # Update class info
            class_info.update({
                "file_path": file_path,
                "chunk_info": chunk_info,
                "business_name": chunk_info.get("business_name", "Unknown Function"),
                "package": self.default_package + ".service",
                "original_cobol": f"{chunk_info['program_id']}.{chunk_info['chunk_name']}"
            })

            return class_info

        except Exception as e:
            self.logger.error(f"Error saving Java code: {str(e)}")
            return {}

    def finalize_project(self, java_project_dir: str, generation_state: Dict[str, Any]) -> None:
        """
        Finalize the Java project structure and configuration.

        Args:
            java_project_dir: Java project directory
            generation_state: Current generation state
        """
        try:
            # Generate Spring Boot application class with required parameters
            package_name = self.default_package
            class_name = "CobolConversionApplication"
            app_path = self.file_generator.generate_application_class(java_project_dir, package_name, class_name)
            self.logger.info(f"Generated application class: {app_path}")

            # Generate application.properties
            props_path = self.generate_application_properties(java_project_dir)
            self.logger.info(f"Generated application.properties: {props_path}")

            # Generate pom.xml with proper dependencies including Lombok and SLF4J
            pom_path = self.generate_pom_xml(java_project_dir)
            self.logger.info(f"Generated pom.xml: {pom_path}")

            # Generate .gitignore
            gitignore_path = self.generate_gitignore(java_project_dir)
            self.logger.info(f"Generated .gitignore: {gitignore_path}")

            # Generate base service class with Lombok and SLF4J
            base_service_path = self.generate_base_service_class(java_project_dir)
            self.logger.info(f"Generated base service: {base_service_path}")

            # Generate logback configuration
            logback_path = self.generate_logback_config(java_project_dir)
            self.logger.info(f"Generated logback config: {logback_path}")

            # Generate README.md with actual project structure
            readme_path = self.generate_readme(java_project_dir, generation_state)
            self.logger.info(f"Generated README: {readme_path}")

        except Exception as e:
            self.logger.error(f"Error finalizing project: {str(e)}")
            raise

    def get_service_package_path(self, java_project_dir: str) -> str:
        """
        Get the service package path for the Java project.

        Args:
            java_project_dir: Java project directory

        Returns:
            str: Service package path
        """
        package_path = self.default_package.replace(".", "/")
        return os.path.join(java_project_dir, "src/main/java", package_path, "service")

    def _extract_class_info_from_java_code(self, java_code: str) -> Dict[str, Any]:
        """
        Extract class information from Java code.

        Args:
            java_code: Java code

        Returns:
            Dict[str, Any]: Class information
        """
        import re

        class_info = {
            "class_name": "",  # Don't default to "UnknownClass" - let extraction fail if no class found
            "package": "",
            "method_name": "",
            "description": ""
        }

        try:
            # Extract package
            package_match = re.search(r'package\s+([^;]+);', java_code)
            if package_match:
                class_info["package"] = package_match.group(1).strip()

            # Extract class name
            class_match = re.search(r'public\s+class\s+(\w+)', java_code)
            if class_match:
                class_info["class_name"] = class_match.group(1)

            # Extract main public method name
            method_match = re.search(r'public\s+\w+\s+(\w+)\s*\([^)]*\)', java_code)
            if method_match:
                class_info["method_name"] = method_match.group(1)

            # Extract class description from JavaDoc
            javadoc_match = re.search(r'/\*\*\s*\n\s*\*\s*([^*\n].*?)\n\s*\*/', java_code, re.DOTALL)
            if javadoc_match:
                class_info["description"] = javadoc_match.group(1).strip()

            return class_info

        except Exception as e:
            self.logger.error(f"Error extracting class info: {str(e)}")
            return class_info

    def generate_base_service_class(self, project_dir: str) -> str:
        """
        Generate a base service class with common functionality.

        Args:
            project_dir: Root directory for the Java project

        Returns:
            str: Path to the generated base service class
        """
        return self.file_generator.generate_base_service_class(project_dir)

    def generate_logback_config(self, project_dir: str) -> str:
        """
        Generate logback-spring.xml configuration file.

        Args:
            project_dir: Root directory for the Java project

        Returns:
            str: Path to the generated logback configuration file
        """
        return self.file_generator.generate_logback_config(project_dir)
