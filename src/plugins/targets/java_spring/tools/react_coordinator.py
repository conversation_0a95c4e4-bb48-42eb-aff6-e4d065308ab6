"""
Java Spring Boot React agent coordination logic.
Extracted from plugin.py for better separation of concerns.
"""
import logging
from typing import Dict, Any

from src.platform.agents.base_agent import AgentInput


class JavaReactCoordinator:
    """
    Handles React agent coordination logic for Java Spring Boot code generation.
    Manages action analysis, decision making, and execution coordination.
    """

    def __init__(self):
        """Initialize the Java React coordinator."""
        self.logger = logging.getLogger(__name__)

    def analyze_and_decide_action(self, generation_state: Dict[str, Any], input_data: AgentInput) -> Dict[str, Any]:
        """
        Analyze current state and decide the next action using React methodology.

        Args:
            generation_state: Current generation state
            input_data: Input data

        Returns:
            Dict[str, Any]: Action to execute
        """
        current_phase = generation_state.get("current_phase", "analysis")

        if current_phase == "analysis":
            # Check if we need to analyze programs and build dependency graph
            if not generation_state.get("generation_order"):
                return {
                    "type": "analyze_dependencies",
                    "description": "Analyze COBOL chunks and build dependency graph"
                }
            else:
                return {
                    "type": "transition_to_data_classes",
                    "description": "Move to data class generation phase"
                }

        elif current_phase == "data_classes":
            # Check if we have copybooks left to generate data classes for
            if not generation_state.get("data_classes_generated", False):
                return {
                    "type": "generate_data_classes",
                    "description": "Generate Java data classes from COBOL copybooks"
                }
            else:
                return {
                    "type": "transition_to_generation",
                    "description": "Move to code generation phase"
                }

        elif current_phase == "generation":
            # Check if we have chunks left to generate
            current_index = generation_state.get("current_chunk_index", 0)
            generation_order = generation_state.get("generation_order", [])

            if current_index < len(generation_order):
                chunk_info = generation_order[current_index]
                return {
                    "type": "generate_chunk",
                    "description": f"Generate Java code for chunk {chunk_info['program_id']}.{chunk_info['chunk_name']}",
                    "chunk_info": chunk_info
                }
            else:
                return {
                    "type": "transition_to_finalization",
                    "description": "Move to project finalization phase"
                }

        elif current_phase == "finalization":
            # Check if finalization is already done
            if generation_state.get("finalization_complete"):
                return {
                    "type": "complete",
                    "description": "Mark generation as completed"
                }
            else:
                return {
                    "type": "finalize_project",
                    "description": "Finalize Java project structure and configuration"
                }

        else:
            return {
                "type": "error",
                "description": f"Unknown phase: {current_phase}"
            }

    def should_continue_generation(self, generation_state: Dict[str, Any], iteration: int, max_iterations: int) -> bool:
        """
        Determine if generation should continue.

        Args:
            generation_state: Current generation state
            iteration: Current iteration number
            max_iterations: Maximum allowed iterations

        Returns:
            bool: True if generation should continue
        """
        if generation_state.get('completed', False):
            self.logger.info("Generation marked as completed")
            return False

        if iteration >= max_iterations:
            self.logger.warning(f"Reached maximum iterations ({max_iterations})")
            return False

        return True

    def validate_action_result(self, action: Dict[str, Any], result: Dict[str, Any]) -> bool:
        """
        Validate that an action result is acceptable.

        Args:
            action: The action that was executed
            result: The result of the action

        Returns:
            bool: True if result is valid and processing should continue
        """
        if not result.get("success", True):
            error_msg = result.get("error", "")
            
            # Check for critical errors that should stop execution
            critical_errors = [
                "COBOL analysis required",
                "COBOL preprocessing required"
            ]
            
            for critical_error in critical_errors:
                if critical_error in error_msg:
                    self.logger.error(f"Critical error detected: {error_msg}")
                    return False

        return True

    def get_generation_progress(self, generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get current generation progress information.

        Args:
            generation_state: Current generation state

        Returns:
            Dict[str, Any]: Progress information
        """
        total_chunks = len(generation_state.get("generation_order", []))
        current_index = generation_state.get("current_chunk_index", 0)
        generated_chunks = len(generation_state.get("generated_chunks", []))
        failed_chunks = len(generation_state.get("failed_chunks", []))

        progress_percentage = 0
        if total_chunks > 0:
            progress_percentage = (current_index / total_chunks) * 100

        return {
            "total_chunks": total_chunks,
            "current_index": current_index,
            "generated_chunks": generated_chunks,
            "failed_chunks": failed_chunks,
            "progress_percentage": progress_percentage,
            "current_phase": generation_state.get("current_phase", "unknown"),
            "completed": generation_state.get("completed", False)
        }

    def log_action_execution(self, action: Dict[str, Any]) -> None:
        """
        Log the execution of an action.

        Args:
            action: Action being executed
        """
        self.logger.info(f"Executing action: {action.get('description')}")
        self.logger.debug(f"Action type: {action.get('type')}")

    def log_iteration_start(self, iteration: int, generation_state: Dict[str, Any]) -> None:
        """
        Log the start of a React iteration.

        Args:
            iteration: Current iteration number
            generation_state: Current generation state
        """
        progress = self.get_generation_progress(generation_state)
        self.logger.info(f"React iteration {iteration}")
        self.logger.info(f"Phase: {progress['current_phase']}, Progress: {progress['progress_percentage']:.1f}% ({progress['current_index']}/{progress['total_chunks']})")

    def log_generation_summary(self, generation_state: Dict[str, Any], total_time: float, iteration: int) -> None:
        """
        Log a summary of the generation process.

        Args:
            generation_state: Final generation state
            total_time: Total processing time
            iteration: Final iteration count
        """
        progress = self.get_generation_progress(generation_state)
        
        if generation_state.get('completed', False):
            self.logger.info("Code generation completed successfully")
            self.logger.info(f"Total processing time: {total_time:.2f} seconds")
            self.logger.info(f"Processed {progress['generated_chunks']} chunks successfully")
            self.logger.info(f"Failed chunks: {progress['failed_chunks']}")
        else:
            self.logger.info(f"Code generation incomplete after {iteration} iterations")
            self.logger.info(f"Partial processing time: {total_time:.2f} seconds")
            self.logger.info(f"Progress: {progress['progress_percentage']:.1f}% ({progress['current_index']}/{progress['total_chunks']})")

    def create_success_result(self, generation_state: Dict[str, Any], java_project_dir: str, total_time: float) -> Dict[str, Any]:
        """
        Create a success result for completed generation.

        Args:
            generation_state: Final generation state
            java_project_dir: Java project directory path
            total_time: Total processing time

        Returns:
            Dict[str, Any]: Success result
        """
        progress = self.get_generation_progress(generation_state)
        
        return {
            "success": True,
            "message": "Successfully generated Java Spring Boot project",
            "artifacts": {"java_project_path": java_project_dir},
            "knowledge_base_updates": {"generation_state": generation_state},
            "performance_metrics": {
                "total_time_seconds": total_time,
                "chunks_processed": progress["generated_chunks"],
                "chunks_failed": progress["failed_chunks"]
            }
        }

    def create_partial_result(self, generation_state: Dict[str, Any], java_project_dir: str, total_time: float, iteration: int) -> Dict[str, Any]:
        """
        Create a partial result for incomplete generation.

        Args:
            generation_state: Current generation state
            java_project_dir: Java project directory path
            total_time: Partial processing time
            iteration: Current iteration count

        Returns:
            Dict[str, Any]: Partial result
        """
        return {
            "success": False,
            "message": f"Code generation incomplete after {iteration} iterations. State saved for continuation.",
            "artifacts": {"java_project_path": java_project_dir, "generation_state": generation_state},
            "knowledge_base_updates": {"generation_state": generation_state},
            "performance_metrics": {
                "partial_time_seconds": total_time,
                "iterations_completed": iteration
            }
        }

    def create_error_result(self, error: str, total_time: float) -> Dict[str, Any]:
        """
        Create an error result for failed generation.

        Args:
            error: Error message
            total_time: Processing time before failure

        Returns:
            Dict[str, Any]: Error result
        """
        return {
            "success": False,
            "message": f"Code generation failed: {error}",
            "errors": [error],
            "performance_metrics": {
                "failed_after_seconds": total_time
            }
        }

    def create_critical_error_result(self, result: Dict[str, Any], total_time: float) -> Dict[str, Any]:
        """
        Create a critical error result that should stop processing.

        Args:
            result: Original error result
            total_time: Processing time before failure

        Returns:
            Dict[str, Any]: Critical error result
        """
        return {
            "success": False,
            "error": result.get("error"),
            "reason": result.get("reason"),
            "missing_files": result.get("missing_files", []),
            "invalid_files": result.get("invalid_files", []),
            "generated_files": [],
            "processing_time": total_time
        }
