"""
Java Spring Boot dependency analysis and JSON validation.
Extracted from plugin.py for better separation of concerns.
"""
import json
import os
import logging
from typing import Dict, Any, List

from src.plugins.legacy.cobol.tools.dependency_analyzer import DependencyAnalyzer
from src.platform.tools.knowledge_database import KnowledgeDatabase


class JavaDependencyAnalyzer:
    """
    Handles dependency analysis and JSON validation for Java Spring Boot code generation.
    Delegates to existing DependencyAnalyzer for actual dependency extraction.
    """

    def __init__(self):
        """Initialize the Java dependency analyzer."""
        self.logger = logging.getLogger(__name__)
        self.knowledge_db = KnowledgeDatabase()
        self.json_analyzer = DependencyAnalyzer()

    def analyze_dependencies(self, generation_state: Dict[str, Any], input_data) -> Dict[str, Any]:
        """
        Analyze dependencies between COBOL chunks using JSON files.

        Args:
            generation_state: Current generation state
            input_data: Input data with working directory

        Returns:
            Dict[str, Any]: Analysis result with generation order and analyzed chunks
        """
        try:
            self.logger.info("Getting sorted chunks from JSON files")

            # Get database statistics for logging
            db_stats = self.knowledge_db.get_database_statistics()
            self.logger.info(f"Database statistics: {db_stats}")

            # Get all chunks to see what we have
            all_chunks = self.knowledge_db.get_all_chunks()
            self.logger.info(f"Total chunks in database: {len(all_chunks)}")

            if all_chunks:
                # Log sample chunk to see structure
                sample_chunk = all_chunks[0]
                self.logger.info(f"Sample chunk structure: {list(sample_chunk.keys())}")
                self.logger.info(f"Sample chunk business_name: {sample_chunk.get('business_name')}")
                self.logger.info(f"Sample chunk business_description: {sample_chunk.get('business_description')}")

            # Get all analyzed COBOL chunks from database
            analyzed_chunks = self.knowledge_db.get_analyzed_chunks()
            self.logger.info(f"Found {len(analyzed_chunks)} analyzed chunks")

            # If no analyzed chunks found, this means COBOL analysis hasn't run yet
            if not analyzed_chunks:
                self.logger.error("No analyzed chunks found in database. COBOL analysis must be run first before Java code generation.")
                self.logger.error("Please run the COBOL analysis phase to populate the knowledge database with analyzed chunks.")
                return {
                    "success": False,
                    "error": "COBOL analysis required before Java generation",
                    "reason": "No analyzed chunks available for code generation",
                    "chunks_to_generate": []
                }

            # Check for JSON files with preprocessed COBOL data
            self.logger.info("Validating JSON files with preprocessed COBOL data")
            json_validation_result = self._validate_json_files(analyzed_chunks, input_data.working_directory)
            if not json_validation_result["success"]:
                return json_validation_result

            # Build dependency graph and get sorted order
            all_generation_order = []
            dependency_graph = {}

            programs = {}
            for chunk in analyzed_chunks:
                program_id = chunk["program_id"]
                if program_id not in programs:
                    programs[program_id] = []
                programs[program_id].append(chunk)

            self.logger.info(f"Building dependency graph for {len(programs)} programs")

            for program_id in programs.keys():
                # Get sorted chunks for this program using DependencyAnalyzer
                sorted_chunks = self.json_analyzer.get_sorted_chunks_for_program(
                    program_id, input_data.working_directory, language="cobol"
                )

                # If no sorted chunks found, use the chunks directly from the database
                if not sorted_chunks:
                    self.logger.warning(f"No sorted chunks found for {program_id}, using database chunks directly")
                    sorted_chunks = programs[program_id]

                # Build dependency graph for this program
                program_dependencies = self._build_program_dependency_graph(
                    program_id, input_data.working_directory, analyzed_chunks
                )
                dependency_graph.update(program_dependencies)

                # Convert to chunk info format
                for chunk in sorted_chunks:
                    chunk_name = chunk["chunk_name"]
                    # Find corresponding analyzed chunk
                    analyzed_chunk = next(
                        (c for c in analyzed_chunks if c["chunk_name"] == chunk_name),
                        None
                    )

                    if analyzed_chunk:
                        chunk_info = {
                            "program_id": program_id,
                            "chunk_name": chunk_name,
                            "business_name": analyzed_chunk.get("business_name", chunk_name),  # Use chunk_name as fallback
                            "business_description": analyzed_chunk.get("business_description", f"Generated from {chunk_name}")  # Fallback description
                        }
                        all_generation_order.append(chunk_info)
                        self.logger.debug(f"Added chunk {program_id}.{chunk_name} to generation order")

            self.logger.info(f"Prepared generation order for {len(all_generation_order)} chunks")
            self.logger.info(f"Built dependency graph with {len(dependency_graph)} entries")

            # Update the generation_state with the dependency graph
            generation_state["dependency_graph"] = dependency_graph
            generation_state["generation_order"] = all_generation_order
            generation_state["analyzed_chunks"] = analyzed_chunks

            self.logger.info(f"Updated generation_state with dependency_graph containing {len(dependency_graph)} entries")

            return {
                "success": True,
                "dependency_graph": dependency_graph,
                "generation_order": all_generation_order,
                "analyzed_chunks": analyzed_chunks
            }

        except Exception as e:
            self.logger.error(f"Error analyzing dependencies: {str(e)}")
            return {"success": False, "error": str(e)}

    def _validate_json_files(self, analyzed_chunks: List[Dict[str, Any]], working_directory: str) -> Dict[str, Any]:
        """
        Validate that JSON files with preprocessed COBOL data exist and contain chunks.

        Args:
            analyzed_chunks: List of analyzed chunks from database
            working_directory: Working directory path

        Returns:
            Dict[str, Any]: Validation result
        """
        try:
            # Get unique program IDs from analyzed chunks
            program_ids = set(chunk["program_id"] for chunk in analyzed_chunks)
            self.logger.info(f"Validating JSON files for {len(program_ids)} programs: {list(program_ids)}")

            # Check JSON directory exists
            json_dir = os.path.join(working_directory, "preprocessed", "cobol", "json")
            if not os.path.exists(json_dir):
                self.logger.error(f"JSON directory not found: {json_dir}")
                self.logger.error("Please run the COBOL preprocessing phase to generate JSON files.")
                return {
                    "success": False,
                    "error": "COBOL preprocessing required before Java generation",
                    "reason": f"JSON directory not found: {json_dir}",
                    "missing_files": []
                }

            missing_files = []
            invalid_files = []

            # Check each program has a corresponding JSON file
            for program_id in program_ids:
                json_file_path = os.path.join(json_dir, f"{program_id}.json")
                self.logger.debug(f"Checking JSON file: {json_file_path}")

                if not os.path.exists(json_file_path):
                    missing_files.append(f"{program_id}.json")
                    self.logger.error(f"Missing JSON file: {json_file_path}")
                    continue

                # Validate JSON file content
                try:
                    with open(json_file_path, 'r') as f:
                        json_data = json.load(f)

                    # Check required structure
                    if not isinstance(json_data, dict):
                        invalid_files.append(f"{program_id}.json - not a valid JSON object")
                        continue

                    if "nodes" not in json_data:
                        invalid_files.append(f"{program_id}.json - missing 'nodes' array")
                        continue

                    if not isinstance(json_data["nodes"], list):
                        invalid_files.append(f"{program_id}.json - 'nodes' is not an array")
                        continue

                    if len(json_data["nodes"]) == 0:
                        invalid_files.append(f"{program_id}.json - 'nodes' array is empty")
                        continue

                    self.logger.debug(f"✅ Valid JSON file: {program_id}.json with {len(json_data['nodes'])} nodes")

                except json.JSONDecodeError as e:
                    invalid_files.append(f"{program_id}.json - invalid JSON: {str(e)}")
                    self.logger.error(f"Invalid JSON in {json_file_path}: {str(e)}")
                except Exception as e:
                    invalid_files.append(f"{program_id}.json - error reading file: {str(e)}")
                    self.logger.error(f"Error reading {json_file_path}: {str(e)}")

            # Report validation results
            if missing_files or invalid_files:
                error_details = []
                if missing_files:
                    error_details.append(f"Missing files: {', '.join(missing_files)}")
                if invalid_files:
                    error_details.append(f"Invalid files: {', '.join(invalid_files)}")

                self.logger.error("JSON file validation failed:")
                for detail in error_details:
                    self.logger.error(f"  - {detail}")
                self.logger.error("Please run the COBOL preprocessing phase to generate valid JSON files.")

                return {
                    "success": False,
                    "error": "COBOL preprocessing required before Java generation",
                    "reason": "Missing or invalid JSON files with preprocessed COBOL data",
                    "missing_files": missing_files,
                    "invalid_files": invalid_files
                }

            self.logger.info(f"✅ All JSON files validated successfully for {len(program_ids)} programs")
            return {"success": True, "validated_programs": list(program_ids)}

        except Exception as e:
            self.logger.error(f"Error validating JSON files: {str(e)}")
            return {"success": False, "error": f"JSON validation error: {str(e)}"}

    def _build_program_dependency_graph(self, program_id: str, working_directory: str, analyzed_chunks: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """
        Build dependency graph for a specific program using JSON analysis.

        Args:
            program_id: Program identifier
            working_directory: Working directory containing JSON files
            analyzed_chunks: List of analyzed chunks from database

        Returns:
            Dict[str, List[str]]: Dependency graph mapping chunk IDs to their dependencies
        """
        try:
            self.logger.info(f"Building dependency graph for program {program_id}")

            # Load JSON data for the program
            json_file_path = os.path.join(working_directory, "preprocessed", "cobol", "json", f"{program_id}.json")
            if not os.path.exists(json_file_path):
                self.logger.warning(f"JSON file not found for program {program_id}: {json_file_path}")
                return {}

            with open(json_file_path, 'r') as f:
                json_data = json.load(f)

            # Use the COBOL dependency analyzer to extract dependencies
            dependencies, name_to_uuid = self.json_analyzer._build_dependency_graph(
                {node['uuid']: node for node in json_data.get('nodes', [])},
                language="cobol"
            )

            # Build mapping from short paragraph names to full chunk names
            # Dependencies use short names like "ENTRY-PARAGRAPH"
            # Analyzed chunks use long names like "CBACT01C_PROC_UNNAMED-SECTION_SECT_ENTRY-PARAGRAPH"
            program_chunks = [chunk for chunk in analyzed_chunks if chunk["program_id"] == program_id]
            short_to_full_name_map = {}

            self.logger.info(f"Building name mapping for {len(program_chunks)} analyzed chunks")

            for chunk in program_chunks:
                full_chunk_name = chunk["chunk_name"]
                # Extract the paragraph name from the full chunk name
                # Format: {program_id}_PROC_{section}_SECT_{paragraph_name}
                if "_SECT_" in full_chunk_name:
                    paragraph_name = full_chunk_name.split("_SECT_")[-1]
                    short_to_full_name_map[paragraph_name] = full_chunk_name
                    self.logger.debug(f"Mapped short name '{paragraph_name}' to full name '{full_chunk_name}'")

            self.logger.info(f"Created name mapping for {len(short_to_full_name_map)} chunks")
            self.logger.debug(f"Short to full name mapping: {short_to_full_name_map}")
            self.logger.debug(f"Dependencies found from JSON: {dependencies}")

            # Convert to chunk ID format using the name mapping
            program_dependency_graph = {}

            for short_chunk_name, deps in dependencies.items():
                # Map short name to full chunk name
                full_chunk_name = short_to_full_name_map.get(short_chunk_name)
                if not full_chunk_name:
                    self.logger.debug(f"No mapping found for short chunk name: {short_chunk_name}")
                    continue

                chunk_id = f"{program_id}.{full_chunk_name}"
                self.logger.debug(f"Processing chunk: {short_chunk_name} -> {full_chunk_name}")

                # Filter dependencies to only include analyzed chunks
                filtered_deps = []
                for dep_short_name in deps:
                    dep_full_name = short_to_full_name_map.get(dep_short_name)
                    if dep_full_name:
                        dep_id = f"{program_id}.{dep_full_name}"
                        filtered_deps.append(dep_id)
                        self.logger.debug(f"Added dependency: {chunk_id} -> {dep_id}")
                    else:
                        self.logger.debug(f"No mapping found for dependency: {dep_short_name}")

                if filtered_deps:
                    program_dependency_graph[chunk_id] = filtered_deps
                    self.logger.info(f"Chunk {chunk_id} depends on: {filtered_deps}")

            self.logger.info(f"Built dependency graph for {program_id} with {len(program_dependency_graph)} entries")
            if program_dependency_graph:
                self.logger.info(f"Dependency graph keys: {list(program_dependency_graph.keys())}")
            else:
                self.logger.warning(f"Dependency graph is empty for {program_id}")
                self.logger.warning(f"Available short names from dependencies: {list(dependencies.keys())}")
                self.logger.warning(f"Available mappings: {list(short_to_full_name_map.keys())}")

            return program_dependency_graph

        except Exception as e:
            self.logger.error(f"Error building dependency graph for program {program_id}: {str(e)}")
            return {}

    def get_chunk_dependencies(self, chunk_info: Dict[str, Any], generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get dependency information for a chunk with actual Java names from mappings.
        Only includes dependencies that correspond to actual generated COBOL chunks.

        Args:
            chunk_info: Chunk information containing program_id and chunk_name
            generation_state: Current generation state containing dependency_graph

        Returns:
            Dict[str, Any]: Dependency information with real Java names

        Raises:
            ValueError: If dependency_graph is not found in generation_state
        """
        # Initialize return structure
        dependencies = {
            "dependent_services": [],
            "input_types": [],
            "output_types": []
        }

        try:
            # Log input parameters
            chunk_id = f"{chunk_info['program_id']}.{chunk_info['chunk_name']}"
            self.logger.info(f"Getting dependencies for chunk: {chunk_id}")
            self.logger.debug(f"Chunk info: {chunk_info}")
            self.logger.debug(f"Generation state keys: {list(generation_state.keys())}")

            # Check if dependency_graph exists in generation_state
            if "dependency_graph" not in generation_state:
                error_msg = f"dependency_graph not found in generation_state. Available keys: {list(generation_state.keys())}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            dependency_graph = generation_state["dependency_graph"]
            self.logger.info(f"Dependency graph contains {len(dependency_graph)} entries")
            self.logger.debug(f"Dependency graph keys: {list(dependency_graph.keys())}")

            # Check if dependency_graph is empty
            if not dependency_graph:
                error_msg = "dependency_graph is empty - no dependencies available for analysis"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # Check if current chunk has dependencies
            if chunk_id not in dependency_graph:
                self.logger.info(f"No dependencies found for chunk {chunk_id}")
                self.logger.debug(f"Available chunks in dependency graph: {list(dependency_graph.keys())}")
                return dependencies

            chunk_deps = dependency_graph[chunk_id]
            self.logger.info(f"Found {len(chunk_deps)} dependencies for chunk {chunk_id}: {chunk_deps}")

            # Process each dependency
            for dep_id in chunk_deps:
                self.logger.debug(f"Processing dependency: {dep_id}")

                # Parse dependency ID to get program and chunk
                parts = dep_id.split(".", 1)
                if len(parts) != 2:
                    self.logger.warning(f"Invalid dependency ID format: {dep_id} (expected format: program_id.chunk_name)")
                    continue

                dep_program_id, dep_chunk_name = parts
                self.logger.debug(f"Parsed dependency: program_id={dep_program_id}, chunk_name={dep_chunk_name}")

                # Get the chunk info from database
                dep_chunk = self.knowledge_db.get_chunk_by_name(dep_program_id, dep_chunk_name)

                if not dep_chunk:
                    self.logger.warning(f"Dependency chunk not found in database: {dep_id}")
                    continue

                self.logger.debug(f"Found dependency chunk: {dep_chunk.get('chunk_name')} with status: {dep_chunk.get('analysis_status')}")

                # Check if chunk is properly analyzed
                if dep_chunk.get("analysis_status") != "complete":
                    self.logger.warning(f"Dependency chunk {dep_id} is not fully analyzed (status: {dep_chunk.get('analysis_status')})")
                    continue

                # Get business name for Java conversion
                business_name = dep_chunk.get("business_name", "")
                if not business_name:
                    self.logger.warning(f"Dependency chunk {dep_id} has no business_name, skipping")
                    continue

                # Convert to Java names
                java_class_name = self._convert_business_name_to_java_class(business_name)
                java_method_name = self._convert_business_name_to_java_method(business_name)

                self.logger.debug(f"Converted '{business_name}' to Java class: {java_class_name}, method: {java_method_name}")

                # Create dependency entry
                dependency_entry = {
                    "class_name": java_class_name,
                    "method_name": java_method_name,
                    "java_class_name": java_class_name,
                    "java_method_name": java_method_name,
                    "description": dep_chunk.get("business_description", ""),
                    "business_name": business_name,
                    "cobol_chunk": dep_chunk_name,
                    "package": "com.generated.cobol.service"
                }

                dependencies["dependent_services"].append(dependency_entry)
                self.logger.info(f"Added dependency service: {java_class_name}.{java_method_name} for chunk {dep_id}")

            self.logger.info(f"Successfully processed {len(dependencies['dependent_services'])} dependencies for chunk {chunk_id}")
            return dependencies

        except Exception as e:
            self.logger.error(f"Error getting chunk dependencies for {chunk_id}: {str(e)}")
            self.logger.error(f"Exception type: {type(e).__name__}")
            # Re-raise the exception to ensure calling code knows about the failure
            raise

    def _convert_business_name_to_java_class(self, business_name: str) -> str:
        """Convert business name to Java class name as fallback."""
        import re
        words = re.findall(r'\b\w+\b', business_name)
        return ''.join(word.capitalize() for word in words) + "Service"

    def _convert_business_name_to_java_method(self, business_name: str) -> str:
        """Convert business name to Java method name as fallback."""
        import re
        words = re.findall(r'\b\w+\b', business_name)
        if not words:
            return "process"
        return words[0].lower() + ''.join(word.capitalize() for word in words[1:])
