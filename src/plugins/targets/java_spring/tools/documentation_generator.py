"""
Java Spring Boot documentation generator.
Handles generation of project documentation including README, API docs, etc.
"""
import os
import logging
from typing import Dict, Any, List

from config.constants import TEMPLATE_DIRS
from src.platform.interfaces.target_plugin import DocumentationGenerator
from src.platform.tools.utils.template_manager import TemplateManager


class JavaSpringDocumentationGenerator(DocumentationGenerator):
    """Java Spring Boot documentation generator."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Initialize template manager for documentation templates
        self.template_manager = TemplateManager(TEMPLATE_DIRS['java_spring_templates'])

    def generate_documentation(self, analysis: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate documentation for Java Spring Boot project.

        Args:
            analysis: Analysis data
            config: Configuration dictionary

        Returns:
            Dict[str, str]: Generated documentation files
        """
        self.logger.info("Starting documentation generation for Java Spring Boot project")

        try:
            docs = {}

            # Generate README
            self.logger.debug("Generating README.md")
            docs["README.md"] = self._generate_readme(analysis, config)

            # Generate API documentation
            self.logger.debug("Generating API.md")
            docs["API.md"] = self._generate_api_docs(analysis, config)

            # Generate deployment guide
            self.logger.debug("Generating DEPLOYMENT.md")
            docs["DEPLOYMENT.md"] = self._generate_deployment_guide(analysis, config)

            # Generate development guide
            self.logger.debug("Generating DEVELOPMENT.md")
            docs["DEVELOPMENT.md"] = self._generate_development_guide(analysis, config)

            self.logger.info(f"Generated {len(docs)} documentation files")
            return docs

        except Exception as e:
            self.logger.error(f"Error generating documentation: {str(e)}")
            return {}

    def _generate_readme(self, analysis: Dict[str, Any], config: Dict[str, Any]) -> str:
        """Generate README.md using template."""
        try:
            template_context = {
                "project_name": config.get("project_name", "Generated Project"),
                "description": config.get("description", "Generated Spring Boot application converted from legacy code"),
                "java_version": config.get("java_version", "17"),
                "maven_version": config.get("maven_version", "3.6"),
                "spring_boot_version": config.get("spring_boot_version", "3.2.0"),
                "package_name": config.get("package_name", "com.generated.app"),
                "analysis": analysis
            }

            return self.template_manager.render_template("readme.j2", template_context)

        except Exception as e:
            self.logger.error(f"Error generating README: {str(e)}")
            # Fallback to basic README
            return self._generate_basic_readme(config)

    def _generate_api_docs(self, analysis: Dict[str, Any], config: Dict[str, Any]) -> str:
        """Generate API documentation using template."""
        try:
            template_context = {
                "project_name": config.get("project_name", "Generated Project"),
                "base_url": config.get("base_url", "http://localhost:8080"),
                "analysis": analysis,
                "endpoints": self._extract_endpoints_from_analysis(analysis)
            }

            return self.template_manager.render_template("api_docs.j2", template_context)

        except Exception as e:
            self.logger.error(f"Error generating API docs: {str(e)}")
            # Fallback to basic API docs
            return self._generate_basic_api_docs(config)

    def _generate_deployment_guide(self, analysis: Dict[str, Any], config: Dict[str, Any]) -> str:
        """Generate deployment guide using template."""
        try:
            template_context = {
                "project_name": config.get("project_name", "Generated Project"),
                "artifact_id": config.get("artifact_id", "generated-app"),
                "java_version": config.get("java_version", "17"),
                "analysis": analysis
            }

            return self.template_manager.render_template("deployment_guide.j2", template_context)

        except Exception as e:
            self.logger.error(f"Error generating deployment guide: {str(e)}")
            return "# Deployment Guide\n\nDeployment guide generation failed. Please check logs for details."

    def _generate_development_guide(self, analysis: Dict[str, Any], config: Dict[str, Any]) -> str:
        """Generate development guide using template."""
        try:
            template_context = {
                "project_name": config.get("project_name", "Generated Project"),
                "package_name": config.get("package_name", "com.generated.app"),
                "analysis": analysis
            }

            return self.template_manager.render_template("development_guide.j2", template_context)

        except Exception as e:
            self.logger.error(f"Error generating development guide: {str(e)}")
            return "# Development Guide\n\nDevelopment guide generation failed. Please check logs for details."

    def _extract_endpoints_from_analysis(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract API endpoints from analysis data."""
        endpoints = []

        # Extract from knowledge base if available
        knowledge_base = analysis.get("knowledge_base", {})
        business_logic = knowledge_base.get("business_logic_summaries", {})

        for program_id, chunks in business_logic.items():
            for chunk_name, logic in chunks.items():
                # Create endpoint based on business logic
                endpoint = {
                    "path": f"/{program_id.lower()}/{chunk_name.lower()}",
                    "method": "POST",
                    "description": f"Process {chunk_name} from {program_id}",
                    "business_logic": logic[:200] + "..." if len(logic) > 200 else logic
                }
                endpoints.append(endpoint)

        return endpoints

    def _generate_basic_readme(self, config: Dict[str, Any]) -> str:
        """Generate basic README as fallback."""
        project_name = config.get("project_name", "Generated Project")

        return f"""# {project_name}

This is a generated Spring Boot application converted from legacy code.

## Getting Started

### Prerequisites
- Java 17 or higher
- Maven 3.6 or higher

### Running the Application
```bash
mvn spring-boot:run
```

### Building the Application
```bash
mvn clean package
```

## API Endpoints

See API.md for detailed API documentation.
"""

    def _generate_basic_api_docs(self, config: Dict[str, Any]) -> str:
        """Generate basic API documentation as fallback."""
        return """# API Documentation

## Endpoints

### GET /
Returns application status.

**Response:**
```
Application is running
```

### Health Check
- **GET /actuator/health** - Application health status

## Error Responses

All endpoints may return the following error responses:

- **400 Bad Request** - Invalid request parameters
- **500 Internal Server Error** - Server error

## Authentication

Currently, no authentication is required for these endpoints.
"""
