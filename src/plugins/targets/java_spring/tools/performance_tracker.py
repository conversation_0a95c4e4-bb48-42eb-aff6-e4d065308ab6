"""
Java Spring Boot performance tracking and metrics.
Extracted from plugin.py for better separation of concerns.
"""
import logging
import time
from typing import Dict, Any


class JavaPerformanceTracker:
    """
    Handles performance tracking and metrics for Java Spring Boot code generation.
    Tracks processing times, success rates, and other performance indicators.
    """

    def __init__(self):
        """Initialize the Java performance tracker."""
        self.logger = logging.getLogger(__name__)
        self._performance_metrics = {
            "total_chunks_processed": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "total_processing_time": 0.0,
            "average_chunk_time": 0.0
        }

    def start_processing(self) -> float:
        """
        Start tracking processing time.

        Returns:
            float: Start time timestamp
        """
        return time.time()

    def end_processing(self, start_time: float) -> float:
        """
        End tracking processing time and update metrics.

        Args:
            start_time: Start time timestamp

        Returns:
            float: Total processing time in seconds
        """
        end_time = time.time()
        total_time = end_time - start_time
        self._performance_metrics["total_processing_time"] += total_time
        return total_time

    def start_chunk_processing(self) -> float:
        """
        Start tracking chunk processing time.

        Returns:
            float: Start time timestamp
        """
        self._performance_metrics["total_chunks_processed"] += 1
        return time.time()

    def end_chunk_processing_success(self, start_time: float) -> float:
        """
        End tracking successful chunk processing time.

        Args:
            start_time: Start time timestamp

        Returns:
            float: Chunk processing time in seconds
        """
        chunk_time = time.time() - start_time
        self._performance_metrics["successful_generations"] += 1
        self._update_average_chunk_time(chunk_time)
        return chunk_time

    def end_chunk_processing_failure(self, start_time: float) -> float:
        """
        End tracking failed chunk processing time.

        Args:
            start_time: Start time timestamp

        Returns:
            float: Chunk processing time in seconds
        """
        chunk_time = time.time() - start_time
        self._performance_metrics["failed_generations"] += 1
        return chunk_time

    def log_performance_metrics(self) -> None:
        """Log comprehensive performance metrics."""
        metrics = self._performance_metrics
        self.logger.info("=== PERFORMANCE METRICS ===")
        self.logger.info(f"Total chunks processed: {metrics['total_chunks_processed']}")
        self.logger.info(f"Successful generations: {metrics['successful_generations']}")
        self.logger.info(f"Failed generations: {metrics['failed_generations']}")
        self.logger.info(f"Success rate: {(metrics['successful_generations'] / max(metrics['total_chunks_processed'], 1)) * 100:.1f}%")
        self.logger.info(f"Total processing time: {metrics['total_processing_time']:.2f} seconds")
        self.logger.info(f"Average chunk time: {metrics['average_chunk_time']:.2f} seconds")
        self.logger.info("=== END METRICS ===")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get current performance metrics.

        Returns:
            Dict[str, Any]: Performance metrics
        """
        return self._performance_metrics.copy()

    def get_performance_summary(self, total_time: float) -> Dict[str, Any]:
        """
        Get performance summary for a completed operation.

        Args:
            total_time: Total processing time

        Returns:
            Dict[str, Any]: Performance summary
        """
        metrics = self._performance_metrics
        return {
            "total_time_seconds": total_time,
            "chunks_processed": metrics["total_chunks_processed"],
            "chunks_successful": metrics["successful_generations"],
            "chunks_failed": metrics["failed_generations"],
            "success_rate_percent": (metrics["successful_generations"] / max(metrics["total_chunks_processed"], 1)) * 100,
            "average_chunk_time_seconds": metrics["average_chunk_time"]
        }

    def get_chunk_performance_metrics(self, chunk_time: float, java_code_length: int, mappings_count: int) -> Dict[str, Any]:
        """
        Get performance metrics for a specific chunk.

        Args:
            chunk_time: Chunk processing time
            java_code_length: Length of generated Java code
            mappings_count: Number of mappings created

        Returns:
            Dict[str, Any]: Chunk performance metrics
        """
        return {
            "chunk_processing_time_seconds": chunk_time,
            "java_code_length": java_code_length,
            "mappings_count": mappings_count,
            "code_generation_rate_chars_per_second": java_code_length / max(chunk_time, 0.001)
        }

    def get_partial_performance_metrics(self, total_time: float, iterations_completed: int) -> Dict[str, Any]:
        """
        Get performance metrics for a partially completed operation.

        Args:
            total_time: Partial processing time
            iterations_completed: Number of iterations completed

        Returns:
            Dict[str, Any]: Partial performance metrics
        """
        return {
            "partial_time_seconds": total_time,
            "iterations_completed": iterations_completed,
            "chunks_processed_so_far": self._performance_metrics["total_chunks_processed"],
            "average_iteration_time": total_time / max(iterations_completed, 1)
        }

    def get_failure_performance_metrics(self, total_time: float) -> Dict[str, Any]:
        """
        Get performance metrics for a failed operation.

        Args:
            total_time: Processing time before failure

        Returns:
            Dict[str, Any]: Failure performance metrics
        """
        return {
            "failed_after_seconds": total_time,
            "chunks_processed_before_failure": self._performance_metrics["total_chunks_processed"],
            "successful_chunks_before_failure": self._performance_metrics["successful_generations"]
        }

    def _update_average_chunk_time(self, chunk_time: float) -> None:
        """
        Update the average chunk processing time.

        Args:
            chunk_time: Latest chunk processing time
        """
        successful = self._performance_metrics["successful_generations"]
        if successful == 1:
            self._performance_metrics["average_chunk_time"] = chunk_time
        else:
            # Calculate running average
            current_avg = self._performance_metrics["average_chunk_time"]
            new_avg = ((current_avg * (successful - 1)) + chunk_time) / successful
            self._performance_metrics["average_chunk_time"] = new_avg

    def log_delegation_performance(self, operation: str, start_time: float, success: bool = True) -> None:
        """
        Log performance metrics for delegation calls.

        Args:
            operation: Name of the delegated operation
            start_time: Start time of the operation
            success: Whether the operation was successful
        """
        duration = time.time() - start_time
        status = "SUCCESS" if success else "FAILED"
        self.logger.debug(f"Action: {operation}; Status: {status}; Duration: {duration:.3f}s")

    def reset_metrics(self) -> None:
        """Reset all performance metrics to initial state."""
        self._performance_metrics = {
            "total_chunks_processed": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "total_processing_time": 0.0,
            "average_chunk_time": 0.0
        }
        self.logger.debug("Performance metrics reset")

    def get_success_rate(self) -> float:
        """
        Get the current success rate as a percentage.

        Returns:
            float: Success rate percentage (0-100)
        """
        total = self._performance_metrics["total_chunks_processed"]
        if total == 0:
            return 0.0
        return (self._performance_metrics["successful_generations"] / total) * 100

    def get_failure_rate(self) -> float:
        """
        Get the current failure rate as a percentage.

        Returns:
            float: Failure rate percentage (0-100)
        """
        total = self._performance_metrics["total_chunks_processed"]
        if total == 0:
            return 0.0
        return (self._performance_metrics["failed_generations"] / total) * 100
