"""
JPA Repository Generator Agent for Java Spring plugin.
Generates Spring Data JPA repository interfaces from COBOL database operations.
"""
import json
import os
import logging
import re
from typing import Dict, List, Any, Optional

from langchain.schema import HumanMessage, SystemMessage
from jinja2 import Environment, FileSystemLoader

from llm_settings import invoke_llm
from src.plugins.targets.java_spring.tools.database_operation_detector import DatabaseOperationDetector


class JavaRepositoryGenerator:
    """
    Generates Spring Data JPA repository interfaces from COBOL database operations.
    """

    def __init__(self, knowledge_db=None):
        """Initialize the repository generator."""
        self.knowledge_db = knowledge_db
        self.logger = logging.getLogger(__name__)
        
        # Initialize database operation detector
        self.db_operation_detector = DatabaseOperationDetector()

        # Set up template environment
        template_dirs = [
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'generation', 'repositories'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'database_operations'),
            os.path.join(os.path.dirname(__file__), '..', 'templates', 'code_generator', 'system_prompts')
        ]
        self.template_env = Environment(loader=FileSystemLoader(template_dirs))
        self.logger.debug(f"Initialized repository generator with template directories: {template_dirs}")

    def generate_repository_interface(self, entity_info: Dict[str, Any], database_operations: List[Any], 
                                    generation_state: Dict[str, Any], tools=None) -> Dict[str, Any]:
        """
        Generate Spring Data JPA repository interface for an entity.
        
        Args:
            entity_info: Information about the entity (name, fields, etc.)
            database_operations: List of detected database operations
            generation_state: Current generation state
            tools: Code generation tools
            
        Returns:
            Dict[str, Any]: Generated repository information
        """
        try:
            entity_name = entity_info.get('entity_name', '')
            program_id = entity_info.get('program_id', '')
            
            self.logger.info(f"Generating repository interface for entity: {entity_name}")
            
            # Build context for repository generation
            context = self._build_repository_context(entity_info, database_operations, generation_state)
            
            # Render system and user prompts
            system_prompt = self._render_system_prompt(context)
            user_prompt = self._render_user_prompt(context)
            
            # Generate repository interface using LLM
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            self.logger.debug(f"Sending repository generation prompt to LLM")
            response = invoke_llm(messages)
            
            # Extract Java code and mappings
            java_code = self._extract_java_code(response)
            mappings = self._extract_mappings(response)
            
            if not java_code:
                self.logger.error(f"No Java repository code generated for entity: {entity_name}")
                return {}
            
            # Save mappings to database
            if tools and mappings:
                self._save_repository_mappings(tools, program_id, entity_name, mappings)
            
            repository_name = mappings.get('repository_info', {}).get('repository_name', f"{entity_name}Repository")
            
            return {
                "java_code": java_code,
                "mappings": mappings,
                "entity_name": entity_name,
                "repository_name": repository_name,
                "program_id": program_id,
                "package": mappings.get('repository_info', {}).get('package', 'com.generated.cobol.repository')
            }
            
        except Exception as e:
            self.logger.error(f"Error generating repository interface: {str(e)}")
            return {}

    def _build_repository_context(self, entity_info: Dict[str, Any], database_operations: List[Any], 
                                 generation_state: Dict[str, Any]) -> Dict[str, Any]:
        """Build context for repository generation."""
        entity_name = entity_info.get('entity_name', '')
        program_id = entity_info.get('program_id', '')
        
        # Classify database operations
        classification = self.db_operation_detector.classify_operations_for_template_selection(database_operations)
        db_context = self.db_operation_detector.prepare_template_context(database_operations, classification)
        
        context = {
            'entity_name': entity_name,
            'program_id': program_id,
            'id_type': entity_info.get('id_type', 'Long'),
            'table_name': entity_info.get('table_name', entity_name.upper()),
            'business_purpose': entity_info.get('business_purpose', f"Repository for {entity_name} entity"),
            'package_name': entity_info.get('package_name', 'com.generated.cobol'),
            'generation_date': generation_state.get('generation_date', ''),
            
            # Database operation context
            **db_context,
            
            # Field mappings from entity
            'field_mappings': entity_info.get('field_mappings', {}),
            
            # Primary key information
            'primary_key_field': entity_info.get('primary_key_field', 'id'),
            'primary_key_type': entity_info.get('primary_key_type', 'Long'),
            
            # Search and sort fields
            'search_field': entity_info.get('search_field', 'name'),
            'search_type': entity_info.get('search_type', 'String'),
            'sort_field': entity_info.get('sort_field', 'id'),
            
            # Common field types for template generation
            'business_key_field': entity_info.get('business_key_field', 'businessKey'),
            'business_key_type': entity_info.get('business_key_type', 'String'),
            'date_field': entity_info.get('date_field', 'createdDate'),
            'update_field': entity_info.get('update_field', 'status'),
            'update_type': entity_info.get('update_type', 'String'),
            'delete_field': entity_info.get('delete_field', 'id'),
            'delete_type': entity_info.get('delete_type', 'Long')
        }
        
        # Add database-driven context if available
        if self.knowledge_db:
            context['existing_mappings'] = self._get_existing_mappings(program_id, entity_name)
            context['business_mappings'] = self._get_business_mappings(program_id, entity_name)
        
        return context

    def _get_existing_mappings(self, program_id: str, entity_name: str) -> Dict[str, Any]:
        """Get existing COBOL-Java mappings for the entity."""
        if not self.knowledge_db:
            return {}
        
        try:
            return self.knowledge_db.get_cobol_java_mappings(program_id)
        except Exception as e:
            self.logger.error(f"Error getting existing mappings: {str(e)}")
            return {}

    def _get_business_mappings(self, program_id: str, entity_name: str) -> Dict[str, Any]:
        """Get business name mappings for the entity."""
        if not self.knowledge_db:
            return {}
        
        try:
            return self.knowledge_db.get_business_name_mappings(program_id)
        except Exception as e:
            self.logger.error(f"Error getting business mappings: {str(e)}")
            return {}

    def _render_system_prompt(self, context: Dict[str, Any]) -> str:
        """Render system prompt for repository generation."""
        try:
            template = self.template_env.get_template('service_generation_system.j2')
            return template.render(**context)
        except Exception as e:
            self.logger.error(f"Error rendering system prompt: {str(e)}")
            return "You are an expert Java Spring Boot developer generating JPA repository interfaces."

    def _render_user_prompt(self, context: Dict[str, Any]) -> str:
        """Render user prompt for repository generation."""
        try:
            template = self.template_env.get_template('jpa_repository.j2')
            return template.render(**context)
        except Exception as e:
            self.logger.error(f"Error rendering user prompt: {str(e)}")
            return f"Generate a Spring Data JPA repository interface for {context.get('entity_name', 'Entity')}."

    def _extract_java_code(self, response: str) -> str:
        """Extract Java code from LLM response."""
        java_pattern = r'```java\s*(.*?)\s*```'
        match = re.search(java_pattern, response, re.DOTALL | re.IGNORECASE)
        
        if match:
            return match.group(1).strip()
        
        return ""

    def _extract_mappings(self, response: str) -> Dict[str, Any]:
        """Extract JSON mappings from LLM response."""
        json_pattern = r'```json\s*(.*?)\s*```'
        match = re.search(json_pattern, response, re.DOTALL | re.IGNORECASE)
        
        if match:
            try:
                return json.loads(match.group(1).strip())
            except json.JSONDecodeError:
                self.logger.error("Failed to parse JSON mappings from repository generation")
        
        return {}

    def _save_repository_mappings(self, tools, program_id: str, entity_name: str, mappings: Dict[str, Any]):
        """Save repository mappings to database."""
        try:
            # Save repository info as class mapping
            if 'repository_info' in mappings:
                repo_info = mappings['repository_info']
                repository_name = repo_info.get('repository_name', '')
                
                repo_mapping_info = {
                    'package': repo_info.get('package', ''),
                    'business_purpose': repo_info.get('business_purpose', ''),
                    'java_type': 'repository_interface',
                    'is_entity_field': False,
                    'has_string_constructor': False,
                    'mapping_notes': f"Generated JPA repository interface for entity {entity_name}",
                    'generation_metadata': repo_info
                }
                
                tools.save_comprehensive_cobol_java_mapping(
                    program_id, entity_name, entity_name,
                    repository_name, 'class', repo_mapping_info
                )
            
            # Save method mappings
            if 'method_mappings' in mappings:
                for cobol_operation, method_info in mappings['method_mappings'].items():
                    java_method_name = method_info.get('java_method_name', '')
                    
                    method_mapping_info = {
                        'java_type': method_info.get('method_signature', ''),
                        'business_purpose': method_info.get('business_purpose', ''),
                        'is_entity_field': False,
                        'has_string_constructor': False,
                        'mapping_notes': f"Generated repository method from COBOL operation {cobol_operation}",
                        'generation_metadata': method_info
                    }
                    
                    tools.save_comprehensive_cobol_java_mapping(
                        program_id, entity_name, cobol_operation,
                        java_method_name, 'method', method_mapping_info
                    )
            
            # Save field mappings
            if 'field_mappings' in mappings:
                for cobol_field, field_info in mappings['field_mappings'].items():
                    java_field_name = field_info.get('java_field_name', '')
                    
                    field_mapping_info = {
                        'java_type': field_info.get('java_type', ''),
                        'business_purpose': field_info.get('business_name', ''),
                        'is_entity_field': True,
                        'has_string_constructor': False,
                        'mapping_notes': f"Entity field mapping from COBOL field {cobol_field}",
                        'generation_metadata': field_info
                    }
                    
                    tools.save_comprehensive_cobol_java_mapping(
                        program_id, entity_name, cobol_field,
                        java_field_name, 'field', field_mapping_info
                    )
            
            self.logger.info(f"Successfully saved repository mappings for {entity_name}")
            
        except Exception as e:
            self.logger.error(f"Error saving repository mappings: {str(e)}")

    def generate_repositories_for_entities(self, entities: List[Dict[str, Any]], 
                                         database_operations: List[Any],
                                         generation_state: Dict[str, Any], 
                                         tools=None) -> List[Dict[str, Any]]:
        """
        Generate repository interfaces for multiple entities.
        
        Args:
            entities: List of entity information
            database_operations: List of detected database operations
            generation_state: Current generation state
            tools: Code generation tools
            
        Returns:
            List[Dict[str, Any]]: List of generated repository information
        """
        repositories = []
        
        for entity_info in entities:
            # Filter database operations relevant to this entity
            entity_operations = self._filter_operations_for_entity(database_operations, entity_info)
            
            # Generate repository for this entity
            repository_info = self.generate_repository_interface(
                entity_info, entity_operations, generation_state, tools
            )
            
            if repository_info:
                repositories.append(repository_info)
        
        self.logger.info(f"Generated {len(repositories)} repository interfaces")
        return repositories

    def _filter_operations_for_entity(self, database_operations: List[Any], entity_info: Dict[str, Any]) -> List[Any]:
        """Filter database operations relevant to a specific entity."""
        entity_name = entity_info.get('entity_name', '')
        table_name = entity_info.get('table_name', entity_name.upper())
        
        relevant_operations = []
        
        for operation in database_operations:
            # Check if operation is related to this entity's table/segment
            if (operation.table_or_segment.upper() == table_name or 
                operation.table_or_segment.upper() == entity_name.upper()):
                relevant_operations.append(operation)
        
        return relevant_operations
