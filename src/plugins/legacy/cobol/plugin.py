"""
COBOL Language Plugin implementation.
"""
import os
import re
import logging
from typing import Dict, Any, List, Optional

from src.platform.interfaces.language_plugin import (
    LanguagePlugin, LanguagePreprocessor, LanguageChunker,
    LanguageAnalyzer, LanguageDetector
)
from .language_patterns import COBOL_PATTERNS, COBOL_DEPENDENCY_PATTERNS
from .tools.preprocessor import CobolPreprocessor as CobolPreprocessorTool
from .tools.chunkers.cobol_chunker import CobolChunker as CobolChunkerTool
from .tools.data_extractors.core import CobolDataDefinitionExtractor

logger = logging.getLogger(__name__)


class CobolDetector(LanguageDetector):
    """COBOL language detector."""

    COBOL_EXTENSIONS = ['.cob', '.cobol', '.cbl', '.cpy', '.copy']

    def detect_language(self, content: str, filename: Optional[str] = None) -> Optional[str]:
        """Detect if content is COBOL."""
        if filename:
            _, ext = os.path.splitext(filename.lower())
            if ext in self.COBOL_EXTENSIONS:
                return "cobol"

        # Check content patterns
        for pattern in COBOL_PATTERNS:
            if re.search(pattern, content, re.MULTILINE | re.IGNORECASE):
                return "cobol"

        return None

    def get_supported_extensions(self) -> List[str]:
        """Get COBOL file extensions."""
        return self.COBOL_EXTENSIONS

    def get_confidence_score(self, content: str, filename: Optional[str] = None) -> float:
        """Get confidence score for COBOL detection."""
        score = 0.0

        # Extension-based scoring
        if filename:
            _, ext = os.path.splitext(filename.lower())
            if ext in self.COBOL_EXTENSIONS:
                score += 0.5

        # Pattern-based scoring
        pattern_matches = 0
        for pattern in COBOL_PATTERNS:
            if re.search(pattern, content, re.MULTILINE | re.IGNORECASE):
                pattern_matches += 1

        # More patterns = higher confidence
        # Give COBOL higher confidence for legacy code analysis priority
        if pattern_matches > 0:
            # Strong COBOL indicators get high confidence
            if pattern_matches >= 2:
                score += 0.6  # High confidence for multiple COBOL patterns
            else:
                score += 0.4  # Medium confidence for single pattern

        return min(1.0, score)

    def get_patterns(self) -> List[str]:
        """Get COBOL detection patterns."""
        return COBOL_PATTERNS


class CobolPreprocessor(LanguagePreprocessor):
    """COBOL preprocessor that delegates to specialized tool."""

    def __init__(self):
        """Initialize the COBOL preprocessor with delegation to specialized tool."""
        self._preprocessor_tool = CobolPreprocessorTool()
        logger.info("COBOL preprocessor initialized with delegation to specialized tool")

    def preprocess(self, content: str, file_path: str) -> str:
        """Preprocess COBOL content by writing to temp file and processing."""
        import tempfile
        import os

        try:
            # Create temporary files for input and output
            with tempfile.NamedTemporaryFile(mode='w', suffix='.cob', delete=False) as temp_input:
                temp_input.write(content)
                temp_input_path = temp_input.name

            with tempfile.NamedTemporaryFile(mode='r', suffix='.cob', delete=False) as temp_output:
                temp_output_path = temp_output.name

            # Use the specialized tool to preprocess
            success = self._preprocessor_tool.preprocess_file(temp_input_path, temp_output_path)

            if success and os.path.exists(temp_output_path):
                with open(temp_output_path, 'r', encoding='utf-8', errors='replace') as f:
                    processed_content = f.read()
            else:
                logger.error(f"Preprocessing failed for content from {file_path}")
                raise Exception("Preprocessing failed")

            # Clean up temporary files
            try:
                os.unlink(temp_input_path)
                os.unlink(temp_output_path)
            except OSError:
                pass

            return processed_content

        except Exception as e:
            logger.error(f"Error in COBOL preprocessing: {str(e)}")
            raise

    def preprocess_file(self, input_path: str, output_path: str) -> bool:
        """
        Preprocess a COBOL file from input path to output path.

        Args:
            input_path: Path to the input COBOL file
            output_path: Path where the preprocessed file will be written

        Returns:
            bool: True if preprocessing was successful, False otherwise
        """
        logger.info(f"Delegating COBOL file preprocessing: {input_path} -> {output_path}")
        return self._preprocessor_tool.preprocess_file(input_path, output_path)


class CobolChunker(LanguageChunker):
    """COBOL chunker that delegates to specialized tool."""

    def __init__(self):
        """Initialize the COBOL chunker with delegation to specialized tool."""
        self._chunker_tool = CobolChunkerTool()
        logger.info("COBOL chunker initialized with delegation to specialized tool")

    def chunk(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Chunk COBOL content by writing to temp file and processing."""
        import tempfile
        import os

        try:
            # Create temporary files for input and output
            with tempfile.NamedTemporaryFile(mode='w', suffix='.cob', delete=False) as temp_input:
                temp_input.write(content)
                temp_input_path = temp_input.name

            temp_output_dir = tempfile.mkdtemp()

            # Use the specialized tool to chunk
            chunk_results = self._chunker_tool.chunk_file(temp_input_path, temp_output_dir)

            # Clean up temporary files
            try:
                os.unlink(temp_input_path)
                import shutil
                shutil.rmtree(temp_output_dir)
            except OSError:
                pass

            return chunk_results

        except Exception as e:
            logger.error(f"Error in COBOL chunking: {str(e)}")
            raise

    def chunk_file(self, input_path: str, output_dir: str) -> List[Dict[str, Any]]:
        """
        Chunk a COBOL file from input path to output directory.

        Args:
            input_path: Path to the preprocessed COBOL file
            output_dir: Directory to write the chunked files

        Returns:
            List[Dict[str, Any]]: List of chunk information dictionaries
        """
        logger.info(f"Delegating COBOL file chunking: {input_path} -> {output_dir}")
        return self._chunker_tool.chunk_file(input_path, output_dir)




class CobolAnalyzer(LanguageAnalyzer):
    """COBOL analyzer that delegates to specialized tools."""

    def __init__(self):
        """Initialize the COBOL analyzer with delegation to specialized tools."""
        self._data_extractor = CobolDataDefinitionExtractor()
        logger.info("COBOL analyzer initialized with delegation to CobolDataDefinitionExtractor")

    def analyze(self, content: str, file_path: str, program_name: str = None) -> Dict[str, Any]:
        """Analyze COBOL content using specialized tools."""
        logger.info(f"Analyzing COBOL content from {file_path}")

        try:
            # Check if this is a DATA_DIVISION chunk that needs data extraction
            if self._is_data_division_chunk(content):
                # Use provided program_name first, then extract from content, then from file_path
                program_id = program_name
                if not program_id:
                    program_id = self._extract_program_id(content)
                if not program_id:
                    program_id = os.path.splitext(os.path.basename(file_path))[0]

                # Create chunk structure expected by CobolDataDefinitionExtractor
                chunk = {
                    "code": content,
                    "chunk_type": "DATA_DIVISION",
                    "chunk_name": f"{program_id}_DATA_DIVISION",
                    "metadata": {
                        "source_file": file_path
                    }
                }

                # Use CobolDataDefinitionExtractor for data extraction
                logger.info(f"Using CobolDataDefinitionExtractor for data division analysis in {file_path}")
                data_items = self._data_extractor.process_chunk(chunk, program_id)

                return {
                    "file_path": file_path,
                    "language": "cobol",
                    "program_id": program_id,
                    "data_items": data_items,
                    "chunk_type": "DATA_DIVISION"
                }
            else:
                # For non-data division content, return basic analysis
                return self._analyze_non_data_division(content, file_path)

        except Exception as e:
            logger.error(f"Error in COBOL analysis for {file_path}: {str(e)}")
            raise

    def _is_data_division_chunk(self, content: str) -> bool:
        """Check if content is a DATA DIVISION chunk using COBOL patterns."""
        data_division_patterns = [
            r'DATA\s+DIVISION',
            r'WORKING-STORAGE\s+SECTION',
            r'FILE\s+SECTION',
            r'LINKAGE\s+SECTION',
            r'^\s*\d+\s+[A-Za-z0-9\-_]+.*PIC'  # Level numbers with PIC clauses
        ]
        return any(re.search(pattern, content, re.MULTILINE | re.IGNORECASE) for pattern in data_division_patterns)

    def can_extract_data_definitions(self, chunk_type: str) -> bool:
        """Check if this analyzer can extract data definitions from the given chunk type."""
        return chunk_type.startswith("DATA_DIVISION")

    def _extract_program_id(self, content: str) -> Optional[str]:
        """Extract program ID from COBOL content."""
        program_id_match = re.search(r'PROGRAM-ID\.\s*(\w+)', content, re.IGNORECASE)
        return program_id_match.group(1) if program_id_match else None

    def _analyze_non_data_division(self, content: str, file_path: str) -> Dict[str, Any]:
        """Analyze non-data division COBOL content."""
        return {
            "file_path": file_path,
            "language": "cobol",
            "program_id": self._extract_program_id(content),
            "divisions": [match.group(1).upper() for match in re.finditer(
                r'(IDENTIFICATION|ENVIRONMENT|DATA|PROCEDURE)\s+DIVISION', content, re.IGNORECASE)],
            "data_items": [],
            "procedures": []
        }




class CobolDependencyAnalyzer:
    """COBOL dependency analyzer."""

    def extract_dependencies(self, content: str) -> set:
        """Extract COBOL procedure dependencies."""
        calls = set()

        # Clean the text for better pattern matching
        clean_text = re.sub(r'\s+', ' ', content.upper())

        # PERFORM statements
        for pattern in COBOL_DEPENDENCY_PATTERNS['perform_patterns']:
            matches = re.findall(pattern, clean_text)
            for match in matches:
                if isinstance(match, tuple):
                    calls.update(match)
                else:
                    calls.add(match)

        # GO TO statements
        goto_matches = re.findall(COBOL_DEPENDENCY_PATTERNS['goto_pattern'], clean_text)
        calls.update(goto_matches)

        # Filter out COBOL keywords and common false positives
        return {call for call in calls
                if call not in COBOL_DEPENDENCY_PATTERNS['keywords_to_filter']
                and len(call) > 2}


class CobolLanguagePlugin(LanguagePlugin):
    """COBOL language plugin."""

    def __init__(self):
        super().__init__("cobol", "1.0.0")
        self._detector = CobolDetector()
        self._preprocessor = CobolPreprocessor()
        self._chunker = CobolChunker()
        self._analyzer = CobolAnalyzer()
        self._dependency_analyzer = CobolDependencyAnalyzer()

    def get_name(self) -> str:
        return "cobol"

    def get_version(self) -> str:
        return "1.0.0"

    def get_description(self) -> str:
        return "COBOL language support with preprocessor, chunker, and analyzer"

    def initialize(self, config: Dict[str, Any] = None) -> bool:
        """Initialize the COBOL plugin."""
        return True

    def cleanup(self) -> None:
        """Clean up COBOL plugin resources."""
        pass

    def get_language_name(self) -> str:
        return "cobol"

    def get_supported_extensions(self) -> List[str]:
        return ['.cob', '.cobol', '.cbl', '.cpy', '.copy']

    def get_preprocessor(self) -> Optional[LanguagePreprocessor]:
        return self._preprocessor

    def get_chunker(self) -> Optional[LanguageChunker]:
        return self._chunker

    def get_analyzer(self) -> Optional[LanguageAnalyzer]:
        return self._analyzer

    def get_detector(self) -> Optional[LanguageDetector]:
        return self._detector

    def can_process_file(self, file_path: str) -> bool:
        """Check if this plugin can process the given file."""
        _, ext = os.path.splitext(file_path.lower())
        return ext in self.get_supported_extensions()

    def get_dependency_analyzer(self):
        """Get the COBOL dependency analyzer."""
        return self._dependency_analyzer
