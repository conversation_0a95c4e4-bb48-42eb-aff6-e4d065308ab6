"""
COBOL language detection patterns.
Contains regex patterns for identifying COBOL code.
"""

# COBOL language detection patterns
# Updated to be more flexible with indentation while maintaining COBOL specificity
COBOL_PATTERNS = [
    r'^\s*(IDENTIFICATION|ID)\s+DIVISION',
    r'^\s*PROGRAM-ID',
    r'^\s*(ENVIRONMENT|DATA|PROCEDURE)\s+DIVISION',
    r'^\s*PIC\s+[SX9A]+',
    r'^\s*COMPUTE\s+',
    r'^\s*MOVE\s+',
    r'^\s*IF\s+.+\s+THEN\s+',
    r'^\s*WORKING-STORAGE\s+SECTION',
    r'^\s*FILE\s+SECTION',
    r'^\s*LINKAGE\s+SECTION'
]

# COBOL dependency analysis patterns
COBOL_DEPENDENCY_PATTERNS = {
    'perform_patterns': [
        r'PERFORM\s+([A-Z0-9\-]+)',
        r'PERFORM\s+([A-Z0-9\-]+)\s+THROUGH\s+([A-Z0-9\-]+)',
        r'PERFORM\s+([A-Z0-9\-]+)\s+THRU\s+([A-Z0-9\-]+)',
        r'PERFORM\s+([A-Z0-9\-]+)\s+UNTIL',
        r'PERFORM\s+([A-Z0-9\-]+)\s+VARYING'
    ],
    'goto_pattern': r'GO\s+TO\s+([A-Z0-9\-]+)',
    'keywords_to_filter': {
        'SPACES', 'LOW-VALUES', 'HIGH-VALUES', 'ZERO', 'ZEROS', 'ZEROES',
        'EXIT', 'EJECT', 'SKIP1', 'SKIP2', 'SKIP3', 'NEXT', 'SENTENCE',
        'END-PERFORM', 'END-IF', 'END-EVALUATE', 'FILLER'
    }
}
