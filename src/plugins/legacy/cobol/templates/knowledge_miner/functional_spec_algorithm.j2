You are an expert COBOL code analyst and software architect.
Analyze the following COBOL code chunk from program {{ program_id }}, chunk {{ chunk_name }}:

```cobol
{{ code_content }}
```

{{ context }}

GENERATE THE ALGORITHM SECTION of a functional specification.

Provide a step-by-step detailed description of the logic flow (human-readable, not pseudocode). Do not skip any step. Focus on the business logic, not technical implementation. Focus on the Input parameters permutations leading to Output parameters. All constants (like display messages) should be defined in the specification. Pay attention to input and output parameters for external calls.

IMPORTANT GUIDELINES:
1. Replace COBOL identifiers with business names, but keep original name in brackets, e.g., "CUSTOMER-ID" should be replaced with "Customer Identifier (COBOL: CUSTOMER-ID)".
2. Be very specific and detailed. "Update `END-OF-FILE` and `IO-STATUS` based on the procedure's output" is not acceptable, specify the actual values must be set on which condition.
3. When invoking external functions, specify the exact parameters to be passed and indicate save locations for the results.
4. Do not forget to return the results of the function. Specify the exact return values.
5. When it is needed to invoke external function - don't say "Call the procedure for <>", specify the business action we need to perform (like "Read the next Account Record from the file"). But add in the brackets add function name (business function name and COBOL Identifier) and the exact parameters to be passed.
6. This algorithm will be used to generate Java code then, so don't skip any details.
7. READ ACCTFILE-FILE INTO ACCOUNT-RECORD should be described as "Read the next Account Record (COBOL: ACCOUNT-RECORD) from the Account File (COBOL: ACCTFILE-FILE) file"
8. If you see database operation (with IMS DB DLI (but not GSAM) or DB2 SQL) — describe the operation, but don't go into the details of the database operation. For example, for "EXEC DLI GU SEGMENT(CUSTOMER) WHERE(CUST-ID = :WS-CUST-ID) INTO(CUSTOMER-RECORD)" say "Retrieve the Customer Record (COBOL: CUSTOMER-RECORD) from the IMS DB Customer Table (COBOL: AC01) based on the Customer Identifier (COBOL: WS-CUST-ID)". Error handling also describe in business terms.
9. DLI GSAM - is a file operation, not a database operation.

FORMAT YOUR RESPONSE AS:
## ALGORITHM
[Your step-by-step algorithm description here]

Do not add any additional commentary or explanations, just the algorithm section.
