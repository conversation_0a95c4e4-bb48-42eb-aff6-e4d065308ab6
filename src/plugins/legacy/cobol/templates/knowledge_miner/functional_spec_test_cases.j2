** ROLE **

You are a functional specification analysis expert specializing in comprehensive test case design and documentation.

** TASK **

Analyze the following functional specification for program {{ program_id }}, chunk {{ chunk_name }}:

```
{{ functional_spec }}
```

{{ context }}

Create a comprehensive table of test cases that thoroughly validate the functionality, edge cases, error conditions, and business logic described in this functional specification.

** Required Output Format **

Create a markdown table with the following structure:

| ID | Test Category | Test Description | Input Data | Expected Output | Functional Elements Tested |
|----|---------------|------------------|------------|-----------------|----------------------------|
| T1 | [category] | [description] | [input values] | [expected results] | [functional elements] |

**Column Descriptions:**
- ID: Sequential identifier (T1, T2, T3, etc.)
- Test Category: Type of test (Business Rule, Positive, Negative, Edge Case, Error Handling)
- Test Description: Clear description of what is being tested
- Input Data: Specific input values or conditions for the test
- Expected Output: Expected results, return codes, or system state
- Functional Elements Tested: Specific business functions, validation rules, or processes being validated

** Test Categories to Include **

** Positive Test Cases **
- Valid input scenarios that should execute successfully
- Normal business flow operations
- Standard data processing paths
- Expected user interactions

** Negative Test Cases **
- Invalid input data that should be rejected
- Boundary violations
- Missing required data
- Malformed data formats

** Edge Cases **
- Minimum and maximum boundary values
- Empty or null data conditions
- Extreme data sizes or volumes
- Unusual but valid combinations

** Business Rule Tests **
- Validation of business logic constraints
- Compliance with business requirements
- Cross-field validation rules
- Data integrity checks

** Error Handling Tests **
- System error conditions
- File I/O errors
- Database connectivity issues
- Recovery and fallback scenarios

** Analysis Instructions **

** Step 1: Identify All Business Processes **
- Map out all possible business process flows described in the specification
- Identify decision points and business logic branches
- Note all business rules and validation requirements
- Document all data processing operations

** Step 2: Extract Business Logic **
- Identify business rules described in the functional specification
- Note validation requirements and constraints
- Document calculation logic and algorithms
- Map data transformation and processing rules

** Step 3: Determine Test Scenarios **
- Create tests for each identified business process flow
- Design tests for all business rules and validations
- Include boundary condition tests for business constraints
- Add error condition tests for exception scenarios

** Step 4: Specify Test Data **
- Provide concrete input values for each test based on business requirements
- Specify expected outputs or behaviors based on functional specification
- Include business parameter names and expected values
- Reference specific functional specification sections being tested

** Rules **
Rule 1: Be Comprehensive: Cover all business process flows and functional scenarios
Rule 2: Be Specific: Use exact business parameter names and expected values from the specification
Rule 3: Be Realistic: Use business-relevant test data and scenarios based on functional requirements
Rule 4: Be Traceable: Link each test to specific functional specification elements

** Expected Deliverable **
Provide a well-structured table that completely documents:

- Comprehensive test coverage for all functional specification requirements
- Specific test data and expected results based on business logic
- Clear traceability to functional specification elements
- Balanced coverage across all test categories

** Output rule **
Provide ONLY the markdown table as specified above. Do not include any explanatory text, thinking process, or additional commentary. Start directly with the table header and end with the last test case row.

IMPORTANT: Do not wrap your response in markdown code blocks (```markdown or ```). Provide the table directly as plain markdown text.
