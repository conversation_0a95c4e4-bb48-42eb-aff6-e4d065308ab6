You are an expert COBOL code analyst.
Analyze the following COBOL code chunk from program {{ program_id }}, chunk {{ chunk_name }}:

```cobol
{{ code_content }}
```

{{ context }}

CREATE A CONCISE BUSINESS-ORIENTED NAME for this code chunk.
- The name should reflect what business function this code performs
- Use clear, descriptive language that a business user would understand
- Focus on the business purpose, not technical implementation
- Keep it concise (3-5 words)
- Use Title Case

Examples of good business names:
{% for example in business_name_examples -%}
- "{{ example }}"
{% endfor %}

Respond with just the business name, nothing else.
