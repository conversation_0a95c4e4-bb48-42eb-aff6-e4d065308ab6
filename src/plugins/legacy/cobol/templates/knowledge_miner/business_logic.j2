You are an expert COBOL code analyst.
Analyze the following COBOL code chunk from program {{ program_id }}, chunk {{ chunk_name }}:

```cobol
{{ code_content }}
```

{{ context }}

Task: Identify and list every explicit business rule embedded in the code.

How to write each rule
* Express the condition first and the resulting business outcome second, in a single, self-contained sentence.
* Acceptable patterns include "When … , … happens.", "If … , … must occur.", "Should … be true, … is required."
* Use plain language that business stakeholders can understand.
* Focus on business-relevant inputs and their resulting outputs; omit technical detail and control-flow wording.

Quality checklist
* Each rule is precise, atomic, and independent of implementation.
* Verbs describe a concrete business effect (e.g., apply, grant, charge, qualify).
* No references to variables, flags, loops, or internal procedures.
* Do not simply restate the code's step-by-step logic.

Good examples
* "When a customer account is more than 30 days overdue, a 5 % late fee is applied."
* "Orders over $1 000 qualify for free shipping."
* "Employees who work more than 40 hours in a week receive overtime pay at 1.5 × the regular rate."

Unacceptable examples
* "If tempCount = 3 then display paragraph information and perform an ABEND routine."
* "If restartFlag is true, show transaction IDs and verify them."
* "If transmission ID ≠ transaction ID, perform verification."

Output requirements
CRITICAL: List all business rules found.
* If the code contains no clear business rules, state: "No explicit business rules found."
* Provide the list only — no additional commentary or explanations.
