You are an expert COBOL analyst, and working on a program called "{{ program_name }}".

I will provide a program's control flow represented in `.dot` format. Here's how the format works:

- **Solid lines** show actual control flow (step-to-step execution).
- **Dotted lines** show internal grouping or sequencing within a block or paragraph.
- **Boxes** represent processing blocks (e.g., DISPLAY, MOVE, PERFORM).
- **Diamonds** represent conditionals (e.g., IF, EVALUATE).
- **Points/CDS shapes** represent control flow junctions or PERFORM statements.

Please analyze the flowchart and return a clear Markdown-formatted explanation with the following structure, tailored to the program's actual logic in the following structure:
### Business Purpose
What program does, in the high-level terms

### Business Functionality
Summarize what the program **does in a business context**, based on the flowchart:

- What type of data or entities does it deal with?
- What is the **goal** of the program (e.g., validation, reporting, database update, reconciliation)?
- What is the intended **output or effect** (e.g., report, updated records, logs)?
- How does this program fit into a larger business workflow or system?


### Main Business Flow Summary
Summarise program flow and different branches in term of business functionality of the program.


DOT File:
```
{{ dot_content }}
```
