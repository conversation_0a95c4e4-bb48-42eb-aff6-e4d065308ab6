<prompt>
  <context>
    <program_description>{{context_elements_text}}</program_description>
    <module_id>{{module_id}}</module_id>
  </context>
  
  <task>
    Based on the following description of a COBOL program and its functionality, generate a JSON object with the recap
    information fields.
  </task>
  
  <output_format>
    <fields>
      - Block: the logical system domain or business area the function belongs to
      - Application: the name of the broader system or application containing the program
      - MainframeProgram: {{module_id}}
      - FunctionalElement: the main function, use case, or process the program implements
      - Version: version number (use "1.0" if unknown)
      - DocumentStatus: status of the document (use "Draft" if unknown)
    </fields>
    
    <format>
      Please respond in valid JSON format only, like the following:
      ```json
      {
      "Block:": "Customer Management",
      "Application:": "Mainframe CRM System",
      "Mainframe Program:": "CUS1234C",
      "Functional Element:": "Customer Address Validation",
      "Version:": "1.0",
      "Document Status:": "Draft"
      }
      ```
    </format>
  </output_format>
  
  <output_rule>
    Respond with valid JSON format only, nothing else.
  </output_rule>
</prompt>