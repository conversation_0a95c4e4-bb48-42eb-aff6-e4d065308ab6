# Reverse Engineering
## Functional Refinement Document

| Block: | Chase Card Core                                         |
|---|---------------------------------------------------------|
| Application: | Reissue                                                 |
| Mainframe Program: | CCPBD600                                                |
| Functional Element: | TO CREATE TRANSMISSION INFORMATION FOR REQUESTED CARDS |
| Version: | 1.00                                                    |
| Document Status: | Draft                                                   |

## Table of Contents
1. Functional information  
   1.1 High-Level Business Requirements   
   1.2 Process Flow step sequence  
   1.3 Business Logic  
2. Main Program Process Flow Diagram  
3. Process description   
   3.1 Preconditions  
      3.1.1 Incoming files  
      3.1.2 Input parameters  
   3.2 Database interactions  
      3.2.1 DB2 Tables used  
      3.2.2 DB2 Tables select / read   
      3.2.3 DB2 Tables insert   
      3.2.4 IMS Tables used   
      3.2.5 IMS Tables select / read (GU / GNP)  
      3.2.6 IMS Tables Insert (ISRT)   
      3.2.7 IMS Tables Replace / Update (REPL)  
   3.3 Called sub-program modules  
   3.4 Output   
   3.5 Exception scenarios  

## 1. Functional information
### 1.1 High-Level Business Requirements

The CCPBD600 program is a card reissue process designed to generate transmission information for requested reissue cards.

The program reads the input transaction file called "CARDREQ," which contains reissue requests. It then makes calls to sub-programs to perform tasks such as retrieving the appropriate disclosure forms, addendums, and inserts that are included in the reissue mailers.

>For this part, we need to analyze the 0000 part. Search for 0000 is needed, as I checked, is not always called 0000-MAINLINE-CONTROL. From there we will see all the main flow and initialization file. Usually the files needed are opened there, so we search OPEN INPUT. The same files should also be listed inside FILE CONTROL.

At a high level, the program follows these steps:

1. **Initialization**: It sets up input files, counters, checkpoint switches, work areas, and flags
2. **Position Establishment**: Determine the starting point for an initial run or restart and open the necessary program files
3. **Request Processing**: Handle reissue requests from the CARDREQ file until all input requests are processed. This involves calling other programs to exchange data, writing files, and updating the database if necessary
4. **Termination**: Perform cleanup and termination tasks

1. **Initialization**: It sets up input files, counters, checkpoint switches, work areas, and flags to prepare the program for processing account information.
2. **Input Retrieval**: This step involves collecting all required input parameters, such as account identifiers, balances, credit limits, and key dates, for subsequent processing.
3. **Data Validation**: The program validates each input parameter to ensure accuracy and consistency, verifying formats and checking for null or invalid values.
4. **Data Formatting**: All validated data is formatted into a structured output layout, ensuring clarity and readability for stakeholders.
5. **Display Output**: The formatted account information is systematically outputted, typically to a console or log, for stakeholder review and decision-making support.
6. **Error Handling**: If any errors or exceptions are encountered during processing, appropriate error messages are logged, and the program execution is adjusted accordingly.
7. **Termination**: The program performs cleanup activities, such as closing files and resetting flags, to conclude the execution gracefully.
 
> This is mainly the explanation from the 2000-PROCESS-REQUEST-FILE chunk, especially points 3 and 4.

> For 1 and 4, these are so generic – all programs have them, we can hardcode them - give instructions to AI to add them

### 1.2 Process Flow step sequence

> I think this is a list of ACCEPTANCE CRITERIA – what this program should do / provide.
These criteria should all be fulfilled before the program goes LIVE.
Usually written by Analysts when the code is developed – not sure if we can extract them correctly from code

| Label | Change Log ID | UI/Process | Requirement Description and Validation Criteria                                                                                                       |
|-------|--------------|------------|-------------------------------------------------------------------------------------------------------------------------------------------------------|
| PR1 | 1.0 | Process | The system shall provide a mechanism to review the account statuses to determine if it is eligible for card reissuance.                               |
| PR2 | 1.0 | Process | The system shall provide a mechanism to determine the account population to route the account through the appropriate reissue decisioning strategies. |
| PR3 | 1.0 | Process | The system shall provide a mechanism to determine the account conditions to make the reissue decision.                                                |
| PR3 | 1.0 | Process | The system shall provide a mechanism to make a decision if the card/s should be reissued for the account.                                             |
| PR4 | 1.0 | Process | The system shall provide a mechanism to include the reissue decision action for the account in the file to the downstream systems.                    |
| PR1 | 1.0 | Process | The system shall provide a mechanism to trigger the reissue event (Event 693).                                                                        |
| PR1 | 1.0 | Process | The system shall provide a mechanism to send the appropriate Approval/Decline letters to the customer if applicable, based on the Reissue Action ID.  |
| PR1 | 1.0 | Process | The system shall provide a mechanism to send the embossing requests for new cards for reissue approvals.                                              |
| PRDK9 | 1.0 | Process | The system shall provide a mechanism to update the account expiration date in case of approvals.                                                      |
| PRDK10 | 1.0 | Process | The system shall provide a mechanism to send the Reissue outcome feed to CDW.                                                                         |
| PRDK11 | 1.3 | Process | The system shall provide a mechanism to evaluate if an account should be included or excluded from the Reissue Driver File.                           |

### 1.3 Business Logic

> This data should be extracted from 2100-PROCESS-REQUEST-FILE. We can create a prompt to try to detect the main Business Logic of what is happening in 2000 and 2100. As will do Down to Top approach, after the AI detects the Business Logic function names, we can use the correct and better description that we already extracted for that functions as just by reading the process names – there is not much info.

| Function | Description of business logic |
|----------|------------------------------|
| 2100-PROCESS-REQUEST-FILE | This section is responsible for processing a request file, which is a core part of the business logic as it likely involves handling input data and initiating various processes |
| 2150-CHECK-CHIP-ERRORS | This part checks for chip errors before writing data, which is crucial for ensuring data integrity and proper processing of card-related transactions |
| 2110-SEARCH-STHC | This section involves searching a transaction table, which is a key operation in processing and validating transaction data |
| 2400-PIN-RETENTION | This section deals with the retention of PINs, which is a critical aspect of security and compliance in financial applications |
| 2200-PROCESS-CARDS | This section processes card-related data, which is central to the application's functionality, especially in a financial or card management system |
| 6100-FORMAT-FEDEX | This part formats data for FedEx delivery, indicating integration with external systems and logistics, which is a significant business operation |

## 2. Main Program Process Flow Diagram

> The Flow Diagram is here for the entire app.

> Also, below the diagram, we should show the process in text-like steps

a. Initialize Fields - Example Convert PC - fields to THC - HEX values (Para 7500-).  
b. Read and process file created from program CCPBD601 and process until end of file. (2000-).  
c. Validate transaction ID on input file (2600- / 2650-). If not valid transaction (TD-CARD-ID) write as error, read next record (Step b).
d. Call program CCCBR010 to get "CARD" name and address information. (2100-).  
e. Select "GU" account data from AM00 IMS table (8310-).  
Etc...

## 3. Process description
### 3.1 Preconditions
#### 3.1.1 Incoming files

> Get all files with OPEN in the program. Also indicate in what part / function are opened

| INPUT FILE NAME | READ STARTS AT |
|-----------------|----------------|
| NEXTNUM | 1000-INITIALIZATION |
| PRMFILE | 1200-READ-JOB-PARM |
| DAY1FILE | 1600-READ-DAY1FILE |
| CARDREQ | 2000-PROCESS-REQUEST-FILE. |
| HEXPRNT | 7500-LOAD-TRANS-ID-TBL |

#### 3.1.2 Input parameters

> TBD. The sample doc shows the Input Parameters separately, but will try to put them lower with the Program Called and the Output, so it's all in one place

### 3.2 Database interactions
#### 3.2.1 DB2 Tables used

> All these tables can be found in  
`EXEC SQL` `INCLUDE COPYBOOK` `END-EXEC`  
Need to somehow get the Table Names and link them to the CopyBooks.

| COPYBOOK | TABLE NAME | TYPE |
|----------|------------|------|
| SQLCA |  | SELECT |
| SCHEMBIF | TCH_EMB_INTERFACE | SELECT |
| SCHCARDM | TCH_CARD_MLR_OPTS | SELECT |
| SCHCRDID | TCH_CARD_ID_DEF | SELECT |
| SCHMLRID | TCH_MAILER_ID_DEF | SELECT |
| SCHINSPD | TCH_INSERT_DETAIL | SELECT |
| SCHEVTRT | TCH_EVENT_RETN_OPT | SELECT |
| SCHCLTNM | TCH_CLIENT_NAME | SELECT |
| SCHPDGCB | TCH_PRIM_DISC_GRP | SELECT |
| SCHADDFM | TCH_ADDENDUM_FORMS | SELECT |
| SCHCCOPT | TCH_CHIP_CARD_OPT | SELECT |
| SCHCAPDF | TCH_CHIP_APP_DEF | SELECT |
| SCHPRREJ | TCH_PROD_REJECTS | INSERT |
| SCHCPOPT | TCH_CHIP_PIN_OPT | SELECT |
| SCHEMBTD | TCH_EMB_TRANS_DEF | SELECT |
| SCHCLADV | TCH_DEFLT_VALUE | SELECT |
| SCHCRDIR | TCH_CARD_MLR_ID | SELECT |
| SCHCLPRC | TCH_PROCESSING_TAB | SELECT |

#### 3.2.2 DB2 Tables select / read

| Function | Table used | Code used |
|----------|------------|------|
| 2650-TRAN-ID-LKUP | TCH_EMB_TRANS_DEF | SELECT EMBTD_TRANSMIT_ID<br>INTO :WS-EMBTD-TRANSMIT-ID<br>FROM TCH_EMB_TRANS_DEF<br>WHERE EMBTD_CLIENT_NBR &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; = :WS-EMBTD-CLIENT-NBR<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;AND EMBTD_CRV &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; = :WS-EMBTD-CRV<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; AND EMBTD_PHOTO_CRD_SW &nbsp;= :WS-EMBTD-PHOTO-CRD-SW<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; AND EMBTD_EMB_DELIV_CD &nbsp;&nbsp;&nbsp; = :WS-EMBTD-EMB-DELIV-CD<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; AND EMBTD_CRD_REQ_TYPE &nbsp; = :WS-EMBTD-CRD-REQ-TYPE<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; AND EMBTD_EMB_AI_INYS &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; = :WS-EMBTD-EMB-AI-INYS<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; AND EMBTD_CARD_TYPE &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; = :WS-EMBTD-CARD-TYPE<br>|

> Continue getting all Select and Read from the program

#### 3.2.3 DB2 Tables insert

| Function | Table used | Code used |
|----------|------------|-----------|
| 9700-PROCESS-REJECTS | TCH_PROD_REJECTS | INSERT INTO TCH_PROD_REJECTS (<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_TRANS_ID, REJ_CLIENT_NUM,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_ADD_TS, REJ_APLIC_NUM,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_APLIC_SUFFIX, REJ_PIN_CUST_TYPE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_PIN_CUST_ID, REJ_PIN_ACCT_NUMB,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_PIN_DT_OF_REQ, REJ_PIN_REQ_TYPE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_CRD_CUST_TYPE, REJ_CRD_CUST_ID,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_CRD_ACCT_NUM, REJ_CRD_CARD_ID,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_CRD_MAILER_ID, REJ_CRD_DT_OF_REQ,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_CRD_MLR_SEQ_NO, REJ_CRD_REQ_TYPE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_CRD_CNTR_ID, REJ_CRD32_SEQ_SQ,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_CURS_PARA_NUM, REJ_CURS_SUB_MOD,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_STATUS_CD, REJ_DB_CALL_TYPE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_DB_FUNCTION, REJ_CURS_ROOT_DT,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_CURS_ROOT_TM, REJ_SEGMENT_TM,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_TABLE_NM, REJ_DMS_SQLSTATE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_DMS_RETURN_CD, REJ_MSG_TYPE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REJ_MSG_SUB_TYPE, REJ_MSG_TEXT)<br> VALUES ( <br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-TRANS-ID, :REJ-CLIENT-NUM,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; CURRENT TIMESTAMP, :REJ-APLIC-NUM,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-APLIC-SUFFIX, :REJ-PIN-CUST-TYPE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-PIN-CUST-ID, :REJ-PIN-<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ACCT-NUMB, CURRENT DATE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-PIN-REQ-TYPE, :REJ-CRD-CUST-TYPE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-CRD-CUST-ID, :REJ-CRD-<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ACCT-NUM, :REJ-CRD-CARD-ID,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-CRD-MAILER-ID, CURRENT DATE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-CRD-MLR-SEQ-NO, :REJ-CRD-<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; REQ-TYPE, :REJ-CRD32-CUST-ID,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-CRD32-SEQ-SQ, :REJ-CURS-PARA-NUM,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-CURS-SUB-MOD, :REJ-CURS-<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; PARA-NM, :REJ-DB-CALL-TYPE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-DB-FUNCTION, :REJ-CURS-ROOT-DT,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-CURS-ROOT-TM, :REJ-SEGMENT-TM,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-DMS-STATUS-CD, :REJ-TABLE-NM,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-DMS-SQLSTATE, :REJ-DMS-RETURN-CD,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-MSG-TYPE, :REJ-MSG-SUB-TYPE,<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :REJ-MSG-TEXT<br> ) |

> Continue getting all Insert from the program

#### 3.2.4 IMS Tables used

| IMS Table Description | Segment Name | Copybook |
|----------------------|--------------|----------|
| ACCOUNT MASTER ROOT SEGMENT | 01 AMCNM00-SEGMENT | AMCNM00 |
| ACCOUNT/CUSTOMER RELATIONSHIP SEGMENT | 01 AMCNM01-SEGMENT | AMCNM01 |
| ACCOUNT CARD NUM SEGMENT | 01 AMCNM0A-SEGMENT | AMCNM0A |
| CHIP CARD ROOT SEGMENT | 01 CCCC00-SEGMENT | CCCC00 |
| CHIP CARD IDENTIFICATION SEGMENT | 01 CCCC10-SEGMENT | CCCC10 |
| CHIP CARD APPLICATION SEGMENT | 01 CCCC20-SEGMENT | CCCC20 |
| CUSTOMER MASTER SEGMENT | 01 CUCNM00-SEGMENT | CUCNM00 |
| CUSTOMER MASTER CUSTOM DATA01 | 01 CUCNM07-SEGMENT | CUCNM07 |
|  | 01 CUCNM07-SEGMENT2 | CUCNM07 |
|  | 01 CUCNM07-SEGMENT3 | CUCNM07 |
| CUSTOMER MASTER PERSONAL INFO | 01 CUCNM05-SEGMENT | CUCNM05 |
| CUSTOMER ADDR SEGMENT | 01 WS-CUST-ADDR-SEGMENT | CUCNM01 |
| PERSONAL EMBOSS SEGMENT | 01 WS-PERS-NAME-SEGMENT | CUCNM02 |
| EVENT LOGGING PARENT SEGMENT | 01 ELCEL00-SEGMENT | ELCEL00 |
| EVENT LOGGING DETAIL SEGMENT | 01 ELCEL02-SEGMENT | ELCEL02 |
| REWARDS DATABASE SEGMENTS | 01 RWCNR00-SEGMENT | RWCNR00 |
|  | 01 RWCNR01-SEGMENT | RWCNR01 |
|  | 01 RWCNR02-SEGMENT | RWCNR02 |
| ACCOUNT EXTENSION SEGMENTS (BASE AND MISC PRIVACY POLICY) | 01 XACNA00-SEGMENT | XACNA00 |
|  | 01 XACNA06-SEGMENT | XACNA06 |

#### 3.2.5 IMS Tables select / read (GU / GNP)

| IMS Table | Action | Function | Code |
|-----------|--------|----------|------|
| AMSAM00 / AMSAM01 | Get Unique | 2154-GU-AM01 | EXEC DLI GU<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM00)</span><br>        WHERE (AMFAM00K = AM00-ACCOUNT-KEY)<br>    <span style="color:orange">SEGMENT (AMSAM01)</span><br>        WHERE (AMFAM01K = AM01-ACCT-CUST-KEY)<br>    INTO (AMCNM01-SEGMENT)<br>END-EXEC |
| AMSAM01 | Get Next Parent | 2440-GET-OTHER-AM01S | EXEC DLI GNP<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM01)</span><br>    INTO (AMCNM01-SEGMENT)<br>    WHERE (RELSTAT = WS-REL-STAT-APPV<br>        OR    RELSTAT = WS-REL-STAT-BLANK)<br>END-EXEC |
| AMSAM00 / AMSAM0A | Get Unique | 4320-UPDATE-AM0A | EXEC DLI GU<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM0A)</span><br>    FROM   (AMCNM0A-SEGMENT)<br>END-EXEC |
| AMSAM00 | Get Unique | 8310-GU-AM00-ACCT-ROOT | EXEC DLI GU<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM00)</span><br>    INTO (AMCNM00-SEGMENT)<br>    WHERE (AMFAM00K = <br>        CMS-AM00-ACCOUNT-KEY)<br>END-EXEC |
| AMSAM01 | Get Next Parent | 8320-GNP-AM01-ACCT-CUST | EXEC DLI GNP<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM01)</span><br>    INTO (AMCNM01-SEGMENT)<br>    WHERE (AMFAM01K = AM01-ACCT-CUST-KEY<br>        AND   RELSTAT ^= WS-AM01-ACCT-<br>        RELSTAT-MERGED)<br>END-EXEC |
| AMSAM01 | Get Next Parent | 8325-GET-PRIME | EXEC DLI GNP<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM01)</span><br>    FIRST<br>    INTO (AMCNM01-SEGMENT)<br>    WHERE (RELSTAT = WS-REL-STAT-APPV<br>        OR    RELSTAT = WS-REL-STAT-<br>BLANK)<br>END-EXEC |
| CUSCM00 | Get Unique | 8330-GU-CUST-BASE-INFO | EXEC DLI GU<br>    <span style="color:orange">USING PCB (DBDCU01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (CUSCM00)</span><br>    INTO (CUCNM00-SEGMENT)<br>    WHERE (CUFCM00K = <br>        CM00-CUS-BASE-INFO-KEY)<br>END-EXEC |
| CUSCM00 | Get Unique | 8331-GU-CUST-DATA-PRIM | EXEC DLI GU<br>    <span style="color:orange">USING PCB (DBDCU01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (CUSCM00)</span><br>    INTO (CUCNM00-SEGMENT)<br>    WHERE (CUFCM00K = <br>        CM00-CUS-BASE-INFO-KEY)<br>END-EXEC |
| CUSCM01 | Get Next Parent | 8335-READ-CUSTOMER-ADDRESS | EXEC DLI GNP <span style="color:orange">USING<br>    PCB     (DBDCU01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (CUSCM01)</span><br>    INTO    (WS-CUST-ADDR-SEGMENT)<br>    WHERE   (CUFCM01K = CM01-ADDRESS-INFO-<br>KEY)<br>END-EXEC |
| AMSAM00 / AMSAM01 | Get Unique | 8350-GU-AM00-AND-AM01 | EXEC DLI GU<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM00)</span><br>    SEGLEVEL<br>    WHERE (AMFAM00K = CMS-AM00-ACCOUNT-KEY)<br>    <span style="color:orange">SEGMENT (AMSAM01)</span><br>    INTO (AMCNM01-SEGMENT)<br>    WHERE (AMFAM01K = AM01-ACCT-CUST-KEY<br>        AND   RELSTAT ^= WS-AM01-ACCT-<br>RELSTAT-MERGED)<br>END-EXEC |
| CUSCM02 | Get Next Parent | 8355-READ-CM02-PERS-EMBOSS | EXEC DLI GNP <span style="color:orange">USING<br>    PCB     (DBDCU01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (CUSCM02)</span><br>    INTO    (WS-PERS-NAME-SEGMENT)<br>    WHERE   (CUFCM02K = CM02-ACCT-RELSHP-<br>INFO-KEY)<br>END-EXEC |
| CUSCM05 | Get Next Parent | 8360-READ-CM05-PERS-INFO | EXEC DLI GNP <span style="color:orange">USING<br>    PCB     (DBDCU01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (CUSCM05)</span><br>    INTO    (CUCNM05-SEGMENT)<br>END-EXEC |
| CUSCM07 | Get Unique | 8370-READ-CM07-CUSTOM-DATA | EXEC DLI GU <span style="color:orange">USING<br>    PCB     (DBDCU01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (CUSCM07)</span><br>    INTO    (CUCNM07-SEGMENT)<br>    WHERE   (CUFCM07K = WS-VALID-INFO-CODE)<br>END-EXEC |
| CUSCM07 | Get Unique | 8380-READ-CM07-CUSTOM-DATA-04 | EXEC DLI GU <span style="color:orange">USING<br>    PCB     (DBDCU01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (CUSCM07)</span><br>    INTO    (CUCNM07-SEGMENT2)<br>    WHERE   (CUFCM07K = WS-VALID-INFO-CODE)<br>END-EXEC |
| CUSCM07 | Get Unique | 8390-READ-CM07-CUSTOM-DATA-02 | EXEC DLI GU <span style="color:orange">USING<br>    PCB     (DBDCU01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (CUSCM07)</span><br>    INTO    (CUCNM07-SEGMENT3)<br>    WHERE   (CUFCM07K = WS-VALID-INFO-CODE)<br>END-EXEC |
| RWSRM00 | Get Unique | 8450-GU-REWARDS-RW00 | EXEC DLI GU <span style="color:orange">USING<br>    PCB     (DBDRW01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (RWSRM00)</span><br>    INTO    (RWCNR00-SEGMENT)<br>    WHERE   (RWFRM00K = RW00-ACCOUNT-KEY)<br>END-EXEC |
| RWSRM00 / RWSRM01 | Get Next Parent | 8453-GNP-REWARDS-EARNING-RW01 | EXEC DLI GNP<br>    <span style="color:orange">USING PCB (DBDRW01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (RWSRM00)</span><br>        WHERE (RWFRM00K = RW00-ACCOUNT-KEY)<br>    <span style="color:orange">SEGMENT (RWSRM01)</span><br>        INTO (RWCNR01-SEGMENT)<br>END-EXEC |
| RWSRM00 / RWSRM02 | Get Unique | 8455-GNP-REWARDS-FULFILL-RW02 | EXEC DLI GU<br>    <span style="color:orange">USING PCB (DBDRW01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (RWSRM00)</span><br>    SEGLEVEL<br>        WHERE (RWFRM00K = RW00-ACCOUNT-KEY)<br>    <span style="color:orange">SEGMENT (RWSRM02)</span><br>        INTO (RWCNR02-SEGMENT)<br>        WHERE (RWFRM02K = RW02-FULFILL-KEY)<br>END-EXEC |
| XASXA00/ XASXA06 | Get Unique | 8460-GU-EXTENSION-XA00-XA06 | EXEC DLI GU <span style="color:orange">USING<br>    PCB     (DBDXA01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (XASXA00)</span><br>    INTO    (XACNA00-SEGMENT)<br>    WHERE   (XAFXA00K = XA00-ACCOUNT-KEY)<br>    <span style="color:orange">SEGMENT (XASXA06)</span><br>    INTO    (XACNA06-SEGMENT)<br>    WHERE   (XAFXA06K = XA06-MISC-ACCT-KEY)<br>END-EXEC |
| AMSAM00 | Get Unique | 9250-READ-REPL-AM00-CONV-CHRS | EXEC DLI GU<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM00)</span><br>    INTO (AMCNM00-SEGMENT)<br>    WHERE (AMFAM00K = <br>            AM00-ACCOUNT-KEY)<br>END-EXEC |

#### 3.2.6 IMS Tables Insert (ISRT)

| IMS Table | Action | Function | Code |
|-----------|--------|----------|------|
| AMSAM00 / AMSAM0A | Insert | 4310-INSERT-AM0A | EXEC DLI ISRT<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM00)</span><br>    WHERE   (AMFAM00K = AM00-ACCOUNT-KEY)<br>    <span style="color:orange">SEGMENT (AMSAM0A)</span><br>    FROM    (AMCNM0A-SEGMENT)<br>END-EXEC |

#### 3.2.7 IMS Tables Replace / Update (REPL)

| IMS Table | Action | Function | Code |
|-----------|--------|----------|------|
| AMSAM0A | Replace | 4320-UPDATE-AM0A | EXEC DLI REPL<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM0A)</span><br>    FROM    (AMCNM0A-SEGMENT)<br>END-EXEC |
| AMSAM01 | Replace | 6350-RANDOM-NUMBER-CHECK | EXEC DLI REPL<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM01)</span><br>    FROM    (AMCNM01-SEGMENT)<br>END-EXEC |
| AMSAM01 | Replace | 8353-UPDATE-AM01 | EXEC DLI REPL<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM01)</span><br>    FROM    (AMCNM01-SEGMENT)<br>END-EXEC |
| AMSAM00 | Replace | 9250-READ-REPL-AM00-CONV-CHRS | EXEC DLI REPL<br>    <span style="color:orange">USING PCB (DBDAM01-PCB-NUM1)</span><br>    <span style="color:orange">SEGMENT (AMSAM00)</span><br>    FROM    (AMCNM00-SEGMENT)<br>END-EXEC |

### 3.3 Called subprogram modules

> Table of all called programs, initialization needed and calling parameters. Check the program for each CALL and take the input and call parameters.

> IMPORTANT !!! I detected that the same program in 2 calls is called with different parameters. Maybe gather all the parameters from both calls and make unique list

 
> CCCBR010 (Made this as sample if I can show both input and output in the same place)
 
<p style="background-color: yellow; padding: 10px; color: red">
vvv <b>I DON'T UNDERSTAND THIS</b> vvv
</p>

| Input values - Initialization |  |
|------------------------------|------------------|
| Field Name | Initialization Value |
| CCCOR-CLIENT-ID-NUMBER | TD-CLIENT-NUM |
| CCCOR-APPLICATION-NUMBER | TD-APPLICATION-NUM |
| CCCOR-APPLICATION-SUFFIX | TD-APPLICATION-SUFFIX |
| CCCOR-REQUESTED-ACTION | 'CARD' |
| CCCOR-CYCLE-STATUS-FLAG | 'N' |
| Output values – Parameters passed in the call |  |
| CCCOR-PASS-FIELDS |  |

<p style="background-color: yellow; padding: 10px; color: red">
^^^ <b>I DON'T UNDERSTAND THIS</b> ^^^
</p>

> Original format, just the passed parameters to the call

| Program called | Parameters passed in the call |
|---------------|------------------------------|
| CCCBR010 | CCCOR-PASS-FIELDS<br>AMCNM00-SEGMENT<br>AMCNM01-SEGMENT<br>CUCNM00-SEGMENT<br>WS-CUST-ADDR-SEGMENT<br>CUCNM05-SEGMENT<br>CUCNM07-SEGMENT3<br>RWCNR02-SEGMENT<br>XACNA06-SEGMENT<br>DBDAM01-PCB-NUM1<br>GSAM-FIXED01-PCB-MASK<br>GSAM-FIXED10-PCB-MASK<br>WS-PDGCB-CNTG-FEE-OPT<br>WS-CURRENT-DATE<br>WS-CM02-CR-BUR-CD<br>WS-DUEDAY-TAB-READ<br>WS-BSF-IND<br>WS-DAYS-DIFF-CHECK<br>CHIP-CARD-WORK-FIELDS<br>WS-CCOPT-FLAT-SPEC<br>RWCNR00-SEGMENT<br>RWCNR02-SEGMENT<br>WS-DAY1-FLAG |
| CCPBS020 | CMS-PRODUCTION-PASSING-AREA |
| CCPBS070 | INSERT-ROUTINE-PARMS<br>CMS-COMMON-FIELDS<br>CMS-PRODUCTION-PASSING-AREA<br>TS2-INVENTORY-RECORD<br>TRANSMISSION-DATA<br>GSAM-FIXEDX4-PCB-MASK |
| CCPBS095 (WS-EMBIP-PROG)<br><br><span style="color:red">This is dynamically set. Not sure how to get it with code</span> | TRANSMISSION-DATA<br>CARD-MAILER-INFORMATION<br>EMB-FORMATTED-EMBOSSING<br>EMB-FORMAT-LAYOUTS<br>INSERT-ROUTINE-PARMS<br>CMS-COMMON-FIELDS<br>CMS-PRODUCTION-PASSING-AREA<br>CARD-MGT-WORK-FIELDS<br>WORK-TRANS-HEX-CODES |
| CCPBS610 | TRANSMISSION-DATA<br>CMS-COMMON-FIELDS<br>CMS-PRODUCTION-PASSING-AREA<br>CARD-MAILER-INFORMATION<br>TS2-INVENTORY-RECORD<br>AMCNM00-SEGMENT<br>CUCNM00-SEGMENT<br>WS-CUST-ADDR-SEGMENT<br>DBDAM01-PCB-NUM1<br>DBDCU01-PCB-NUM1<br>GSAM-FIXED04-PCB-MASK<br>RPT-ROUTINE-PARMS<br>CARD-MGT-WORK-FIELDS |
| CCPBS622 | TRANSMISSION-DATA<br>AMCNM00-SEGMENT<br>AMCNM0A-SEGMENT<br>AMCNM01-SEGMENT<br>CUCNM00-SEGMENT<br>CUCNM07-SEGMENT<br>CUCNM07-SEGMENT2<br>CMS-COMMON-FIELDS<br>CMS-PRODUCTION-PASSING-AREA<br>WORK-TRANS-HEX-CODES<br>ENP-RECORD-LAYOUT<br>CARD-MGT-WORK-FIELDS<br>DBDCU01-PCB-NUM1<br>NEW-RRDM-COPYBOOK-FIELDS<br>KEY-EXCHANGE-AREA |
| CCPBS630 | TRANSMISSION-DATA<br>AMCNM00-SEGMENT<br>AMCNM01-SEGMENT<br>CUCNM00-SEGMENT<br>CMS-COMMON-FIELDS<br>CMS-PRODUCTION-PASSING-AREA<br>EMBC-FORMATTED-EMBOSSING<br>TS2-INVENTORY-RECORD<br>WORK-TRANS-HEX-CODES<br>DBDAM01-PCB-NUM1<br>DBDCU01-PCB-NUM1<br>GSAM-FIXED04-PCB-MASK<br>WS-PERSONAL-EMBOSS<br>CARD-MGT-WORK-FIELDS<br>CHIP-CARD-WORK-FIELDS |
| CCPBS800 | CHIP-CARD-WORK-FIELDS<br>CMS-PRODUCTION-PASSING-AREA<br>CC00-SEGMENT<br>CC10-SEGMENT<br>CC20-SEGMENT<br>AM00-ACCOUNT-IDENTIFIER<br>AMCNM01-SEGMENT<br>DBDCC01-PCB-NUM1<br>DBDSH01-PCB-NUM1<br>GSAM-FIXED06-PCB-MASK<br>CHIP-KEY-RECORD<br>GSAM-FIXED08-PCB-MASK<br>TRANSMISSION-DATA<br>NEXT-NUMBER-FILE-LAYOUT<br>CARD-MGT-WORK-FIELDS<br>WS-CCOPT-FLAT-SPEC<br>DBDAM01-PCB-NUM1<br>KEY-EXCHANGE-AREA<br>GSAM-FIXED11-PCB-MASK |
| CRVBS030 | ELCEL00-SEGMENT<br>ELCEL02-SEGMENT<br>DBDEL01-PCB-NUM1<br>CHECKPOINT-WORK-FIELDS<br>EVP-COM-MOD-PCB<br>GSAM-VARIABLE01-PCB-MASK |

### 3.4    Output

| Output File | Function | Copybook fields written |
|------------|----------|------------------------|
| GSAM-FIXED02 | 5110-WRITE-TMIS-DATA | CTMXT2CB |
| GSAM-FIXED03 | 5210-WRITE-EMBOSSED-RPT | CCPEMBCB |
| GSAM-FIXED06 | 5510-WRITE-REEVAL-RPT | CCPRDECB |

> TBD. The sample template says 3 files, but in the code I see more. Need more checking

### 3.5    Exception scenarios

The CCPBD600 program will abend if any of the following conditions is meet

1. On READ the NEXT-NUMBER-FILE "AT END" condition (1000-)
2. If WS-PRMFILE-FILE-STATUS not equal to "00" on PRMFILE file OPEN (1100-)
3. If WS-PRMFILE-FILE-STATUS not equal "00" on PRMFILE READ (1200-)
4. If DAY1FILE-ST not equal to "00" (OK) on DAY1FILE OPEN (1500-)
Etc...

> Big list of every time a check is done in the program that can provoke the program to abort