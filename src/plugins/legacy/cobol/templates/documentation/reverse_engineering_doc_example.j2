# Reverse Engineering Template Technical Refinement Document

&nbsp;

&nbsp;

&nbsp;

&nbsp;

&nbsp;

|                         |                             |
|-------------------------|-----------------------------|
| **Block:**              | Chase Card Core             |
| **Application:**        | Reissue                     |
| **Mainframe Program:**  | {{module_id}}               |
| **Functional Element:** | {{el_type}}: {{el_name}}    |
| **Element UUID:**       | {{el_uuid}}                 |
| **Version:**            | 1.00                        |
| **Document Status:**    | Draft                       |

## Table of Contents

1. Functional Information
   - Business Requirement Management (BRM)
   - Process Flow (step sequence)
   - Sub-program modules

2. Process diagrams of the main program
   - Mermaid diagrams 
     - Flow diagram
   - UML diagrams
     - Sequence diagram

3. Process description
   - Description
     - Incoming Files
     - Input Parameters
     - Database connection
   - Business rules
   - Steps
   - Database integration
     - Output Schema/table structure
     - Message structure
     - Input query notes (SQL and PL)
   - Sub program
     - COBOL/JCL description
   - Output criteria
     - Database update
     - Event creation
     - Metrics
   - Events created
   - API Links
     - Response
   - Test cases (positive and negative)
   - Exception scenarios

4. Process diagrams for subprograms/paragraphs
    - Mermaid diagrams
        - Flow diagram
    - UML diagrams
        - Use case diagram
        - Sequence diagram

5. Process description for Sub Program
   - Description
     - Incoming Files
     - Database connection
   - Business rules
   - Steps
   - Database integration
     - Output table
     - Message
     - DB logs
   - Sub program
   - Output criteria
   - Test case
   - Exception scenarios

6. Reference Documentation

7. Commonly asked questions (FAQ/discussed in calls)
   - Questions discussed in previous calls
   - Questions arise in review meeting
   - Questions discussed in testing team

## 1. Functional information

### 1.a. Business Requirement: (Sample..................)

Reissue is the decision made by Chase prior to the expiration of account to renew the account. The decision to renew an account is taken in the Reissue Decision process, where the account is evaluated based on the rules and conditions defined in the dynamically assigned Reissue Options.

The timeframe for Reissue evaluation is determined by the expiration date assigned to the account.

Accounts qualifying for Reissue evaluation are gathered into the Reissue Driver File that is passed to the Reissue Decision process.

Accounts are added to or excluded from Reissue Driver File for processing based on the criteria defined (see the Reissue Driver File Process Description for details).

Accounts that would satisfy the criteria for reissue evaluation on a non-processing day will be included in the Driver File for the next processing day for reissue evaluation.
Accounts are evaluated for reissue two (2) months prior to expiration so the card can be sent to the Cardmember on approval before the actual expiration date, to enhance customer experience.

Based on the outcome of the Reissue Decision process, the Reissue Action process will perform the appropriate actions.

## 1.b. Process flow step sequence

| Label | Change Log ID | UI/Process | Requirement Description and Validation Criteria |
|-------|--------------|------------|----------------------------------------------|
| PR1 | 1.0 | Process | The system shall provide a mechanism to review the account statuses to determine if it is eligible for card reissuance. |
| PR2 | 1.0 | Process | The system shall provide a mechanism to determine the account population to route the account through the appropriate reissue decisioning strategies. |
| PR3 | 1.0 | Process | The system shall provide a mechanism to determine the account conditions to make the reissue decision. |
| PR3 | 1.0 | Process | The system shall provide a mechanism to make a decision if the card's should be reissued for the account. |
| PR4 | 1.0 | Process | The system shall provide a mechanism to include the reissue decision action for the account in the file to the downstream systems. |
| PR1 | 1.0 | Process | The system shall provide a mechanism to trigger the reissue event (Event 693). |
| PR1 | 1.0 | Process | The system shall provide a mechanism to send the appropriate Approval/Decline letters to the customer if applicable, based on the Reissue Action ID. |
| PR1 | 1.0 | Process | The system shall provide a mechanism to send the embossing requests for new cards for reissue approvals. |
| PRDk9 | 1.0 | Process | The system shall provide a mechanism to update the account expiration date in case of approvals. |
| PRDk10 | 1.0 | Process | The system shall provide a mechanism to send the Reissue outcome feed to CDW. |
| PRDk11 | 1.3 | Process | The system shall provide a mechanism to evaluate if an account should be included or excluded from the Reissue Driver File. |

### Called Sub program module

Function 1

table

ims fields name, data type, enrichment

DB2 fields name, data type, enrichment

Input fields

field 1 - enrichment done

Output fields

Logic information

Flow diagram
or UML Sequence diagram
