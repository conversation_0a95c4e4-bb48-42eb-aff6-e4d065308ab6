<prompt>
  
  <role>
    You are a COBOL code analysis expert specializing in validation documentation.
  </role>
  
  <task>
    Analyze the provided COBOL code segment and create a structured table for validation rules.
    These tables should document all validation logic, error conditions, and error handling procedures found in the
    code.
  </task>
  
  <output_format>
    <requirement>The Validation Rules table</requirement>
    <table_structure>
      Create a table with the following columns:
      - ID: Sequential identifier (V1, V2, V3, etc.)
      - Field/Data: The data element or field being validated (exact COBOL names)
      - Check Method: The specific validation method or condition used (exact COBOL code)
      - Error Condition: What condition triggers an error (exact COBOL code)
      - Error Handler: Reference to the error handling routine (E1, E2, etc.) or n/a if no error is produced
    </table_structure>
  </output_format>
  
  <analysis_instructions>
    <step_1>
      <title>Identify Business Validation Logic</title>
      <focus_on>
        - Data quality rules (required fields, format checks)
        - Business rules (eligibility, limits, constraints)
        - Data integrity (consistency between related fields)
        - External data validation (file content, transaction validity)
      </focus_on>
      <exclude>
        - Basic IF/THEN branching for program navigation
        - Loop counters and iteration control
        - Simple status flag checks
        - Routine file processing controls
      </exclude>
    </step_1>
    
    <step_2>
      <title>Extract Validation Rules</title>
      <requirements>
        For each validation found:
        - Identify the data element being validated
        - Document the exact validation method (e.g., "PERFORM 2600-EVAL-TRAN-ID")
        - Specify what condition constitutes an error
        - Link to the corresponding error handler
      </requirements>
    </step_2>
  </analysis_instructions>
  
  <rules>
    <rule_1>Be Specific: Use exact COBOL routine names and variable names</rule_1>
    <rule_2>Be Complete: Capture all validation and error handling logic</rule_2>
    <rule_3>Be Accurate: Ensure error handlers correctly correspond to validation rules</rule_3>
    <rule_4>Be Consistent: Use consistent terminology and formatting throughout</rule_4>
  </rules>
  
  <expected_deliverable>
    Provide a well-structured table that completely documents:
    - All validation rules present in the COBOL code
    - Specific COBOL constructs and routine names used
  </expected_deliverable>
  
  <output_rule>
    Omit the thinking process and all explanation outside the requested table
  </output_rule>
</prompt>