import os
import logging
from typing import List, Tuple

from src.platform.tools.data.kuzu import KuzuConnector
from .config import Config
from .preprocessor import CopybookExpander, TextNormalizer
from .parser import <PERSON><PERSON><PERSON><PERSON><PERSON>, AstBuilder
from .ir import <PERSON><PERSON><PERSON><PERSON>, IRSerializer


class Orchestrator:
    """Coordinates the COBOL parsing process"""

    def __init__(self, config: Config):
        """Initialize the orchestrator with configuration

        Args:
            config: The configuration object
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize components
        copybook_path = self.config.get('directories.copybooks')

        # Handle relative path by converting to absolute
        if copybook_path and not os.path.isabs(copybook_path):
            copybook_path = os.path.abspath(copybook_path)
            self.logger.debug(f"Resolved copybook path to: {copybook_path}")

        # Ensure the copybook path exists and create list of dirs with only one entry
        if copybook_path and os.path.exists(copybook_path):
            copybook_dirs = [copybook_path]
            self.logger.debug(f"Confirmed copybook directory: {copybook_path}")
        else:
            if copybook_path:
                self.logger.warning(f"Copybook directory does not exist: {copybook_path}")
            copybook_dirs = []

        # Add debug logging
        self.logger.debug(f"Using copybook directories: {copybook_dirs}")

        max_recursion_depth = self.config.get('parser.max_recursion_depth', 10)
        self.copybook_expander = CopybookExpander(copybook_dirs, max_recursion_depth)

        preserve_comments = self.config.get('parser.preserve_comments', True)
        self.text_normalizer = TextNormalizer(preserve_comments)

        self.parser = CobolParser()
        self.ir_builder = IRBuilder()
        self.ir_serializer = IRSerializer()

        # Neo4j connector
        self.graph_db_connector = KuzuConnector()

    def process_files(self, file_paths: List[str]) -> Tuple[bool, int]:
        """Process a list of COBOL files

        Args:
            file_paths: List of paths to COBOL files

        Returns:
            Tuple of (overall success, failure count)
        """
        self.logger.info(f"Processing {len(file_paths)} files")
        failure_count = 0

        # Connect to Neo4j
        if not self.graph_db_connector.connect():
            self.logger.error("Failed to connect to Neo4j, continuing with IR only")
        else:
            self.graph_db_connector.create_schema()

        # Process each file
        for file_path in file_paths:
            try:
                success = self.process_file(file_path)
                if not success:
                    failure_count += 1
            except Exception as e:
                self.logger.error(f"Error processing {file_path}: {str(e)}")
                failure_count += 1

        # Disconnect from Neo4j
        self.graph_db_connector.disconnect()

        # Return overall success status and failure count
        return (failure_count == 0), failure_count

    def process_file(self, file_path: str) -> bool:
        """Process a single COBOL file

        Args:
            file_path: Path to the COBOL file

        Returns:
            True if processing was successful, False otherwise
        """
        self.logger.info(f"Processing file: {file_path}")

        # Read file
        try:
            with open(file_path, 'r') as f:
                source_text = f.read()
        except Exception as e:
            self.logger.error(f"Failed to read file {file_path}: {str(e)}")
            return False

        # Extract module ID from filename
        file_name = os.path.basename(file_path)
        module_id = os.path.splitext(file_name)[0].upper()

        try:
            # Preprocess - copybook expansion and normalization
            self.logger.info(f"Expanding copybooks for {file_path}")
            expanded_text = self.copybook_expander.expand_source(source_text, file_name)
            self.logger.info(f"Normalizing text for {file_path}")
            normalized_text = self.text_normalizer.normalize(expanded_text) # TODO ?? why we do this here and here src/plugins/legacy/cobol/tools/preprocessor.py:156

            # Explicitly save the preprocessed file
            output_dir = self.config.get('directories.output')
            if output_dir:
                # Save preprocessed file (after copybook expansion and normalization)
                preprocessed_file = os.path.join(output_dir, f"{module_id}_preprocessed.cob")
                self.logger.info(f"Saving preprocessed file to {preprocessed_file}")
                with open(preprocessed_file, 'w') as f:
                    f.write(normalized_text)

            # Parse
            ast = self.parser.parse(normalized_text, file_name)
            if not ast:
                self.logger.error(f"Failed to parse {file_path}")
                return False

            # Build AST
            ast_builder = AstBuilder(module_id, file_name, source_text)
            ast = ast_builder.transform(ast)

            # Build IR
            ir = self.ir_builder.build(ast)

            # Save IR to file
            if output_dir:
                output_file = os.path.join(output_dir, f"{module_id}.json")
                self.logger.info(f"Saving IR file to {output_file}")
                self.ir_serializer.to_json(ir, output_file)

            # Store in Neo4j
            if self.graph_db_connector.connection:
                if not self.graph_db_connector.store_ir(ir):
                    self.logger.error(f"Failed to store {file_path} in graph database")
                    return False

            self.logger.info(f"Successfully processed {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error processing {file_path}: {str(e)}")
            return False
