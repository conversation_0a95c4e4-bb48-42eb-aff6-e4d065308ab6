import re
import logging
from typing import Any, Optional
from lark import Tree, Token


# noinspection PyMethodMayBeStatic
class CobolParser:
    """Parser for IBM ENTERPRISE COBOL source code"""

    def __init__(self):
        """Initialize the COBOL parser"""
        self.logger = logging.getLogger(__name__)

    def parse(self, source_text: str, file_name: str = "<unknown>") -> Optional[Any]:
        """Parse COBOL source text into an abstract syntax tree

        Args:
            source_text: The normalized COBOL source text
            file_name: The name of the source file (for error reporting)

        Returns:
            The abstract syntax tree or None if parsing failed
        """
        try:
            self.logger.info(f"Parsing {file_name}")

            # Skip the full parser and go straight to the regex approach
            # This is much more robust for real-world COBOL
            divisions = self._extract_divisions(source_text, file_name)
            if divisions:
                self.logger.info("Using regex-based parsing which is more reliable for complex COBOL")
                # Store the actual division text in the AST for later processing
                ast = self._create_fallback_ast(divisions, file_name)
                # Save the divisions content directly in the AST as metadata
                ast.divisions_content = {
                    'identification': divisions.get('identification', ''),
                    'environment': divisions.get('environment', ''),
                    'data': divisions.get('data', ''),
                    'data_sections': divisions.get('data_sections', {}),
                    'procedure': divisions.get('procedure', ''),
                    'procedure_sections': divisions.get('procedure_sections', {})
                }
                return ast
            else:
                self.logger.warning("Could not extract COBOL divisions using regex")
                return None

        except Exception as e:
            self.logger.error(f"Unexpected error parsing {file_name}: {str(e)}")
            return None


    def _is_dummy_exit_paragraph(self, paragraph: str) -> bool:
        lines = paragraph.splitlines()
        found_exit_statement = False

        for line in lines:
            stripped = line.strip()

            # Skip comments, empty lines, and page breaks
            if not stripped or stripped.startswith('*') or stripped == '/':
                continue

            # Handle lines that might contain both paragraph name and statements
            # Split by periods to separate potential paragraph name from statements
            parts = [part.strip() for part in stripped.split('.') if part.strip()]

            for part in parts:
                part_lower = part.lower()
                if part_lower == 'exit' or part_lower == 'eject':
                    found_exit_statement = True
                elif part and not part_lower.endswith('-exit') and not part_lower.endswith('-eject'):
                    # Found actual logic (not just paragraph naming)
                    return False

        return found_exit_statement


    def _extract_divisions(self, source_text: str, file_name: str) -> dict:
        """Extract the main divisions from the COBOL source using regex

        This method uses a more robust regex-based approach to handle real-world COBOL code.

        Args:
            source_text: The normalized COBOL source text
            file_name: The name of the source file

        Returns:
            A dictionary with the extracted divisions
        """
        import os
        divisions = {}

        try:
            # Extract program ID from the identification division
            program_id_match = re.search(r'PROGRAM-ID\s*\.\s*([A-Za-z0-9-]+)', source_text, re.IGNORECASE | re.DOTALL)
            if program_id_match:
                divisions['program_id'] = program_id_match.group(1).strip()  # This should be stripped as it's not code indentation
            else:
                # Try to extract from filename
                basename = os.path.basename(file_name) if file_name else "UNKNOWN"
                divisions['program_id'] = os.path.splitext(basename)[0].upper()

            # Find divisions
            identification_match = re.search(r'IDENTIFICATION\s+DIVISION', source_text, re.IGNORECASE)
            environment_match = re.search(r'ENVIRONMENT\s+DIVISION', source_text, re.IGNORECASE)
            data_match = re.search(r'DATA\s+DIVISION', source_text, re.IGNORECASE)
            procedure_match = re.search(r'PROCEDURE\s+DIVISION', source_text, re.IGNORECASE)

            # If we found the divisions, extract their content based on their start positions
            if identification_match:
                id_start = identification_match.start()
                id_end = environment_match.start() if environment_match else (data_match.start() if data_match else (procedure_match.start() if procedure_match else len(source_text)))
                divisions['identification'] = source_text[id_start:id_end].rstrip()

            if environment_match:
                env_start = environment_match.start()
                env_end = data_match.start() if data_match else (procedure_match.start() if procedure_match else len(source_text))
                divisions['environment'] = source_text[env_start:env_end].rstrip()

            if data_match:
                data_start = data_match.start()
                data_end = procedure_match.start() if procedure_match else len(source_text)
                divisions['data'] = source_text[data_start:data_end].rstrip()

                # Look for sections within the data division
                data_content = source_text[data_start:data_end]
                data_sections_found = {}

                # Check for data sections
                file_section_match = re.search(r'FILE\s+SECTION', data_content, re.IGNORECASE)
                working_storage_match = re.search(r'WORKING-STORAGE\s+SECTION', data_content, re.IGNORECASE)
                local_storage_match = re.search(r'LOCAL-STORAGE\s+SECTION', data_content, re.IGNORECASE)
                linkage_match = re.search(r'LINKAGE\s+SECTION', data_content, re.IGNORECASE)

                if file_section_match:
                    file_start = file_section_match.start()
                    file_end = working_storage_match.start() if working_storage_match else (local_storage_match.start() if local_storage_match else (linkage_match.start() if linkage_match else len(data_content)))
                    data_sections_found['file'] = data_content[file_start:file_end].rstrip()

                if working_storage_match:
                    ws_start = working_storage_match.start()
                    ws_end = local_storage_match.start() if local_storage_match else (linkage_match.start() if linkage_match else len(data_content))
                    data_sections_found['working_storage'] = data_content[ws_start:ws_end].rstrip()

                if local_storage_match:
                    ls_start = local_storage_match.start()
                    ls_end = linkage_match.start() if linkage_match else len(data_content)
                    data_sections_found['local_storage'] = data_content[ls_start:ls_end].rstrip()

                if linkage_match:
                    linkage_start = linkage_match.start()
                    data_sections_found['linkage'] = data_content[linkage_start:].rstrip()

                # Always set data_sections in divisions, even if empty
                divisions['data_sections'] = data_sections_found

            if procedure_match:
                proc_start = procedure_match.start()
                procedure_content = source_text[proc_start:].rstrip()
                divisions['procedure'] = procedure_content

                # Extract sections and paragraphs from the procedure division
                procedure_sections = {}

                # First, let's extract the procedure division header line
                proc_div_header_match = re.search(r'^(\s*PROCEDURE\s+DIVISION.*?\.)', procedure_content, re.IGNORECASE | re.MULTILINE)
                proc_div_header = ""
                procedure_content_after_header = procedure_content

                if proc_div_header_match:
                    proc_div_header = proc_div_header_match.group(1)
                    procedure_content_after_header = procedure_content[proc_div_header_match.end():]

                # Initialize the unnamed section
                if 'unnamed_section' not in procedure_sections:
                    procedure_sections['unnamed_section'] = {
                        'name': 'UNNAMED-SECTION',
                        'paragraphs': {}
                    }

                lines = procedure_content_after_header.split('\n')
                declaratives_content = []
                # 1. Skip DECLARATIVES if present (and save them to a dedicated `declaratives_content` list)
                # TODO >> Probably, we should also add this declaratives into the AST graph, but their calls
                #  usually managed by OS so they shouldn't have CALLS | REFERENCES relations in the graph
                start_line = self._skip_declaratives(declaratives_content, lines)

                # 2. Find all PERFORM targets to identify subroutines
                performed_paragraphs = self._collect_all_performed_statements(start_line, lines)

                # 3. Find STOP RUN., GOBACK., or the declaration of the first paragraph present in the `performed` set.
                unnamed_content = self._get_entry_paragraph_content(start_line, lines, performed_paragraphs)
                self.logger.debug(f"Procedure content before first paragraph: '{unnamed_content}'")

                # Add this as a special entry paragraph if it has a content
                if unnamed_content and not re.match(r'^\s*$', unnamed_content):
                    self.logger.warning(f"*** EXTRACTING UNNAMED ENTRY PARAGRAPH with {len(unnamed_content)} chars ***")
                    self.logger.warning(f"*** ENTRY PARAGRAPH CONTENT: '{unnamed_content[:50]}...' ***")
                    procedure_sections['unnamed_section']['paragraphs']['ENTRY-PARAGRAPH'] = unnamed_content

                section_matches = re.finditer(r'([A-Z0-9-]+)\s+SECTION\s*\.', procedure_content, re.IGNORECASE)

                # Get the positions of all section starts to determine boundaries
                section_positions = []
                for match in section_matches:
                    section_name = match.group(1).strip().upper()
                    section_positions.append((match.start(), section_name))

                # Extract paragraphs when no sections are defined
                if not section_positions:
                    # In COBOL, paragraphs are identified by names starting in area A (columns 8-11)
                    # or area B (columns 12+) followed by a period. We need to match only paragraph names,
                    # not periods within sentences or periods in string literals.

                    # Split the content into lines to process line by line
                    lines = procedure_content.split('\n')
                    paragraph_starts = []

                    # First pass: identify paragraph starts using the improved validation
                    for i, line in enumerate(lines):
                        # Check for paragraph names in area A or B
                        match = re.match(r'^\s{0,29}([A-Z0-9-]+)\s*\.\s*', line, re.IGNORECASE)
                        if match:
                            paragraph_name = match.group(1).strip().upper()
                            # Use the improved validation function
                            if self._is_valid_paragraph_name(paragraph_name):
                                paragraph_starts.append((i, paragraph_name))

                    # First, collect lines with comment blocks
                    comment_blocks = {}

                    # Process each paragraph and find its comments
                    for i, (line_idx, paragraph_name) in enumerate(paragraph_starts):
                        if line_idx > 0:
                            # Check backwards from the paragraph start to find comments
                            comment_lines = []
                            j = line_idx - 1

                            # Go backwards until we hit a non-empty, non-comment line
                            while j >= 0:
                                line = lines[j]
                                if re.match(r'^\s*\*', line):  # It's a comment
                                    comment_lines.insert(0, line)
                                elif line.strip():  # Non-empty, non-comment line
                                    break
                                j -= 1

                            if comment_lines:
                                comment_blocks[line_idx] = comment_lines

                    # Now extract paragraph content with proper comments
                    paragraphs = {}

                    for i, (line_idx, paragraph_name) in enumerate(paragraph_starts):
                        # Get comments that belong to this paragraph
                        paragraph_comments = comment_blocks.get(line_idx, [])

                        # Determine paragraph end
                        if i < len(paragraph_starts) - 1:
                            end_line_idx = paragraph_starts[i+1][0]

                            # We need to check if the comments before the next paragraph
                            # actually belong to that paragraph
                            next_para_comments = comment_blocks.get(end_line_idx, [])

                            # Extract lines for this paragraph, including its header line
                            para_lines = lines[line_idx:end_line_idx]

                            # Remove any comments that belong to the next paragraph
                            for comment in next_para_comments:
                                if comment in para_lines:
                                    para_lines.remove(comment)

                            # Combine the paragraph's comments with its lines
                            para_content = '\n'.join(paragraph_comments + para_lines)
                        else:
                            # Last paragraph goes to end of procedure division
                            para_content = '\n'.join(paragraph_comments + lines[line_idx:])

                        if not self._is_dummy_exit_paragraph(para_content) and paragraph_name in performed_paragraphs:
                            paragraphs[paragraph_name] = para_content.rstrip()
                        else:
                            self.logger.warning(f"Paragraph {paragraph_name} skipped as it is recognized as an EXIT paragraph.\nContent:\n{para_content}")

                    if paragraphs:
                        # Make sure we don't overwrite the existing unnamed_section if it already has an ENTRY-PARAGRAPH
                        if 'unnamed_section' not in procedure_sections:
                            procedure_sections['unnamed_section'] = {
                                'name': 'UNNAMED-SECTION',
                                'paragraphs': paragraphs
                            }
                        else:
                            # Merge the paragraphs with any existing ones (like the ENTRY-PARAGRAPH)
                            procedure_sections['unnamed_section']['paragraphs'].update(paragraphs)
                else:
                    # Process sections and their paragraphs
                    for i, (section_start, section_name) in enumerate(section_positions):
                        # Determine section end
                        section_end = section_positions[i+1][0] if i < len(section_positions) - 1 else len(procedure_content)

                        # Extract section content
                        section_content = procedure_content[section_start:section_end].rstrip()

                        # Use the same line-by-line approach for paragraphs within sections
                        section_lines = section_content.split('\n')
                        paragraph_starts = []

                        # First pass: identify paragraph starts using improved validation
                        for j, line in enumerate(section_lines):
                            # Skip the first line which has the section header
                            if j == 0 and "SECTION" in line:
                                continue

                            # Check for paragraph names in area A or B
                            match = re.match(r'^\s{0,29}([A-Z0-9-]+)\s*\.\s*', line, re.IGNORECASE)
                            if match:
                                paragraph_name = match.group(1).strip().upper()
                                # Use the improved validation function
                                if self._is_valid_paragraph_name(paragraph_name):
                                    paragraph_starts.append((j, paragraph_name))

                        # First, collect lines with comment blocks
                        comment_blocks = {}

                        # Process each paragraph and find its comments
                        for j, (line_idx, paragraph_name) in enumerate(paragraph_starts):
                            if line_idx > 0:
                                # Check backwards from the paragraph start to find comments
                                comment_lines = []
                                k = line_idx - 1

                                # Go backwards until we hit a non-empty, non-comment line
                                while k >= 0:
                                    line = section_lines[k]
                                    if re.match(r'^\s*\*', line):  # It's a comment
                                        comment_lines.insert(0, line)
                                    elif line.strip():  # Non-empty, non-comment line
                                        break
                                    k -= 1

                                if comment_lines:
                                    comment_blocks[line_idx] = comment_lines

                        # Now extract paragraph content with proper comments
                        paragraphs = {}

                        paragraph_name_for_logger = "No paragraph was found"
                        paragraph_content_for_logger = "No content was found"
                        for j, (line_idx, paragraph_name) in enumerate(paragraph_starts):
                            # Get comments that belong to this paragraph
                            paragraph_comments = comment_blocks.get(line_idx, [])
                            paragraph_name_for_logger = paragraph_name

                            # Determine paragraph end
                            if j < len(paragraph_starts) - 1:
                                end_line_idx = paragraph_starts[j+1][0]

                                # We need to check if the comments before the next paragraph
                                # actually belong to that paragraph
                                next_para_comments = comment_blocks.get(end_line_idx, [])

                                # Extract lines for this paragraph, including its header line
                                para_lines = section_lines[line_idx:end_line_idx]

                                # Remove any comments that belong to the next paragraph
                                for comment in next_para_comments:
                                    if comment in para_lines:
                                        para_lines.remove(comment)

                                # Combine the paragraph's comments with its lines
                                para_content = '\n'.join(paragraph_comments + para_lines)
                                paragraph_content_for_logger = para_content
                            else:
                                # Last paragraph goes to end of section
                                para_content = '\n'.join(paragraph_comments + section_lines[line_idx:])
                                paragraph_content_for_logger = para_content

                            if not self._is_dummy_exit_paragraph(para_content):
                                paragraphs[paragraph_name] = para_content.rstrip()
                            else:
                                self.logger.warning(f"Paragraph {paragraph_name} skipped as it is recognized as an EXIT paragraph.\nContent:\n{para_content}")

                        # If no paragraphs found, create an unnamed paragraph with the section content
                        if not paragraphs and len(section_lines) > 1:  # More than just the section header
                            # Skip the section header and use remaining content
                            unnamed_content = '\n'.join(section_lines[1:])

                            if not self._is_dummy_exit_paragraph(unnamed_content):
                                paragraphs["UNNAMED-PARA"] = unnamed_content.rstrip()
                            else:
                                self.logger.warning(f"Paragraph {paragraph_name_for_logger} skipped as it is recognized as an EXIT paragraph.\nContent:\n{paragraph_content_for_logger}")

                        procedure_sections[section_name] = {
                            'name': section_name,
                            'paragraphs': paragraphs
                        }

                if procedure_sections:
                    divisions['procedure_sections'] = procedure_sections

            # Check if we found the required divisions
            if 'identification' in divisions and 'procedure' in divisions:
                self.logger.info(f"Successfully extracted divisions from {file_name}")
                return divisions
            else:
                self.logger.error(f"Required divisions missing in {file_name}. Found divisions: {', '.join(divisions.keys())}")
                # Return empty dict to indicate failure (all COBOL programs must have at least ID and PROCEDURE divisions)
                return {}

        except Exception as e:
            self.logger.error(f"Error extracting divisions: {str(e)}")
            return {}

    def _create_fallback_ast(self, divisions: dict, file_name: str) -> Any:
        """Create a fallback AST from extracted divisions

        Args:
            divisions: Dictionary with the extracted divisions
            file_name: The name of the source file

        Returns:
            A simplified AST
        """
        # Create a simplified AST
        program = Tree('program', [])
        module = Tree('cobol_module', [])

        # Add identification division
        if 'identification' in divisions:
            ident_div = Tree('identification_division', [
                Token('IDENTIFICATION', 'IDENTIFICATION'),
                Token('DIVISION', 'DIVISION'),
                Token('DOT', '.'),
            ])

            # Add program ID if available
            if 'program_id' in divisions:
                program_id_entry = Tree('program_id_entry', [
                    Token('PROGRAM_ID', 'PROGRAM-ID'),
                    Token('DOT', '.'),
                    Token('IDENTIFIER', divisions['program_id']),
                    Token('DOT', '.'),
                ])
                ident_div.children.append(program_id_entry)

            module.children.append(ident_div)

        # Add environment division if available
        if 'environment' in divisions:
            env_div = Tree('environment_division', [
                Token('ENVIRONMENT', 'ENVIRONMENT'),
                Token('DIVISION', 'DIVISION'),
                Token('DOT', '.'),
            ])
            module.children.append(env_div)

        # Add data division if available
        if 'data' in divisions:
            data_div = Tree('data_division', [
                Token('DATA', 'DATA'),
                Token('DIVISION', 'DIVISION'),
                Token('DOT', '.'),
            ])

            # Add data sections if available
            if 'data_sections' in divisions:
                for section_name, section_content in divisions['data_sections'].items():
                    if section_name == 'file':
                        section = Tree('file_section', [
                            Token('FILE', 'FILE'),
                            Token('SECTION', 'SECTION'),
                            Token('DOT', '.'),
                        ])
                        data_div.children.append(section)
                    elif section_name == 'working_storage':
                        section = Tree('working_storage_section', [
                            Token('WORKING_STORAGE', 'WORKING-STORAGE'),
                            Token('SECTION', 'SECTION'),
                            Token('DOT', '.'),
                        ])
                        data_div.children.append(section)
                    elif section_name == 'local_storage':
                        section = Tree('local_storage_section', [
                            Token('LOCAL_STORAGE', 'LOCAL-STORAGE'),
                            Token('SECTION', 'SECTION'),
                            Token('DOT', '.'),
                        ])
                        data_div.children.append(section)
                    elif section_name == 'linkage':
                        section = Tree('linkage_section', [
                            Token('LINKAGE', 'LINKAGE'),
                            Token('SECTION', 'SECTION'),
                            Token('DOT', '.'),
                        ])
                        data_div.children.append(section)

            module.children.append(data_div)

        # Add procedure division if available
        if 'procedure' in divisions:
            proc_div = Tree('procedure_division', [
                Token('PROCEDURE', 'PROCEDURE'),
                Token('DIVISION', 'DIVISION'),
                Token('DOT', '.'),
            ])

            # Add sections and paragraphs if available
            if 'procedure_sections' in divisions:
                for section_name, section_info in divisions['procedure_sections'].items():
                    # Create section node
                    section = Tree('section', [])

                    # Create section header
                    section_header = Tree('section_header', [
                        Token('IDENTIFIER', section_info['name']),
                        Token('SECTION', 'SECTION'),
                        Token('DOT', '.'),
                    ])
                    section.children.append(section_header)

                    # Add paragraphs to section
                    for para_name, para_content in section_info.get('paragraphs', {}).items():
                        paragraph = Tree('paragraph', [])

                        # Create paragraph header
                        para_header = Tree('paragraph_header', [
                            Token('IDENTIFIER', para_name),
                            Token('DOT', '.'),
                        ])
                        paragraph.children.append(para_header)

                        # Add sentence placeholder for paragraph content
                        sentence = Tree('sentence', [
                            Token('TEXT', para_content),
                            Token('DOT', '.'),
                        ])
                        paragraph.children.append(sentence)

                        # Add paragraph to section
                        section.children.append(paragraph)

                    # Add section to procedure division
                    proc_div.children.append(section)

            module.children.append(proc_div)

        program.children.append(module)
        return program


    def _skip_declaratives(self, declaratives_content: list[str], lines: list[str]) -> int:
        """
        Skips DECLARATIVES if present (and saves them to a dedicated `declaratives_content` list)
        returns the index of the first line after the DECLARATIVES section
        """
        in_declaratives = False
        for i, line in enumerate(lines):

            if re.match(r'DECLARATIVES', line, re.IGNORECASE):
                in_declaratives = True
                declaratives_content.append(line)
                continue

            if re.match(r'END\s+DECLARATIVES', line, re.IGNORECASE):
                in_declaratives = False
                declaratives_content.append(line)
                continue

            if not in_declaratives:
                return i
            else:
                declaratives_content.append(line)

        return 0


    # : Helper function to validate paragraph names
    def _is_valid_paragraph_name(self, name):
        """Check if a name is a valid paragraph name and not a COBOL statement"""
        cobol_statements = [
            "UNTIL", "GOBACK", "STOP", "STOP RUN", "EXIT", "EXIT PROGRAM", "FOREVER", "TIMES", "VARYING"
            "CONTINUE", "END-IF", "END-EVALUATE", "END-READ", "WITH",
            "END-PERFORM", "END-COMPUTE", "END-STRING", "END-UNSTRING",
            "END-CALL", "END-SEARCH", "END-WRITE", "END-REWRITE", "END-DELETE",
            "NEXT SENTENCE", "END-ADD", "END-SUBTRACT", "END-MULTIPLY", "END-DIVIDE"
        ]

        # Check if it's a COBOL statement
        if name.upper() in cobol_statements:
            return False

        # Check if it looks like a SECTION or DIVISION declaration
        if "SECTION" in name.upper() or "DIVISION" in name.upper():
            return False

        # Check if it starts with "END-" (likely a COBOL statement)
        if name.upper().startswith("END-"):
            return False

        return True

    def _collect_all_performed_statements(self, start_line: int, lines: list[str]) -> set[str]:
        """
        Collects all PERFORM targets to identify subroutines called from the entry paragraph.
        Returns a set of all performed statements (upper-case)
        """
        performed_statements = set()
        for line in lines[start_line:]:
            perf_match = re.search(r'PERFORM\s+([A-Z0-9-]+\s*)(\s+.*)*', line, re.IGNORECASE)
            if perf_match:
                if self._is_valid_paragraph_name(perf_match.group(1).strip()):
                    performed_statements.add(perf_match.group(1).strip().upper())

        return performed_statements

    def _get_entry_paragraph_content(self, start_line: int, lines: list[str], performed_paragraphs: set[str]) -> str:
        """
        Finds the entry paragraph which ends with STOP RUN., GOBACK., or the declaration of the first paragraph
        present in the `performed` paragraphs set.

        Returns the content of the entry paragraph
        """
        entry_paragraph_lines = []
        lines_size = len(lines)
        last_line_idx = start_line
        for i in range(start_line, lines_size):

            comment_line_match = re.match(r'^\s*\*', lines[i], re.IGNORECASE)
            if comment_line_match:
                entry_paragraph_lines.append(lines[i])
                continue

            para_match = re.match(r'^\s*([A-Z0-9-]+)\s*\.\s*', lines[i], re.IGNORECASE)
            if para_match:
                para_name = para_match.group(1).strip().upper()
                # if this paragraph is PERFORMed, it's a subroutine - stop here
                if para_name in performed_paragraphs and len(entry_paragraph_lines) > 0:
                    self.logger.debug(f"Found the first paragraph '{para_name}' at the position {i}")
                    last_line_idx = i - 1 - start_line
                    break

            # Check explicit entry paragraph termination
            if re.search(r'STOP\s+RUN.', lines[i], re.IGNORECASE) or re.search(r'GOBACK.', lines[i], re.IGNORECASE):
                entry_paragraph_lines.append(lines[i])
                last_line_idx = i - start_line
                break

            entry_paragraph_lines.append(lines[i])

        # remove trailing comments
        comment_line_match = re.match(r'^\s*\*', entry_paragraph_lines[last_line_idx], re.IGNORECASE)
        while comment_line_match:
            last_line_idx -= 1
            comment_line_match = re.match(r'^\s*\*', entry_paragraph_lines[last_line_idx], re.IGNORECASE)

        return "\n".join(entry_paragraph_lines[:last_line_idx + 1])
