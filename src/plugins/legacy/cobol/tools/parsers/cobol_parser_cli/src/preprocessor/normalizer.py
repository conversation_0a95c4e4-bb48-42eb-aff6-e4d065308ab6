import re
import logging
from typing import List, Tuple

class TextNormalizer:
    """Normalizes COBOL source text for parsing"""
    
    def __init__(self, preserve_comments: bool = True):
        """Initialize the text normalizer
        
        Args:
            preserve_comments: Whether to preserve comments in the normalized text
        """
        self.preserve_comments = preserve_comments
        self.logger = logging.getLogger(__name__)
    
    def normalize(self, source_text: str) -> str:
        """Normalize COBOL source text
        
        Args:
            source_text: The COBOL source text
            
        Returns:
            The normalized source text
        """
        self.logger.info("Starting normalization of COBOL text")
        lines = source_text.splitlines()
        self.logger.info(f"Source has {len(lines)} lines")
        normalized_lines = []
        
        continuation = False
        current_line = ""
        found_non_comment = False  # Track if we've found any non-comment line
        
        for line_number, line in enumerate(lines, start=1):
            # Skip empty lines
            if not line.strip():
                normalized_lines.append("")  # Preserve empty lines
                continue
            
            # Handle line length
            if len(line) > 72:
                line = line[:72]  # Truncate to standard COBOL line length
            
            # Check if it's a comment line (column 7 is '*' or '/' or line starts with '*')
            is_comment = False
            if len(line) > 6 and line[6] in ['*', '/']:
                is_comment = True
            # Handle full-line comments that don't follow COBOL column rules
            elif line.strip().startswith('*'):
                is_comment = True
                
            if is_comment:
                if self.preserve_comments and found_non_comment:
                    # Only keep comments after we've found non-comment lines
                    # Extract line content, ignoring columns 1-6 (sequence number area)
                    # and column 7 (indicator area)
                    normalized_lines.append(line[6:].rstrip())
                continue
            
            # We've found a non-comment line
            found_non_comment = True
            
            # Check for line continuation
            is_continuation = False
            if len(line) > 6 and line[6] != ' ':
                is_continuation = True
            
            # Extract line content, ignoring columns 1-6 (sequence number area)
            # and column 7 (indicator area)
            if len(line) <= 7:
                line_content = ""
            else:
                line_content = line[7:].rstrip()
            
            # Handle continuation
            if continuation:
                # When continuing, join with the previous content
                current_line += line_content
                if not is_continuation:
                    # End of continuation
                    normalized_lines.append(current_line)
                    current_line = ""
                    continuation = False
            else:
                if is_continuation:
                    # Start of a continuation
                    current_line = line_content
                    continuation = True
                else:
                    # Regular line
                    normalized_lines.append(line_content)
        
        # Handle any remaining continuation
        if continuation and current_line:
            normalized_lines.append(current_line)
        
        # Join and normalize all lines
        normalized_text = "\n".join(normalized_lines)
        
        # Replace multiple spaces with a single space (except in literals)
        normalized_text = self._normalize_spaces(normalized_text)
        
        self.logger.info(f"Normalized to {len(normalized_lines)} lines")
        if normalized_lines:
            self.logger.info(f"First line of normalized text: {normalized_lines[0][:50]}...")
        else:
            self.logger.warning("No non-comment content found in COBOL source")
        
        return normalized_text
    
    def _normalize_spaces(self, text: str) -> str:
        """Normalize spaces in the text, preserving spaces in literals
        
        Args:
            text: The text to normalize
            
        Returns:
            The normalized text
        """
        result = ""
        in_literal = False
        literal_delimiter = None
        i = 0
        
        while i < len(text):
            char = text[i]
            
            # Handle string literals
            if char in ["'", '"'] and (i == 0 or text[i-1] != '\\'):
                if not in_literal:
                    # Start of a literal
                    in_literal = True
                    literal_delimiter = char
                elif char == literal_delimiter:
                    # End of a literal
                    in_literal = False
                
                result += char
            
            # Handle spaces
            elif char.isspace() and not in_literal:
                # Keep newlines
                if char == '\n':
                    result += char
                # For spaces, we need to be careful with COBOL indentation
                elif char == ' ':
                    prev_char = result[-1] if result else None
                    
                    # If this is a space after a newline (line start), preserve all spaces for COBOL indentation
                    if prev_char == '\n':
                        # Always add the first space
                        result += ' '
                        
                        # Count how many spaces follow this one (including current one)
                        space_count = 0
                        while i + 1 + space_count < len(text) and text[i + 1 + space_count] == ' ':
                            space_count += 1
                        
                        # Add all spaces at the beginning of the line (for COBOL indentation)
                        for j in range(space_count):
                            result += ' '
                            i += 1
                    # Otherwise, collapse multiple spaces into one (but not at line start)
                    else:
                        # Always add the first space
                        result += ' '
                        while i + 1 < len(text) and text[i + 1] == ' ':
                            i += 1
            else:
                result += char
            
            i += 1
        
        return result
