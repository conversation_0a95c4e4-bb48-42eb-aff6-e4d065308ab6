# Default configuration for COBOL Parser

# Neo4j connection settings
neo4j:
  url: "bolt://localhost:7687"
  user: "neo4j"
  password: "12345678"
  batch_size: 100

# Directories
directories:
  copybooks: "./copybooks"
  output: "./output"

# Logging configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "cobol_parser.log"

# Parser settings
parser:
  preserve_comments: true
  max_recursion_depth: 10  # For copybook expansion
