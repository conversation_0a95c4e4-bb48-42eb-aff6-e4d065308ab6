import os
import logging
import json
from typing import List, Dict, Any

logger = logging.getLogger("tools.chunkers.cobol")

class CobolChunker:
    """
    Chunks COBOL code into logical segments that can be processed by LLMs.
    Expects an IR JSON file created by the preprocessor.

    Divides code into:
    1. Identification Division
    2. Environment Division
    3. Data Division (broken down by sections)
    4. Procedure Division (broken down by procedures/paragraphs)
    """

    def chunk_file(self, input_path: str, output_dir: str) -> List[Dict[str, Any]]:
        """
        Chunk a COBOL file into logical segments using the IR produced by the preprocessor

        Args:
            input_path: Path to the preprocessed COBOL file
            output_dir: Directory to write the chunked files

        Returns:
            List[Dict[str, Any]]: List of chunk information dictionaries
        """
        logger.info(f"Chunking COBOL file: {input_path}")

        # Create output directory if it doesn't exist
        try:
            os.makedirs(output_dir, exist_ok=True)
        except PermissionError as e:
            logger.error(f"Cannot create output directory {output_dir}: {str(e)}")
            return []

        # Look for IR file in the json subdirectory
        json_dir = os.path.join(os.path.dirname(input_path), "json")
        ir_filename = f"{os.path.basename(os.path.splitext(input_path)[0])}.json"
        ir_path = os.path.join(json_dir, ir_filename)

        if not os.path.exists(ir_path):
            logger.error(f"IR file not found: {ir_path}")
            # Try to create basic chunks from the source file directly
            return self._create_fallback_chunks(input_path, output_dir)

        try:
            # Load the IR file
            with open(ir_path, 'r', encoding='utf-8') as file:
                ir_data = json.load(file)

            # Extract program ID from file name
            program_name = os.path.splitext(os.path.basename(input_path))[0]

            # Extract chunks from IR
            chunks = self._extract_divisions(output_dir, ir_data, program_name)

            # Create chunk info list for return
            chunk_info = []
            for i, chunk in enumerate(chunks):
                info = {
                    "index": i,
                    "chunk_type": chunk['chunk_type'],
                    "chunk_name": chunk['chunk_name'],
                    "path": os.path.join(output_dir, f"{i:03d}_{chunk['chunk_type']}.txt"),
                    "code": chunk['code'],
                    "chunk_size": len(chunk['code']),
                    "metadata": chunk.get('metadata', {})
                }
                chunk_info.append(info)

            # No longer writing index.json file

            logger.info(f"Successfully chunked COBOL file into {len(chunks)} chunks")
            return chunk_info

        except Exception as e:
            logger.error(f"Error chunking COBOL file {input_path}: {str(e)}")
            logger.info("Falling back to source-based chunking")
            return self._create_fallback_chunks(input_path, output_dir)

    def _extract_divisions(self, chunks_dir: str, ir_data: dict, program_name: str) -> List[Dict[str, Any]]:
        """
        Extract Divisions from provided IR datasource.

        Args:
            chunks_dir: Directory to store chunk files
            ir_data: The IR data from the JSON file
            program_name: Name of the program
        Returns:
            List of extracted chunks.
        """
        chunks = []
        chunk_count = 0

        # First extract top-level divisions (ID, ENV, DATA, PROCEDURE)
        for ir_node in ir_data["nodes"]:
            # Extract Identification division
            if "CobolIdentificationDivision" in ir_node.get("type", ""):
                chunk_count += 1
                chunk_data = {
                    'program_id': program_name,
                    'chunk_type': 'IDENTIFICATION_DIVISION',
                    'chunk_name': "ID_DIV",
                    'code': ir_node.get("full_text", "")
                }
                chunks.append(chunk_data)

                # Write chunk to file
                with open(os.path.join(chunks_dir, f"{chunk_count:03d}_IDENTIFICATION_DIVISION.txt"), 'w') as f:
                    f.write(chunk_data['code'])

            # Extract Environment division
            elif "CobolEnvironmentDivision" in ir_node.get("type", ""):
                chunk_count += 1
                chunk_data = {
                    'program_id': program_name,
                    'chunk_type': 'ENVIRONMENT_DIVISION',
                    'chunk_name': "ENV_DIV",
                    'code': ir_node.get("full_text", "")
                }
                chunks.append(chunk_data)

                # Write chunk to file
                with open(os.path.join(chunks_dir, f"{chunk_count:03d}_ENVIRONMENT_DIVISION.txt"), 'w') as f:
                    f.write(chunk_data['code'])

        # Now find all data division sections
        for ir_node in ir_data["nodes"]:
            # Extract data division sections
            if "CobolSection" in ir_node.get("type", "") and "DATA DIVISION" in ir_node.get("parent_name", ""):
                chunk_count = self._extract_as_data_division(chunk_count, chunks, chunks_dir, ir_node, program_name)

        # Extract procedure division and its main content
        proc_div_node = None
        for ir_node in ir_data["nodes"]:
            if "CobolProcedureDivision" in ir_node.get("type", ""):
                proc_div_node = ir_node
                break

        if proc_div_node:
            chunk_count += 1
            chunk_data = {
                'program_id': program_name,
                'chunk_type': 'PROCEDURE_DIVISION_MAIN',
                'chunk_name': f"{program_name}_PROC_MAIN",
                'code': proc_div_node.get("full_text", ""),
                'metadata': {'division_name': 'MAIN', 'division_type': 'MAIN'}
            }
            chunks.append(chunk_data)

            # Write chunk to file
            with open(os.path.join(chunks_dir, f"{chunk_count:03d}_PROCEDURE_DIVISION_MAIN.txt"), 'w') as f:
                f.write(chunk_data['code'])

        # Extract any sections in procedure division
        for ir_node in ir_data["nodes"]:
            if "CobolSection" in ir_node.get("type", "") and "PROCEDURE DIVISION" in ir_node.get("parent_name", ""):
                chunk_count += 1
                section_name = ir_node.get("section_name", "UNKNOWN")
                chunk_data = {
                    'program_id': program_name,
                    'chunk_type': 'PROCEDURE_DIVISION_SECTION',
                    'chunk_name': f"{program_name}_PROC_{section_name}",
                    'code': ir_node.get("full_text", ""),
                    'metadata': {'division_name': section_name, 'division_type': 'SECTION'}
                }
                chunks.append(chunk_data)

                # Write chunk to file
                with open(os.path.join(chunks_dir,
                                    f"{chunk_count:03d}_PROCEDURE_DIVISION_SECTION_{section_name}.txt"),
                        'w') as f:
                    f.write(chunk_data['code'])

        # Finally, extract all paragraphs - this is the key change to fix the bug
        for ir_node in ir_data["nodes"]:
            # Extract regular paragraphs (this was the issue - we were restricting to only specific types)
            if "CobolParagraph" in ir_node.get("type", ""):
                chunk_count += 1
                paragraph_name = ir_node.get("paragraph_name", "UNKNOWN")
                section_name = ir_node.get("parent_name", "UNNAMED-SECTION")
                # Clean the full_text (remove metadata JSON parts)
                code_text = ir_node.get("full_text", "")
                # Remove any JSON metadata text that might have been included in full_text
                # Extract only what appears to be the actual COBOL code

                chunk_data = {
                    'program_id': program_name,
                    'chunk_type': 'PROCEDURE_DIVISION_PARAGRAPH',
                    'chunk_name': f"{program_name}_PROC_{section_name}_SECT_{paragraph_name}",
                    'code': code_text,
                    'metadata': {'division_name': paragraph_name, 'division_type': 'PARAGRAPH'}
                }
                chunks.append(chunk_data)

                # Write chunk to file
                with open(os.path.join(chunks_dir,
                                    f"{chunk_count:03d}_PROCEDURE_DIVISION_SECTION_{section_name}_PARAGRAPH_{paragraph_name}.txt"),
                        'w') as f:
                    f.write(code_text)

            # Extract entry paragraphs
            elif "CobolEntryParagraph" in ir_node.get("type", ""):
                chunk_count += 1
                paragraph_name = ir_node.get("paragraph_name", "UNKNOWN")
                section_name = ir_node.get("parent_name", "UNNAMED-SECTION")
                code_text = ir_node.get("full_text", "")

                chunk_data = {
                    'program_id': program_name,
                    'chunk_type': 'PROCEDURE_DIVISION_PARAGRAPH',
                    'chunk_name': f"{program_name}_PROC_{section_name}_SECT_{paragraph_name}",
                    'code': code_text,
                    'metadata': {'division_name': paragraph_name, 'division_type': 'PARAGRAPH', 'entry': True}
                }
                chunks.append(chunk_data)

                # Write chunk to file
                with open(os.path.join(chunks_dir,
                                    f"{chunk_count:03d}_PROCEDURE_DIVISION_PARAGRAPH_{paragraph_name}.txt"),
                        'w') as f:
                    f.write(code_text)

        return chunks

    def _extract_as_data_division(self, chunk_count: int, chunks: List[Dict], chunks_dir: str, ir_data: dict, program_name: str) -> int:
        """
        Extract Data Division from provided IR datasource.

        Args:
            chunk_count: Sequential numbering of chunks.
            chunks: List for chunks to populate, by extracted data
            chunks_dir: Directory to store chunk files
            ir_data: The IR data from the JSON file
            program_name: Name of the program

        Return:
            Increased chunk_count value
        """
        section_name = ir_data["section_name"]
        # Extract file section
        if "FILE-SECTION" in section_name:
            chunk_count += 1
            chunk_data = {
                'program_id': program_name,
                'chunk_type': 'DATA_DIVISION_FILE_SECTION',
                'chunk_name': f"{program_name}_DATA_FILE",
                'code': ir_data.get("full_text", ""),
                'metadata': {'division_name': 'FILE_SECTION'}
            }
            chunks.append(chunk_data)

            # Write chunk to file
            with open(os.path.join(chunks_dir, f"{chunk_count:03d}_DATA_DIVISION_FILE_SECTION.txt"), 'w') as f:
                f.write(chunk_data['code'])

        # Extract working storage section
        if "WORKING_STORAGE-SECTION" in section_name:
            chunk_count += 1
            chunk_data = {
                'program_id': program_name,
                'chunk_type': 'DATA_DIVISION_WS_SECTION',
                'chunk_name': f"{program_name}_DATA_WS",
                'code': ir_data.get("full_text", ""),
                'metadata': {'division_name': 'WORKING_STORAGE_SECTION'}
            }
            chunks.append(chunk_data)

            # Write chunk to file
            with open(os.path.join(chunks_dir, f"{chunk_count:03d}_DATA_DIVISION_WS_SECTION.txt"), 'w') as f:
                f.write(chunk_data['code'])

        # Extract linkage section
        if "LINKAGE-SECTION" in section_name:
            chunk_count += 1
            chunk_data = {
                'program_id': program_name,
                'chunk_type': 'DATA_DIVISION_LINKAGE_SECTION',
                'chunk_name': f"{program_name}_DATA_LINKAGE",
                'code': ir_data.get("full_text", ""),
                'metadata': {'division_name': 'LINKAGE_SECTION'}
            }
            chunks.append(chunk_data)

            # Write chunk to file
            with open(os.path.join(chunks_dir, f"{chunk_count:03d}_DATA_DIVISION_LINKAGE_SECTION.txt"), 'w') as f:
                f.write(chunk_data['code'])

        return chunk_count

    def _create_fallback_chunks(self, input_path: str, output_dir: str) -> List[Dict[str, Any]]:
        """
        Create basic chunks from COBOL source when IR file is not available.
        This provides a fallback mechanism to ensure processing can continue.

        Args:
            input_path: Path to the COBOL source file
            output_dir: Directory to write the chunked files

        Returns:
            List[Dict[str, Any]]: List of chunk information
        """
        logger.info(f"Creating fallback chunks for {input_path}")

        try:
            # Read the source file
            with open(input_path, 'r', encoding='utf-8', errors='replace') as f:
                source_content = f.read()

            program_name = os.path.splitext(os.path.basename(input_path))[0]
            chunks = []
            chunk_count = 0

            # Create output directory
            os.makedirs(output_dir, exist_ok=True)

            # Split content by divisions using simple text parsing
            lines = source_content.split('\n')
            current_division = None
            division_content = []

            for line in lines:
                line_upper = line.strip().upper()

                if 'IDENTIFICATION DIVISION' in line_upper:
                    if current_division:
                        chunk_count = self._create_fallback_chunk(
                            chunks, chunk_count, current_division, '\n'.join(division_content),
                            program_name, output_dir
                        )
                    current_division = 'IDENTIFICATION'
                    division_content = [line]
                elif 'ENVIRONMENT DIVISION' in line_upper:
                    if current_division:
                        chunk_count = self._create_fallback_chunk(
                            chunks, chunk_count, current_division, '\n'.join(division_content),
                            program_name, output_dir
                        )
                    current_division = 'ENVIRONMENT'
                    division_content = [line]
                elif 'DATA DIVISION' in line_upper:
                    if current_division:
                        chunk_count = self._create_fallback_chunk(
                            chunks, chunk_count, current_division, '\n'.join(division_content),
                            program_name, output_dir
                        )
                    current_division = 'DATA'
                    division_content = [line]
                elif 'PROCEDURE DIVISION' in line_upper:
                    if current_division:
                        chunk_count = self._create_fallback_chunk(
                            chunks, chunk_count, current_division, '\n'.join(division_content),
                            program_name, output_dir
                        )
                    current_division = 'PROCEDURE'
                    division_content = [line]
                else:
                    if current_division:
                        division_content.append(line)

            # Add the last division
            if current_division:
                chunk_count = self._create_fallback_chunk(
                    chunks, chunk_count, current_division, '\n'.join(division_content),
                    program_name, output_dir
                )

            # Create chunk info list for return
            chunk_info = []
            for i, chunk in enumerate(chunks):
                info = {
                    "index": i,
                    "chunk_type": chunk['chunk_type'],
                    "chunk_name": chunk['chunk_name'],
                    "path": os.path.join(output_dir, f"{i:03d}_{chunk['chunk_type']}.txt"),
                    "code": chunk['code'],
                    "chunk_size": len(chunk['code']),
                    "metadata": chunk.get('metadata', {})
                }
                chunk_info.append(info)

            logger.info(f"Successfully created {len(chunks)} fallback chunks")
            return chunk_info

        except Exception as e:
            logger.error(f"Error creating fallback chunks: {str(e)}")
            return []

    def _create_fallback_chunk(self, chunks: List[Dict], chunk_count: int, division_type: str,
                              content: str, program_name: str, output_dir: str) -> int:
        """
        Create a single fallback chunk for a division.

        Args:
            chunks: List to append the chunk to
            chunk_count: Current chunk count
            division_type: Type of division
            content: Division content
            program_name: Name of the program
            output_dir: Output directory for chunk files

        Returns:
            int: Updated chunk count
        """
        chunk_count += 1

        chunk_data = {
            'program_id': program_name,
            'chunk_type': f'{division_type.upper()}_DIVISION',
            'chunk_name': f"{program_name}_{division_type.upper()}_DIV",
            'code': content,
            'metadata': {'division_name': division_type, 'fallback': True}
        }
        chunks.append(chunk_data)

        # Write chunk to file
        filename = f"{chunk_count:03d}_{division_type.upper()}_DIVISION.txt"
        with open(os.path.join(output_dir, filename), 'w', encoding='utf-8') as f:
            f.write(content)

        return chunk_count

    @property
    def version(self) -> str:
        """
        Get the version of the COBOL chunker

        Returns:
            str: Version number
        """
        return "2.1.0"

    @property
    def encoding(self) -> str:
        """
        Get the default encoding for COBOL files

        Returns:
            str: Recommended encoding
        """
        return "utf-8"