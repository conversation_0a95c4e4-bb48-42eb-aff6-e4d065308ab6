import os
import re
import logging
from typing import List, Dict, Any, Tuple
import json

logger = logging.getLogger("tools.chunkers.cobol")

class CobolChunker:
    """
    Chunks COBOL code into logical segments that can be processed by LLMs.

    Divides code into:
    1. Identification Division
    2. Environment Division
    3. Data Division (broken down by sections)
    4. Procedure Division (broken down by procedures/paragraphs)
    """

    # Reserved COBOL words and patterns to ignore when creating chunks
    RESERVED_WORDS = {
        "GOBACK", "EXIT", "STOP", "END-IF", "END-READ", "END-PERFORM",
        "END-EVALUATE", "END-COMPUTE", "CONTINUE", "NEXT", "SENTENCE"
    }

    def chunk_file(self, input_path: str, output_dir: str) -> List[Dict[str, Any]]:
        """
        Chunk a COBOL file into logical segments

        Args:
            input_path: Path to the preprocessed COBOL file
            output_dir: Directory to write the chunked files

        Returns:
            List[Dict[str, Any]]: List of chunk information dictionaries
        """
        try:
            logger.info(f"Chunking COBOL file: {input_path}")

            # Read the input file
            with open(input_path, 'r', encoding='utf-8', errors='replace') as file:
                content = file.read()

            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Perform chunking
            chunks = self._chunk_content(content)

            # Write chunks to files
            chunk_info = []
            for i, chunk in enumerate(chunks):
                # Generate chunk filename
                filename = f"{i:03d}_{chunk['type']}.txt"
                filepath = os.path.join(output_dir, filename)

                # Write chunk content
                with open(filepath, 'w', encoding='utf-8') as file:
                    file.write(chunk['content'])

                # Create chunk info
                info = {
                    "index": i,
                    "chunk_type": chunk['type'],
                    "chunk_name": chunk['name'],
                    "path": filepath,
                    "code": chunk['content'],
                    "chunk_size": len(chunk['content']),
                    "metadata": chunk['metadata']
                }
                chunk_info.append(info)

            # Write chunk index file
            index_path = os.path.join(output_dir, "index.json")
            with open(index_path, 'w', encoding='utf-8') as file:
                json.dump(chunk_info, file, indent=2)

            logger.info(f"Successfully chunked COBOL file into {len(chunks)} chunks")
            return chunk_info

        except Exception as e:
            logger.error(f"Error chunking COBOL file {input_path}: {str(e)}")
            return []

    def _chunk_content(self, content: str) -> List[Dict[str, Any]]:
        """
        Chunk COBOL content into logical segments

        Args:
            content: COBOL code content

        Returns:
            List[Dict[str, Any]]: List of chunk dictionaries
        """
        lines = content.splitlines()

        # Regular expression to detect comment lines
        comment_pattern = re.compile(r'^\s*\*', re.IGNORECASE)

        # Extract program ID
        program_id_match = re.search(r'PROGRAM-ID\s*\.\s*([A-Za-z0-9-_]+)', content, re.IGNORECASE)
        program_id = program_id_match.group(1) if program_id_match else "UNKNOWN"

        # Find all division and section boundaries
        boundaries = self._find_all_boundaries(lines)

        # Process the divisions
        chunks = []
        for i, (div_type, div_name, start_line, end_line) in enumerate(boundaries):
            # Collect comments that precede this chunk
            preceding_comments = []
            comment_cut_off = start_line
            if i > 0:
                prev_chunk_end = boundaries[i-1][3]

                # Collect comments and blank lines between chunks
                preceding_comments = [
                    line for line in lines[prev_chunk_end:start_line]
                    if comment_pattern.match(line) or not line.strip()
                ]

                # Find the first non-comment, non-blank line to start the chunk
                for j in range(start_line, end_line):
                    if lines[j].strip() and not comment_pattern.match(lines[j]):
                        comment_cut_off = j
                        break

            # Combine comments and chunk content
            current_chunk_lines = preceding_comments + lines[comment_cut_off:end_line]

            # Create chunk info
            chunk_info = {
                'type': div_type,
                'name': f"{div_name}",
                'content': '\n'.join(current_chunk_lines),
                'metadata': {
                    'program_id': program_id
                }
            }

            # Special handling for procedure divisions
            if div_type.startswith('PROCEDURE_DIVISION'):
                # Skip tiny chunks like single line procedures with just EXIT or .
                if div_type in ['PROCEDURE_DIVISION_PARAGRAPH', 'PROCEDURE_DIVISION_SECTION']:
                    # Clean the content to check actual substance
                    clean_lines = [
                        line.strip() for line in current_chunk_lines
                        if line.strip() and not comment_pattern.match(line)
                    ]

                    # Skip if chunk is essentially empty or just minor procedural markers
                    if not clean_lines or all(
                            word.upper() in self.RESERVED_WORDS or
                            word == '.' or
                            (len(word) > 3 and word.upper().endswith('-EXIT'))
                            for word in ' '.join(clean_lines).split()
                    ):
                        continue

                # Set metadata for the chunk
                if div_type == 'PROCEDURE_DIVISION_MAIN':
                    chunk_info['metadata']['division_name'] = 'MAIN'
                    chunk_info['metadata']['division_type'] = 'MAIN'
                else:
                    section_match = re.search(r'SECTION', chunk_info['content'], re.IGNORECASE)
                    is_section = bool(section_match)
                    div_type_value = "SECTION" if is_section else "PARAGRAPH"

                    # Extract the actual name from the first non-comment line
                    non_comment_lines = [
                        line for line in current_chunk_lines
                        if line.strip() and not comment_pattern.match(line)
                    ]
                    first_line = non_comment_lines[0] if non_comment_lines else ""
                    name_match = re.search(r'^\s*([A-Za-z0-9-_]+)', first_line)
                    actual_name = name_match.group(1) if name_match else div_name

                    chunk_info['metadata']['division_name'] = actual_name
                    chunk_info['metadata']['division_type'] = div_type_value

            chunks.append(chunk_info)

        return chunks

    def _find_all_boundaries(self, lines: List[str]) -> List[Tuple[str, str, int, int]]:
        """
        Find all division and section boundaries in the COBOL code

        Args:
            lines: List of code lines

        Returns:
            List[Tuple[str, str, int, int]]: List of (division_type, division_name, start_line, end_line)
        """
        # Find main divisions first
        id_div_line = self._find_division_line(lines, "IDENTIFICATION")
        env_div_line = self._find_division_line(lines, "ENVIRONMENT")
        data_div_line = self._find_division_line(lines, "DATA")
        proc_div_line = self._find_division_line(lines, "PROCEDURE")

        # Find sections within DATA DIVISION
        file_section_line = -1
        ws_section_line = -1
        linkage_section_line = -1

        if data_div_line >= 0:
            for i in range(data_div_line, len(lines)):
                if proc_div_line >= 0 and i >= proc_div_line:
                    break

                if re.search(r'FILE\s+SECTION', lines[i], re.IGNORECASE):
                    file_section_line = i
                elif re.search(r'WORKING-STORAGE\s+SECTION', lines[i], re.IGNORECASE):
                    ws_section_line = i
                elif re.search(r'LINKAGE\s+SECTION', lines[i], re.IGNORECASE):
                    linkage_section_line = i

        # Find paragraphs and sections in PROCEDURE DIVISION
        proc_sections = []
        proc_paragraphs = []

        if proc_div_line >= 0:
            # Use more comprehensive patterns
            section_pattern = re.compile(r'^(?:\s*\*.*\n)*\s{6,7}([A-Za-z0-9-_]+)\s+SECTION\s*\.', re.IGNORECASE)
            paragraph_header_pattern = re.compile(r'^(?:\s*\*.*\n)*\s{6,7}([A-Za-z0-9-_]+)\s*\.?', re.IGNORECASE)

            for i in range(proc_div_line, len(lines)):
                # Check for sections
                section_match = section_pattern.match(lines[i])
                if section_match:
                    section_name = section_match.group(1)
                    proc_sections.append((section_name, i))
                    continue

                # Check for paragraphs
                paragraph_match = paragraph_header_pattern.match(lines[i])
                if paragraph_match:
                    paragraph_name = paragraph_match.group(1).strip()

                    # Modify handling of -EXIT paragraphs
                    if paragraph_name.upper().endswith('-EXIT'):
                        # Skip if this is an exit paragraph
                        continue

                    # Additional checks to validate paragraph
                    clean_name = paragraph_name.split('-')[0]
                    if (clean_name.upper() not in self.RESERVED_WORDS and
                            clean_name != '.' and
                            clean_name):

                        # Look ahead to ensure it's a meaningful paragraph
                        is_meaningful = False
                        for j in range(i+1, min(i+10, len(lines))):
                            line = lines[j].strip()
                            if not line or line.startswith('*'):
                                continue

                            # Check if line contains non-trivial code
                            if not (line == '.' or
                                    line.upper() in ['EXIT', 'GOBACK'] or
                                    any(reserved.upper() in line.upper() for reserved in self.RESERVED_WORDS)):
                                is_meaningful = True
                                break

                        if is_meaningful:
                            proc_paragraphs.append((paragraph_name, i))

        # Create ordered list of all boundaries
        all_boundaries = []

        # Add IDENTIFICATION DIVISION with optional end
        if id_div_line >= 0:
            next_section = env_div_line if env_div_line >= 0 else data_div_line if data_div_line >= 0 else proc_div_line if proc_div_line >= 0 else len(lines)
            all_boundaries.append(("IDENTIFICATION_DIVISION", "ID_DIV", id_div_line, next_section))

        # Add ENVIRONMENT DIVISION with optional end
        if env_div_line >= 0:
            next_section = data_div_line if data_div_line >= 0 else proc_div_line if proc_div_line >= 0 else len(lines)
            all_boundaries.append(("ENVIRONMENT_DIVISION", "ENV_DIV", env_div_line, next_section))

        # Add DATA DIVISION sections
        if data_div_line >= 0:
            # Main DATA DIVISION section (if no subsections)
            if file_section_line < 0 and ws_section_line < 0 and linkage_section_line < 0:
                all_boundaries.append(("DATA_DIVISION", "DATA_DIV",
                                       data_div_line,
                                       proc_div_line if proc_div_line >= 0 else len(lines)))
            else:
                # FILE SECTION
                if file_section_line >= 0:
                    next_section = min([x for x in [ws_section_line, linkage_section_line, proc_div_line] if x >= 0], default=len(lines))
                    all_boundaries.append(("DATA_DIVISION_FILE_SECTION", "DATA_FILE",
                                           file_section_line,
                                           next_section))

                # WORKING-STORAGE SECTION
                if ws_section_line >= 0:
                    next_section = min([x for x in [linkage_section_line, proc_div_line] if x >= 0], default=len(lines))
                    all_boundaries.append(("DATA_DIVISION_WS_SECTION", "DATA_WS",
                                           ws_section_line,
                                           next_section))

                # LINKAGE SECTION
                if linkage_section_line >= 0:
                    all_boundaries.append(("DATA_DIVISION_LINKAGE_SECTION", "DATA_LINKAGE",
                                           linkage_section_line,
                                           proc_div_line if proc_div_line >= 0 else len(lines)))

        # Add PROCEDURE DIVISION and its paragraphs/sections
        if proc_div_line >= 0:
            # Modify proc_paragraphs to include next non-exit paragraph
            cleaned_paragraphs = []
            for i in range(len(proc_paragraphs)):
                current_paragraph = proc_paragraphs[i]
                current_name = current_paragraph[0]
                current_line = current_paragraph[1]

                # Check if this is a main paragraph (not an exit paragraph)
                if not current_name.upper().endswith('-EXIT'):
                    # Find the next paragraph
                    next_paragraph_line = len(lines)
                    if i < len(proc_paragraphs) - 1:
                        next_paragraph_line = proc_paragraphs[i+1][1]

                    cleaned_paragraphs.append((current_name, current_line, next_paragraph_line))

            # Combine and sort all procedures
            if not cleaned_paragraphs:
                # No procedures - add whole PROCEDURE DIVISION
                all_boundaries.append(("PROCEDURE_DIVISION", "PROC_MAIN",
                                       proc_div_line,
                                       len(lines)))
            else:
                # Add main PROCEDURE DIVISION part (up to first procedure)
                first_proc_line = cleaned_paragraphs[0][1]
                if first_proc_line > proc_div_line:
                    all_boundaries.append(("PROCEDURE_DIVISION_MAIN", "PROC_MAIN",
                                           proc_div_line,
                                           first_proc_line))

                # Add all procedures
                for i, (proc_name, proc_line, next_line) in enumerate(cleaned_paragraphs):
                    # Determine if this is a section
                    is_section = any(section_name == proc_name for section_name, _ in proc_sections)

                    # Create procedure/section boundary
                    div_type = "PROCEDURE_DIVISION_SECTION" if is_section else "PROCEDURE_DIVISION_PARAGRAPH"
                    all_boundaries.append((div_type, f"{proc_name}",
                                           proc_line,
                                           next_line))

        return all_boundaries

    def _find_division_line(self, lines: List[str], division_name: str) -> int:
        """
        Find the line number where a division starts

        Args:
            lines: List of code lines
            division_name: Name of the division (IDENTIFICATION, ENVIRONMENT, DATA, PROCEDURE)

        Returns:
            int: Line number or -1 if not found
        """
        pattern = f"{division_name}\\s+DIVISION"
        for i, line in enumerate(lines):
            if re.search(pattern, line, re.IGNORECASE):
                return i

        # Special case for IDENTIFICATION DIVISION (can be abbreviated as ID DIVISION)
        if division_name == "IDENTIFICATION":
            for i, line in enumerate(lines):
                if re.search(r"ID\s+DIVISION", line, re.IGNORECASE):
                    return i

        return -1

    @property
    def version(self) -> str:
        """
        Get the version of the COBOL chunker

        Returns:
            str: Version number
        """
        return "1.1.0"

    @property
    def encoding(self) -> str:
        """
        Get the default encoding for COBOL files

        Returns:
            str: Recommended encoding
        """
        return "utf-8"