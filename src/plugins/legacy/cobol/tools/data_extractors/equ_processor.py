"""
EQU directive processing functionality for COBOL data definition extraction.
Handles extraction and processing of EQU constants.
"""
import re
import logging
from typing import Dict, List, Any, Tuple

from .data_type_analyzer import DataTypeAnalyzer

logger = logging.getLogger("tools.extractors.equ_processor")


class EquProcessor:
    """
    Handles processing of COBOL EQU directives and constants.
    """

    def __init__(self):
        """Initialize the EQU processor."""
        self.data_type_analyzer = DataTypeAnalyzer()

    def extract_equ_directives(self, content: str, program_id: str, source_file_name: str) -> List[Dict[str, Any]]:
        """
        Extract EQU directives from the content.

        Args:
            content: The content to search for EQU directives
            program_id: ID of the program being processed
            source_file_name: Source file name

        Returns:
            List[Dict[str, Any]]: List of extracted EQU items
        """
        equ_items = []

        # Find all EQU directives
        equ_pattern = re.compile(r'^\s*([A-Za-z0-9-_]+)\s+EQU\s+(.+?)(?:\.|$|\s*\*)', re.MULTILINE)
        matches = equ_pattern.finditer(content)

        for match in matches:
            name = match.group(1)
            value = match.group(2).strip()

            # Get surrounding context for LLM processing
            context_start = max(0, match.start() - 200)
            context_end = min(len(content), match.end() + 200)
            context = content[context_start:context_end]

            # Generate simple business name and description
            business_name = self._generate_business_name(name)
            description = f"EQU constant: {name} = {value}"

            # Determine data type based on value
            data_type, length = self.data_type_analyzer.determine_type_from_value(value)

            # Create the item
            item = {
                "name": name,
                "top_parent_datastructure_name": None,  # EQU items are not part of a structure
                "source_file_name": source_file_name,
                "program_id": program_id,
                "business_name": business_name,
                "item_type": "constant",
                "data_type": data_type,
                "length": length,
                "description": description,
                "default_value": value,
                "possible_values": [value],
                "level": 0,  # EQU items don't have a level
                "is_equ": True
            }

            equ_items.append(item)
            logger.debug(f"Extracted EQU directive: {name} = {value}")

        logger.info(f"Extracted {len(equ_items)} EQU directives")
        return equ_items

    def _generate_business_name(self, technical_name: str) -> str:
        """
        Generate a simple business name from technical COBOL name.

        Args:
            technical_name: Technical COBOL name

        Returns:
            str: Business-friendly name
        """
        # Simple transformation: replace hyphens with spaces and title case
        business_name = technical_name.replace('-', ' ').replace('_', ' ')
        business_name = ' '.join(word.capitalize() for word in business_name.split())
        return business_name

    def validate_equ_value(self, value: str) -> bool:
        """
        Validate that an EQU value is properly formatted.

        Args:
            value: The EQU value to validate

        Returns:
            bool: True if valid, False otherwise
        """
        # Remove quotes and whitespace
        cleaned_value = value.strip().strip('"\'')
        
        # Check for common EQU value patterns
        patterns = [
            r'^\d+$',  # Numeric
            r'^[A-Za-z0-9-_]+$',  # Alphanumeric identifier
            r'^"[^"]*"$',  # Quoted string
            r"^'[^']*'$",  # Single quoted string
            r'^\([^)]+\)$',  # Parenthesized expression
        ]
        
        return any(re.match(pattern, value) for pattern in patterns)

    def extract_equ_references(self, content: str, equ_names: List[str]) -> Dict[str, List[str]]:
        """
        Find references to EQU constants in the content.

        Args:
            content: Content to search for references
            equ_names: List of EQU constant names to look for

        Returns:
            Dict mapping EQU names to lists of line numbers where they're referenced
        """
        references = {name: [] for name in equ_names}
        lines = content.splitlines()

        for line_num, line in enumerate(lines, 1):
            # Skip comment lines and EQU definition lines
            if line.strip().startswith('*') or 'EQU' in line:
                continue

            for equ_name in equ_names:
                # Look for the EQU name as a whole word
                pattern = r'\b' + re.escape(equ_name) + r'\b'
                if re.search(pattern, line, re.IGNORECASE):
                    references[equ_name].append(line_num)

        return references

    def categorize_equ_constants(self, equ_items: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Categorize EQU constants by their type and usage.

        Args:
            equ_items: List of EQU items to categorize

        Returns:
            Dict mapping categories to lists of EQU items
        """
        categories = {
            'numeric_constants': [],
            'string_constants': [],
            'file_constants': [],
            'status_codes': [],
            'configuration': [],
            'other': []
        }

        for item in equ_items:
            value = item.get('default_value', '').upper()
            name = item.get('name', '').upper()
            
            # Categorize based on name patterns and value types
            if any(keyword in name for keyword in ['FILE', 'DD', 'DATASET']):
                categories['file_constants'].append(item)
            elif any(keyword in name for keyword in ['STATUS', 'CODE', 'RC', 'RETURN']):
                categories['status_codes'].append(item)
            elif any(keyword in name for keyword in ['CONFIG', 'PARM', 'OPTION']):
                categories['configuration'].append(item)
            elif item.get('data_type') in ['NUMERIC', 'DECIMAL']:
                categories['numeric_constants'].append(item)
            elif item.get('data_type') in ['STRING', 'ALPHANUMERIC']:
                categories['string_constants'].append(item)
            else:
                categories['other'].append(item)

        return categories

    def generate_equ_summary(self, equ_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a summary of EQU constants found.

        Args:
            equ_items: List of EQU items

        Returns:
            Dict containing summary information
        """
        if not equ_items:
            return {
                'total_count': 0,
                'categories': {},
                'data_types': {},
                'business_functions': []
            }

        categories = self.categorize_equ_constants(equ_items)
        
        # Count by data type
        data_type_counts = {}
        for item in equ_items:
            data_type = item.get('data_type', 'UNKNOWN')
            data_type_counts[data_type] = data_type_counts.get(data_type, 0) + 1

        # Extract unique business functions
        business_functions = list(set(
            item.get('business_name', 'Unknown') 
            for item in equ_items 
            if item.get('business_name')
        ))

        return {
            'total_count': len(equ_items),
            'categories': {cat: len(items) for cat, items in categories.items() if items},
            'data_types': data_type_counts,
            'business_functions': business_functions[:10]  # Limit to top 10
        }
