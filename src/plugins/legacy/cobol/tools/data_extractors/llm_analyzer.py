"""
LLM-based analysis functionality for COBOL data definition extraction.
Handles complex analysis using language models for deeper insights.
"""
import json
import logging
from typing import Dict, List, Any

from llm_settings import invoke_llm
from langchain.schema import HumanMessage, SystemMessage
from .data_type_analyzer import DataTypeAnalyzer
from src.platform.tools.utils.template_manager import get_template_manager

logger = logging.getLogger("tools.extractors.llm_analyzer")


# noinspection PyMethodMayBeStatic
class LLMAnalyzer:
    """
    Uses LLM for complex analysis of COBOL data structures and extraction of business insights.
    """

    def __init__(self):
        """Initialize the LLM analyzer."""
        
        self.data_type_analyzer = DataTypeAnalyzer()
        self.template_manager = get_template_manager()

        # Configuration for retry behavior
        self.max_retries = 3
        self.retry_delay = 1  # seconds

    def analyze_structure_with_llm(self, structure: str, top_parent: str, program_id: str, source_file_name: str) -> List[Dict[str, Any]]:
        """
        Use LLM to analyze a complete COBOL data structure.

        Args:
            structure: Content of the data structure
            top_parent: Name of the top-level parent
            program_id: ID of the program being processed
            source_file_name: Source file name

        Returns:
            List[Dict[str, Any]]: List of extracted data items
        """
        logger.info(f"Starting LLM analysis for structure {top_parent}")


        # Retry logic for LLM analysis
        for attempt in range(self.max_retries):
            try:
                logger.info(f"LLM analysis attempt {attempt + 1}/{self.max_retries} for structure {top_parent}")

                # Use templates for prompts
                system_prompt = self.template_manager.render_template(
                    "data_extractors/structure_analysis_system.j2",
                    {}
                )

                user_prompt = self.template_manager.render_template(
                    "data_extractors/structure_analysis_user.j2",
                    {
                        "top_parent": top_parent,
                        "program_id": program_id,
                        "structure": structure
                    }
                )

                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=user_prompt)
                ]

                logger.info(f"Invoking LLM for structure {top_parent}")
                response = invoke_llm(messages)
                logger.info(f"LLM response received for structure {top_parent}, length: {len(response)}")

                # Parse LLM response with enhanced error handling
                items = self._parse_llm_response_enhanced(response, top_parent, program_id, source_file_name)

                if items:
                    logger.info(f"LLM analysis successful: extracted {len(items)} items from structure {top_parent}")
                    return items
                else:
                    logger.warning(f"LLM parsing returned no items for structure {top_parent} on attempt {attempt + 1}")
                    if attempt == self.max_retries - 1:
                        # Last attempt failed, raise exception
                        logger.error(f"All LLM attempts failed for structure {top_parent}")
                        raise Exception(f"LLM analysis failed to extract any items from structure {top_parent} after {self.max_retries} attempts")

            except Exception as e:
                logger.error(f"LLM analysis attempt {attempt + 1} failed for structure {top_parent}: {str(e)}")
                if attempt == self.max_retries - 1:
                    # Last attempt failed, re-raise the exception to be handled by caller
                    logger.error(f"All LLM attempts failed for structure {top_parent}, re-raising exception")
                    raise Exception(f"LLM analysis failed after {self.max_retries} attempts: {str(e)}")

                # Wait a bit before retrying
                import time
                time.sleep(self.retry_delay)

        # This should never be reached, but just in case
        raise Exception(f"Unexpected error in LLM analysis for structure {top_parent}")

    def analyze_structure_chunk_with_llm(self, chunk_info: Dict[str, Any], top_parent: str,
                                       program_id: str, source_file_name: str,
                                       chunk_num: int, total_chunks: int) -> List[Dict[str, Any]]:
        """
        Use LLM to analyze a chunk of a large structure.

        Args:
            chunk_info: Dictionary containing chunk content and metadata
            top_parent: Name of the top-level parent
            program_id: ID of the program being processed
            source_file_name: Source file name
            chunk_num: Current chunk number
            total_chunks: Total number of chunks

        Returns:
            List[Dict[str, Any]]: List of extracted data items
        """
        content = chunk_info["content"]
        hierarchy_context = chunk_info.get("hierarchy_context", {})
        parent_definitions = chunk_info.get("parent_definitions", {})

        context_info = ""
        parent_context_info = ""

        if total_chunks > 1:
            context_info = f"""

            IMPORTANT CONTEXT:
            - This is chunk {chunk_num} of {total_chunks} from a large data structure
            - The complete structure name is: {top_parent}
            - This chunk contains approximately {chunk_info['variable_count']} variables
            - All items in this chunk belong to the top-level structure: {top_parent}
            """

            # Add parent context information if available
            if parent_definitions:
                parent_context_info = f"""

            PARENT HIERARCHY CONTEXT:
            The following parent items are defined elsewhere in the complete structure:
            {chr(10).join(f"- {name}: {definition}" for name, definition in list(parent_definitions.items())[:5])}
            """

        logger.info(f"Starting LLM chunk analysis for {top_parent} chunk {chunk_num}/{total_chunks}")

        # Retry logic for chunk analysis
        for attempt in range(self.max_retries):
            try:
                logger.info(f"LLM chunk analysis attempt {attempt + 1}/{self.max_retries} for chunk {chunk_num}")

                # Use templates for prompts
                system_prompt = self.template_manager.render_template(
                    "data_extractors/chunk_analysis_system.j2",
                    {
                        "context_info": context_info,
                        "parent_context_info": parent_context_info
                    }
                )

                user_prompt = self.template_manager.render_template(
                    "data_extractors/chunk_analysis_user.j2",
                    {
                        "top_parent": top_parent,
                        "chunk_num": chunk_num,
                        "total_chunks": total_chunks,
                        "program_id": program_id,
                        "content": content
                    }
                )

                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=user_prompt)
                ]

                logger.info(f"Invoking LLM for chunk {chunk_num}")
                response = invoke_llm(messages)
                logger.info(f"LLM response received for chunk {chunk_num}, length: {len(response)}")

                # Parse LLM response with enhanced error handling
                items = self._parse_llm_response_enhanced(response, top_parent, program_id, source_file_name)

                if items:
                    logger.info(f"LLM chunk analysis successful: extracted {len(items)} items from chunk {chunk_num}")
                    return items
                else:
                    logger.warning(f"LLM parsing returned no items for chunk {chunk_num} on attempt {attempt + 1}")
                    if attempt == self.max_retries - 1:
                        # Last attempt failed, raise exception
                        logger.error(f"All LLM attempts failed for chunk {chunk_num}")
                        raise Exception(f"LLM chunk analysis failed to extract any items from chunk {chunk_num} after {self.max_retries} attempts")

            except Exception as e:
                logger.error(f"LLM chunk analysis attempt {attempt + 1} failed for chunk {chunk_num}: {str(e)}")
                if attempt == self.max_retries - 1:
                    # Last attempt failed, re-raise the exception to be handled by caller
                    logger.error(f"All LLM attempts failed for chunk {chunk_num}, re-raising exception")
                    raise Exception(f"LLM chunk analysis failed after {self.max_retries} attempts: {str(e)}")

                # Wait a bit before retrying
                import time
                time.sleep(self.retry_delay)

        # This should never be reached, but just in case
        raise Exception(f"Unexpected error in LLM chunk analysis for chunk {chunk_num}")

    def _parse_llm_response_enhanced(self, response: str, top_parent: str, program_id: str, source_file_name: str) -> List[Dict[str, Any]]:
        """
        Enhanced parsing of LLM response with better error handling and multiple fallback strategies.

        Args:
            response: Raw LLM response
            top_parent: Name of the top-level parent
            program_id: ID of the program
            source_file_name: Source file name

        Returns:
            List[Dict[str, Any]]: Parsed data items
        """
        logger.info(f"Parsing LLM response for structure {top_parent}: OK.")
        logger.debug(f"LLM response for structure {top_parent}: {response}")

        try:
            # Try multiple JSON extraction strategies
            json_content = self._extract_json_from_response_enhanced(response)
            if not json_content:
                logger.warning(f"No valid JSON found in LLM response for structure {top_parent}")
                logger.debug(f"LLM response content: {response}...")
                return []

            raw_items = json.loads(json_content)
            if not isinstance(raw_items, list):
                logger.warning(f"LLM response is not a JSON array for structure {top_parent}")
                return []

            # Convert to standardized format
            standardized_items = []
            for i, item in enumerate(raw_items):
                try:
                    standardized_item = self._standardize_data_item(
                        item, top_parent, program_id, source_file_name
                    )
                    if standardized_item:
                        standardized_items.append(standardized_item)
                    else:
                        logger.warning(f"Failed to standardize item {i} for structure {top_parent}")
                except Exception as e:
                    logger.error(f"Error standardizing item {i} for structure {top_parent}: {str(e)}")
                    continue

            logger.info(f"Successfully parsed {len(standardized_items)} items from LLM response for structure {top_parent}")
            return standardized_items

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error for structure {top_parent}: {str(e)}")
            logger.debug(f"Problematic JSON content: {json_content[:200] if 'json_content' in locals() else 'N/A'}...")
            return []
        except Exception as e:
            logger.error(f"Error parsing LLM response for structure {top_parent}: {str(e)}")
            return []

    def _parse_llm_response(self, response: str, top_parent: str, program_id: str, source_file_name: str) -> List[Dict[str, Any]]:
        """
        Parse LLM response and convert to standardized data item format.

        Args:
            response: Raw LLM response
            top_parent: Name of the top-level parent
            program_id: ID of the program
            source_file_name: Source file name

        Returns:
            List[Dict[str, Any]]: Parsed data items
        """
        try:
            # Try to extract JSON from response
            json_match = self._extract_json_from_response(response)
            if not json_match:
                logger.warning("No valid JSON found in LLM response")
                return []

            raw_items = json.loads(json_match)
            if not isinstance(raw_items, list):
                logger.warning("LLM response is not a JSON array")
                return []

            # Convert to standardized format
            standardized_items = []
            for item in raw_items:
                standardized_item = self._standardize_data_item(
                    item, top_parent, program_id, source_file_name
                )
                if standardized_item:
                    standardized_items.append(standardized_item)

            return standardized_items

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Error parsing LLM response: {str(e)}")
            return []

    def _extract_json_from_response_enhanced(self, response: str) -> str:
        """Enhanced JSON extraction with multiple strategies."""
        import re

        # Strategy 1: Look for JSON array patterns with various delimiters
        json_patterns = [
            r'```json\s*(\[.*?\])\s*```',  # Markdown JSON code block
            r'```\s*(\[.*?\])\s*```',      # Generic code block
            r'json\s*:\s*(\[.*?\])',       # json: [...]
            r'(\[(?:[^[\]]|(?:\[[^\]]*\]))*\])',  # Balanced brackets
            r'(\[.*\])',                   # Simple array pattern (greedy)
        ]

        for i, pattern in enumerate(json_patterns):
            try:
                match = re.search(pattern, response, re.DOTALL)
                if match:
                    json_content = match.group(1) if len(match.groups()) > 0 else match.group(0)
                    # Validate it's parseable JSON
                    import json
                    json.loads(json_content)
                    logger.debug(f"JSON extracted using pattern {i+1}")
                    return json_content
            except (json.JSONDecodeError, AttributeError):
                continue

        # Strategy 2: Try to find and fix common JSON issues
        try:
            # Look for array-like content and try to fix common issues
            array_match = re.search(r'\[.*\]', response, re.DOTALL)
            if array_match:
                json_candidate = array_match.group(0)
                # Try to fix common issues
                json_candidate = self._fix_common_json_issues(json_candidate)
                import json
                json.loads(json_candidate)
                logger.debug("JSON extracted and fixed using strategy 2")
                return json_candidate
        except (json.JSONDecodeError, AttributeError):
            pass

        logger.warning("No valid JSON found in LLM response")
        return ""

    def _fix_common_json_issues(self, json_str: str) -> str:
        """Fix common JSON formatting issues in LLM responses."""
        import re

        # Remove trailing commas
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)

        # Fix unquoted keys
        json_str = re.sub(r'(\w+):', r'"\1":', json_str)

        # Fix single quotes to double quotes
        json_str = json_str.replace("'", '"')

        return json_str

    def _extract_json_from_response(self, response: str) -> str:
        """Extract JSON content from LLM response."""
        import re

        # Look for JSON array patterns - use greedy matching to capture complete arrays
        json_patterns = [
            r'```json\s*(\[.*\])\s*```',  # Markdown code block (greedy)
            r'```\s*(\[.*\])\s*```',  # Generic code block (greedy)
            r'(\[(?:[^[\]]|(?:\[[^\]]*\]))*\])',  # Balanced brackets for nested arrays
        ]

        for pattern in json_patterns:
            match = re.search(pattern, response, re.DOTALL)
            if match:
                if len(match.groups()) > 0:
                    return match.group(1)
                else:
                    return match.group(0)

        return ""

    def _standardize_data_item(self, item: Dict[str, Any], top_parent: str,
                             program_id: str, source_file_name: str) -> Dict[str, Any] | None:
        """
        Convert LLM-extracted item to standardized format.

        Args:
            item: Raw item from LLM
            top_parent: Top-level parent name
            program_id: Program ID
            source_file_name: Source file name

        Returns:
            Dict[str, Any]: Standardized data item
        """
        try:
            # Helper function to safely handle string operations on potentially None values
            def safe_string_op(value, default="", operation="strip"):
                if value is None:
                    return default
                if not isinstance(value, str):
                    value = str(value)
                if operation == "strip":
                    return value.strip()
                elif operation == "upper":
                    return value.upper()
                elif operation == "lower":
                    return value.lower()
                return value

            # Helper function to safely convert level to integer
            def safe_level_conversion(level_value, default=0):
                if level_value is None:
                    return default
                try:
                    if isinstance(level_value, str):
                        # Handle string levels like "01", "05", etc.
                        return int(level_value)
                    elif isinstance(level_value, (int, float)):
                        return int(level_value)
                    else:
                        return default
                except (ValueError, TypeError):
                    return default

            standardized = {
                "name": safe_string_op(item.get("name"), ""),
                "top_parent_datastructure_name": top_parent,
                "source_file_name": source_file_name,
                "program_id": program_id,
                "business_name": safe_string_op(item.get("business_name"), ""),
                "item_type": safe_string_op(item.get("item_type", "variable"), "variable", "lower"),
                "data_type": safe_string_op(item.get("data_type", "UNKNOWN"), "UNKNOWN", "upper"),
                "length": item.get("length"),
                "description": safe_string_op(item.get("description"), ""),
                "parent_name": safe_string_op(item.get("parent_name"), ""),
                "level": safe_level_conversion(item.get("level"), 0),  # Use safe level conversion
                "default_value": item.get("default_value"),
                "possible_values": item.get("possible_values", []),
                "occurs": item.get("occurs_info"),  # Map occurs_info to occurs for database compatibility
                "redefines": item.get("redefines_info")  # Map redefines_info to redefines for database compatibility
            }

            # Validate required fields
            if not standardized["name"]:
                return None

            # Set parent name to top_parent if not specified and level > 1
            if not standardized["parent_name"] and standardized["level"] > 1:
                standardized["parent_name"] = top_parent

            return standardized

        except Exception as e:
            logger.error(f"Error standardizing data item: {str(e)}, item: {item}")
            return None


