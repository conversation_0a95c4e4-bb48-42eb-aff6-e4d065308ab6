"""
Structure processing functionality for COBOL data definition extraction.
Handles splitting and processing of hierarchical data structures.
"""
import re
import logging
from typing import Dict, List, Any, Tuple

logger = logging.getLogger("tools.extractors.structure_processor")


class StructureProcessor:
    """
    Handles processing of COBOL data structures, including splitting large structures
    and maintaining hierarchical relationships.
    """

    def __init__(self, max_variables_per_chunk: int = 50):
        """
        Initialize the structure processor.

        Args:
            max_variables_per_chunk: Maximum number of variables per chunk when splitting
        """
        self.max_variables_per_chunk = max_variables_per_chunk

    def split_into_structures(self, content: str) -> List[str]:
        """
        Split the DATA DIVISION content into separate 01-level structures.

        Args:
            content: The DATA DIVISION content

        Returns:
            List[str]: List of 01-level structures
        """
        structures = []
        lines = content.splitlines()

        current_structure = []
        capturing = False

        for line in lines:
            # Skip comment lines
            if line.strip().startswith('*') or line.strip() == '':
                if capturing:
                    current_structure.append(line)
                continue

            # Check for 01-level item
            match = re.search(r'^\s*01\s+([A-Za-z0-9-_]+)', line)
            if match:
                # If we were capturing a previous structure, save it
                if capturing and current_structure:
                    structures.append('\n'.join(current_structure))

                # Start capturing a new structure
                current_structure = [line]
                capturing = True
            elif capturing:
                # Continue capturing the current structure
                current_structure.append(line)

        # Add the last structure if we were capturing one
        if capturing and current_structure:
            structures.append('\n'.join(current_structure))

        return structures

    def extract_structure_name(self, structure: str) -> str:
        """
        Extract the name of the 01-level structure.

        Args:
            structure: Content of the structure

        Returns:
            str: Name of the structure, or empty string if not found
        """
        match = re.search(r'^\s*01\s+([A-Za-z0-9-_]+)', structure, re.MULTILINE)
        return match.group(1) if match else ""

    def count_variables_in_structure(self, structure: str) -> int:
        """
        Count the number of data variables in a structure.

        Args:
            structure: The structure content

        Returns:
            int: Number of variables found
        """
        lines = structure.splitlines()
        variable_count = 0

        for line in lines:
            # Skip comment lines and empty lines
            if line.strip().startswith('*') or line.strip() == '':
                continue

            # Look for level numbers (excluding 88-level condition names for main count)
            match = re.search(r'^\s*(\d+)\s+([A-Za-z0-9-_]+|FILLER)', line)
            if match:
                level = int(match.group(1))
                # Count all levels except 88-level condition names in the main count
                # (88-level items will be included as metadata for their parent)
                if level != 88:
                    variable_count += 1

        return variable_count

    def split_large_structure(self, structure: str, top_parent: str) -> List[Dict[str, Any]]:
        """
        Split a large structure into smaller chunks while preserving hierarchy.

        Args:
            structure: The structure content
            top_parent: Name of the top-level parent

        Returns:
            List[Dict[str, Any]]: List of structure chunks with metadata
        """
        lines = structure.splitlines()
        chunks = []
        current_chunk_lines = []
        current_chunk_variables = 0

        # Keep track of the complete hierarchy for context
        hierarchy_stack = []  # Stack of (level, name, line_content) tuples
        parent_definitions = {}  # Maps item name to its definition line
        current_parents = {}  # Maps level to current parent name at that level

        # First, find the 01-level line to include in every chunk
        header_line = ""
        for line in lines:
            if re.search(r'^\s*01\s+([A-Za-z0-9-_]+)', line):
                header_line = line
                break

        # First pass: build complete hierarchy and parent definitions
        all_hierarchy_info = self.build_complete_hierarchy(structure)

        for line in lines:
            # Skip comment lines and empty lines for counting, but include them in chunks
            if line.strip().startswith('*') or line.strip() == '':
                current_chunk_lines.append(line)
                continue

            # Check for data definition line
            match = re.search(r'^\s*(\d+)\s+([A-Za-z0-9-_]+|FILLER)', line)
            if match:
                level = int(match.group(1))
                name = match.group(2)

                # Update hierarchy tracking
                if level == 1:  # 01-level
                    hierarchy_stack = [(level, name, line)]
                    current_parents = {level: None}
                    parent_definitions[name] = line
                else:
                    # Find the correct parent by popping stack until we find a lower level
                    while hierarchy_stack and hierarchy_stack[-1][0] >= level:
                        hierarchy_stack.pop()

                    # Update current parents mapping
                    immediate_parent = hierarchy_stack[-1][1] if hierarchy_stack else None
                    current_parents[level] = immediate_parent

                    # Store the definition for this item
                    parent_definitions[name] = line
                    hierarchy_stack.append((level, name, line))

                # Count non-88 level items
                if level != 88:
                    current_chunk_variables += 1

                current_chunk_lines.append(line)

                # Check if we need to split (but not on 01-level or 88-level items)
                if (current_chunk_variables >= self.max_variables_per_chunk and
                        level not in [1, 88] and
                        len(current_chunk_lines) > 1):

                    # Get the parent context for items in this chunk
                    parent_context_lines = self.get_parent_context_lines(
                        current_chunk_lines, all_hierarchy_info, header_line
                    )

                    # Create chunk metadata
                    chunk_info = {
                        "content": "\n".join(parent_context_lines + current_chunk_lines),
                        "variable_count": current_chunk_variables,
                        "top_parent": top_parent,
                        "hierarchy_context": dict(current_parents),
                        "parent_definitions": dict(parent_definitions),
                        "chunk_type": "partial_structure"
                    }
                    chunks.append(chunk_info)

                    # Start new chunk, keeping hierarchy context
                    current_chunk_lines = [line]  # Start with current line
                    current_chunk_variables = 1  # Count current line
            else:
                # Non-data lines (VALUE clauses, etc.)
                current_chunk_lines.append(line)

        # Add remaining lines as final chunk
        if current_chunk_lines:
            if len(chunks) > 0:  # This is a continuation chunk
                parent_context_lines = self.get_parent_context_lines(
                    current_chunk_lines, all_hierarchy_info, header_line
                )
                content = "\n".join(parent_context_lines + current_chunk_lines)
            else:  # This is the complete structure
                content = "\n".join(current_chunk_lines)

            chunk_info = {
                "content": content,
                "variable_count": current_chunk_variables,
                "top_parent": top_parent,
                "hierarchy_context": dict(current_parents),
                "parent_definitions": dict(parent_definitions),
                "chunk_type": "partial_structure" if len(chunks) > 0 else "complete_structure"
            }
            chunks.append(chunk_info)

        return chunks

    def build_complete_hierarchy(self, structure: str) -> Dict[str, Dict[str, Any]]:
        """
        Build a complete hierarchy map of the structure.

        Args:
            structure: The complete structure content

        Returns:
            Dict mapping item names to their hierarchy info
        """
        lines = structure.splitlines()
        hierarchy_info = {}
        hierarchy_stack = []

        for line in lines:
            if line.strip().startswith('*') or line.strip() == '':
                continue

            match = re.search(r'^\s*(\d+)\s+([A-Za-z0-9-_]+|FILLER)', line)
            if match:
                level = int(match.group(1))
                name = match.group(2)

                # Update hierarchy stack
                while hierarchy_stack and hierarchy_stack[-1]['level'] >= level:
                    hierarchy_stack.pop()

                immediate_parent = hierarchy_stack[-1]['name'] if hierarchy_stack else None

                hierarchy_info[name] = {
                    'level': level,
                    'line': line,
                    'immediate_parent': immediate_parent,
                    'full_path': [item['name'] for item in hierarchy_stack] + [name]
                }

                hierarchy_stack.append({'level': level, 'name': name, 'line': line})

        return hierarchy_info

    def get_parent_context_lines(self, chunk_lines: List[str], all_hierarchy_info: Dict[str, Dict[str, Any]], header_line: str) -> List[str]:
        """
        Get the parent context lines needed for a chunk to understand the hierarchy.

        Args:
            chunk_lines: Lines in the current chunk
            all_hierarchy_info: Complete hierarchy information
            header_line: The 01-level header line

        Returns:
            List of parent context lines to include
        """
        context_lines = [header_line] if header_line else []
        needed_parents = set()

        # Find all items in this chunk and their parents
        for line in chunk_lines:
            match = re.search(r'^\s*(\d+)\s+([A-Za-z0-9-_]+|FILLER)', line)
            if match:
                name = match.group(2)
                if name in all_hierarchy_info:
                    # Add all parents in the path except the top-level (already included)
                    full_path = all_hierarchy_info[name]['full_path']
                    for parent_name in full_path[1:-1]:  # Exclude top-level and self
                        if parent_name in all_hierarchy_info:
                            needed_parents.add(parent_name)

        # Add parent definition lines
        for parent_name in needed_parents:
            if parent_name in all_hierarchy_info:
                parent_line = all_hierarchy_info[parent_name]['line']
                if parent_line not in context_lines:
                    context_lines.append(parent_line)

        # Sort context lines by level to maintain proper hierarchy
        def get_level_from_line(line):
            match = re.search(r'^\s*(\d+)', line)
            return int(match.group(1)) if match else 0

        context_lines.sort(key=get_level_from_line)
        return context_lines
