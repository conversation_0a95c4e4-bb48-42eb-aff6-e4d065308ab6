"""
Core CobolDataDefinitionExtractor implementation.
Main orchestration for extracting data definitions from COBOL code.
"""
import logging
from typing import Dict, List, Any

from .structure_processor import StructureProcessor
from .equ_processor import EquProcessor
from .llm_analyzer import LLMAnalyzer
from src.platform.tools.knowledge_database import KnowledgeDatabase

logger = logging.getLogger("tools.extractors.cobol_data_extractor")


class CobolDataDefinitionExtractor:
    """
    Extracts data definitions from COBOL code chunks.

    This extractor processes COBOL DATA DIVISION chunks to extract information about
    variables, constants, and data structures. It handles hierarchical data structures,
    REDEFINES, OCCURS, condition names (88-level items), and special features of IBM COBOL.

    Extracted data is stored in a knowledge database for further processing.
    """

    def __init__(self):
        """Initialize the COBOL data definition extractor."""
        self.knowledge_db = KnowledgeDatabase()
        self.max_variables_per_chunk = 50

        # Initialize specialized processors
        self.structure_processor = StructureProcessor(self.max_variables_per_chunk)
        self.equ_processor = EquProcessor()
        self.llm_analyzer = LLMAnalyzer()

    def process_chunk(self, chunk: Dict[str, Any], program_id: str) -> List[Dict[str, Any]]:
        """
        Process a COBOL data division chunk to extract data definitions.

        Args:
            chunk: Dictionary containing the chunk metadata and content
            program_id: ID of the program being processed

        Returns:
            List[Dict[str, Any]]: List of extracted data definitions
        """
        logger.info(f"Processing COBOL data definitions for program {program_id}")

        # Get the content from the chunk
        content = chunk.get("code", "")
        if not content:
            logger.warning(f"Empty content in chunk for program {program_id}")
            return []

        # Determine the source file name
        source_file_name = ""
        chunk_type = chunk.get("chunk_type", "")

        # Skip if this is not a data division chunk
        if not chunk_type.startswith("DATA_DIVISION"):
            logger.info(f"Skipping non-DATA DIVISION chunk: {chunk_type}")
            return []

        # Extract EQU directives first (they're outside the hierarchical structure)
        equ_items = self.equ_processor.extract_equ_directives(content, program_id, source_file_name)

        # Split content into 01-level structures for hierarchical processing
        structures = self.structure_processor.split_into_structures(content)

        # Process each 01-level structure
        all_items = []
        for struct in structures:
            # Extract items from this structure
            items = self._process_structure(struct, program_id, source_file_name)
            all_items.extend(items)

        # Add EQU items to the result
        all_items.extend(equ_items)

        return all_items

    def _process_structure(self, structure: str, program_id: str, source_file_name: str) -> List[Dict[str, Any]]:
        """
        Process a single 01-level structure to extract all data items.

        Args:
            structure: Content of the 01-level structure
            program_id: ID of the program being processed
            source_file_name: Source file name

        Returns:
            List[Dict[str, Any]]: List of extracted data items
        """
        # Extract the structure name
        top_parent = self.structure_processor.extract_structure_name(structure)
        if not top_parent:
            logger.warning(f"Could not find 01-level name in structure:\n{structure}")
            return []

        # Check if structure is too large
        variable_count = self.structure_processor.count_variables_in_structure(structure)
        logger.info(f"Structure {top_parent} has {variable_count} variables")

        if variable_count > self.max_variables_per_chunk:
            logger.info(f"Structure {top_parent} is large ({variable_count} variables), splitting into chunks")
            return self._process_large_structure(structure, top_parent, program_id, source_file_name)
        else:
            # Always use LLM to get deeper insights about the structure
            logger.info(f"Using LLM analysis for structure {top_parent} ({variable_count} variables)")
            return self.llm_analyzer.analyze_structure_with_llm(structure, top_parent, program_id, source_file_name)

    def _process_large_structure(self, structure: str, top_parent: str, program_id: str, source_file_name: str) -> List[Dict[str, Any]]:
        """
        Process a large structure by splitting it into manageable chunks.

        Args:
            structure: Content of the 01-level structure
            top_parent: Name of the top-level parent
            program_id: ID of the program being processed
            source_file_name: Source file name

        Returns:
            List[Dict[str, Any]]: List of extracted data items from all chunks
        """
        chunks = self.structure_processor.split_large_structure(structure, top_parent)
        all_items = []

        for i, chunk_info in enumerate(chunks):
            logger.info(f"Processing chunk {i+1}/{len(chunks)} of structure {top_parent} "
                        f"({chunk_info['variable_count']} variables)")

            # Analyze this chunk with additional context about the overall structure
            chunk_items = self.llm_analyzer.analyze_structure_chunk_with_llm(
                chunk_info, top_parent, program_id, source_file_name, i+1, len(chunks)
            )
            all_items.extend(chunk_items)

        return all_items

    def save_to_knowledge_base(self, items: List[Dict[str, Any]], program_id: str) -> None:
        """
        Save extracted data definitions to the knowledge database.

        Args:
            items: List of extracted data items
            program_id: ID of the program being processed
        """
        logger.info(f"Saving {len(items)} data definitions to knowledge base for program {program_id}")

        for item in items:
            try:
                self.knowledge_db.save_data_definition(item)
            except Exception as e:
                logger.error(f"Error saving data definition {item.get('name', 'unknown')}: {str(e)}")

    def get_data_definitions_for_program(self, program_id: str) -> List[Dict[str, Any]]:
        """
        Retrieve all data definitions for a specific program.

        Args:
            program_id: ID of the program

        Returns:
            List[Dict[str, Any]]: List of data definitions for the program
        """
        return self.knowledge_db.get_data_definitions_by_program(program_id)

    def search_data_definitions(self, search_term: str) -> List[Dict[str, Any]]:
        """
        Search for data definitions by name or business name.

        Args:
            search_term: Term to search for

        Returns:
            List[Dict[str, Any]]: List of matching data definitions
        """
        return self.knowledge_db.search_data_definitions(search_term)

    def get_structure_hierarchy(self, top_parent: str, program_id: str) -> Dict[str, Any]:
        """
        Get the complete hierarchy for a data structure.

        Args:
            top_parent: Name of the top-level structure
            program_id: ID of the program

        Returns:
            Dict[str, Any]: Hierarchical structure information
        """
        return self.knowledge_db.get_structure_hierarchy(top_parent, program_id)

    def validate_data_definitions(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Validate extracted data definitions for completeness and consistency.

        Args:
            items: List of data items to validate

        Returns:
            List[Dict[str, Any]]: List of validation issues found
        """
        issues = []

        for item in items:
            # Check required fields
            required_fields = ['name', 'item_type', 'data_type', 'program_id']
            for field in required_fields:
                if not item.get(field):
                    issues.append({
                        'type': 'missing_field',
                        'item': item.get('name', 'unknown'),
                        'field': field,
                        'message': f"Missing required field: {field}"
                    })

            # Check data type consistency
            data_type = item.get('data_type', '')
            length = item.get('length')
            if data_type in ['NUMERIC', 'DECIMAL'] and not length:
                issues.append({
                    'type': 'inconsistent_data',
                    'item': item.get('name', 'unknown'),
                    'message': f"Numeric field missing length specification"
                })

        return issues
