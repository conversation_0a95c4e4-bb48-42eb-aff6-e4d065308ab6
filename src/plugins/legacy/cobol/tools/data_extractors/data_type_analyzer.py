"""
Data type analysis functionality for COBOL data definition extraction.
Handles determination of data types, lengths, and formats.
"""
import re
import logging
from typing import Dict, List, Any, Tuple, Optional

logger = logging.getLogger("tools.extractors.data_type_analyzer")


class DataTypeAnalyzer:
    """
    Analyzes COBOL data definitions to determine data types, lengths, and formats.
    """

    def __init__(self):
        """Initialize the data type analyzer."""
        self.cobol_picture_patterns = {
            'numeric': [
                r'9+',  # Numeric digits
                r'S9+',  # Signed numeric
                r'9+V9+',  # Decimal with implied decimal point
                r'S9+V9+',  # Signed decimal with implied decimal point
                r'9+\((\d+)\)',  # Repeated numeric pattern
                r'S9+\((\d+)\)',  # Signed repeated numeric
            ],
            'alphanumeric': [
                r'X+',  # Alphanumeric characters
                r'X+\((\d+)\)',  # Repeated alphanumeric pattern
                r'A+',  # Alphabetic characters
                r'A+\((\d+)\)',  # Repeated alphabetic pattern
            ],
            'display': [
                r'9+',  # Display numeric
                r'X+',  # Display alphanumeric
            ],
            'computational': [
                r'COMP',  # Computational
                r'COMP-1',  # Single precision floating point
                r'COMP-2',  # Double precision floating point
                r'COMP-3',  # Packed decimal
                r'COMP-4',  # Binary
                r'COMP-5',  # Native binary
            ]
        }

    def determine_type_from_value(self, value: str) -> Tuple[str, Optional[int]]:
        """
        Determine data type and length from a COBOL value.

        Args:
            value: The value to analyze

        Returns:
            Tuple of (data_type, length)
        """
        if not value:
            return "UNKNOWN", None

        value = value.strip()

        # Check for quoted strings
        if (value.startswith('"') and value.endswith('"')) or (value.startswith("'") and value.endswith("'")):
            content = value[1:-1]
            return "STRING", len(content)

        # Check for numeric values
        if re.match(r'^-?\d+$', value):
            return "NUMERIC", len(value.lstrip('-'))

        # Check for decimal values
        if re.match(r'^-?\d+\.\d+$', value):
            return "DECIMAL", len(value.lstrip('-'))

        # Check for hexadecimal values
        if re.match(r'^X\'[0-9A-Fa-f]+\'$', value):
            hex_content = value[2:-1]
            return "HEXADECIMAL", len(hex_content) // 2

        # Check for binary values
        if re.match(r'^B\'[01]+\'$', value):
            binary_content = value[2:-1]
            return "BINARY", len(binary_content)

        # Default to alphanumeric
        return "ALPHANUMERIC", len(value)

    def analyze_picture_clause(self, picture: str) -> Dict[str, Any]:
        """
        Analyze a COBOL PICTURE clause to determine data characteristics.

        Args:
            picture: The PICTURE clause content

        Returns:
            Dict containing analysis results
        """
        if not picture:
            return {
                'data_type': 'UNKNOWN',
                'length': None,
                'decimal_places': None,
                'signed': False,
                'usage': 'DISPLAY'
            }

        picture = picture.upper().strip()
        
        # Remove PIC or PICTURE keyword
        picture = re.sub(r'^(PIC|PICTURE)\s+', '', picture)

        analysis = {
            'data_type': 'UNKNOWN',
            'length': 0,
            'decimal_places': None,
            'signed': False,
            'usage': 'DISPLAY'
        }

        # Check for sign
        if 'S' in picture:
            analysis['signed'] = True

        # Analyze numeric patterns
        if self._is_numeric_picture(picture):
            analysis['data_type'] = 'NUMERIC'
            analysis['length'] = self._calculate_numeric_length(picture)
            analysis['decimal_places'] = self._calculate_decimal_places(picture)
        
        # Analyze alphanumeric patterns
        elif self._is_alphanumeric_picture(picture):
            analysis['data_type'] = 'ALPHANUMERIC'
            analysis['length'] = self._calculate_alphanumeric_length(picture)
        
        # Analyze alphabetic patterns
        elif self._is_alphabetic_picture(picture):
            analysis['data_type'] = 'ALPHABETIC'
            analysis['length'] = self._calculate_alphabetic_length(picture)

        return analysis

    def _is_numeric_picture(self, picture: str) -> bool:
        """Check if picture clause represents numeric data."""
        numeric_patterns = [r'9', r'S9', r'V', r'P']
        return any(pattern in picture for pattern in numeric_patterns)

    def _is_alphanumeric_picture(self, picture: str) -> bool:
        """Check if picture clause represents alphanumeric data."""
        return 'X' in picture

    def _is_alphabetic_picture(self, picture: str) -> bool:
        """Check if picture clause represents alphabetic data."""
        return 'A' in picture and 'X' not in picture

    def _calculate_numeric_length(self, picture: str) -> int:
        """Calculate the total length of a numeric picture clause."""
        length = 0
        
        # Count 9s
        nines = re.findall(r'9+', picture)
        for nine_group in nines:
            length += len(nine_group)
        
        # Handle repeated patterns like 9(5)
        repeated_patterns = re.findall(r'9\((\d+)\)', picture)
        for repeat_count in repeated_patterns:
            length += int(repeat_count)
        
        # Handle V (implied decimal point) - doesn't add to length
        # Handle P (assumed decimal scaling) - doesn't add to display length
        
        return length

    def _calculate_decimal_places(self, picture: str) -> Optional[int]:
        """Calculate decimal places in a numeric picture clause."""
        # Look for V followed by 9s
        v_match = re.search(r'V(9+)', picture)
        if v_match:
            return len(v_match.group(1))
        
        # Look for V followed by repeated pattern
        v_repeat_match = re.search(r'V9\((\d+)\)', picture)
        if v_repeat_match:
            return int(v_repeat_match.group(1))
        
        return None

    def _calculate_alphanumeric_length(self, picture: str) -> int:
        """Calculate the length of an alphanumeric picture clause."""
        length = 0
        
        # Count Xs
        xs = re.findall(r'X+', picture)
        for x_group in xs:
            length += len(x_group)
        
        # Handle repeated patterns like X(20)
        repeated_patterns = re.findall(r'X\((\d+)\)', picture)
        for repeat_count in repeated_patterns:
            length += int(repeat_count)
        
        return length

    def _calculate_alphabetic_length(self, picture: str) -> int:
        """Calculate the length of an alphabetic picture clause."""
        length = 0
        
        # Count As
        as_chars = re.findall(r'A+', picture)
        for a_group in as_chars:
            length += len(a_group)
        
        # Handle repeated patterns like A(15)
        repeated_patterns = re.findall(r'A\((\d+)\)', picture)
        for repeat_count in repeated_patterns:
            length += int(repeat_count)
        
        return length

    def determine_usage_type(self, usage_clause: str) -> str:
        """
        Determine the USAGE type from a USAGE clause.

        Args:
            usage_clause: The USAGE clause content

        Returns:
            str: The usage type
        """
        if not usage_clause:
            return 'DISPLAY'

        usage = usage_clause.upper().strip()
        
        # Remove USAGE keyword
        usage = re.sub(r'^USAGE\s+', '', usage)
        
        usage_mappings = {
            'DISPLAY': 'DISPLAY',
            'COMP': 'COMPUTATIONAL',
            'COMPUTATIONAL': 'COMPUTATIONAL',
            'COMP-1': 'COMPUTATIONAL-1',
            'COMPUTATIONAL-1': 'COMPUTATIONAL-1',
            'COMP-2': 'COMPUTATIONAL-2',
            'COMPUTATIONAL-2': 'COMPUTATIONAL-2',
            'COMP-3': 'COMPUTATIONAL-3',
            'COMPUTATIONAL-3': 'COMPUTATIONAL-3',
            'PACKED-DECIMAL': 'COMPUTATIONAL-3',
            'COMP-4': 'COMPUTATIONAL-4',
            'COMPUTATIONAL-4': 'COMPUTATIONAL-4',
            'BINARY': 'COMPUTATIONAL-4',
            'COMP-5': 'COMPUTATIONAL-5',
            'COMPUTATIONAL-5': 'COMPUTATIONAL-5',
            'INDEX': 'INDEX',
            'POINTER': 'POINTER'
        }
        
        return usage_mappings.get(usage, 'DISPLAY')

    def analyze_occurs_clause(self, occurs_clause: str) -> Dict[str, Any]:
        """
        Analyze an OCCURS clause to determine array characteristics.

        Args:
            occurs_clause: The OCCURS clause content

        Returns:
            Dict containing array analysis
        """
        if not occurs_clause:
            return {'is_array': False}

        occurs = occurs_clause.upper().strip()
        
        # Simple OCCURS n TIMES
        simple_match = re.search(r'OCCURS\s+(\d+)\s+TIMES', occurs)
        if simple_match:
            return {
                'is_array': True,
                'min_occurs': int(simple_match.group(1)),
                'max_occurs': int(simple_match.group(1)),
                'depending_on': None
            }
        
        # Variable OCCURS with DEPENDING ON
        variable_match = re.search(r'OCCURS\s+(\d+)\s+TO\s+(\d+)\s+TIMES\s+DEPENDING\s+ON\s+([A-Za-z0-9-_]+)', occurs)
        if variable_match:
            return {
                'is_array': True,
                'min_occurs': int(variable_match.group(1)),
                'max_occurs': int(variable_match.group(2)),
                'depending_on': variable_match.group(3)
            }
        
        return {'is_array': False}
