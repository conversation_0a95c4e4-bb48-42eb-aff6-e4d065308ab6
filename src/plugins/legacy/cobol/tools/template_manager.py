"""
COBOL-specific template manager.
Handles template rendering for COBOL language plugin.
"""
import os
import logging
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
from typing import Dict, Any


class CobolTemplateManager:
    """Template manager for COBOL plugin templates."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Get the directory where this file is located
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # Go up one level to the plugin root, then to templates
        self.template_dir = os.path.join(os.path.dirname(current_dir), "templates")
        
        if not os.path.exists(self.template_dir):
            raise RuntimeError(f"COBOL template directory not found: {self.template_dir}")
        
        # Initialize Jinja2 environment
        self.env = Environment(
            loader=FileSystemLoader(self.template_dir),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        self.logger.info(f"COBOL template manager initialized with directory: {self.template_dir}")
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """
        Render a template with the given context.
        
        Args:
            template_name: Name of the template file (relative to template directory)
            context: Dictionary of variables to pass to the template
            
        Returns:
            str: Rendered template content
            
        Raises:
            TemplateNotFound: If the template file doesn't exist
            Exception: If template rendering fails
        """
        try:
            template = self.env.get_template(template_name)
            rendered = template.render(**context)
            self.logger.debug(f"Successfully rendered COBOL template: {template_name}")
            return rendered
        except TemplateNotFound:
            self.logger.error(f"COBOL template not found: {template_name}")
            raise
        except Exception as e:
            self.logger.error(f"Error rendering COBOL template {template_name}: {str(e)}")
            raise
    
    def list_templates(self) -> list:
        """
        List all available templates.
        
        Returns:
            list: List of template file names
        """
        templates = []
        for root, dirs, files in os.walk(self.template_dir):
            for file in files:
                if file.endswith('.j2'):
                    rel_path = os.path.relpath(os.path.join(root, file), self.template_dir)
                    templates.append(rel_path)
        return templates
    
    def template_exists(self, template_name: str) -> bool:
        """
        Check if a template exists.
        
        Args:
            template_name: Name of the template file
            
        Returns:
            bool: True if template exists, False otherwise
        """
        try:
            self.env.get_template(template_name)
            return True
        except TemplateNotFound:
            return False


# Global instance for easy access
_cobol_template_manager = None


def get_cobol_template_manager() -> CobolTemplateManager:
    """
    Get the global COBOL template manager instance.
    
    Returns:
        CobolTemplateManager: Global template manager instance
    """
    global _cobol_template_manager
    if _cobol_template_manager is None:
        _cobol_template_manager = CobolTemplateManager()
    return _cobol_template_manager
