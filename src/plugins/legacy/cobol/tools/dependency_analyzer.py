import json
import re
import os
from collections import defaultdict, deque
from typing import List, Dict, Set, Tuple, Optional
import logging


class DependencyAnalyzer:
    """
    Language-agnostic analyzer that extracts procedure chunks and sorts them in topological order
    based on language-specific dependencies (dependencies first, entry points last).
    Uses language plugins for language-specific dependency extraction.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def get_sorted_chunks_for_program(self, program_id: str, working_directory: str, language: str = None) -> List[Dict[str, str]]:
        """
        Extract and sort procedure chunks in topological order using language plugins.

        Args:
            program_id: Program identifier
            working_directory: Working directory containing JSON files
            language: Programming language (auto-detected if not provided)

        Returns:
            List[Dict[str, str]]: Sorted chunks with chunk_name and code fields
        """
        try:
            # Load JSON data for the program
            json_data = self._load_program_json(program_id, working_directory)
            if not json_data:
                self.logger.warning(f"No JSON data found for program {program_id}")
                return []

            # Store relationships for section lookup
            self.relationships = json_data.get('relationships', [])
            self.all_nodes = {node['uuid']: node for node in json_data.get('nodes', [])}
            self.program_id = program_id

            # Extract procedure chunks
            procedure_chunks = self._extract_procedure_chunks(json_data)
            if not procedure_chunks:
                self.logger.warning(f"No procedure chunks found for program {program_id}")
                return []

            # Sort chunks in topological order based on language-specific call dependencies
            sorted_chunks = self._topological_sort_chunks(procedure_chunks, language)

            self.logger.info(f"Extracted {len(sorted_chunks)} procedure chunks")
            return sorted_chunks

        except Exception as e:
            self.logger.error(f"Error processing program {program_id}: {str(e)}")
            return []

    def _load_program_json(self, program_id: str, working_directory: str) -> Optional[Dict]:
        """Load JSON file for the program"""
        json_path = os.path.join(working_directory, "preprocessed", "cobol", "json", f"{program_id}.json")

        if not os.path.exists(json_path):
            self.logger.info(f"Looking for JSON file: {json_path}")
            return None

        try:
            self.logger.info(f"Reading JSON file for program {program_id}")
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.logger.info(f"JSON data loaded. Keys: {list(data.keys())}")
            if 'nodes' in data:
                self.logger.info(f"Found {len(data['nodes'])} nodes in JSON")
            return data
        except Exception as e:
            self.logger.error(f"Error loading JSON file {json_path}: {str(e)}")
            return None

    def _extract_procedure_chunks(self, json_data: Dict) -> Dict[str, Dict]:
        """Extract CobolParagraph and CobolEntryParagraph nodes from JSON data"""
        nodes = {node['uuid']: node for node in json_data.get('nodes', [])}
        relationships = json_data.get('relationships', [])

        # Find all CobolParagraph and CobolEntryParagraph nodes
        paragraphs = {
            uuid: node for uuid, node in nodes.items()
            if node.get('type') in ['CobolParagraph', 'CobolEntryParagraph']
        }

        self.logger.info(f"Found {len(paragraphs)} CobolParagraph and CobolEntryParagraph nodes")

        # Find procedure division to get only procedure chunks
        procedure_div = None
        for node in nodes.values():
            if node.get('type') == 'CobolProcedureDivision':
                procedure_div = node
                break

        if not procedure_div:
            self.logger.warning("No CobolProcedureDivision found")
            return paragraphs

        # Filter paragraphs that are within procedure division
        proc_paragraphs = {}
        for uuid, paragraph in paragraphs.items():
            if self._is_descendant_of(uuid, procedure_div['uuid'], relationships):
                proc_paragraphs[uuid] = paragraph

        self.logger.info(f"Found {len(proc_paragraphs)} paragraphs in procedure division")
        return proc_paragraphs

    def _is_descendant_of(self, child_uuid: str, parent_uuid: str, relationships: List[Dict]) -> bool:
        """Check if child_uuid is a descendant of parent_uuid"""
        # Build parent-child map
        children_map = defaultdict(list)
        for rel in relationships:
            if rel.get('type') == 'INCLUDES':
                children_map[rel['from_uuid']].append(rel['to_uuid'])

        # BFS to check ancestry
        queue = deque([parent_uuid])
        visited = {parent_uuid}

        while queue:
            current = queue.popleft()
            if current == child_uuid:
                return True

            for child in children_map.get(current, []):
                if child not in visited:
                    visited.add(child)
                    queue.append(child)

        return False

    def _topological_sort_chunks(self, paragraphs: Dict[str, Dict], language: str = None) -> List[Dict[str, str]]:
        """Sort paragraphs in topological order: dependencies first, entry points last"""

        # Build dependency graph from language-specific calls
        dependencies, name_to_uuid = self._build_dependency_graph(paragraphs, language)

        # Collect ALL paragraphs, not just named ones
        all_paragraphs = list(paragraphs.values())

        if not dependencies:
            # No dependencies found, return all paragraphs in original order
            self.logger.info(f"No dependencies found, returning {len(all_paragraphs)} paragraphs in original order")
            return self._convert_to_chunk_format(all_paragraphs)

        # Topological sort using DFS for named paragraphs only
        all_names = set(name_to_uuid.keys())
        result_names = []
        visited = set()
        visiting = set()  # For cycle detection

        def dfs(name: str) -> bool:
            """DFS with cycle detection. Returns False if cycle detected."""
            if name in visiting:
                self.logger.warning(f"Cycle detected involving: {name}")
                return False
            if name in visited:
                return True

            visiting.add(name)

            # Process all dependencies first (visit callees before callers)
            for dependency in dependencies.get(name, set()):
                if dependency in all_names:
                    if not dfs(dependency):
                        visiting.remove(name)
                        return False

            visiting.remove(name)
            visited.add(name)
            result_names.append(name)
            return True

        # Process all named paragraphs
        for name in all_names:
            if name not in visited:
                dfs(name)

        # Convert names back to paragraph objects
        sorted_named_paragraphs = []
        for name in result_names:
            if name in name_to_uuid:
                uuid = name_to_uuid[name]
                if uuid in paragraphs:
                    sorted_named_paragraphs.append(paragraphs[uuid])

        # Get unnamed/unprocessed paragraphs
        processed_uuids = {name_to_uuid[name] for name in result_names if name in name_to_uuid}
        unprocessed_paragraphs = [
            para for uuid, para in paragraphs.items()
            if uuid not in processed_uuids
        ]

        # Combine: unnamed/unprocessed first, then topologically sorted named paragraphs
        all_sorted = unprocessed_paragraphs + sorted_named_paragraphs

        self.logger.info(f"Topologically sorted {len(all_sorted)} paragraphs ({len(unprocessed_paragraphs)} unprocessed + {len(sorted_named_paragraphs)} sorted)")
        return self._convert_to_chunk_format(all_sorted)

    def _build_dependency_graph(self, paragraphs: Dict[str, Dict], language: str = None) -> Tuple[Dict[str, Set[str]], Dict[str, str]]:
        """Build dependency graph: paragraph_name -> set of called paragraphs"""
        dependencies = defaultdict(set)
        name_to_uuid = {}

        # Map paragraph names to UUIDs
        for uuid, para in paragraphs.items():
            para_name = para.get('paragraph_name', '').upper().strip()
            if para_name and para_name != 'UNNAMED-PARA':
                name_to_uuid[para_name] = uuid

        # Extract dependencies for each paragraph
        for uuid, para in paragraphs.items():
            para_name = para.get('paragraph_name', '').upper().strip()
            full_text = para.get('full_text', '')

            if para_name and para_name != 'UNNAMED-PARA':
                calls = self._extract_language_calls(full_text, language)
                # Only include calls to paragraphs that exist in our set
                valid_calls = {call for call in calls if call in name_to_uuid}
                dependencies[para_name] = valid_calls

                if valid_calls:
                    self.logger.debug(f"{para_name} calls: {', '.join(sorted(valid_calls))}")

        return dict(dependencies), name_to_uuid

    def _extract_language_calls(self, text: str, language: str = None) -> Set[str]:
        """Extract language-specific calls from code text using language plugins"""
        try:
            # Try to use language-specific dependency analyzer from plugins
            from src.platform.plugins.plugin_loader import get_plugin_loader
            plugin_loader = get_plugin_loader()

            # Auto-detect language if not provided
            if not language:
                from src.platform.tools.language_detector import detect_language
                language = detect_language(text)

            # Get the appropriate language plugin
            language_plugin = plugin_loader.get_language_plugin(language)
            if language_plugin and hasattr(language_plugin, 'get_dependency_analyzer'):
                dependency_analyzer = language_plugin.get_dependency_analyzer()
                if dependency_analyzer:
                    return dependency_analyzer.extract_dependencies(text)

        except Exception as e:
            self.logger.warning(f"Plugin-based dependency extraction failed: {str(e)}")

        # No fallback - plugins are required for language-specific processing
        self.logger.warning(f"No plugin available for language '{language}' dependency extraction")
        return set()

    def _find_section_for_paragraph(self, paragraph_uuid: str) -> str:
        """Find the section name that contains this paragraph"""
        # Build parent-child relationships
        parent_map = {}
        for rel in self.relationships:
            if rel.get('type') == 'INCLUDES':
                parent_map[rel['to_uuid']] = rel['from_uuid']

        # Traverse up the hierarchy to find the section
        current = paragraph_uuid
        while current in parent_map:
            parent_uuid = parent_map[current]
            if parent_uuid in self.all_nodes:
                parent_node = self.all_nodes[parent_uuid]
                if parent_node.get('type') == 'CobolSection':
                    section_name = parent_node.get('section_name', 'UNKNOWN-SECTION')
                    # Keep original section name exactly as is
                    if section_name and section_name != 'UNKNOWN-SECTION':
                        return section_name
                    return 'UNKNOWN-SECTION'
            current = parent_uuid

        return 'NO-SECTION'

    def _convert_to_chunk_format(self, paragraphs: List[Dict]) -> List[Dict[str, str]]:
        """Convert paragraph objects to expected chunk format with proper naming"""
        chunks = []

        for para in paragraphs:
            original_paragraph_name = para.get('paragraph_name', 'UNNAMED')

            # Keep original paragraph name for chunk naming
            paragraph_name_for_chunk = original_paragraph_name
            if paragraph_name_for_chunk in ['', None]:
                paragraph_name_for_chunk = 'UNNAMED'

            # Find the section for this paragraph
            section_name = self._find_section_for_paragraph(para.get('uuid', ''))

            # Build chunk name: {program_name}_PROC_{section_name}_SECT_{paragraph_name}
            chunk_name = f"{self.program_id}_PROC_{section_name}_SECT_{paragraph_name_for_chunk}"

            chunk = {
                'chunk_name': chunk_name,
                'code': para.get('full_text', ''),
                'chunk_type': 'PROCEDURE_DIVISION_PARAGRAPH',
                'business_name': original_paragraph_name,
                'uuid': para.get('uuid', ''),
                'section_name': section_name,
                'paragraph_name': original_paragraph_name
            }
            chunks.append(chunk)

        return chunks