[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
langchain = "*"
langgraph = "*"
langchain-openai = "*"
langchain-community = "*"
pydantic = "*"
python-magic = "*"
chardet = "*"
pygments = "*"
zipfile36 = "*"
chromadb = "*"
streamlit = "*"
streamlit-chat = "*"
streamlit-extras = "*"
python-dotenv = "*"
pytest = "*"
py2neo = "*"
click = "*"
pyyaml = "*"
lark = "*"
chevron = "*"

[dev-packages]

[requires]
python_version = "3.11"

[scripts]
ui = "streamlit run ./app.py"
